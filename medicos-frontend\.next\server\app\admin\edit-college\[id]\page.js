(()=>{var e={};e.id=6935,e.ids=[6935],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13484:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>P});var t=s(60687),a=s(43210),o=s(63239),n=s(63442),l=s(27605),i=s(45880),d=s(87834),c=s(29523),m=s(71669),p=s(12048),u=s(34729),x=s(78632),h=s(20140),g=s(16189),j=s(26423),f=s(31981);async function v(e){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let s=new FormData;s.append("file",e);let t=await fetch("http://localhost:3000/api/upload",{method:"POST",headers:{Authorization:`Bearer ${r}`},body:s});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||`Error: ${t.status}`)}return(await t.json()).url}catch(e){throw console.error("Error uploading file:",e),e}}let b=["image/jpeg","image/jpg","image/png","image/svg+xml"],y=i.Ik({collegeName:i.Yj().min(2,{message:"College name must be at least 2 characters."}),phone:i.Yj().min(10,{message:"Phone number must be valid."}),email:i.Yj().email({message:"Please enter a valid email address."}),address:i.Yj().max(100,{message:"Address must not exceed 100 characters."}),logo:i.bz().optional().refine(e=>!e||0===e.length||e[0]?.size<=0x3200000,"Max file size is 50MB.").refine(e=>!e||0===e.length||b.includes(e[0]?.type),"Only .jpg, .jpeg, .png and .svg formats are supported.")});function C({collegeData:e,collegeId:r}){let[s,o]=(0,a.useState)([]),[i,C]=(0,a.useState)(!1),[P,N]=(0,a.useState)(null),w=(0,g.useRouter)(),k=(0,l.mN)({resolver:(0,n.u)(y),defaultValues:{collegeName:"",phone:"",email:"",address:""}});async function q(e){C(!0);try{let t=P;s.length>0&&(t=await v(s[0]));let a={name:e.collegeName,address:e.address,contactPhone:e.phone,contactEmail:e.email,logoUrl:t||void 0},o=await (0,j.N0)(r,a);(0,f.cY)(o)&&w.push("/admin/college")}catch(e){console.error("Unexpected error updating college:",e),(0,h.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{C(!1)}}return(0,t.jsx)(m.lV,{...k,children:(0,t.jsxs)("form",{onSubmit:k.handleSubmit(q),className:"space-y-8 mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.zB,{control:k.control,name:"collegeName",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"College name"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(p.p,{placeholder:"",...e})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:k.control,name:"phone",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsxs)(m.lR,{children:["Phone",(0,t.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Required"})]}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(x.L,{...e})}),(0,t.jsx)(m.C5,{})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.zB,{control:k.control,name:"email",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Email address"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(p.p,{placeholder:"",...e})}),(0,t.jsx)(m.Rr,{children:"We'll never share your details."}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:k.control,name:"address",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(m.lR,{children:"Address details"}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.value.length,"/100"]})]}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(u.T,{placeholder:"",className:"resize-none",maxLength:100,...e})}),(0,t.jsx)(m.C5,{})]})})]}),(0,t.jsx)(m.zB,{control:k.control,name:"logo",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsxs)(m.lR,{children:["Upload logo",(0,t.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Optional"})]}),P&&(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Current logo:"}),(0,t.jsx)("div",{className:"w-20 h-20 bg-gray-100 rounded-md p-2 flex items-center justify-center",children:(0,t.jsx)("img",{src:P,alt:"Current logo",className:"max-w-full max-h-full object-contain"})})]}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.l,{value:e.value,onChange:r=>{e.onChange(r),o(Array.from(r||[]))},maxSize:0x3200000,acceptedTypes:b})}),(0,t.jsx)(m.Rr,{children:"Upload a new logo to replace the current one, or leave empty to keep the existing logo."}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(c.$,{type:"submit",className:"bg-[#05603A] hover:bg-[#04502F]",disabled:i,children:i?"Updating...":"Update college"}),(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:function(){w.push("/admin/college")},disabled:i,children:"Cancel"})]})]})})}let P=()=>{let[e,r]=(0,a.useState)(null),[s,n]=(0,a.useState)(!0),l=(0,g.useParams)(),i=(0,g.useRouter)(),d=l.id;return(0,a.useEffect)(()=>{let e=async()=>{try{let e=await (0,j.qk)(d);if(e.success&&e.data)r(e.data);else throw Error(e.message||"Failed to load college data")}catch(e){console.error("Failed to fetch college:",e),(0,h.o)({title:"Error",description:e.message||"Failed to load college data. Please try again.",variant:"destructive"}),i.push("/admin/college")}finally{n(!1)}};d&&e()},[d,i]),(0,t.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Edit College"}),(0,t.jsx)(o.A,{items:[{label:"Home",href:"/"},{label:"College Management",href:"/admin/college"},{label:"Edit College"}],className:"text-sm mt-1"})]}),(0,t.jsx)("div",{className:"container mx-auto py-10",children:s?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#05603A]"})}):(0,t.jsx)(C,{collegeData:e,collegeId:d})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36215:(e,r,s)=>{Promise.resolve().then(s.bind(s,13484))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63505:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=s(65239),a=s(48088),o=s(88170),n=s.n(o),l=s(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);s.d(r,i);let d={children:["",{children:["admin",{children:["edit-college",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,89113)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/edit-college/[id]/page",pathname:"/admin/edit-college/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89113:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\edit-college\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},99263:(e,r,s)=>{Promise.resolve().then(s.bind(s,89113))}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,4619,3287,9592,2581,1991,3442,8211,4707,6658,1037],()=>s(63505));module.exports=t})();