"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7546],{5623:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},42355:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},91769:(e,t,r)=>{r.d(t,{I:()=>k});var s=r(50920),i=r(7165),n=r(39853),u=r(25910),a=r(73504),h=r(52020),c=class extends u.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,a.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#s=void 0;#i=void 0;#n=void 0;#u;#a;#r;#t;#h;#c;#l;#o;#d;#p;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#s.addObserver(this),l(this.#s,this.options)?this.#y():this.updateResult(),this.#R())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return o(this.#s,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return o(this.#s,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#v(),this.#b(),this.#s.removeObserver(this)}setOptions(e){let t=this.options,r=this.#s;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,h.Eh)(this.options.enabled,this.#s))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#Q(),this.#s.setOptions(this.options),t._defaulted&&!(0,h.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#s,observer:this});let s=this.hasListeners();s&&d(this.#s,r,this.options,t)&&this.#y(),this.updateResult(),s&&(this.#s!==r||(0,h.Eh)(this.options.enabled,this.#s)!==(0,h.Eh)(t.enabled,this.#s)||(0,h.d2)(this.options.staleTime,this.#s)!==(0,h.d2)(t.staleTime,this.#s))&&this.#m();let i=this.#I();s&&(this.#s!==r||(0,h.Eh)(this.options.enabled,this.#s)!==(0,h.Eh)(t.enabled,this.#s)||i!==this.#p)&&this.#g(i)}getOptimisticResult(e){var t,r;let s=this.#e.getQueryCache().build(this.#e,e),i=this.createResult(s,e);return t=this,r=i,(0,h.f8)(t.getCurrentResult(),r)||(this.#n=i,this.#a=this.options,this.#u=this.#s.state),i}getCurrentResult(){return this.#n}trackResult(e,t){let r={};return Object.keys(e).forEach(s=>{Object.defineProperty(r,s,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(s),t?.(s),e[s])})}),r}trackProp(e){this.#f.add(e)}getCurrentQuery(){return this.#s}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#y({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#n))}#y(e){this.#Q();let t=this.#s.fetch(this.options,e);return e?.throwOnError||(t=t.catch(h.lQ)),t}#m(){this.#v();let e=(0,h.d2)(this.options.staleTime,this.#s);if(h.S$||this.#n.isStale||!(0,h.gn)(e))return;let t=(0,h.j3)(this.#n.dataUpdatedAt,e);this.#o=setTimeout(()=>{this.#n.isStale||this.updateResult()},t+1)}#I(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#s):this.options.refetchInterval)??!1}#g(e){this.#b(),this.#p=e,!h.S$&&!1!==(0,h.Eh)(this.options.enabled,this.#s)&&(0,h.gn)(this.#p)&&0!==this.#p&&(this.#d=setInterval(()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#y()},this.#p))}#R(){this.#m(),this.#g(this.#I())}#v(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}#b(){this.#d&&(clearInterval(this.#d),this.#d=void 0)}createResult(e,t){let r;let s=this.#s,i=this.options,u=this.#n,c=this.#u,o=this.#a,f=e!==s?e.state:this.#i,{state:y}=e,R={...y},v=!1;if(t._optimisticResults){let r=this.hasListeners(),u=!r&&l(e,t),a=r&&d(e,s,t,i);(u||a)&&(R={...R,...(0,n.k)(y.data,e.options)}),"isRestoring"===t._optimisticResults&&(R.fetchStatus="idle")}let{error:b,errorUpdatedAt:Q,status:m}=R;if(t.select&&void 0!==R.data){if(u&&R.data===c?.data&&t.select===this.#h)r=this.#c;else try{this.#h=t.select,r=t.select(R.data),r=(0,h.pl)(u?.data,r,t),this.#c=r,this.#t=null}catch(e){this.#t=e}}else r=R.data;if(void 0!==t.placeholderData&&void 0===r&&"pending"===m){let e;if(u?.isPlaceholderData&&t.placeholderData===o?.placeholderData)e=u.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#l?.state.data,this.#l):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#t=null}catch(e){this.#t=e}void 0!==e&&(m="success",r=(0,h.pl)(u?.data,e,t),v=!0)}this.#t&&(b=this.#t,r=this.#c,Q=Date.now(),m="error");let I="fetching"===R.fetchStatus,g="pending"===m,O="error"===m,E=g&&I,S=void 0!==r,T={status:m,fetchStatus:R.fetchStatus,isPending:g,isSuccess:"success"===m,isError:O,isInitialLoading:E,isLoading:E,data:r,dataUpdatedAt:R.dataUpdatedAt,error:b,errorUpdatedAt:Q,failureCount:R.fetchFailureCount,failureReason:R.fetchFailureReason,errorUpdateCount:R.errorUpdateCount,isFetched:R.dataUpdateCount>0||R.errorUpdateCount>0,isFetchedAfterMount:R.dataUpdateCount>f.dataUpdateCount||R.errorUpdateCount>f.errorUpdateCount,isFetching:I,isRefetching:I&&!g,isLoadingError:O&&!S,isPaused:"paused"===R.fetchStatus,isPlaceholderData:v,isRefetchError:O&&S,isStale:p(e,t),refetch:this.refetch,promise:this.#r};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===T.status?e.reject(T.error):void 0!==T.data&&e.resolve(T.data)},r=()=>{t(this.#r=T.promise=(0,a.T)())},i=this.#r;switch(i.status){case"pending":e.queryHash===s.queryHash&&t(i);break;case"fulfilled":("error"===T.status||T.data!==i.value)&&r();break;case"rejected":("error"!==T.status||T.error!==i.reason)&&r()}}return T}updateResult(){let e=this.#n,t=this.createResult(this.#s,this.options);this.#u=this.#s.state,this.#a=this.options,void 0!==this.#u.data&&(this.#l=this.#s),!(0,h.f8)(t,e)&&(this.#n=t,this.#O({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#f.size)return!0;let s=new Set(r??this.#f);return this.options.throwOnError&&s.add("error"),Object.keys(this.#n).some(t=>this.#n[t]!==e[t]&&s.has(t))})()}))}#Q(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#s)return;let t=this.#s;this.#s=e,this.#i=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#R()}#O(e){i.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#n)}),this.#e.getQueryCache().notify({query:this.#s,type:"observerResultsUpdated"})})}};function l(e,t){return!1!==(0,h.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&o(e,t,t.refetchOnMount)}function o(e,t,r){if(!1!==(0,h.Eh)(t.enabled,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function d(e,t,r,s){return(e!==t||!1===(0,h.Eh)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,h.Eh)(t.enabled,e)&&e.isStaleByTime((0,h.d2)(t.staleTime,e))}var f=r(12115),y=r(26715);r(95155);var R=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),v=()=>f.useContext(R);function b(){}var Q=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},m=e=>{f.useEffect(()=>{e.clearReset()},[e])},I=e=>{let{result:t,errorResetBoundary:r,throwOnError:s,query:i,suspense:n}=e;return t.isError&&!r.isReset()&&!t.isFetching&&i&&(n&&void 0===t.data||function(e,t){return"function"==typeof e?e(...t):!!e}(s,[t.error,i]))},g=f.createContext(!1),O=()=>f.useContext(g);g.Provider;var E=e=>{let t=e.staleTime;e.suspense&&(e.staleTime="function"==typeof t?(...e)=>Math.max(t(...e),1e3):Math.max(t??1e3,1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},S=(e,t)=>e.isLoading&&e.isFetching&&!t,T=(e,t)=>e?.suspense&&t.isPending,C=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function k(e,t){return function(e,t,r){var s,n,u,a,c;let l=(0,y.jE)(r),o=O(),d=v(),p=l.defaultQueryOptions(e);null===(n=l.getDefaultOptions().queries)||void 0===n||null===(s=n._experimental_beforeQuery)||void 0===s||s.call(n,p),p._optimisticResults=o?"isRestoring":"optimistic",E(p),Q(p,d),m(d);let R=!l.getQueryCache().get(p.queryHash),[g]=f.useState(()=>new t(l,p)),k=g.getOptimisticResult(p),x=!o&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback(e=>{let t=x?g.subscribe(i.jG.batchCalls(e)):b;return g.updateResult(),t},[g,x]),()=>g.getCurrentResult(),()=>g.getCurrentResult()),f.useEffect(()=>{g.setOptions(p)},[p,g]),T(p,k))throw C(p,g,d);if(I({result:k,errorResetBoundary:d,throwOnError:p.throwOnError,query:l.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw k.error;if(null===(a=l.getDefaultOptions().queries)||void 0===a||null===(u=a._experimental_afterQuery)||void 0===u||u.call(a,p,k),p.experimental_prefetchInRender&&!h.S$&&S(k,o)){let e=R?C(p,g,d):null===(c=l.getQueryCache().get(p.queryHash))||void 0===c?void 0:c.promise;null==e||e.catch(b).finally(()=>{g.updateResult()})}return p.notifyOnChangeProps?k:g.trackResult(k)}(e,c,t)}}}]);