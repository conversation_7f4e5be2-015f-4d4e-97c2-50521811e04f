"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1557],{11557:(e,t,a)=>{a.d(t,{default:()=>h});var r=a(26597),i=a(18125),n=a(22638);class s{static renderToHTML(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a={...this.DEFAULT_OPTIONS,...t};try{console.log("Rendering LaTeX expression: ".concat(e));let t=this.cleanExpression(e);if(!t.trim())return{html:"",success:!1,error:"Empty expression",fallbackText:""};let r=n.Ay.renderToString(t,a);return console.log("Successfully rendered: ".concat(t)),{html:r,success:!0}}catch(r){console.warn('KaTeX rendering failed for "'.concat(e,'":'),r);let t=this.generateFallbackText(e);return{html:'<span style="color: '.concat(a.errorColor,';">').concat(t,"</span>"),success:!1,error:r instanceof Error?r.message:String(r),fallbackText:t}}}static cleanExpression(e){return e.trim().replace(/^\$+/,"").replace(/\$+$/,"").replace(/\s+/g," ").trim()}static generateFallbackText(e){return e.replace(/^\$+/,"").replace(/\$+$/,"").replace(/\\Rightarrow/g,"⇒").replace(/\\Leftarrow/g,"⇐").replace(/\\Leftrightarrow/g,"⇔").replace(/\\rightarrow/g,"→").replace(/\\leftarrow/g,"←").replace(/\\leftrightarrow/g,"↔").replace(/\\geq/g,"≥").replace(/\\leq/g,"≤").replace(/\\neq/g,"≠").replace(/\\approx/g,"≈").replace(/\\equiv/g,"≡").replace(/\\sim/g,"∼").replace(/\\pm/g,"\xb1").replace(/\\mp/g,"∓").replace(/\\times/g,"\xd7").replace(/\\div/g,"\xf7").replace(/\\cdot/g,"⋅").replace(/\\alpha/g,"α").replace(/\\beta/g,"β").replace(/\\gamma/g,"γ").replace(/\\delta/g,"δ").replace(/\\epsilon/g,"ε").replace(/\\theta/g,"θ").replace(/\\lambda/g,"λ").replace(/\\mu/g,"μ").replace(/\\pi/g,"π").replace(/\\sigma/g,"σ").replace(/\\phi/g,"φ").replace(/\\omega/g,"ω").replace(/\\sum/g,"∑").replace(/\\prod/g,"∏").replace(/\\int/g,"∫").replace(/\\in/g,"∈").replace(/\\notin/g,"∉").replace(/\\subset/g,"⊂").replace(/\\supset/g,"⊃").replace(/\\cup/g,"∪").replace(/\\cap/g,"∩").replace(/\\forall/g,"∀").replace(/\\exists/g,"∃").replace(/\\emptyset/g,"∅").replace(/\\infty/g,"∞").replace(/\\partial/g,"∂").replace(/\\nabla/g,"∇").replace(/\\mathbb{N}/g,"ℕ").replace(/\\mathbb{Z}/g,"ℤ").replace(/\\mathbb{Q}/g,"ℚ").replace(/\\mathbb{R}/g,"ℝ").replace(/\\mathbb{C}/g,"ℂ").replace(/\\sqrt{([^}]+)}/g,"√($1)").replace(/\\frac{([^}]+)}{([^}]+)}/g,"($1)/($2)").replace(/\\left\(/g,"(").replace(/\\right\)/g,")").replace(/\\left\[/g,"[").replace(/\\right\]/g,"]").replace(/\\left\{/g,"{").replace(/\\right\}/g,"}").replace(/\\left\|/g,"|").replace(/\\right\|/g,"|").replace(/\\left/g,"").replace(/\\right/g,"").replace(/\\ldots/g,"...").replace(/\\cdots/g,"⋯").replace(/\^{([^}]+)}/g,"^($1)").replace(/_{([^}]+)}/g,"_($1)").replace(/\^(\w)/g,"^$1").replace(/_(\w)/g,"_$1").replace(/\\mathrm{([^}]+)}/g,"$1").replace(/\\mathbf{([^}]+)}/g,"$1").replace(/\\text{([^}]+)}/g,"$1").replace(/\\[a-zA-Z]+\{([^}]*)\}/g,"$1").replace(/\\[a-zA-Z]+/g,"").replace(/\s+/g," ").trim()||e}static extractMathExpressions(e){let t;let a=[],r=/(\$\$[\s\S]*?\$\$|\$[^$]*?\$)/g;for(;null!==(t=r.exec(e));){let e;let r=t[0],i=r.startsWith("$$")&&r.endsWith("$$");e=i?r.slice(2,-2).trim():r.slice(1,-1).trim(),a.push({expression:e,startIndex:t.index,endIndex:t.index+r.length,isBlock:i})}return a}static processTextWithMath(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=this.extractMathExpressions(e);if(0===a.length)return e;let r="",i=0;for(let n of a){r+=e.substring(i,n.startIndex);let a=this.renderToHTML(n.expression,{...t,displayMode:n.isBlock});a.success?r+=a.html:r+=a.fallbackText||n.expression,i=n.endIndex}return r+e.substring(i)}}s.DEFAULT_OPTIONS={displayMode:!1,throwOnError:!1,errorColor:"#cc0000",strict:!1,trust:!1,macros:{},colorIsTextColor:!0,maxSize:500,maxExpand:1e3};var l=a(49641).Buffer;class o{static async renderMathToSVG(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={...this.DEFAULT_OPTIONS,...a};try{console.log("Rendering math to SVG:",e);let a=s.renderToHTML(e,{displayMode:t,throwOnError:!1,strict:!1}).fallbackText||e,i=t?1.3*r.fontSize:r.fontSize,n=a.length,o=.55*i,c=Math.min(n*o,r.maxWidth),h=1.1*i,g=c+2*r.padding,d=h+2*r.padding,p=this.createMathSVG(a,g,d,i,r),u="data:image/svg+xml;base64,".concat(l.from(p).toString("base64"));return console.log("Successfully rendered math to SVG"),{svg:p,dataUrl:u,width:g,height:d,success:!0}}catch(i){console.error("SVG math rendering failed:",i);let t=this.createErrorSVG(e,r),a="data:image/svg+xml;base64,".concat(l.from(t).toString("base64"));return{svg:t,dataUrl:a,width:100,height:30,success:!1,error:i instanceof Error?i.message:String(i)}}}static createMathSVG(e,t,a,r,i){return'\n      <svg width="'.concat(t,'" height="').concat(a,'" xmlns="http://www.w3.org/2000/svg">\n        <defs>\n          <style>\n            .math-text {\n              font-family: ').concat(i.fontFamily,";\n              font-size: ").concat(r,"px;\n              fill: ").concat(i.color,";\n              text-anchor: middle;\n              dominant-baseline: alphabetic;\n            }\n          </style>\n        </defs>\n        ").concat("transparent"!==i.backgroundColor?'<rect width="100%" height="100%" fill="'.concat(i.backgroundColor,'"/>'):"",'\n        <text x="').concat(t/2,'" y="').concat(.75*a,'" class="math-text">').concat(this.escapeXML(e),"</text>\n      </svg>\n    ").trim()}static createErrorSVG(e,t){return'\n      <svg width="100" height="30" xmlns="http://www.w3.org/2000/svg">\n        <rect width="100%" height="100%" fill="#f8f8f8" stroke="#ddd" stroke-width="1"/>\n        <text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" \n              font-family="monospace" font-size="10" fill="#666">\n          Math Error\n        </text>\n      </svg>\n    '.trim()}static escapeXML(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")}static async renderMultipleMath(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=new Map;for(let r of e)try{let e=await this.renderMathToSVG(r.expression,r.isBlock,t);a.set(r.id,e)}catch(i){console.error("Failed to render math expression ".concat(r.id,":"),i);let e={svg:this.createErrorSVG(r.expression,t),dataUrl:"",width:100,height:30,success:!1,error:i instanceof Error?i.message:String(i)};a.set(r.id,e)}return a}static estimateDimensions(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r={...this.DEFAULT_OPTIONS,...a},i=s.renderToHTML(e,{displayMode:t,throwOnError:!1}).fallbackText||e,n=t?1.2*r.fontSize:r.fontSize;return{width:Math.min(i.length*(.6*n),r.maxWidth)+2*r.padding,height:1.2*n+2*r.padding}}static isComplexExpression(e){return[/\\frac/,/\\sqrt/,/\\sum/,/\\int/,/\\prod/,/\\matrix/,/\\begin/,/\^{[^}]{2,}}/,/_{[^}]{2,}}/].some(t=>t.test(e))}}o.DEFAULT_OPTIONS={fontSize:16,fontFamily:"Times New Roman, serif",color:"#000000",backgroundColor:"transparent",padding:4,maxWidth:400};class c{getCurrentColumnX(){return"left"===this.currentColumn?this.leftColumnX:this.rightColumnX}getCurrentColumnY(){return"left"===this.currentColumn?this.leftColumnY:this.rightColumnY}updateCurrentColumnY(e){"left"===this.currentColumn?this.leftColumnY=e:this.rightColumnY=e}switchToNextColumn(){"left"===this.currentColumn?this.currentColumn="right":this.currentColumn="left"}moveToNextAvailablePosition(){return"left"!==this.currentColumn||(this.currentColumn="right",!1)}resetColumnsForNewPage(){this.currentColumn="left",this.leftColumnY=this.pageContentStartY,this.rightColumnY=this.pageContentStartY}async checkColumnPageBreak(e,t,a){if(this.getCurrentColumnY()+e>this.pageHeight-this.margin){if(this.moveToNextAvailablePosition())return await this.addNewPage(t,a),!0;this.updateCurrentColumnY(this.pageContentStartY)}return!1}addColumnSeparator(){let e=this.margin+this.columnWidth+this.columnGap/2;this.doc.setDrawColor(150,150,150),this.doc.line(e,this.currentY,e,this.pageHeight-this.margin)}async addWatermark(e){let t=e.logoUrl||"/assets/logo/medicos-logo.svg";if(!t){console.log("No logo URL available for watermark.");return}try{let e;if(console.log("Attempting to add watermark..."),console.log("Watermark image URL:",t),t.startsWith("data:"))e=t;else{let a=await fetch(t,{mode:"cors",credentials:"omit"});if(console.log("Fetched watermark image, status:",a.status),!a.ok)throw Error("Failed to fetch watermark image from ".concat(t," with status ").concat(a.status));let r=await a.blob();console.log("Watermark image blob created, type:",r.type),e=await new Promise((e,t)=>{let a=new FileReader;a.onload=()=>e(a.result),a.onerror=t,a.readAsDataURL(r)})}console.log("Watermark image data URL created.");let a=this.doc.internal.getNumberOfPages();console.log("Adding watermark to ".concat(a," pages."));let r=e.includes("data:image/png")?"PNG":e.includes("data:image/jpeg")||e.includes("data:image/jpg")?"JPEG":e.includes("data:image/svg")?"SVG":"PNG";for(let t=1;t<=a;t++){this.doc.setPage(t),this.doc.saveGraphicsState(),this.doc.setGState(new this.doc.GState({opacity:.1}));let a=this.doc.internal.pageSize.getWidth(),i=this.doc.internal.pageSize.getHeight(),n=(a-110)/2,s=(i-110)/2;this.doc.addImage(e,r,n,s,110,110),this.doc.restoreGraphicsState()}console.log("Watermark added successfully.")}catch(e){console.error("Failed to add watermark image: ",e);try{let e=this.doc.internal.getNumberOfPages();for(let t=1;t<=e;t++){this.doc.setPage(t),this.doc.saveGraphicsState(),this.doc.setGState(new this.doc.GState({opacity:.1})),this.doc.setFontSize(48),this.doc.setTextColor(200,200,200);let e=this.doc.internal.pageSize.getWidth(),a=this.doc.internal.pageSize.getHeight();this.doc.text("MEDICOS",e/2,a/2,{align:"center",angle:45}),this.doc.restoreGraphicsState()}console.log("Fallback text watermark added.")}catch(e){console.error("Failed to add fallback watermark:",e)}}}async addHeader(e,t){let a=this.margin,r=this.margin,n=this.pageWidth-this.margin,s=this.pageWidth/2,l=(this.pageWidth-2*this.margin)/3,o=0,c=0,h=0;if(e){let t=r;if(e.logoUrl)try{let r=await (0,i.UF)(e.logoUrl),n=new Image;n.src=r,await new Promise((e,t)=>{n.onload=()=>e(),n.onerror=t});let s=10*n.width/n.height;s>0&&(this.doc.addImage(r,"PNG",t,a,s,10),t+=s+2,o=Math.max(o,10))}catch(e){console.error("Failed to load college logo",e)}this.doc.setFont("helvetica","bold"),this.doc.setFontSize(this.FONT_SIZES.COLLEGE_NAME);let n=this.doc.splitTextToSize(e.name,l-(t-r));this.doc.text(n,t,a+4),o=Math.max(o,4*n.length)}this.doc.setFont("helvetica","bold"),this.doc.setFontSize(this.FONT_SIZES.TITLE);let g=this.doc.splitTextToSize(t.title,l+10);this.doc.text(g,s,a+4,{align:"center"}),c=5*g.length,this.doc.setFont("helvetica","normal"),this.doc.setFontSize(this.FONT_SIZES.EXAM_DETAILS);let d="Duration: ".concat(t.duration," mins"),p="Total Marks: ".concat(t.totalMarks);this.doc.text(d,n,a+2,{align:"right"}),this.doc.text(p,n,a+2+5,{align:"right"}),h=12,this.currentY=a+Math.max(o,c,h)+3,this.doc.setDrawColor(0),this.doc.setLineWidth(.5),this.doc.line(this.margin,this.currentY,this.pageWidth-this.margin,this.currentY),this.currentY+=5,this.doc.setLineWidth(.2),this.pageContentStartY=this.currentY}checkPageBreak(e){return this.currentY+e>this.pageHeight-this.margin&&(this.addNewPage(),!0)}async addNewPage(e,t){this.addInstitutionFooter(),this.doc.addPage(),this.resetColumnsForNewPage(),this.currentY=this.margin,t&&this.addSubjectHeader(t),this.addColumnSeparator()}addSubjectHeader(e){this.doc.setFontSize(this.FONT_SIZES.PART_HEADER),this.doc.setFont("helvetica","bold"),this.doc.text("Subject: ".concat(e),this.margin,this.currentY),this.currentY+=12}addPartHeader(e,t){this.doc.setFontSize(this.FONT_SIZES.PART_HEADER),this.doc.setFont("helvetica","bold");let a="PART - ".concat(e," (").concat(t.toUpperCase(),")");this.doc.text(a,this.margin,this.currentY),this.currentY+=8,this.doc.setDrawColor(0),this.doc.line(this.margin,this.currentY,this.pageWidth-this.margin,this.currentY),this.currentY+=5}addSectionHeader(e,t){this.doc.setFontSize(this.FONT_SIZES.SECTION_HEADER),this.doc.setFont("helvetica","bold");let a="".concat(e," (Questions ").concat(t,")");this.doc.text(a,this.margin,this.currentY),this.currentY+=6}addMarkingScheme(){this.doc.setFontSize(this.FONT_SIZES.MARKING_SCHEME),this.doc.setFont("helvetica","italic"),this.doc.text("Marking Scheme: +4 for correct, -1 for incorrect, 0 for unattempted.",this.pageWidth-this.margin,this.currentY,{align:"right"}),this.currentY+=8}async renderMathAsImage(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;console.log("Rendering math expression as SVG image:",e);try{let n=await o.renderMathToSVG(e,t,{fontSize:t?18:16,backgroundColor:"transparent",padding:2,maxWidth:i});if(n.success&&n.dataUrl){let e=n.width,t=n.height;if(e>i){let a=i/e;e=i,t*=a}let s=r-.7*t;return this.doc.addImage(n.dataUrl,"SVG",a,s,e,t),console.log("Successfully added math SVG to PDF"),{success:!0,width:e,height:t,newX:a+e+2,newY:r}}return console.log("SVG rendering failed, falling back to text"),this.renderMathAsText(e,t,a,r,i)}catch(n){return console.error("Math SVG rendering error:",n),this.renderMathAsText(e,t,a,r,i)}}async renderMathAsText(e,t,a,r,i){let n=this.convertMathToUnicode(e),s=this.doc.getTextWidth(n);if(s<=i)return this.doc.text(n,a,r),{success:!0,width:s,height:16,newX:a+s,newY:r};{let e=i/s;return this.doc.setFontSize(this.doc.getFontSize()*e),this.doc.text(n,a,r),this.doc.setFontSize(this.doc.getFontSize()/e),{success:!0,width:i,height:16*e,newX:a+i,newY:r}}}convertMathToUnicode(e){console.log("Using Unicode fallback for:",e);let t=e.replace(/\\Rightarrow/g,"⇒").replace(/\\Leftarrow/g,"⇐").replace(/\\Leftrightarrow/g,"⇔").replace(/\\rightarrow/g,"→").replace(/\\leftarrow/g,"←").replace(/\\leftrightarrow/g,"↔").replace(/\\uparrow/g,"↑").replace(/\\downarrow/g,"↓").replace(/\\updownarrow/g,"↕").replace(/\\Uparrow/g,"⇑").replace(/\\Downarrow/g,"⇓").replace(/\\Updownarrow/g,"⇕").replace(/\\geq/g,"≥").replace(/\\leq/g,"≤").replace(/\\neq/g,"≠").replace(/\\approx/g,"≈").replace(/\\equiv/g,"≡").replace(/\\sim/g,"∼").replace(/\\simeq/g,"≃").replace(/\\cong/g,"≅").replace(/\\propto/g,"∝").replace(/\\pm/g,"\xb1").replace(/\\mp/g,"∓").replace(/\\times/g,"\xd7").replace(/\\div/g,"\xf7").replace(/\\cdot/g,"⋅").replace(/\\ast/g,"∗").replace(/\\star/g,"⋆").replace(/\\circ/g,"∘").replace(/\\bullet/g,"•").replace(/\\oplus/g,"⊕").replace(/\\ominus/g,"⊖").replace(/\\otimes/g,"⊗").replace(/\\oslash/g,"⊘").replace(/\\alpha/g,"α").replace(/\\beta/g,"β").replace(/\\gamma/g,"γ").replace(/\\delta/g,"δ").replace(/\\epsilon/g,"ε").replace(/\\varepsilon/g,"ε").replace(/\\zeta/g,"ζ").replace(/\\eta/g,"η").replace(/\\theta/g,"θ").replace(/\\vartheta/g,"ϑ").replace(/\\iota/g,"ι").replace(/\\kappa/g,"κ").replace(/\\lambda/g,"λ").replace(/\\mu/g,"μ").replace(/\\nu/g,"ν").replace(/\\xi/g,"ξ").replace(/\\pi/g,"π").replace(/\\varpi/g,"ϖ").replace(/\\rho/g,"ρ").replace(/\\varrho/g,"ϱ").replace(/\\sigma/g,"σ").replace(/\\varsigma/g,"ς").replace(/\\tau/g,"τ").replace(/\\upsilon/g,"υ").replace(/\\phi/g,"φ").replace(/\\varphi/g,"φ").replace(/\\chi/g,"χ").replace(/\\psi/g,"ψ").replace(/\\omega/g,"ω").replace(/\\Gamma/g,"Γ").replace(/\\Delta/g,"Δ").replace(/\\Theta/g,"Θ").replace(/\\Lambda/g,"Λ").replace(/\\Xi/g,"Ξ").replace(/\\Pi/g,"Π").replace(/\\Sigma/g,"Σ").replace(/\\Upsilon/g,"Υ").replace(/\\Phi/g,"Φ").replace(/\\Psi/g,"Ψ").replace(/\\Omega/g,"Ω").replace(/\\sum/g,"∑").replace(/\\prod/g,"∏").replace(/\\coprod/g,"∐").replace(/\\int/g,"∫").replace(/\\iint/g,"∬").replace(/\\iiint/g,"∭").replace(/\\oint/g,"∮").replace(/\\bigcup/g,"⋃").replace(/\\bigcap/g,"⋂").replace(/\\bigoplus/g,"⨁").replace(/\\bigotimes/g,"⨂").replace(/\\in/g,"∈").replace(/\\notin/g,"∉").replace(/\\ni/g,"∋").replace(/\\subset/g,"⊂").replace(/\\supset/g,"⊃").replace(/\\subseteq/g,"⊆").replace(/\\supseteq/g,"⊇").replace(/\\cup/g,"∪").replace(/\\cap/g,"∩").replace(/\\setminus/g,"∖").replace(/\\forall/g,"∀").replace(/\\exists/g,"∃").replace(/\\nexists/g,"∄").replace(/\\emptyset/g,"∅").replace(/\\varnothing/g,"∅").replace(/\\infty/g,"∞").replace(/\\partial/g,"∂").replace(/\\nabla/g,"∇").replace(/\\angle/g,"∠").replace(/\\triangle/g,"△").replace(/\\square/g,"□").replace(/\\diamond/g,"◊").replace(/\\clubsuit/g,"♣").replace(/\\diamondsuit/g,"♦").replace(/\\heartsuit/g,"♥").replace(/\\spadesuit/g,"♠").replace(/\\mathbb{N}/g,"ℕ").replace(/\\mathbb{Z}/g,"ℤ").replace(/\\mathbb{Q}/g,"ℚ").replace(/\\mathbb{R}/g,"ℝ").replace(/\\mathbb{C}/g,"ℂ").replace(/\\mathbb{P}/g,"ℙ").replace(/\\sqrt{([^}]+)}/g,"√($1)").replace(/\\frac{([^}]+)}{([^}]+)}/g,"($1)/($2)").replace(/\\binom{([^}]+)}{([^}]+)}/g,"C($1,$2)").replace(/\\choose/g,"C").replace(/\\left\(/g,"(").replace(/\\right\)/g,")").replace(/\\left\[/g,"[").replace(/\\right\]/g,"]").replace(/\\left\{/g,"{").replace(/\\right\}/g,"}").replace(/\\left\|/g,"|").replace(/\\right\|/g,"|").replace(/\\left</g,"⟨").replace(/\\right>/g,"⟩").replace(/\\left/g,"").replace(/\\right/g,"").replace(/\\ldots/g,"...").replace(/\\cdots/g,"⋯").replace(/\\vdots/g,"⋮").replace(/\\ddots/g,"⋱").replace(/\^{([^}]+)}/g,"^($1)").replace(/_{([^}]+)}/g,"_($1)").replace(/\^(\w)/g,"^$1").replace(/_(\w)/g,"_$1").replace(/\\mathrm{([^}]+)}/g,"$1").replace(/\\mathbf{([^}]+)}/g,"$1").replace(/\\mathit{([^}]+)}/g,"$1").replace(/\\mathcal{([^}]+)}/g,"$1").replace(/\\text{([^}]+)}/g,"$1").replace(/\\textbf{([^}]+)}/g,"$1").replace(/\\textit{([^}]+)}/g,"$1").replace(/\\[a-zA-Z]+\{([^}]*)\}/g,"$1").replace(/\\[a-zA-Z]+/g,"").replace(/\s+/g," ").trim().replace(/\$([^$]+)\$/g,"$1").replace(/\$\$([^$]+)\$\$/g,"$1");return console.log("Converted to:",t),t}addInstitutionFooter(){let e=this.pageHeight-10;this.doc.setFontSize(this.FONT_SIZES.FOOTER),this.doc.setFont("helvetica","italic"),this.doc.text("Generated by Medicos - ".concat(new Date().toLocaleDateString()),this.pageWidth/2,e,{align:"center"})}detectQuestionType(e){return e.content.toLowerCase().includes("assertion")&&e.content.toLowerCase().includes("reason")?"assertion-reason":e.content.toLowerCase().includes("column i")&&e.content.toLowerCase().includes("column ii")?"match-columns":"standard"}formatAssertionReasonQuestion(e){return e}formatMatchColumnsQuestion(e){return e}processTextWithImages(e){let{cleanText:t,images:a}=(0,i.Xw)(e);return{cleanText:t,images:a}}async addImageToPDF(e,t,a,r){if(!(0,i.XI)(e))return console.error("Image source is not a base64 string."),0;let n=(0,i.UF)(e),s=new Image;s.src=n,await new Promise(e=>s.onload=e);let l=s.width/s.height,o=Math.min(r,s.width),c=o/l;return this.doc.addImage(n,"JPEG",t,a,o,c),c}standardizeOptions(e){return e.map(e=>e.replace(/\n/g,""))}async addRichContent(e,t,a,r,i){let n;let s=/(\$\$[^$]+\$\$|\$[^$]+?\$)/g,l=0,o=this.getCurrentColumnX(),c=this.getCurrentColumnY(),h=this.getCurrentColumnX(),g=async()=>{this.updateCurrentColumnY(this.getCurrentColumnY()+6),await this.checkColumnPageBreak(6,r,i),c=this.getCurrentColumnY(),o=this.getCurrentColumnX()};for(;null!==(n=s.exec(e));){let t=e.substring(l,n.index).trim();t&&(await this.renderSimpleText(t,o,c,a,h,g),o=this.getCurrentColumnX(),c=this.getCurrentColumnY());let r=n[0],i=r.startsWith("$$"),d=i?r.slice(2,-2).trim():r.slice(1,-1).trim();d&&(await this.renderSimpleMath(d,i,o,c,a,h,g),o=this.getCurrentColumnX(),c=this.getCurrentColumnY()),l=s.lastIndex}let d=e.substring(l).trim();d&&await this.renderSimpleText(d,o,c,a,h,g),this.updateCurrentColumnY(this.getCurrentColumnY()+6)}async renderSimpleText(e,t,a,r,i,n){let s=e.split(" ").filter(e=>e.length>0),l=t;for(let e of s){let t=this.doc.getTextWidth(e),s=this.doc.getTextWidth(" "),o=l>i;l+(t+(o?s:0))>i+r-5?(await n(),l=this.getCurrentColumnX(),a=this.getCurrentColumnY()):o&&(this.doc.text(" ",l,a),l+=s),this.doc.text(e,l,a),l+=t}this.updateCurrentColumnX(l)}async renderSimpleMath(e,t,a,r,i,n,s){try{let s=await this.renderMathAsImage(e,t,a,r,i-(a-n));if(s.success){this.updateCurrentColumnX(s.newX);return}}catch(e){console.warn("Math image rendering failed:",e)}let l=this.convertMathToUnicode(e),o=this.doc.getTextWidth(l);a+o>n+i-5&&(await s(),a=this.getCurrentColumnX(),r=this.getCurrentColumnY()),this.doc.text(l,a,r),this.updateCurrentColumnX(a+o)}updateCurrentColumnX(e){this.getCurrentColumnX()}async addQuestion(e,t,a,r,i){let n=this.estimateQuestionHeight(e,a);await this.checkColumnPageBreak(n,r,i);let s=this.getCurrentColumnX();this.doc.setFontSize(this.FONT_SIZES.QUESTION_TEXT),this.doc.setFont("helvetica","normal");let l="".concat(t,". ").concat(e.content);if(await this.addRichContent(l,s,this.columnWidth,r,i),e.options&&e.options.length>0){this.updateCurrentColumnY(this.getCurrentColumnY()+2),this.doc.setFontSize(this.FONT_SIZES.OPTION_TEXT);for(let t=0;t<e.options.length;t++){let a="(".concat(String.fromCharCode(97+t),") ").concat(e.options[t]);await this.addRichContent(a,s,this.columnWidth,r,i),this.updateCurrentColumnY(this.getCurrentColumnY()+1)}}if(a&&e.answer){this.updateCurrentColumnY(this.getCurrentColumnY()+2),this.doc.setFontSize(this.FONT_SIZES.ANSWER_TEXT),this.doc.setFont("helvetica","bold");let t="Ans: ".concat(e.answer);await this.addRichContent(t,s,this.columnWidth,r,i),this.doc.setFont("helvetica","normal")}this.updateCurrentColumnY(this.getCurrentColumnY()+3)}async addQuestionWithSolutions(e,t,a,r,i,n,s){await this.addQuestion(e,t,a,n,s);let l=this.getCurrentColumnX();if(r&&e.solution){this.updateCurrentColumnY(this.getCurrentColumnY()+2),this.doc.setFontSize(this.FONT_SIZES.SOLUTION_HEADER),this.doc.setFont("helvetica","bold"),await this.addRichContent("Solution:",l,this.columnWidth,n,s),this.doc.setFontSize(this.FONT_SIZES.SOLUTION_TEXT),this.doc.setFont("helvetica","normal");let t=e.solution.final_explanation||(e.solution.steps||[]).join("\n");await this.addRichContent(t,l,this.columnWidth,n,s)}if(i&&e.hints&&e.hints.length>0){this.updateCurrentColumnY(this.getCurrentColumnY()+2),this.doc.setFontSize(this.FONT_SIZES.SOLUTION_HEADER),this.doc.setFont("helvetica","bold"),await this.addRichContent("Hints:",l,this.columnWidth,n,s),this.doc.setFontSize(this.FONT_SIZES.HINT_TEXT),this.doc.setFont("helvetica","normal");let t=e.hints.map(e=>"• ".concat(e)).join("\n");await this.addRichContent(t,l,this.columnWidth,n,s)}}estimateQuestionHeight(e,t){let a=0,i=new r.Ay;return i.setFontSize(this.FONT_SIZES.QUESTION_TEXT),a+=4*i.splitTextToSize(e.content,this.columnWidth).length,e.options&&e.options.length>0&&(a+=20),t&&e.answer&&(a+=6),a+=10}estimateQuestionHeightWithSolutions(e,t,a,r){let i=this.estimateQuestionHeight(e,t);return a&&e.solution&&(i+=40),r&&e.hints&&e.hints.length>0&&(i+=20),i}async generatePDF(e,t){return e.isMultiSubject?await this.generateMultiSubjectPDF(e,t):await this.generateSingleSubjectPDF(e,t),await this.addWatermark(t||{}),this.doc.output("blob")}async generateSingleSubjectPDF(e,t){var a;await this.addHeader(t,e),(null===(a=e.subjectId)||void 0===a?void 0:a.name)&&this.addSubjectHeader(e.subjectId.name),this.resetColumnsForNewPage(),this.leftColumnY=this.currentY,this.rightColumnY=this.currentY,this.addColumnSeparator();for(let a=0;a<e.questions.length;a++){let r=e.questions[a];try{e.withSolutions||e.withHints?await this.addQuestionWithSolutions(r,a+1,e.withAnswers,e.withSolutions||!1,e.withHints||!1,t):await this.addQuestion(r,a+1,e.withAnswers,t)}catch(e){console.error("Error processing question ".concat(a+1,":"),e)}}this.addInstitutionFooter()}async generateMultiSubjectPDF(e,t){if(!e.sections)return;let a=1;await this.addHeader(t,e),this.resetColumnsForNewPage(),this.leftColumnY=this.currentY,this.rightColumnY=this.currentY;for(let i=0;i<e.sections.length;i++){var r;let n=e.sections[i],s=n.subjectName||n.name||"Subject ".concat(i+1),l=String.fromCharCode(65+i);if(i>0&&await this.addNewPage(t,s),this.addPartHeader(l,s),this.addSectionHeader("Section - I: Single Correct",(null===(r=n.questions)||void 0===r?void 0:r.length)||0),this.addColumnSeparator(),n.questions&&n.questions.length>0)for(let r of n.questions){let i=r.question;try{e.withSolutions||e.withHints?await this.addQuestionWithSolutions(i,a,e.withAnswers,e.withSolutions||!1,e.withHints||!1,t,s):await this.addQuestion(i,a,e.withAnswers,t,s),a++}catch(e){console.error("Error processing question ".concat(a,":"),e),a++}}}this.addInstitutionFooter()}constructor(){this.FONT_SIZES={TITLE:14,PART_HEADER:12,SECTION_HEADER:11,COLLEGE_NAME:12,COLLEGE_ADDRESS:9,EXAM_DETAILS:10,QUESTION_NUMBER:10,QUESTION_TEXT:10,OPTION_TEXT:9,ANSWER_TEXT:10,SOLUTION_HEADER:10,SOLUTION_TEXT:9,HINT_TEXT:9,MARKING_SCHEME:9,INSTRUCTIONS:8,WATERMARK:60,LOGO:8,FOOTER:8},this.doc=new r.Ay("p","mm","a4"),this.pageWidth=this.doc.internal.pageSize.getWidth(),this.pageHeight=this.doc.internal.pageSize.getHeight(),this.margin=20,this.currentY=this.margin,this.columnGap=8,this.columnWidth=(this.pageWidth-2*this.margin-this.columnGap)/2,this.leftColumnX=this.margin,this.rightColumnX=this.margin+this.columnWidth+this.columnGap,this.currentColumn="left",this.leftColumnY=this.margin,this.rightColumnY=this.margin,this.pageContentStartY=this.margin,this.contentWidth=this.pageWidth-2*this.margin;try{this.doc.setFont("times","normal")}catch(e){console.warn("Times font not available, using default"),this.doc.setFont("helvetica","normal")}}}let h=c},18125:(e,t,a)=>{function r(e){return!!e&&"string"==typeof e&&(!!/^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i.test(e)||e.length>100&&/^[A-Za-z0-9+/]*={0,2}$/.test(e))}function i(e){if(!e)return"";let t=/^(data:image\/[^;]+;base64,)(data:image\/[^;]+;base64,)/;t.test(e)&&(e=e.replace(t,"$2"));let a=e.match(/^(data:image\/[^;]+;base64,)+/);if(a){let t=a[0].length,r=e.substring(t),i=a[0].match(/data:image\/[^;]+;base64,$/);if(i)return i[0]+r}return e.startsWith("data:image/")?e:"data:image/jpeg;base64,".concat(e)}function n(e,t){if(!e)return{cleanText:e,images:[]};let a=[],n=e,s=[...e.matchAll(/<img\s+[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*\/?>/gi)];s.length>0&&s.forEach((e,t)=>{let s=e[0],l=e[1],o=e[2]||"Image ".concat(a.length+1);if((l=l.trim()).includes("base64,")||r(l)){let e=i(l);a.push({id:"extracted-image-html-".concat(t),src:e,alt:o}),n=n.replace(s,"")}});let l=[...n.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g)];l.length>0&&l.forEach((e,s)=>{let l=e[0],o=e[1]||"Image ".concat(a.length+1),c=e[2];if((c=c.trim()).includes("base64,")||r(c)){let e=i(c);a.push({id:"extracted-image-markdown-".concat(s),src:e,alt:o}),n=n.replace(l,"")}else if(t){let e=function(e,t){if(t[e])return e;for(let a of[e,e.replace(".jpeg","").replace(".jpg","").replace(".png",""),"img-".concat(e),"image-".concat(e),e.replace("img-","").replace("image-","")])for(let e of Object.keys(t))if(e.includes(a)||a.includes(e))return e;let a=e.match(/\d+/g);if(a){for(let e of a)for(let a of Object.keys(t))if(a.includes(e))return a}return null}(c,t);if(e&&t[e]){let r=i(t[e]);a.push({id:"extracted-image-ref-".concat(s),src:r,alt:o}),n=n.replace(l,"")}else n=n.replace(l,"[Missing Image: ".concat(c,"]"))}else n=n.replace(l,"[Image: ".concat(c,"]"))});let o=n.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g);o&&o.forEach((e,t)=>{if(r(e)){let r=i(e);a.push({id:"extracted-image-dataurl-".concat(t),src:r,alt:"Extracted image ".concat(a.length+1)}),n=n.replace(e,"")}});let c=n.match(/\b[A-Za-z0-9+/]{200,}={0,2}\b/g);return c&&c.forEach((e,t)=>{if(r(e)){let r=i(e);a.push({id:"extracted-image-raw-".concat(t),src:r,alt:"Extracted image ".concat(a.length+1)}),n=n.replace(e,"")}}),{cleanText:n=n.replace(/\s+/g," ").trim(),images:a}}function s(e){if(!e||!r(e))return null;try{return i(e)}catch(e){return console.warn("Invalid base64 image source:",e),null}}a.d(t,{B_:()=>s,UF:()=>i,XI:()=>r,Xw:()=>n})}}]);