(()=>{var e={};e.id=7097,e.ids=[7097],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34821:(e,t,i)=>{"use strict";i.a(e,async(e,o)=>{try{i.r(t),i.d(t,{POST:()=>l});var n=i(32190),a=i(83636),s=e([a]);function r(e){if(!e)return"";let t=e,i=(t=t.replace(/!\s*\(\s*(data:image\/[^;]+;base64,[A-Za-z0-9+/=]+)\s*\)/g,(e,t)=>{let i=t.replace(/\s+/g,"");return`<img src="${i}" alt="Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`})).match(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g);return i&&i.forEach(e=>{let i=RegExp(`<img[^>]*src=["']${e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}["'][^>]*>`,"i");if(!t.match(i)){let i=e.replace(/\s+/g,""),o=`<img src="${i}" alt="Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`;t=t.replace(e,o)}}),t=t.replace(/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g,e=>{try{let t=e.trim(),i=(t=t.replace(/<br\s*\/?>/gi," ")).split("\n").filter(e=>e.trim());if(i.length<2)return e;let o=[],n=!1;for(let e of i){let t=e.split("|").map(e=>e.trim()).filter(e=>e);if(0!==t.length){if(t.every(e=>e.match(/^:?-+:?$/))){n=!0;continue}o.push(t)}}if(0===o.length)return e;let a="<table>";if(n&&o.length>0){for(let e of(a+="<thead><tr>",o[0]))a+=`<th>${e}</th>`;if(a+="</tr></thead>",o.length>1){a+="<tbody>";for(let e=1;e<o.length;e++){for(let t of(a+="<tr>",o[e]))a+=`<td>${t}</td>`;a+="</tr>"}a+="</tbody>"}}else{for(let e of(a+="<tbody>",o)){for(let t of(a+="<tr>",e))a+=`<td>${t}</td>`;a+="</tr>"}a+="</tbody>"}return a+="</table>"}catch(t){return console.warn("Error processing table:",t),e}})}a=(s.then?(await s)():s)[0];let l=async e=>{try{let t=await e.json();console.log("Solutions PDF API called with payload:",t);let{title:i,description:o,duration:s,totalMarks:l,questions:d,filename:p="question-paper-solutions.pdf",collegeName:g="",collegeLogoUrl:c=""}=t,m=`<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>${i} - Solutions</title>
  <link href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      if (window.renderMathInElement) {
        window.renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ]
        });
      }
    });
  </script>
  <style>
    @page { size: A4; margin: 20mm 15mm; }
    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }
    h1,h2,h3 { margin: 0; padding: 0; }
    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }
    /* Watermark */
    body::before {
      content: 'MEDICOS';
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-30deg);
      font-size: 96pt;
      font-weight: bold;
      color: rgba(0,128,0,0.08); /* greenish */
      z-index: 0;
      pointer-events: none;
    }
    /* Header / Footer */
    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .college { display: flex; align-items: center; gap: 6px; }
    .college img { height: 32px; width: auto; }
    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }
    .meta { text-align: right; font-size: 10pt; }
    .meta div { margin: 0; }

    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }
    .question { break-inside: avoid; margin-bottom: 20px; page-break-inside: avoid; }
    .question-content { margin-bottom: 8px; }
    .options { margin-left: 12px; margin-bottom: 8px; }
    .options p { margin: 2px 0; }
    .answer { margin-top: 8px; padding: 6px 10px; background-color: #f0f8ff; border-left: 4px solid #2563eb; border-radius: 3px; }
    .answer-text { font-weight: bold; color: #000; font-size: 10pt; }
    .solution { margin-top: 10px; padding: 8px 12px; background-color: #f9f9f9; border-left: 4px solid #10b981; border-radius: 3px; }
    .solution-title { font-weight: bold; color: #059669; margin-bottom: 6px; font-size: 10pt; }
    .solution-content { font-size: 9pt; line-height: 1.4; }
    .solution-content p { margin: 4px 0; }
    .solution-content ol { margin: 4px 0; padding-left: 16px; }
    .solution-content li { margin: 2px 0; }
    .hints { margin-top: 10px; padding: 8px 12px; background-color: #fef3c7; border-left: 4px solid #f59e0b; border-radius: 3px; }
    .hints-title { font-weight: bold; color: #d97706; margin-bottom: 6px; font-size: 10pt; }
    .hint-item { margin: 3px 0; font-size: 9pt; line-height: 1.3; }
    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }
    .subject-heading { font-weight: bold; margin: 12px 0 8px; font-size: 12pt; color: #333; border-bottom: 1px solid #ddd; padding-bottom: 4px; }
    /* Table styling for proper rendering */
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 8px 0;
      font-size: 9pt;
      break-inside: avoid;
    }
    th, td {
      border: 1px solid #333;
      padding: 4px 6px;
      text-align: left;
      vertical-align: top;
    }
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    /* Math rendering support */
    .katex {
      font-size: 1em;
    }
    .katex-display {
      margin: 0.3em 0;
    }
  </style>
</head>
<body>
  <header>
    <div class="college">
      ${c?`<img src="${c}" alt="logo" />`:""}
      <span>${g}</span>
    </div>
    <div class="title">${i} - Solutions</div>
    <div class="meta">
      <div>Total Marks: ${l}</div>
      <div>Duration: ${s} mins</div>
    </div>
  </header>
  <hr />
  <p>${o}</p>
  <div class="questions">
    ${d.reduce((e,t,i)=>{let o=t.subject&&(0===i||d[i-1].subject!==t.subject)?`<div class="subject-heading">Subject: ${t.subject}</div>`:"",n=t.options.findIndex(e=>e===t.answer),a=-1!==n?String.fromCharCode(97+n):t.answer,s="";if(t.solution){let e=[];if(t.solution.final_explanation){let i=r(t.solution.final_explanation);e.push(`<p><strong>Explanation:</strong> ${i}</p>`)}if(t.solution.methodology){let i=r(t.solution.methodology);e.push(`<p><strong>Method:</strong> ${i}</p>`)}if(t.solution.steps&&t.solution.steps.length>0){let i=t.solution.steps.map(e=>r(e));e.push(`<p><strong>Steps:</strong></p><ol>${i.map(e=>`<li>${e}</li>`).join("")}</ol>`)}e.length>0&&(s=`
            <div class="solution">
              <div class="solution-title">Solution:</div>
              <div class="solution-content">
                ${e.join("")}
              </div>
            </div>`)}let l="";if(t.hints&&t.hints.length>0){let e=t.hints.map(e=>r(e));l=`
          <div class="hints">
            <div class="hints-title">Hints:</div>
            ${e.map((e,t)=>`<div class="hint-item">${t+1}. ${e}</div>`).join("")}
          </div>`}let p=t.question,g=t.imageUrls||[];g&&g.length>0&&!(p=(p=p.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,t)=>`<img src="${g[0]}" alt="${t||"Question Image"}" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`)).replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi,()=>`<img src="${g[0]}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`)).includes("<img")&&g.length>0&&/image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(p)&&(p+=`
<img src="${g[0]}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`);let c=t.imageData||t.chemicalImages;if(c&&"object"==typeof c&&!p.includes("<img")&&!(p.includes("data:image/")||p.includes("!["))){let e=Object.keys(c)[0];e&&c[e]&&(p=p+"\n"+c[e])}let m=r(p);m=m.replace(/!\[([^\]]*)\]\(data:image\/([^;]+);base64,([^)]+)\)/g,'<img src="data:image/$2;base64,$3" alt="$1" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />').replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,i,o)=>{let n=t.imageData||t.chemicalImages;if(n&&"object"==typeof n){let e=null;if(n[o])e=o;else{let t=o.replace(/\.(jpeg|jpg|png)$/i,"");e=Object.keys(n).find(e=>e.includes(t)||e.replace(/\.(jpeg|jpg|png)$/i,"")===t)}if(e||(e=Object.keys(n).find(e=>e.includes(o)||o.includes(e))),!e){let t=o.match(/\d+/g);t&&(e=Object.keys(n).find(e=>t.some(t=>e.includes(t))))}if(e&&n[e])return`<img src="${n[e]}" alt="${i}" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" />`}return`[Missing Image: ${o}]`}).replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"").replace(/[A-Za-z0-9+/]{100,}={0,2}/g,"");let u=t.options.map(e=>r(e).replace(/!\[([^\]]*)\]\(data:image\/([^;]+);base64,([^)]+)\)/g,'<img src="data:image/$2;base64,$3" alt="$1" style="max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;" />').replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,i,o)=>{let n=t.imageData||t.chemicalImages;if(n&&"object"==typeof n){let e=null;if(n[o])e=o;else{let t=o.replace(/\.(jpeg|jpg|png)$/i,"");e=Object.keys(n).find(e=>e.includes(t)||e.replace(/\.(jpeg|jpg|png)$/i,"")===t)}if(e||(e=Object.keys(n).find(e=>e.includes(o)||o.includes(e))),!e){let t=o.match(/\d+/g);t&&(e=Object.keys(n).find(e=>t.some(t=>e.includes(t))))}if(e&&n[e])return`<img src="${n[e]}" alt="${i}" style="max-width:200px;height:auto;display:inline-block;margin:5px;border:1px solid #ddd;padding:3px;" />`}return`[Missing Image: ${o}]`}).replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"").replace(/[A-Za-z0-9+/]{100,}={0,2}/g,"")),f=`
        <div class="question">
          <div class="question-content">
            <p><strong>${i+1}.</strong> ${m}</p>
          </div>
          <div class="options">
            ${u.map((e,t)=>`<p>${String.fromCharCode(97+t)}) ${e}</p>`).join("")}
          </div>
          <div class="answer">
            <p class="answer-text">Answer: ${a})</p>
          </div>
          ${s}
          ${l}
        </div>`;return e+o+f},"")}
  </div>
  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>
</body>
</html>`;console.log("Launching Puppeteer for solutions PDF generation...");let u=await a.default.launch({args:["--no-sandbox","--disable-setuid-sandbox"]}),f=await u.newPage();console.log("Setting HTML content for solutions PDF..."),await f.setContent(m,{waitUntil:"networkidle0"}),await f.waitForFunction(()=>Array.from(document.querySelectorAll(".katex")).length>0,{timeout:3e3}).catch(()=>{}),await new Promise(e=>setTimeout(e,200)),console.log("Generating solutions PDF...");let h=await f.pdf({format:"A4",printBackground:!0,margin:{top:"20mm",right:"15mm",bottom:"20mm",left:"15mm"}});return await u.close(),console.log("Solutions PDF generated successfully, size:",h.length,"bytes"),new n.NextResponse(h,{status:200,headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="${p}"`}})}catch(e){return console.error("Solutions PDF generation failed:",e),new n.NextResponse(JSON.stringify({error:"Solutions PDF generation failed"}),{status:500})}};o()}catch(e){o(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83636:e=>{"use strict";e.exports=import("puppeteer")},85777:(e,t,i)=>{"use strict";i.a(e,async(e,o)=>{try{i.r(t),i.d(t,{patchFetch:()=>d,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>c});var n=i(96559),a=i(48088),s=i(37719),r=i(34821),l=e([r]);r=(l.then?(await l)():l)[0];let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/generate-solutions-pdf/route",pathname:"/api/generate-solutions-pdf",filename:"route",bundlePath:"app/api/generate-solutions-pdf/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-solutions-pdf\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:g,workUnitAsyncStorage:c,serverHooks:m}=p;function d(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:c})}o()}catch(e){o(e)}})},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),o=t.X(0,[4447,580],()=>i(85777));module.exports=o})();