(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={1132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7500:(e,t,r)=>{Promise.resolve().then(r.bind(r,90618))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20943:(e,t,r)=>{Promise.resolve().then(r.bind(r,99111))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,t,r)=>{"use strict";r.d(t,{tU:()=>D,av:()=>M,j7:()=>O,Xi:()=>S});var a=r(60687),n=r(43210),s=r(70569),i=r(11273),o=r(72942),l=r(46059),c=r(3416),d=r(43),u=r(65551),p=r(96963),m="Tabs",[h,f]=(0,i.A)(m,[o.RG]),x=(0,o.RG)(),[y,v]=h(m),g=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:s,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:m="automatic",...h}=e,f=(0,d.jH)(l),[x,v]=(0,u.i)({prop:n,onChange:s,defaultProp:i});return(0,a.jsx)(y,{scope:r,baseId:(0,p.B)(),value:x,onValueChange:v,orientation:o,dir:f,activationMode:m,children:(0,a.jsx)(c.sG.div,{dir:f,"data-orientation":o,...h,ref:t})})});g.displayName=m;var b="TabsList",j=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...s}=e,i=v(b,r),l=x(r);return(0,a.jsx)(o.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,a.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...s,ref:t})})});j.displayName=b;var w="TabsTrigger",A=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:i=!1,...l}=e,d=v(w,r),u=x(r),p=P(d.baseId,n),m=E(d.baseId,n),h=n===d.value;return(0,a.jsx)(o.q7,{asChild:!0,...u,focusable:!i,active:h,children:(0,a.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:p,...l,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,s.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||i||!e||d.onValueChange(n)})})})});A.displayName=w;var k="TabsContent",N=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,forceMount:i,children:o,...d}=e,u=v(k,r),p=P(u.baseId,s),m=E(u.baseId,s),h=s===u.value,f=n.useRef(h);return n.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(l.C,{present:i||h,children:({present:r})=>(0,a.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":p,hidden:!r,id:m,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:r&&o})})});function P(e,t){return`${e}-trigger-${t}`}function E(e,t){return`${e}-content-${t}`}N.displayName=k;var C=r(4780);function D({className:e,...t}){return(0,a.jsx)(g,{"data-slot":"tabs",className:(0,C.cn)("flex flex-col gap-2",e),...t})}function O({className:e,...t}){return(0,a.jsx)(j,{"data-slot":"tabs-list",className:(0,C.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function S({className:e,...t}){return(0,a.jsx)(A,{"data-slot":"tabs-trigger",className:(0,C.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function M({className:e,...t}){return(0,a.jsx)(N,{"data-slot":"tabs-content",className:(0,C.cn)("flex-1 outline-none",e),...t})}},31981:(e,t,r)=>{"use strict";r.d(t,{$y:()=>s,cY:()=>i,hS:()=>n});var a=r(52581);function n(e,t="An error occurred. Please try again.",r=!0){let s,i=t;return e?.message?i=e.message:"string"==typeof e?i=e:e?.response?.data?.message?i=e.response.data.message:e?.data?.message&&(i=e.data.message),e?.status?s=e.status:e?.response?.status&&(s=e.response.status),i.includes("already exists")||(i.includes("Authentication")||i.includes("Unauthorized")?i="Please log in again to continue. Your session may have expired.":i.includes("Network")||i.includes("fetch")?i="Please check your internet connection and try again.":i.includes("not found")?i="The requested resource was not found.":i.includes("Forbidden")?i="You do not have permission to perform this action.":500===s?i="Server error. Please try again later.":503===s&&(i="Service temporarily unavailable. Please try again later.")),r&&a.oR.error(i),{success:!1,error:i,statusCode:s}}function s(e,t=!1,r){return t&&r&&a.oR.success(r),{success:!0,data:e}}function i(e){return!0===e.success}},33873:e=>{"use strict";e.exports=require("path")},37004:(e,t,r)=>{Promise.resolve().then(r.bind(r,1132))},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},42505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(37413),n=r(92555);function s(){return(0,a.jsx)(n.W,{message:"Loading admin dashboard..."})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>s,aR:()=>i});var a=r(60687);r(43210);var n=r(4780);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},46708:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(60687);r(43210);var n=r(4780),s=r(65668),i=r(93613),o=r(44493),l=r(85726);let c=({icon:e=(0,a.jsx)(s.A,{className:"h-5 w-5 text-muted-foreground"}),label:t,value:r,className:c,iconClassName:d,labelClassName:u,valueClassName:p,loading:m=!1,error:h=!1})=>(0,a.jsx)(o.Zp,{className:(0,n.cn)("p-6",c),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:(0,n.cn)("inline-flex h-10 w-10 items-center justify-center rounded-lg bg-muted",d),children:h?(0,a.jsx)(i.A,{className:"h-5 w-5 text-destructive"}):e}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:(0,n.cn)("text-sm font-medium text-muted-foreground",u),children:t}),m?(0,a.jsx)(l.E,{className:"h-9 w-24"}):h?(0,a.jsx)("p",{className:(0,n.cn)("text-sm font-medium text-destructive"),children:"Failed to load"}):(0,a.jsx)("p",{className:(0,n.cn)("text-3xl font-bold",p),children:r})]})]})})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61193:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(60687),n=r(66327),s=r(99557),i=r(45285),o=r(53355);function l({children:e}){return(0,a.jsx)(i.A,{allowedRoles:[s.g.SUPER_ADMIN],children:(0,a.jsx)(o.default,{children:(0,a.jsx)(n.N,{role:s.g.SUPER_ADMIN,children:e})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66307:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),n=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80775:(e,t,r)=>{Promise.resolve().then(r.bind(r,61193))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90618:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ex});var a=r(60687),n=r(43210),s=r.n(n),i=r(66327),o=r(99557),l=r(44493),c=r(48482),d=r(92491),u=r(49384),p=r(93492),m=r(5231),h=r.n(m),f=r(90453),x=r.n(f),y=r(37456),v=r.n(y),g=r(77822),b=r.n(g),j=r(71967),w=r.n(j),A=r(81888),k=r(95530),N=r(98986),P=r(98845),E=r(20237),C=r(22989),D=r(30087),O=r(54186),S=["layout","type","stroke","connectNulls","isRange","ref"],M=["key"];function F(e){return(F="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function L(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(Object.prototype.hasOwnProperty.call(e,a)){if(t.indexOf(a)>=0)continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function I(){return(I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){W(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function T(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,B(a.key),a)}}function $(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return($=function(){return!!e})()}function q(e){return(q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function z(e,t){return(z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function W(e,t,r){return(t=B(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B(e){var t=function(e,t){if("object"!=F(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=F(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==F(t)?t:t+""}var G=function(e){var t,r;function a(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,a);for(var e,t,r,n=arguments.length,s=Array(n),i=0;i<n;i++)s[i]=arguments[i];return t=a,r=[].concat(s),t=q(t),W(e=function(e,t){if(t&&("object"===F(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,$()?Reflect.construct(t,r||[],q(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0}),W(e,"id",(0,C.NF)("recharts-area-")),W(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),h()(t)&&t()}),W(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),h()(t)&&t()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&z(e,t)}(a,e),t=[{key:"renderDots",value:function(e,t,r){var n=this.props.isAnimationActive,i=this.state.isAnimationFinished;if(n&&!i)return null;var o=this.props,l=o.dot,c=o.points,d=o.dataKey,u=(0,O.J9)(this.props,!1),p=(0,O.J9)(l,!0),m=c.map(function(e,t){var r=_(_(_({key:"dot-".concat(t),r:3},u),p),{},{index:t,cx:e.x,cy:e.y,dataKey:d,value:e.value,payload:e.payload,points:c});return a.renderDotItem(l,r)}),h={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return s().createElement(N.W,I({className:"recharts-area-dots"},h),m)}},{key:"renderHorizontalRect",value:function(e){var t=this.props,r=t.baseLine,a=t.points,n=t.strokeWidth,i=a[0].x,o=a[a.length-1].x,l=e*Math.abs(i-o),c=x()(a.map(function(e){return e.y||0}));return((0,C.Et)(r)&&"number"==typeof r?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(x()(r.map(function(e){return e.y||0})),c)),(0,C.Et)(c))?s().createElement("rect",{x:i<o?i:i-l,y:0,width:l,height:Math.floor(c+(n?parseInt("".concat(n),10):1))}):null}},{key:"renderVerticalRect",value:function(e){var t=this.props,r=t.baseLine,a=t.points,n=t.strokeWidth,i=a[0].y,o=a[a.length-1].y,l=e*Math.abs(i-o),c=x()(a.map(function(e){return e.x||0}));return((0,C.Et)(r)&&"number"==typeof r?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(x()(r.map(function(e){return e.x||0})),c)),(0,C.Et)(c))?s().createElement("rect",{x:0,y:i<o?i:i-l,width:c+(n?parseInt("".concat(n),10):1),height:Math.floor(l)}):null}},{key:"renderClipRect",value:function(e){return"vertical"===this.props.layout?this.renderVerticalRect(e):this.renderHorizontalRect(e)}},{key:"renderAreaStatically",value:function(e,t,r,a){var n=this.props,i=n.layout,o=n.type,l=n.stroke,c=n.connectNulls,d=n.isRange,u=(n.ref,L(n,S));return s().createElement(N.W,{clipPath:r?"url(#clipPath-".concat(a,")"):null},s().createElement(A.I,I({},(0,O.J9)(u,!0),{points:e,connectNulls:c,type:o,baseLine:t,layout:i,stroke:"none",className:"recharts-area-area"})),"none"!==l&&s().createElement(A.I,I({},(0,O.J9)(this.props,!1),{className:"recharts-area-curve",layout:i,type:o,connectNulls:c,fill:"none",points:e})),"none"!==l&&d&&s().createElement(A.I,I({},(0,O.J9)(this.props,!1),{className:"recharts-area-curve",layout:i,type:o,connectNulls:c,fill:"none",points:t})))}},{key:"renderAreaWithAnimation",value:function(e,t){var r=this,a=this.props,n=a.points,i=a.baseLine,o=a.isAnimationActive,l=a.animationBegin,c=a.animationDuration,d=a.animationEasing,u=a.animationId,m=this.state,h=m.prevPoints,f=m.prevBaseLine;return s().createElement(p.Ay,{begin:l,duration:c,isActive:o,easing:d,from:{t:0},to:{t:1},key:"area-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(a){var o=a.t;if(h){var l,c=h.length/n.length,d=n.map(function(e,t){var r=Math.floor(t*c);if(h[r]){var a=h[r],n=(0,C.Dj)(a.x,e.x),s=(0,C.Dj)(a.y,e.y);return _(_({},e),{},{x:n(o),y:s(o)})}return e});return l=(0,C.Et)(i)&&"number"==typeof i?(0,C.Dj)(f,i)(o):v()(i)||b()(i)?(0,C.Dj)(f,0)(o):i.map(function(e,t){var r=Math.floor(t*c);if(f[r]){var a=f[r],n=(0,C.Dj)(a.x,e.x),s=(0,C.Dj)(a.y,e.y);return _(_({},e),{},{x:n(o),y:s(o)})}return e}),r.renderAreaStatically(d,l,e,t)}return s().createElement(N.W,null,s().createElement("defs",null,s().createElement("clipPath",{id:"animationClipPath-".concat(t)},r.renderClipRect(o))),s().createElement(N.W,{clipPath:"url(#animationClipPath-".concat(t,")")},r.renderAreaStatically(n,i,e,t)))})}},{key:"renderArea",value:function(e,t){var r=this.props,a=r.points,n=r.baseLine,s=r.isAnimationActive,i=this.state,o=i.prevPoints,l=i.prevBaseLine,c=i.totalLength;return s&&a&&a.length&&(!o&&c>0||!w()(o,a)||!w()(l,n))?this.renderAreaWithAnimation(e,t):this.renderAreaStatically(a,n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,a=t.dot,n=t.points,i=t.className,o=t.top,l=t.left,c=t.xAxis,d=t.yAxis,p=t.width,m=t.height,h=t.isAnimationActive,f=t.id;if(r||!n||!n.length)return null;var x=this.state.isAnimationFinished,y=1===n.length,g=(0,u.A)("recharts-area",i),b=c&&c.allowDataOverflow,j=d&&d.allowDataOverflow,w=b||j,A=v()(f)?this.id:f,k=null!==(e=(0,O.J9)(a,!1))&&void 0!==e?e:{r:3,strokeWidth:2},E=k.r,C=k.strokeWidth,D=((0,O.sT)(a)?a:{}).clipDot,S=void 0===D||D,M=2*(void 0===E?3:E)+(void 0===C?2:C);return s().createElement(N.W,{className:g},b||j?s().createElement("defs",null,s().createElement("clipPath",{id:"clipPath-".concat(A)},s().createElement("rect",{x:b?l:l-p/2,y:j?o:o-m/2,width:b?p:2*p,height:j?m:2*m})),!S&&s().createElement("clipPath",{id:"clipPath-dots-".concat(A)},s().createElement("rect",{x:l-M/2,y:o-M/2,width:p+M,height:m+M}))):null,y?null:this.renderArea(w,A),(a||y)&&this.renderDots(w,S,A),(!h||x)&&P.Z.renderCallByParent(this.props,n))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,curBaseLine:e.baseLine,prevPoints:t.curPoints,prevBaseLine:t.curBaseLine}:e.points!==t.curPoints||e.baseLine!==t.curBaseLine?{curPoints:e.points,curBaseLine:e.baseLine}:null}}],t&&T(a.prototype,t),r&&T(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);W(G,"displayName","Area"),W(G,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!E.m.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),W(G,"getBaseValue",function(e,t,r,a){var n=e.layout,s=e.baseValue,i=t.props.baseValue,o=null!=i?i:s;if((0,C.Et)(o)&&"number"==typeof o)return o;var l="horizontal"===n?a:r,c=l.scale.domain();if("number"===l.type){var d=Math.max(c[0],c[1]),u=Math.min(c[0],c[1]);return"dataMin"===o?u:"dataMax"===o?d:d<0?d:Math.max(Math.min(c[0],c[1]),0)}return"dataMin"===o?c[0]:"dataMax"===o?c[1]:c[0]}),W(G,"getComposedData",function(e){var t,r=e.props,a=e.item,n=e.xAxis,s=e.yAxis,i=e.xAxisTicks,o=e.yAxisTicks,l=e.bandSize,c=e.dataKey,d=e.stackedData,u=e.dataStartIndex,p=e.displayedData,m=e.offset,h=r.layout,f=d&&d.length,x=G.getBaseValue(r,a,n,s),y="horizontal"===h,v=!1,g=p.map(function(e,t){f?r=d[u+t]:Array.isArray(r=(0,D.kr)(e,c))?v=!0:r=[x,r];var r,a=null==r[1]||f&&null==(0,D.kr)(e,c);return y?{x:(0,D.nb)({axis:n,ticks:i,bandSize:l,entry:e,index:t}),y:a?null:s.scale(r[1]),value:r,payload:e}:{x:a?null:n.scale(r[1]),y:(0,D.nb)({axis:s,ticks:o,bandSize:l,entry:e,index:t}),value:r,payload:e}});return t=f||v?g.map(function(e){var t=Array.isArray(e.value)?e.value[0]:null;return y?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null}:{x:null!=t?n.scale(t):null,y:e.y}}):y?s.scale(x):n.scale(x),_({points:g,baseLine:t,layout:h,isRange:v},m)}),W(G,"renderDotItem",function(e,t){var r;if(s().isValidElement(e))r=s().cloneElement(e,t);else if(h()(e))r=e(t);else{var a=(0,u.A)("recharts-area-dot","boolean"!=typeof e?e.className:""),n=t.key,i=L(t,M);r=s().createElement(k.c,I({},i,{key:n,className:a}))}return r});var U=r(27747),J=r(9920),V=r(84629),K=(0,d.gu)({chartName:"AreaChart",GraphicalChild:G,axisComponents:[{axisType:"xAxis",AxisComp:U.W},{axisType:"yAxis",AxisComp:J.h}],formatAxisMap:V.pr}),Z=r(85168),Y=r(38246),H=r(29844),X=r(40228),Q=r(29523),ee=r(62185);r(31981);let et=async(e,t,r)=>{try{let a="";e&&(a+=`year=${e}`),t&&(a+=`${a?"&":""}startDate=${t}`),r&&(a+=`${a?"&":""}endDate=${r}`);let n=`/analytics/usage-trends${a?`?${a}`:""}`;return await (0,ee.apiCall)(n)}catch(e){throw console.error("Error fetching usage trends:",e),Error(e.message||"Failed to fetch usage trends")}},er=async(e,t,r,a)=>{try{let n="";e&&(n+=`year=${e}`),t&&(n+=`${n?"&":""}startDate=${t}`),r&&(n+=`${n?"&":""}endDate=${r}`),a&&(n+=`${n?"&":""}view=${a}`);let s=`/analytics/college-growth${n?`?${n}`:""}`;return await (0,ee.apiCall)(s)}catch(e){throw console.error("Error fetching college growth:",e),Error(e.message||"Failed to fetch college growth")}},ea=({active:e,payload:t,label:r})=>e&&t&&t.length?(0,a.jsxs)("div",{className:"bg-white p-3 border border-gray-200 shadow-sm rounded-md",children:[(0,a.jsx)("p",{className:"text-sm font-medium mb-1",children:r}),t.map((e,t)=>(0,a.jsxs)("p",{className:"text-sm",style:{color:e.color},children:[(0,a.jsxs)("span",{className:"font-medium",children:[e.name,": "]}),e.value]},`item-${t}`))]}):null,en=({title:e="Total Colleges",subtitle:t="Target you've set for each month",data:r,lines:s=[{key:"target",name:"Target",color:"#4F46E5",gradientId:"colorTarget",startColor:"#4F46E5",endColor:"#4F46E5"},{key:"actual",name:"Actual",color:"#60A5FA",gradientId:"colorActual",startColor:"#60A5FA",endColor:"#60A5FA"}],xAxisKey:i="month",yAxisDomain:o=[0,1e3],yAxisTicks:d=[0,200,400,600,800,1e3],showTabs:u=!0,tabOptions:p=[{value:"overview",label:"Overview"},{value:"sales",label:"Sales"},{value:"revenue",label:"Revenue"}],defaultTabValue:m="overview",showDateRange:h=!0,dateRangeLabel:f="05 Feb - 06 March"})=>{let[x,y]=(0,n.useState)([]),[v,g]=(0,n.useState)(!1),[b,j]=(0,n.useState)(null),[w,A]=(0,n.useState)(m);return(0,n.useEffect)(()=>{(async()=>{if(r){y(r);return}g(!0),j(null);try{let e=new Date().getFullYear().toString(),t=(await er(e,void 0,void 0,w)).data.map(e=>({month:e.monthName,target:e.monthlyTarget,actual:e.cumulativeColleges,collegesAdded:e.collegesAdded,targetAchievement:e.targetAchievement,revenue:e.revenue}));y(t)}catch(e){console.error("Error fetching college growth:",e),j(e instanceof Error?e.message:"Failed to fetch college growth data"),y(["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e,t)=>{let r=30*Math.sin(.5*t)+10*t,a=20*Math.sin((t+1)*.7)+12*t;return{month:e,target:Math.round(720+r+5*t),actual:Math.round(480+a+6*t)}}))}finally{g(!1)}})()},[r,w]),(0,a.jsxs)(l.Zp,{className:"w-full",children:[(0,a.jsxs)(l.aR,{className:"pb-2 pt-6 px-6 flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:e}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:t})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between w-full sm:w-auto sm:justify-end gap-4",children:[u&&(0,a.jsx)(H.tU,{value:w,onValueChange:A,children:(0,a.jsx)(H.j7,{className:"bg-gray-100/80",children:p.map(e=>(0,a.jsx)(H.Xi,{value:e.value,children:e.label},e.value))})}),h&&(0,a.jsxs)(Q.$,{variant:"outline",className:"gap-1",children:[(0,a.jsx)(X.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"hidden sm:inline-block text-sm font-normal",children:f})]})]})]}),(0,a.jsx)(l.Wu,{className:"pt-0 px-3 sm:px-6",children:(0,a.jsx)("div",{className:"h-[350px] w-full mt-4",children:v?(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):b?(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-red-500",children:(0,a.jsx)("p",{children:"Failed to load chart data"})}):(0,a.jsx)(c.u,{width:"100%",height:"100%",children:(0,a.jsxs)(K,{data:x,margin:{top:10,right:10,left:0,bottom:20},children:[(0,a.jsx)("defs",{children:s.map(e=>(0,a.jsxs)("linearGradient",{id:e.gradientId,x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,a.jsx)("stop",{offset:"5%",stopColor:e.startColor,stopOpacity:.2}),(0,a.jsx)("stop",{offset:"95%",stopColor:e.endColor,stopOpacity:0})]},e.gradientId))}),(0,a.jsx)(Z.d,{vertical:!1,strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(U.W,{dataKey:i,axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},dy:10}),(0,a.jsx)(J.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},domain:o,ticks:d}),(0,a.jsx)(Y.m,{content:(0,a.jsx)(ea,{})}),s.map(e=>(0,a.jsx)(G,{type:"monotone",dataKey:e.key,name:e.name,stroke:e.color,strokeWidth:2.5,fillOpacity:1,fill:`url(#${e.gradientId})`,activeDot:{r:6,strokeWidth:0}},e.key))]})})})})]})};var es=r(2041),ei=r(90812),eo=r(25679),el=r(93661);let ec=({active:e,payload:t,label:r})=>e&&t&&t.length?(0,a.jsx)("div",{className:"bg-white p-2 border border-gray-200 shadow-sm rounded-md",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:`${r}: ${t[0].value}`})}):null,ed=({title:e="Usage trends",data:t,dataKey:r="totalUsage",xAxisKey:s="monthName",yAxisDomain:i,yAxisTicks:o,barSize:d=20,barGap:u=2,maxBarSize:p=20,highlightIndex:m,highlightColor:h="#4F46E5",defaultBarColor:f="#F3F4F6",showMoreButton:x=!0})=>{let[y,v]=(0,n.useState)([]),[g,b]=(0,n.useState)(!1),[j,w]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{if(t){v(t);return}b(!0),w(null);try{let e=new Date().getFullYear().toString(),t=(await et(e)).data.map(e=>({month:e.monthName,[r]:e[r]||e.totalUsage,questionsCreated:e.questionsCreated,papersGenerated:e.papersGenerated}));if(v(t),!i||!o){let e=Math.max(...t.map(e=>e[r])),a=200*Math.ceil(e/200);if(i||(i=[0,a]),!o){let e=a/4;o=Array.from({length:5},(t,r)=>r*e)}}}catch(e){console.error("Error fetching usage trends:",e),w(e instanceof Error?e.message:"Failed to fetch usage data"),v(function(){let e=[450,750,380,620,350,680,500,650,580,600,400,520];return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((t,a)=>{let n={month:t};return n[r]=e[a],n})}())}finally{b(!1)}})()},[t,r]);let A=i||[0,800],k=o||[0,200,400,600,800];return(0,a.jsxs)(l.Zp,{className:"w-full",children:[(0,a.jsxs)(l.aR,{className:"flex flex-row items-center justify-between pb-0 pt-4 px-4",children:[(0,a.jsx)(l.ZB,{className:"text-base font-medium text-gray-800",children:e}),x&&(0,a.jsx)(Q.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,a.jsx)(el.A,{className:"h-5 w-5 text-gray-400"})})]}),(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsx)("div",{className:"h-[200px] w-full",children:g?(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-teacher-blue"})}):j?(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-red-500",children:(0,a.jsx)("p",{children:"Failed to load chart data"})}):(0,a.jsx)(c.u,{width:"100%",height:"100%",children:(0,a.jsxs)(es.E,{data:y,margin:{top:5,right:10,left:-20,bottom:5},barSize:d,barGap:u,maxBarSize:p,children:[(0,a.jsx)(Z.d,{vertical:!1,horizontal:!0,strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,a.jsx)(U.W,{dataKey:s,axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},dy:10}),(0,a.jsx)(J.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},tickCount:k.length,domain:A,ticks:k,dx:-10}),(0,a.jsx)(Y.m,{content:(0,a.jsx)(ec,{}),cursor:!1}),(0,a.jsx)(ei.y,{dataKey:r,radius:[4,4,0,0],children:y.map((e,t)=>(0,a.jsx)(eo.f,{fill:void 0!==m?t===m?h:f:h},`cell-${t}`))})]})})})})]})};var eu=r(46708);let ep=(0,r(62688).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var em=r(41312),eh=r(42405),ef=r(31158);function ex(){let e=new Date().getMonth(),[t,r]=(0,n.useState)(null),[s,i]=(0,n.useState)(!0),[o,l]=(0,n.useState)(null);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard Overview"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsx)(eu.A,{icon:(0,a.jsx)(ep,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Colleges",value:t?.totalColleges??0,loading:s,error:!!o}),(0,a.jsx)(eu.A,{icon:(0,a.jsx)(em.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Teachers",value:t?.totalTeachers??0,loading:s,error:!!o,iconClassName:"bg-blue-100",valueClassName:"text-blue-600"}),(0,a.jsx)(eu.A,{icon:(0,a.jsx)(eh.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Questions",value:t?.totalQuestions??0,loading:s,error:!!o,iconClassName:"bg-green-100",valueClassName:"text-green-600"}),(0,a.jsx)(eu.A,{icon:(0,a.jsx)(ef.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Downloads",value:t?.totalDownloads??0,loading:s,error:!!o,iconClassName:"bg-amber-100",valueClassName:"text-amber-600"})]}),(0,a.jsx)("div",{className:"max-w-full mx-auto",children:(0,a.jsxs)("div",{className:"grid gap-8",children:[(0,a.jsx)(ed,{highlightIndex:e}),(0,a.jsx)(en,{})]})})]})}r(52581),ex.getLayout=function(e){return(0,a.jsx)(i.N,{role:o.g.COLLEGE_ADMIN,children:e})}},93661:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,4619,3287,9592,2581,9575,4707,6658],()=>r(66307));module.exports=a})();