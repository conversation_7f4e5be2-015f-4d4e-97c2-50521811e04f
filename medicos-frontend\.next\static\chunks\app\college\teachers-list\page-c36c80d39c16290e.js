(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1389],{1800:(e,t,a)=>{Promise.resolve().then(a.bind(a,33475))},6101:(e,t,a)=>{"use strict";a.d(t,{s:()=>s,t:()=>o});var r=a(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let a=!1,r=e.map(e=>{let r=n(e,t);return a||"function"!=typeof r||(a=!0),r});if(a)return()=>{for(let t=0;t<r.length;t++){let a=r[t];"function"==typeof a?a():n(e[t],null)}}}}function s(...e){return r.useCallback(o(...e),e)}},11723:(e,t,a)=>{"use strict";a.d(t,{HC:()=>o,P_:()=>i,Rb:()=>c,Sp:()=>s,q6:()=>n,qg:()=>l});var r=a(55097);let n=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let n=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e,"/teachers"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to add teacher. Please try again.")}let o=await n.json();return(0,r.$y)(o,!0,"Teacher added successfully!")}catch(e){return console.error("Error adding teacher:",e),(0,r.hS)(e.message||"Failed to add teacher. Please try again.","Failed to add teacher. Please try again.")}},o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};try{let o=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!o)return console.error("No authentication token found"),(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let s=new URLSearchParams({page:t.toString(),limit:a.toString(),...n}),l="".concat("http://localhost:3000/api","/colleges/").concat(e,"/teachers?").concat(s);console.log("Fetching teachers: ".concat(l));let i=await fetch(l,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o)},cache:"no-store"});if(!i.ok){let e=await i.text();try{let t=JSON.parse(e);return(0,r.hS)(t.message||"Error: ".concat(i.status),"Failed to load teachers. Please try again.")}catch(e){return(0,r.hS)("Error: ".concat(i.status," - ").concat(i.statusText),"Failed to load teachers. Please try again.")}}let c=await i.json();if(console.log("Raw API response:",c),Array.isArray(c)){console.log("API returned an array, converting to paginated format");let e={teachers:c,total:c.length,page:t,limit:a,totalPages:Math.ceil(c.length/a)};return(0,r.$y)(e)}return(0,r.$y)(c)}catch(e){return console.error("Error fetching college teachers:",e),(0,r.hS)(e.message||"Failed to load teachers. Please try again.","Failed to load teachers. Please try again.")}},s=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher with data:",t);let n=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to update teacher. Please try again.")}let o=await n.json();return(0,r.$y)(o,!0,"Teacher updated successfully!")}catch(e){return console.error("Error updating teacher:",e),(0,r.hS)(e.message||"Failed to update teacher. Please try again.","Failed to update teacher. Please try again.")}},l=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let a=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to delete teacher. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"Teacher deleted successfully!")}catch(e){return console.error("Error deleting teacher:",e),(0,r.hS)(e.message||"Failed to delete teacher","Failed to delete teacher. Please try again.")}},i=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher profile with data:",e);let a=await fetch("".concat("http://localhost:3000/api","/teachers/me"),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to update profile. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"Profile updated successfully!")}catch(e){return console.error("Error updating teacher profile:",e),(0,r.hS)(e.message||"Failed to update profile. Please try again.","Failed to update profile. Please try again.")}},c=async()=>{try{let e=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let t=await fetch("".concat("http://localhost:3000/api","/users/me"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(t.status),"Failed to load profile. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching current teacher profile:",e),(0,r.hS)(e.message||"Failed to load profile. Please try again.","Failed to load profile. Please try again.")}}},12379:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(95155),n=a(12115),o=a(6874),s=a.n(o),l=a(59434),i=a(13052),c=a(5623);let d=e=>{let{items:t,maxItems:a=4,className:o}=e,d=n.useMemo(()=>t.length<=a?t:[t[0],{label:"..."},...t.slice(-2)],[t,a]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,l.cn)("flex items-center text-sm",o),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let a=t===d.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(i.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}):a?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(s(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},l=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...h}=e;return(0,r.createElement)("svg",{ref:t,...i,width:n,height:n,stroke:a,strokeWidth:s?24*Number(o)/Number(n):o,className:l("lucide",c),...h},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let a=(0,r.forwardRef)((a,o)=>{let{className:i,...d}=a;return(0,r.createElement)(c,{ref:o,iconNode:t,className:l("lucide-".concat(n(s(e))),"lucide-".concat(e),i),...d})});return a.displayName=s(e),a}},25731:(e,t,a)=>{"use strict";async function r(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function n(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let a=await t.json();if(!a||!a.accessToken||!a.user||!a.user.role)throw Error("Invalid response format from server");return a}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function o(e,t){try{let a=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(a.status))}let r=await a.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),r=localStorage.getItem("firebaseToken"),n=localStorage.getItem("backendToken"),o={"Content-Type":"application/json",...n?{Authorization:"Bearer ".concat(n)}:r?{Authorization:"Bearer ".concat(r)}:{},...t.headers};try{let e=await fetch(a,{...t,headers:o});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let r=e.headers.get("content-type");if(r&&r.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}a.d(t,{K8:()=>n,V7:()=>r,Xw:()=>o,apiCall:()=>s})},28883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29797:(e,t,a)=>{"use strict";a.d(t,{d:()=>l});var r=a(95155);a(12115);var n=a(30285),o=a(42355),s=a(13052);function l(e){let{currentPage:t,totalPages:a,onPageChange:l,pageSize:i,totalItems:c,onPageSizeChange:d,pageSizeOptions:u=[5,10,20,50]}=e,h=Math.min(c,(t-1)*i+1),m=Math.min(c,t*i);return(0,r.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,r.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:c>0?(0,r.jsxs)("p",{children:["Showing ",h," to ",m," of ",c," items"]}):(0,r.jsx)("p",{children:"No items"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[d&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,r.jsx)("select",{className:"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm",value:i,onChange:e=>d(Number(e.target.value)),children:u.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>l(t-1),disabled:1===t,className:"h-8 w-8 p-0",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Previous page"})]}),(()=>{let e=[];if(a<=5)for(let t=1;t<=a;t++)e.push(t);else{e.push(1),t>3&&e.push("ellipsis");let r=Math.max(2,t-1),n=Math.min(a-1,t+1);for(let t=r;t<=n;t++)e.push(t);t<a-2&&e.push("ellipsis"),a>1&&e.push(a)}return e})().map((e,a)=>"ellipsis"===e?(0,r.jsx)("span",{className:"px-2",children:"..."},"ellipsis-".concat(a)):(0,r.jsx)(n.$,{variant:t===e?"default":"outline",size:"sm",onClick:()=>l(e),className:"h-8 w-8 p-0",children:e},"page-".concat(e))),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>l(t+1),disabled:t===a||0===a,className:"h-8 w-8 p-0",children:[(0,r.jsx)(s.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Next page"})]})]})]})]})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>l});var r=a(95155);a(12115);var n=a(66634),o=a(74466),s=a(59434);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:o,asChild:i=!1,...c}=e,d=i?n.Slot:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,s.cn)(l({variant:a,size:o,className:t})),...c})}},33475:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>z});var r=a(95155),n=a(12115),o=a(91769),s=a(12379),l=a(56671),i=a(71007),c=a(19946);let d=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),u=(0,c.A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]]);var h=a(62525);function m(e){let{data:t=[],title:a="View Teachers list",onEdit:o,onDelete:s,columns:l=["name","department","email","phone","status","actions"],itemsPerPage:c=5,isLoading:m=!1,onRefresh:g,onFilter:p}=e,[f,x]=(0,n.useState)([]),[y,v]=(0,n.useState)(1);(0,n.useEffect)(()=>{Array.isArray(t)?x(t):x([])},[t]),(0,n.useEffect)(()=>{v(1)},[t]),null==f||f.length;let b=(y-1)*c,w=(null==f?void 0:f.slice(b,b+c))||[];return(0,r.jsxs)("div",{className:"w-full bg-white rounded-xl shadow border border-gray-200 p-0",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between px-6 pt-6 pb-2",children:[(0,r.jsx)("h2",{className:"text-xl font-bold mb-2 md:mb-0",children:"View Techers list"}),(0,r.jsx)("div",{className:"flex gap-2"})]}),(0,r.jsx)("div",{className:"overflow-x-auto px-6 pb-6",children:(0,r.jsxs)("table",{className:"min-w-full bg-white rounded-xl",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200 text-gray-500 text-sm",children:[(0,r.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Teacher Name"}),(0,r.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Subject Assigned"}),(0,r.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Email Id"}),(0,r.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Phone number"}),(0,r.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Status"}),(0,r.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:m?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:6,className:"text-center py-10",children:"Loading..."})}):0===w.length?(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:6,className:"text-center py-10 text-gray-500",children:"No teachers found"})}):w.map(e=>(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition",children:[(0,r.jsxs)("td",{className:"py-3 px-4 flex items-center gap-3 min-w-[200px]",children:[e.avatar?(0,r.jsx)("img",{src:e.avatar,alt:e.name,className:"w-10 h-10 rounded-full object-cover border"}):(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"w-6 h-6 text-gray-400"})}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)(d,{className:"w-4 h-4 text-blue-500 ml-1",fill:"#3b82f6"})]}),(0,r.jsx)("td",{className:"py-3 px-4 text-gray-700",children:e.department||"-"}),(0,r.jsx)("td",{className:"py-3 px-4 text-gray-700",children:e.email}),(0,r.jsx)("td",{className:"py-3 px-4 font-medium text-green-600",children:e.phone}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-white text-sm font-medium ".concat("active"===e.status.toLowerCase()?"bg-emerald-500":"bg-red-500"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,r.jsx)("td",{className:"py-3 px-4",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("button",{onClick:()=>o&&o(e.id),className:"rounded-full bg-yellow-100 hover:bg-yellow-200 p-2 text-yellow-700 transition",title:"Edit",children:(0,r.jsx)(u,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>s&&s(e.id),className:"rounded-full bg-red-100 hover:bg-red-200 p-2 text-red-700 transition",title:"Delete",children:(0,r.jsx)(h.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})]})}var g=a(92657),p=a(51154);let f=[{key:"view",label:"View Teachers List",icon:(0,r.jsx)(g.A,{className:"w-4 h-4 mr-2"})},{key:"add",label:"Add Teachers",icon:(0,r.jsx)("span",{className:"font-bold text-xl mr-2",children:"+"})}],x=e=>{let{activeTab:t,onTabChange:a,collegeIdMissing:o}=e,[s,l]=(0,n.useState)(null),i=e=>{o||(l(e),setTimeout(()=>{a(e),l(null)},300))};return(0,r.jsx)("div",{className:"border-b mb-6",children:(0,r.jsx)("div",{className:"flex flex-wrap space-x-4",children:f.map(e=>{let a=t===e.key,n=s===e.key;return(0,r.jsxs)("button",{onClick:()=>i(e.key),disabled:o,className:"\n                inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 -mb-px transition-all\n                ".concat(a?"border-teacher-blue text-teacher-blue font-semibold":"border-transparent text-muted-foreground hover:text-foreground hover:border-border","\n                ").concat(o?"opacity-50 cursor-not-allowed":"","\n              "),children:[n?(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2 animate-spin"}):e.icon,e.label]},e.key)})})})};var y=a(62523),v=a(85057),b=a(30285),w=a(28883),j=a(66474),k=a(35695),N=a(55594),S=a(11723),A=a(51790),P=a(55097);let E=N.z.object({name:N.z.string().min(2,{message:"Name must be at least 2 characters"}),email:N.z.string().email({message:"Please enter a valid email address"}),phone:N.z.string().min(10,{message:"Please enter a valid phone number"}),department:N.z.string().optional(),designation:N.z.string().optional()}),I=e=>{let{onCancel:t,onSuccess:a}=e;(0,k.useParams)();let{user:o,userRole:s}=(0,A.A)(),[i,c]=(0,n.useState)(null);(0,n.useEffect)(()=>{try{let e=null;for(let t of["token","backendToken","authToken","jwtToken"]){let a=localStorage.getItem(t);if(a){console.log("Found token with key: ".concat(t)),e=a;break}}if(e){let t=e.split(".");if(3===t.length){let e=JSON.parse(atob(t[1]));console.log("JWT payload:",e),e.collegeId&&(console.log("Found collegeId in JWT:",e.collegeId),c(e.collegeId))}}else console.warn("No token found in localStorage")}catch(e){console.error("Error parsing JWT token:",e)}},[]),(0,n.useEffect)(()=>{console.log("localStorage keys:",Object.keys(localStorage)),Object.keys(localStorage).forEach(e=>{e.toLowerCase().includes("token")&&console.log("Found potential token in localStorage with key: ".concat(e))})},[]);let[d,u]=(0,n.useState)(!1),[h,m]=(0,n.useState)({name:"",email:"",phone:"",department:"",designation:""}),[g,p]=(0,n.useState)(!1),[f,x]=(0,n.useState)({});if((0,n.useEffect)(()=>{i||console.warn("College ID is missing. Form submission will fail.")},[i]),d)return(0,r.jsx)("div",{children:"Loading..."});if(!i)return null;let I=()=>{try{return E.parse(h),x({}),!0}catch(e){if(e instanceof N.z.ZodError){let t={};e.errors.forEach(e=>{t[e.path[0]]=e.message}),x(t)}return!1}},C=async e=>{if(e.preventDefault(),!I())return;let t=i;if(!t)try{for(let e of["token","backendToken","authToken","jwtToken"]){let a=localStorage.getItem(e);if(a)try{let r=JSON.parse(atob(a.split(".")[1]));if(r.collegeId){t=r.collegeId,console.log("Found collegeId in ".concat(e,":"),t);break}}catch(t){console.error("Error parsing token from ".concat(e,":"),t)}}}catch(e){console.error("Error getting collegeId from tokens:",e)}if(!t){l.oR.error("College ID is missing. Please try again or contact support.");return}p(!0);try{console.log("Submitting teacher with collegeId:",t);let e=await (0,S.q6)(t,h);(0,P.cY)(e)&&(m({name:"",email:"",phone:"",department:"",designation:""}),a&&a())}catch(e){console.error("Unexpected error adding teacher:",e),l.oR.error("An unexpected error occurred. Please try again.")}finally{p(!1)}},T=e=>{let{name:t,value:a}=e.target;m(e=>({...e,[t]:a})),f[t]&&x(e=>{let a={...e};return delete a[t],a})};return(0,r.jsxs)("form",{onSubmit:C,className:"space-y-6 w-[450px]",children:[(0,r.jsx)("h2",{className:"text-xl font-bold mb-8 md:mb-0",children:"Add Teachers"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(v.J,{htmlFor:"name",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Teacher's Name"}),(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"Required"})]}),(0,r.jsx)(y.p,{id:"name",name:"name",value:h.name,onChange:T,placeholder:"Enter teacher name",className:"mt-1 w-[450px] h-[46px] ".concat(f.name?"border-red-500":"")}),f.name&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(v.J,{htmlFor:"email",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Email"}),(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"Required"})]}),(0,r.jsxs)("div",{className:"relative mt-1",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(w.A,{className:"h-4 w-4 text-gray-400"})}),(0,r.jsx)(y.p,{id:"email",name:"email",type:"email",value:h.email,onChange:T,placeholder:"Email address",className:"pl-10 w-[450px] h-[46px] ".concat(f.email?"border-red-500":"")})]}),f.email&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(v.J,{htmlFor:"phone",className:"text-base font-medium text-gray-700",children:"Phone"}),(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"Required"})]}),(0,r.jsxs)("div",{className:"flex w-full h-12 border rounded-md overflow-hidden ".concat(f.phone?"border-red-500":"border-gray-200"),style:{width:"450px",height:"46px"},children:[(0,r.jsx)("div",{className:"flex items-center justify-center bg-white border-r border-gray-200 w-24 px-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-6 h-4 rounded-sm overflow-hidden",children:(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-b from-orange-500 via-white to-green-600 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-3 h-3 border border-blue-800 rounded-full bg-white flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-1.5 h-1.5 bg-blue-800 rounded-full"})})})}),(0,r.jsx)("span",{className:"text-sm",children:"IN"}),(0,r.jsx)(j.A,{className:"h-4 w-4 text-gray-400"})]})}),(0,r.jsx)(y.p,{id:"phone",name:"phone",type:"tel",value:h.phone,onChange:e=>{let t=e.target.value.replace(/\D/g,"");if(0===t.length){m({...h,phone:""}),f.phone&&x(e=>{let t={...e};return delete t.phone,t});return}let a=t;a.startsWith("91")&&(a=a.slice(2)),a.length>10&&(a=a.slice(0,10));let r="";r=0===a.length?"":a.length<=5?"+91 ".concat(a):"+91 ".concat(a.slice(0,5)," ").concat(a.slice(5)),m({...h,phone:r}),f.phone&&x(e=>{let t={...e};return delete t.phone,t})},placeholder:"+91 00000 00000",className:"border-none flex-1 focus-visible:ring-0 h-full"})]}),f.phone&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.phone})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(v.J,{htmlFor:"department",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Teacher's Department"}),(0,r.jsx)(y.p,{id:"department",name:"department",value:h.department,onChange:T,placeholder:"Enter teacher department",className:"mt-1 w-[450px] h-[46px]"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(v.J,{htmlFor:"designation",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Teacher's Designation"}),(0,r.jsx)(y.p,{id:"designation",name:"designation",value:h.designation,onChange:T,placeholder:"Enter teacher designation",className:"mt-1 w-[450px] h-[46px]"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,r.jsx)(b.$,{type:"button",variant:"outline",onClick:t,disabled:g,className:"text-white hover:text-white bg-[#EF4444] hover:bg-red-600 border-none",children:"Cancel"}),(0,r.jsx)(b.$,{type:"button",variant:"outline",onClick:()=>{m({name:"",email:"",phone:"",department:"",designation:""}),x({})},disabled:g,children:"Reset"}),(0,r.jsx)(b.$,{type:"submit",disabled:g,className:"text-white bg-[#05603A] hover:bg-[#04502F] border-none",children:g?"Adding...":"Add Teacher"})]})]})},C=e=>{let{teacher:t,onCancel:a,onSubmit:o}=e,[s,i]=(0,n.useState)({phone:"",department:"",designation:"",status:""}),[c,d]=(0,n.useState)(!1),[u,h]=(0,n.useState)({});(0,n.useEffect)(()=>{var e;i({phone:t.phone||"",department:t.department||"",designation:"",status:(null===(e=t.status)||void 0===e?void 0:e.toLowerCase())||"active"})},[t]);let m=()=>{let e={};return s.phone.trim()||(e.phone="Phone number is required"),h(e),0===Object.keys(e).length},g=e=>{let{name:t,value:a}=e.target;i(e=>({...e,[t]:a})),u[t]&&h(e=>({...e,[t]:""}))},p=async e=>{if(e.preventDefault(),m()){d(!0);try{await o({phone:s.phone,department:s.department,designation:s.designation,status:s.status})}catch(e){console.error("Error updating teacher:",e),l.oR.error("Failed to update teacher. Please try again.")}finally{d(!1)}}};return(0,r.jsxs)("form",{onSubmit:p,className:"space-y-6 w-[450px]",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(v.J,{htmlFor:"phone",children:"Phone Number"}),(0,r.jsx)(y.p,{id:"phone",name:"phone",value:s.phone,onChange:g,placeholder:"Enter phone number",className:u.phone?"border-red-500":""}),u.phone&&(0,r.jsx)("p",{className:"text-red-500 text-sm mt-1",children:u.phone})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(v.J,{htmlFor:"designation",children:"Designation"}),(0,r.jsx)(y.p,{id:"designation",name:"designation",value:s.designation,onChange:g,placeholder:"Enter designation"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(v.J,{htmlFor:"status",children:"Status"}),(0,r.jsxs)("select",{id:"status",name:"status",value:s.status,onChange:e=>i(t=>({...t,status:e.target.value})),className:"w-full p-2 border rounded-md",children:[(0,r.jsx)("option",{value:"active",children:"Active"}),(0,r.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)(b.$,{type:"button",variant:"outline",onClick:a,disabled:c,className:"text-white hover:text-white bg-[#EF4444] hover:bg-red-600 border-none",children:"Cancel"}),(0,r.jsx)(b.$,{type:"submit",disabled:c,className:"text-white bg-[#05603A] hover:bg-[#04502F] border-none",children:c?"Updating...":"Update Teacher"})]})]})};var T=a(54165),F=a(29797);let z=()=>{let[e,t]=(0,n.useState)("view"),[a,i]=(0,n.useState)(null),[c,d]=(0,n.useState)(!1),[u,h]=(0,n.useState)(null),[g,p]=(0,n.useState)(!1),[f,y]=(0,n.useState)({name:"",department:"",status:"all",email:""}),[v,w]=(0,n.useState)([]),[j,k]=(0,n.useState)([]),[N,A]=(0,n.useState)(null),[E,z]=(0,n.useState)(!1),[R,O]=(0,n.useState)(""),[D,J]=(0,n.useState)(1),[$,L]=(0,n.useState)(10),[U,M]=(0,n.useState)(0),[q,W]=(0,n.useState)(1);(0,n.useEffect)(()=>{let e=localStorage.getItem("collegeId");if(e){console.log("Found collegeId in localStorage:",e),A(e);return}try{for(let e of["token","backendToken","authToken","jwtToken"]){let t=localStorage.getItem(e);if(t)try{let a=t.split(".");if(3===a.length){let t=JSON.parse(atob(a[1]));if(console.log("JWT payload:",t),t.collegeId){console.log("Found collegeId in ".concat(e,":"),t.collegeId),A(t.collegeId),localStorage.setItem("collegeId",t.collegeId);return}}}catch(t){console.error("Error parsing token from ".concat(e,":"),t)}}console.log("All localStorage keys:",Object.keys(localStorage)),console.error("Could not find collegeId in any token or localStorage")}catch(e){console.error("Error getting collegeId:",e)}},[]);let{data:V=[],isLoading:_,refetch:B,error:G}=(0,o.I)({queryKey:["teachers",N,D,$,f],queryFn:async()=>{if(!N)return console.log("No collegeId found, returning empty array"),[];try{console.log("Fetching teachers for collegeId: ".concat(N,", page: ").concat(D,", limit: ").concat($));let e={};f.name&&(e.name=f.name),f.email&&(e.email=f.email),f.department&&"all_departments"!==f.department&&(e.department=f.department),f.status&&"all"!==f.status&&(e.status=f.status);let t=await (0,S.HC)(N,D,$,e);if(console.log("API returned response:",t),!(0,P.cY)(t))return[];let a=t.data,r=[],n=0,o=1;if(Array.isArray(a))r=a,n=a.length,o=1;else{if(!a||!a.teachers)return console.error("API returned invalid data format:",a),[];r=a.teachers,n=a.total||a.teachers.length,o=a.totalPages||Math.ceil(n/$)}return M(n),W(o),r.map(e=>({id:e._id,name:e.displayName||e.name||"Unknown",email:e.email||"No email",department:e.department||"N/A",phone:e.phone||"N/A",status:"active"===e.status?"Active":"Inactive",avatar:e.avatar||null}))}catch(e){return console.error("Failed to fetch teachers:",e),l.oR.error("Failed to load teachers. Please try again."),[]}},enabled:!!N,staleTime:0,refetchOnWindowFocus:!0});(0,n.useEffect)(()=>{G&&console.error("Query error:",G)},[G]),(0,n.useEffect)(()=>{console.log("teachersData changed:",V),console.log("teachersData length:",(null==V?void 0:V.length)||0),V&&V.length>0?(w(Array.from(new Set(V.map(e=>e.department).filter(Boolean)))),k([...V]),console.log("Setting filtered teachers to:",V.length)):console.log("No teachers data to set")},[V]);let Y=e=>{console.log("Page received tab change:",e),"edit"!==e&&i(null),setTimeout(()=>{t(e)},100)},H=async()=>{if(u)try{let e=await (0,S.qg)(u);(0,P.cY)(e)&&B()}catch(e){console.error("Unexpected error deleting teacher:",e),l.oR.error("An unexpected error occurred. Please try again.")}finally{d(!1),h(null)}},K=async e=>{if(a)try{let r={phone:e.phone,department:e.department,designation:e.designation,status:e.status.toLowerCase()},n=await (0,S.Sp)(a.id,r);(0,P.cY)(n)&&(B(),t("view"),i(null))}catch(e){console.error("Unexpected error updating teacher:",e),l.oR.error("An unexpected error occurred. Please try again.")}},Z=(j.length>0?j:V).slice((D-1)*$,D*$);return(0,r.jsxs)("div",{className:"container py-6",children:[!N&&(0,r.jsx)("div",{className:"mb-6 p-4 border border-yellow-300 bg-yellow-50 rounded-md",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)("p",{className:"text-yellow-800",children:"College ID not found. Please enter it manually or check your login status."}),E?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"text",value:R,onChange:e=>O(e.target.value),placeholder:"Enter College ID",className:"px-3 py-2 border rounded-md flex-1"}),(0,r.jsx)(b.$,{onClick:()=>{R.trim()&&(console.log("Setting manual collegeId:",R),A(R),localStorage.setItem("collegeId",R),z(!1),B())},children:"Submit"}),(0,r.jsx)(b.$,{variant:"outline",onClick:()=>z(!1),children:"Cancel"})]}):(0,r.jsx)(b.$,{variant:"outline",onClick:()=>z(!0),children:"Enter College ID Manually"})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"view"===e?"Teacher's List View":"add"===e?"Add Teachers":"edit"===e?"Edit Teacher":"Teacher Activity Logs"}),(0,r.jsx)(s.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"view"===e?"Teacher list":"add"===e?"Add teachers":"edit"===e?"Edit teacher":"Teacher activity logs"}],className:"mt-2"})]}),(0,r.jsxs)("div",{className:"rounded-lg border bg-card text-card-foreground shadow",children:[(0,r.jsx)("div",{className:"flex justify-between items-center px-6 pt-6",children:(0,r.jsx)(x,{activeTab:"edit"===e?"view":e,onTabChange:Y,collegeIdMissing:!N})}),"view"===e&&(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(m,{data:Z,onEdit:e=>{console.log("Edit teacher with id: ".concat(e));let a=V.find(t=>t.id===e);a&&(i(a),t("edit"))},onDelete:e=>{console.log("Delete teacher with id: ".concat(e)),h(e),d(!0)},itemsPerPage:$,isLoading:_,onRefresh:()=>{y({name:"",department:"",status:"all",email:""}),k([]),B()},onFilter:()=>{p(!0)},columns:["name","department","email","phone","status","actions"]}),!_&&V.length>0&&(0,r.jsx)(F.d,{currentPage:D,totalPages:Math.ceil((j.length>0?j.length:U)/$),pageSize:$,totalItems:j.length>0?j.length:U,onPageChange:e=>{J(e)},onPageSizeChange:e=>{L(e),J(1)},pageSizeOptions:[5,10,20,50]})]}),"add"===e&&(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(I,{onCancel:()=>Y("view"),onSuccess:()=>{B(),t("view")}})}),"edit"===e&&a&&(0,r.jsx)("div",{className:"p-6",children:(0,r.jsx)(C,{teacher:a,onCancel:()=>Y("view"),onSubmit:K})})]})]}),(0,r.jsx)(T.lG,{open:c,onOpenChange:d,children:(0,r.jsxs)(T.Cf,{children:[(0,r.jsxs)(T.c7,{children:[(0,r.jsx)(T.L3,{children:"Confirm Deletion"}),(0,r.jsx)(T.rr,{children:"Are you sure you want to delete this teacher? This action cannot be undone."})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 mt-4",children:[(0,r.jsx)(b.$,{variant:"outline",onClick:()=>d(!1),children:"Cancel"}),(0,r.jsx)(b.$,{variant:"destructive",onClick:H,children:"Delete"})]})]})})]})}},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51790:(e,t,a)=>{"use strict";a.d(t,{AuthProvider:()=>u,A:()=>h,t:()=>m});var r=a(95155),n=a(12115),o=a(16203),s=a(23915);let l=0===(0,s.Dk)().length?(0,s.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,s.Dk)()[0],i=(0,o.xI)(l);var c=a(25731);let d=(0,n.createContext)(void 0),u=e=>{let{children:t}=e,[a,s]=(0,n.useState)(null),[l,u]=(0,n.useState)(null),[h,m]=(0,n.useState)(!0);(0,n.useEffect)(()=>{let e=(0,o.hg)(i,async e=>{if(s(e),e){let t=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",t),t)u(t);else try{console.log("No role in localStorage, trying to get from backend");let t=await e.getIdToken();localStorage.setItem("firebaseToken",t);let a=await (0,c.K8)();a&&a.user&&a.user.role&&(console.log("Got role from backend:",a.user.role),localStorage.setItem("userRole",a.user.role),u(a.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else u(null);m(!1)});return()=>e()},[]);let g=async(e,t,a)=>{try{let r=await (0,o.eJ)(i,e,t);r.user&&(await (0,o.r7)(r.user,{displayName:a}),await (0,c.V7)(r.user))}catch(e){throw console.error("Error signing up:",e),e}},p=async(e,t)=>{try{let a=await (0,o.x9)(i,e,t);await (0,c.V7)(a.user)}catch(e){throw console.error("Error logging in:",e),e}},f=async()=>{try{let e=new o.HF,t=await (0,o.df)(i,e);await (0,c.V7)(t.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},x=async()=>{try{await (0,o.CI)(i),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},y=async e=>{try{await (0,o.J1)(i,e);try{await fetch("".concat("http://localhost:3000/api","/auth/reset-password-request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},v=async()=>{try{if(!a)throw Error("No authenticated user found");await (0,c.V7)(a);try{let e=await (0,c.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},b=async()=>{try{let e=i.currentUser;e&&(await (0,o.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,r.jsx)(d.Provider,{value:{user:a,userRole:l,loading:h,signUp:g,login:p,loginWithGoogle:f,logout:x,resetPassword:y,setUserRole:e=>{localStorage.setItem("userRole",e),u(e)},handlePasswordResetCompletion:v,deleteAccount:b},children:t})};function h(){let e=(0,n.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function m(){return(0,n.useContext)(d)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>d,L3:()=>h,c7:()=>u,lG:()=>l,rr:()=>m});var r=a(95155);a(12115);var n=a(4033),o=a(54416),s=a(59434);function l(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,r.jsx)(n.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function d(e){let{className:t,children:a,...l}=e;return(0,r.jsxs)(i,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...l,children:[a,(0,r.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,s.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,s.cn)("text-lg leading-none font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55097:(e,t,a)=>{"use strict";a.d(t,{$y:()=>o,cY:()=>s,hS:()=>n});var r=a(56671);function n(e){var t,a,n,o;let s,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=l;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)&&(c=e.data.message),(null==e?void 0:e.status)?s=e.status:(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)&&(s=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===s?c="Server error. Please try again later.":503===s&&(c="Service temporarily unavailable. Please try again later.")),i&&r.oR.error(c),{success:!1,error:c,statusCode:s}}function o(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&r.oR.success(a),{success:!0,data:e}}function s(e){return!0===e.success}},59434:(e,t,a)=>{"use strict";a.d(t,{b:()=>s,cn:()=>o});var r=a(52596),n=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function s(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>o});var r=a(95155);a(12115);var n=a(59434);function o(e){let{className:t,type:a,...o}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63540:(e,t,a)=>{"use strict";a.d(t,{sG:()=>u,hO:()=>h});var r=a(12115),n=a(47650),o=a(6101),s=a(95155),l=r.forwardRef((e,t)=>{let{children:a,...n}=e,o=r.Children.toArray(a),l=o.find(d);if(l){let e=l.props.children,a=o.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,s.jsx)(i,{...n,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,s.jsx)(i,{...n,ref:t,children:a})});l.displayName="Slot";var i=r.forwardRef((e,t)=>{let{children:a,...n}=e;if(r.isValidElement(a)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=t&&"isReactWarning"in t&&t.isReactWarning;return a?e.ref:(a=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a),s=function(e,t){let a={...t};for(let r in t){let n=e[r],o=t[r];/^on[A-Z]/.test(r)?n&&o?a[r]=(...e)=>{o(...e),n(...e)}:n&&(a[r]=n):"style"===r?a[r]={...n,...o}:"className"===r&&(a[r]=[n,o].filter(Boolean).join(" "))}return{...e,...a}}(n,a.props);return a.type!==r.Fragment&&(s.ref=t?(0,o.t)(t,e):e),r.cloneElement(a,s)}return r.Children.count(a)>1?r.Children.only(null):null});i.displayName="SlotClone";var c=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});function d(e){return r.isValidElement(e)&&e.type===c}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let a=r.forwardRef((e,a)=>{let{asChild:r,...n}=e,o=r?l:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(o,{...n,ref:a})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function h(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},66474:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>s});var r=a(95155);a(12115);var n=a(40968),o=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},92657:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7146,4277,6874,6671,2571,685,7117,5631,6967,7546,8441,1684,7358],()=>t(1800)),_N_E=e.O()}]);