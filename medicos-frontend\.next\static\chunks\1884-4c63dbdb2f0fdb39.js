"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1884],{17972:(e,t,a)=>{a.d(t,{G7:()=>c,Gj:()=>d,L$:()=>g,h_:()=>m,oI:()=>u,uB:()=>l,xL:()=>h});var r=a(95155);a(12115);var s=a(77740),o=a(47924),n=a(59434),i=a(54165);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.uB,{"data-slot":"command",className:(0,n.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...a})}function d(e){let{title:t="Command Palette",description:a="Search for a command to run...",children:s,...o}=e;return(0,r.jsxs)(i.lG,{...o,children:[(0,r.jsxs)(i.c7,{className:"sr-only",children:[(0,r.jsx)(i.L3,{children:t}),(0,r.jsx)(i.rr,{children:a})]}),(0,r.jsx)(i.Cf,{className:"overflow-hidden p-0",children:(0,r.jsx)(l,{className:"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:s})})]})}function c(e){let{className:t,...a}=e;return(0,r.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,r.jsx)(o.A,{className:"size-4 shrink-0 opacity-50"}),(0,r.jsx)(s.uB.Input,{"data-slot":"command-input",className:(0,n.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",t),...a})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.uB.List,{"data-slot":"command-list",className:(0,n.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",t),...a})}function h(e){let{...t}=e;return(0,r.jsx)(s.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.uB.Group,{"data-slot":"command-group",className:(0,n.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(s.uB.Item,{"data-slot":"command-item",className:(0,n.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}},22346:(e,t,a)=>{a.d(t,{w:()=>n});var r=a(95155);a(12115);var s=a(87489),o=a(59434);function n(e){let{className:t,orientation:a="horizontal",decorative:n=!0,...i}=e;return(0,r.jsx)(s.b,{"data-slot":"separator-root",decorative:n,orientation:a,className:(0,o.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...i})}},25731:(e,t,a)=>{async function r(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function s(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let a=await t.json();if(!a||!a.accessToken||!a.user||!a.user.role)throw Error("Invalid response format from server");return a}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function o(e,t){try{let a=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(a.status))}let r=await a.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),r=localStorage.getItem("firebaseToken"),s=localStorage.getItem("backendToken"),o={"Content-Type":"application/json",...s?{Authorization:"Bearer ".concat(s)}:r?{Authorization:"Bearer ".concat(r)}:{},...t.headers};try{let e=await fetch(a,{...t,headers:o});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let r=e.headers.get("content-type");if(r&&r.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}a.d(t,{K8:()=>s,V7:()=>r,Xw:()=>o,apiCall:()=>n})},26126:(e,t,a)=>{a.d(t,{E:()=>l});var r=a(95155);a(12115);var s=a(66634),o=a(74466),n=a(59434);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:o=!1,...l}=e,d=o?s.Slot:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(i({variant:a}),t),...l})}},30285:(e,t,a)=>{a.d(t,{$:()=>l,r:()=>i});var r=a(95155);a(12115);var s=a(66634),o=a(74466),n=a(59434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:o,asChild:l=!1,...d}=e,c=l?s.Slot:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:a,size:o,className:t})),...d})}},40124:(e,t,a)=>{a.d(t,{p:()=>s});let r=a(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}});async function s(){try{let e=localStorage.getItem("backendToken");console.log("Token being sent:",e);let t=await fetch("http://localhost:3000/api/users/me",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json",...e?{Authorization:"Bearer ".concat(e)}:{}}});if(!t.ok)return console.error("Failed to fetch current user:",t.statusText),null;return await t.json()}catch(e){return console.error("Error fetching current user:",e),null}}r.interceptors.request.use(e=>{{let t=localStorage.getItem("token");if(!t)return window.location.href="/login",Promise.reject("No token found");e.headers.Authorization="Bearer ".concat(t)}return e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)})},44838:(e,t,a)=>{a.d(t,{SQ:()=>l,_2:()=>d,lp:()=>c,mB:()=>u,rI:()=>n,ty:()=>i});var r=a(95155);a(12115);var s=a(76215),o=a(59434);function n(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t})}function l(e){let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function d(e){let{className:t,inset:a,variant:n="default",...i}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function c(e){let{className:t,inset:a,...n}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},46102:(e,t,a)=>{a.d(t,{Bc:()=>n,ZI:()=>d,k$:()=>l,m_:()=>i});var r=a(95155);a(12115);var s=a(78082),o=a(59434);function n(e){let{delayDuration:t=0,...a}=e;return(0,r.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function i(e){let{...t}=e;return(0,r.jsx)(n,{children:(0,r.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function l(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function d(e){let{className:t,sideOffset:a=0,children:n,...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,o.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...i,children:[n,(0,r.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},51790:(e,t,a)=>{a.d(t,{AuthProvider:()=>u,A:()=>h,t:()=>g});var r=a(95155),s=a(12115),o=a(16203),n=a(23915);let i=0===(0,n.Dk)().length?(0,n.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,n.Dk)()[0],l=(0,o.xI)(i);var d=a(25731);let c=(0,s.createContext)(void 0),u=e=>{let{children:t}=e,[a,n]=(0,s.useState)(null),[i,u]=(0,s.useState)(null),[h,g]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,o.hg)(l,async e=>{if(n(e),e){let t=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",t),t)u(t);else try{console.log("No role in localStorage, trying to get from backend");let t=await e.getIdToken();localStorage.setItem("firebaseToken",t);let a=await (0,d.K8)();a&&a.user&&a.user.role&&(console.log("Got role from backend:",a.user.role),localStorage.setItem("userRole",a.user.role),u(a.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else u(null);g(!1)});return()=>e()},[]);let m=async(e,t,a)=>{try{let r=await (0,o.eJ)(l,e,t);r.user&&(await (0,o.r7)(r.user,{displayName:a}),await (0,d.V7)(r.user))}catch(e){throw console.error("Error signing up:",e),e}},f=async(e,t)=>{try{let a=await (0,o.x9)(l,e,t);await (0,d.V7)(a.user)}catch(e){throw console.error("Error logging in:",e),e}},p=async()=>{try{let e=new o.HF,t=await (0,o.df)(l,e);await (0,d.V7)(t.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},x=async()=>{try{await (0,o.CI)(l),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},b=async e=>{try{await (0,o.J1)(l,e);try{await fetch("".concat("http://localhost:3000/api","/auth/reset-password-request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},v=async()=>{try{if(!a)throw Error("No authenticated user found");await (0,d.V7)(a);try{let e=await (0,d.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},w=async()=>{try{let e=l.currentUser;e&&(await (0,o.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,r.jsx)(c.Provider,{value:{user:a,userRole:i,loading:h,signUp:m,login:f,loginWithGoogle:p,logout:x,resetPassword:b,setUserRole:e=>{localStorage.setItem("userRole",e),u(e)},handlePasswordResetCompletion:v,deleteAccount:w},children:t})};function h(){let e=(0,s.useContext)(c);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function g(){return(0,s.useContext)(c)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},54165:(e,t,a)=>{a.d(t,{Cf:()=>c,L3:()=>h,c7:()=>u,lG:()=>i,rr:()=>g});var r=a(95155);a(12115);var s=a(4033),o=a(54416),n=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function c(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(d,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[a,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},59434:(e,t,a)=>{a.d(t,{b:()=>n,cn:()=>o});var r=a(52596),s=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function n(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},61884:(e,t,a)=>{a.d(t,{N:()=>ei});var r=a(95155),s=a(12115),o=a(6874),n=a.n(o),i=a(35695),l=a(66634),d=a(74466);function c(){let[e,t]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}var u=a(59434),h=a(30285),g=a(62523);a(22346);var m=a(4033),f=a(54416);function p(e){let{...t}=e;return(0,r.jsx)(m.bL,{"data-slot":"sheet",...t})}function x(e){let{...t}=e;return(0,r.jsx)(m.ZL,{"data-slot":"sheet-portal",...t})}function b(e){let{className:t,...a}=e;return(0,r.jsx)(m.hJ,{"data-slot":"sheet-overlay",className:(0,u.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function v(e){let{className:t,children:a,side:s="right",...o}=e;return(0,r.jsxs)(x,{children:[(0,r.jsx)(b,{}),(0,r.jsxs)(m.UC,{"data-slot":"sheet-content",className:(0,u.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===s&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===s&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===s&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===s&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...o,children:[a,(0,r.jsxs)(m.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(f.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function w(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,u.cn)("flex flex-col gap-1.5 p-4",t),...a})}function j(e){let{className:t,...a}=e;return(0,r.jsx)(m.hE,{"data-slot":"sheet-title",className:(0,u.cn)("text-foreground font-semibold",t),...a})}function y(e){let{className:t,...a}=e;return(0,r.jsx)(m.VY,{"data-slot":"sheet-description",className:(0,u.cn)("text-muted-foreground text-sm",t),...a})}a(68856);var N=a(46102);let E=s.createContext(null);function k(){let e=s.useContext(E);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function A(e){let{defaultOpen:t=!0,open:a,onOpenChange:o,className:n,style:i,children:l,...d}=e,h=c(),[g,m]=s.useState(!1),[f,p]=s.useState(t),x=null!=a?a:f,b=s.useCallback(e=>{let t="function"==typeof e?e(x):e;o?o(t):p(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[o,x]),v=s.useCallback(()=>h?m(e=>!e):b(e=>!e),[h,b,m]);s.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),v())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[v]);let w=x?"expanded":"collapsed",j=s.useMemo(()=>({state:w,open:x,setOpen:b,isMobile:h,openMobile:g,setOpenMobile:m,toggleSidebar:v}),[w,x,b,h,g,m,v]);return(0,r.jsx)(E.Provider,{value:j,children:(0,r.jsx)(N.Bc,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,u.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...d,children:l})})})}function _(e){let{side:t="left",variant:a="sidebar",collapsible:s="offcanvas",className:o,children:n,...i}=e,{isMobile:l,state:d,openMobile:c,setOpenMobile:h}=k();return"none"===s?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,u.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",o),...i,children:n}):l?(0,r.jsx)(p,{open:c,onOpenChange:h,...i,children:(0,r.jsxs)(v,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,r.jsxs)(w,{className:"sr-only",children:[(0,r.jsx)(j,{children:"Sidebar"}),(0,r.jsx)(y,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:n})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":d,"data-collapsible":"collapsed"===d?s:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,u.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,u.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",o),...i,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:n})})]})}function S(e){let{className:t,onClick:a,...s}=e,{toggleSidebar:o}=k();return(0,r.jsxs)(h.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,u.cn)("size-10",t),onClick:e=>{null==a||a(e),o()},...s,children:[(0,r.jsx)("img",{src:"/assets/icons/sidebar-trigger.svg",alt:"icon",className:"h-9 w-9"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function I(e){let{className:t,...a}=e,{toggleSidebar:s}=k();return(0,r.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:s,title:"Toggle Sidebar",className:(0,u.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...a})}function C(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,u.cn)("flex flex-col gap-2 p-2",t),...a})}function P(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,u.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function L(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,u.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function T(e){let{className:t,asChild:a=!1,...s}=e,o=a?l.Slot:"div";return(0,r.jsx)(o,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,u.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...s})}function D(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,u.cn)("w-full text-sm",t),...a})}function z(e){let{className:t,...a}=e;return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,u.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function R(e){let{className:t,...a}=e;return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,u.cn)("group/menu-item relative",t),...a})}let M=(0,d.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function U(e){let{asChild:t=!1,isActive:a=!1,variant:s="default",size:o="default",tooltip:n,className:i,...d}=e,c=t?l.Slot:"button",{isMobile:h,state:g}=k(),m=(0,r.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":o,"data-active":a,className:(0,u.cn)(M({variant:s,size:o}),i),...d});return n?("string"==typeof n&&(n={children:n}),(0,r.jsxs)(N.m_,{children:[(0,r.jsx)(N.k$,{asChild:!0,children:m}),(0,r.jsx)(N.ZI,{side:"right",align:"center",hidden:"collapsed"!==g||h,...n})]})):m}var O=a(26126),G=a(71987),B=a(73783);let F=[{title:"MENU",items:[{title:"Dashboard",icon:null,iconPath:"/assets/icons/dashboard.svg",href:"/admin",submenu:[{title:"Overview",href:"/dashboard"}],roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Colleges",icon:null,href:"/admin/college",roles:[G.g.SUPER_ADMIN]},{title:"Add College",icon:null,href:"/admin/add-college",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Add Subjects, Chapters & Topics",icon:null,href:"/admin/add-subjectandtopic",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Question Bank",icon:null,href:"/admin/question-bank",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Add Question",icon:null,href:"/admin/add-question",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Analytics",icon:null,href:"/admin",badge:"NEW",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]},{title:"SUPPORT",items:[{title:"Chat",iconPath:"/assets/icons/chat.svg",icon:null,href:"/dashboard/chat",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Email",iconPath:"/assets/icons/email.svg",icon:null,href:"/dashboard/email",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Invoice",iconPath:"/assets/icons/invoice.svg",icon:null,href:"/dashboard/invoice",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]}],H=[{title:"MENU",items:[{title:"Generate Questions",iconPath:"/assets/icons/dashboard.svg",href:"/teacher",submenu:[{title:"Overview",href:"/dashboard"}],roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Downloaded Papers",iconPath:"/assets/icons/download.svg",href:"/teacher/downloaded-papers",roles:[G.g.TEACHER]},{title:"Settings",iconPath:"",href:"/teacher/settings",roles:[G.g.SUPER_ADMIN]},{title:"Profile",iconPath:"",href:"/teacher/profile",roles:[G.g.SUPER_ADMIN]}]},{title:"SUPPORT",items:[{title:"Chat",iconPath:"/assets/icons/chat.svg",icon:null,href:"/dashboard/chat",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Email",iconPath:"/assets/icons/email.svg",icon:null,href:"/dashboard/email",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Invoice",iconPath:"/assets/icons/invoice.svg",icon:null,href:"/dashboard/invoice",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]}],K=[{title:"MENU",items:[{title:"Dashboard",icon:B.A,href:"/college",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Teacher management",icon:null,href:"/college/teachers-list",roles:[G.g.SUPER_ADMIN]}]},{title:"SUPPORT",items:[{title:"Chat",iconPath:"/assets/icons/chat.svg",icon:null,href:"/dashboard/chat",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Email",iconPath:"/assets/icons/email.svg",icon:null,href:"/dashboard/email",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Invoice",iconPath:"/assets/icons/invoice.svg",icon:null,href:"/dashboard/invoice",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]}];function q(e){let{role:t}=e,a=(0,i.usePathname)(),s=F;switch(t){case G.g.COLLEGE_ADMIN:s=K;break;case G.g.TEACHER:s=H;break;case G.g.SUPER_ADMIN:default:s=F}return(0,r.jsxs)(_,{children:[(0,r.jsx)(C,{className:"p-4",children:(0,r.jsx)(n(),{href:"/dashboard",className:"flex items-center gap-2",children:(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("img",{src:"/assets/logo/medicos-logo.svg",alt:"Logo",className:"h-[70px] w-auto"})})})}),(0,r.jsx)(P,{children:s.map(e=>(0,r.jsxs)(L,{children:[(0,r.jsx)(T,{className:"font-outfit font-normal text-[12px] leading-[20px] uppercase mb-3 text-[#98A2B3]",children:e.title}),(0,r.jsx)(D,{children:(0,r.jsx)(z,{className:"space-y-2",children:e.items.map(e=>{let t=a===e.href||e.submenu&&e.submenu.some(e=>a===e.href);return(0,r.jsx)(R,{children:(0,r.jsx)(U,{asChild:!0,isActive:t,className:(0,u.cn)(t&&"!bg-[#E8F5E8] hover:!bg-[#E8F5E8]"),style:t?{background:"#E8F5E8"}:{},children:(0,r.jsxs)(n(),{href:e.href,className:(0,u.cn)("flex items-center",t&&"bg-[#E8F5E8]"),children:[e.iconPath?(0,r.jsx)("img",{src:e.iconPath,alt:"".concat(e.title," icon"),className:"mr-2 h-5 w-5 object-contain"}):e.icon?(0,r.jsx)(e.icon,{className:"mr-2 h-5 w-5 ".concat(t?"text-[#05603A]":"text-white")}):(0,r.jsx)("div",{className:"mr-2 h-5 w-5"}),(0,r.jsx)("span",{className:(0,u.cn)("text-sm font-medium",t?"text-[#05603A]":"text-white"),children:e.title}),e.badge&&(0,r.jsx)(O.E,{variant:"secondary",className:"ml-auto text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",children:e.badge})]})})},e.title)})})})]},e.title))}),(0,r.jsx)(I,{})]})}var V=a(47924),W=a(66474),Q=a(71007),J=a(381),$=a(34835),Z=a(44838),Y=a(91394),X=a(57434),ee=a(5040),et=a(17580),ea=a(17972);function er(e){let{open:t,onOpenChange:a,role:o}=e,n=(0,i.useRouter)();(0,s.useEffect)(()=>{let e=e=>{"k"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),a(!t))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,a]);let l=e=>{a(!1),e()};return(0,r.jsxs)(ea.Gj,{open:t,onOpenChange:a,children:[(0,r.jsx)(ea.G7,{placeholder:"Type a command or search..."}),(0,r.jsxs)(ea.oI,{children:[(0,r.jsx)(ea.xL,{children:"No results found."}),(0,r.jsxs)(ea.L$,{heading:"Navigation",children:[o===G.g.TEACHER&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/teacher")),children:[(0,r.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Generate Questions"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/teacher/downloaded-papers")),children:[(0,r.jsx)(X.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Downloaded Papers"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/teacher/settings")),children:[(0,r.jsx)(J.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/teacher/profile")),children:[(0,r.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile"})]})]}),o===G.g.SUPER_ADMIN&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/admin")),children:[(0,r.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dashboard"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/admin/college")),children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Colleges"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/admin/question-bank")),children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Question Bank"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/admin/add-question")),children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Add Question"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/admin/add-college")),children:[(0,r.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Add College"})]})]}),o===G.g.COLLEGE_ADMIN&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/college")),children:[(0,r.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Dashboard"})]}),(0,r.jsxs)(ea.h_,{onSelect:()=>l(()=>n.push("/college/teachers-list")),children:[(0,r.jsx)(et.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Teachers"})]})]})]})]})]})}var es=a(51790),eo=a(40124);function en(){var e;let[t,a]=(0,s.useState)(!1),[o,n]=(0,s.useState)(3),[l,d]=(0,s.useState)(null),{logout:c}=(0,es.A)(),u=(0,i.useRouter)();return(0,s.useEffect)(()=>{(async()=>{try{let e=await (0,eo.p)();console.log("User info:",e),d(e)}catch(e){console.error("Failed to fetch user info:",e)}})()},[]),(0,r.jsxs)("header",{className:"sticky top-0 z-30 flex h-16 w-full items-center justify-between border-b bg-background px-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(S,{className:"mr-2"}),(0,r.jsxs)("div",{className:"relative hidden md:block",children:[(0,r.jsx)(V.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(g.p,{type:"search",placeholder:"Search or type command...",className:"w-[300px] pl-8 md:w-[300px] lg:w-[400px]",onClick:()=>a(!0),style:{fontFamily:"Typeface/family/family",fontWeight:400,fontSize:"Typeface/size/Text sm",lineHeight:"Typeface/line height/Text sm",letterSpacing:"0%"}}),(0,r.jsxs)("kbd",{className:"pointer-events-none absolute right-2 top-2 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-xs font-medium opacity-100 sm:flex",children:[(0,r.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-4",children:(0,r.jsxs)(Z.rI,{children:[(0,r.jsx)(Z.ty,{asChild:!0,children:(0,r.jsxs)(h.$,{variant:"ghost",className:"flex items-center gap-2",children:[(0,r.jsxs)(Y.eu,{className:"h-8 w-8",children:[(0,r.jsx)(Y.BK,{src:"/placeholder.svg?height=32&width=32",alt:"User"}),(0,r.jsx)(Y.q5,{children:(null==l?void 0:l.displayName)&&(e=l.displayName)?e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U"})]}),(0,r.jsx)("div",{className:"hidden md:block text-left",style:{fontFamily:"Typeface/family/family",fontWeight:500,fontSize:"Typeface/size/Text sm",lineHeight:"Typeface/line height/Text sm",letterSpacing:"0%"},children:(0,r.jsx)("p",{className:"text-sm font-medium",children:(null==l?void 0:l.displayName)||"Loading..."})}),(0,r.jsx)(W.A,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,r.jsxs)(Z.SQ,{align:"end",className:"w-56",children:[(0,r.jsx)(Z.lp,{children:"My Account"}),(null==l?void 0:l.role)==="teacher"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Z.mB,{}),(0,r.jsxs)(Z._2,{onClick:()=>u.push("/profile"),children:[(0,r.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),"Profile"]}),(0,r.jsxs)(Z._2,{onClick:()=>u.push("/settings"),children:[(0,r.jsx)(J.A,{className:"mr-2 h-4 w-4"}),"Settings"]}),(0,r.jsx)(Z.mB,{})]}),(0,r.jsxs)(Z._2,{onClick:async()=>{await c(),u.push("/login")},children:[(0,r.jsx)($.A,{className:"mr-2 h-4 w-4"}),"Log out"]})]})]})}),t&&(0,r.jsx)(er,{open:t,onOpenChange:a,role:(null==l?void 0:l.role)||""})]})}function ei(e){let{children:t,role:a}=e,o=c(),[n,i]=(0,s.useState)(!o);return(0,r.jsx)(A,{defaultOpen:!o,children:(0,r.jsxs)("div",{className:"flex min-h-screen w-full bg-background",children:[(0,r.jsx)(q,{role:a}),(0,r.jsxs)("div",{className:"flex flex-1 flex-col w-full",children:[(0,r.jsx)(en,{}),(0,r.jsx)("main",{className:"flex-1 p-6",children:t})]})]})})}},62523:(e,t,a)=>{a.d(t,{p:()=>o});var r=a(95155);a(12115);var s=a(59434);function o(e){let{className:t,type:a,...o}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},68856:(e,t,a)=>{a.d(t,{E:()=>o});var r=a(95155),s=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,s.cn)("bg-accent animate-pulse rounded-md",t),...a})}},71987:(e,t,a)=>{a.d(t,{g:()=>r});var r=function(e){return e.SUPER_ADMIN="superAdmin",e.COLLEGE_ADMIN="collegeAdmin",e.TEACHER="teacher",e}({})},91394:(e,t,a)=>{a.d(t,{BK:()=>i,eu:()=>n,q5:()=>l});var r=a(95155);a(12115);var s=a(85977),o=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)(s._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}}}]);