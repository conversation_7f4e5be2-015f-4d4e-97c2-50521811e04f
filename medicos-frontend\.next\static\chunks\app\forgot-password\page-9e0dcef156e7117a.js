(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162,3954],{17759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>v,MJ:()=>x,Rr:()=>p,eI:()=>g,lR:()=>f,lV:()=>c,zB:()=>u});var o=r(95155),a=r(12115),s=r(66634),n=r(62177),i=r(59434),l=r(85057);let c=n.Op,d=a.createContext({}),u=e=>{let{...t}=e;return(0,o.jsx)(d.Provider,{value:{name:t.name},children:(0,o.jsx)(n.xI,{...t})})},m=()=>{let e=a.useContext(d),t=a.useContext(h),{getFieldState:r}=(0,n.xW)(),o=(0,n.lN)({name:e.name}),s=r(e.name,o);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...s}},h=a.createContext({});function g(e){let{className:t,...r}=e,s=a.useId();return(0,o.jsx)(h.Provider,{value:{id:s},children:(0,o.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...r})})}function f(e){let{className:t,...r}=e,{error:a,formItemId:s}=m();return(0,o.jsx)(l.J,{"data-slot":"form-label","data-error":!!a,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...r})}function x(e){let{...t}=e,{error:r,formItemId:a,formDescriptionId:n,formMessageId:i}=m();return(0,o.jsx)(s.Slot,{"data-slot":"form-control",id:a,"aria-describedby":r?"".concat(n," ").concat(i):"".concat(n),"aria-invalid":!!r,...t})}function p(e){let{className:t,...r}=e,{formDescriptionId:a}=m();return(0,o.jsx)("p",{"data-slot":"form-description",id:a,className:(0,i.cn)("text-muted-foreground text-sm",t),...r})}function v(e){var t;let{className:r,...a}=e,{error:s,formMessageId:n}=m(),l=s?String(null!==(t=null==s?void 0:s.message)&&void 0!==t?t:""):a.children;return l?(0,o.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-destructive text-sm",r),...a,children:l}):null}},25731:(e,t,r)=>{"use strict";async function o(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function a(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let r=await t.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function s(e,t){try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let o=await r.json();if(!o||!o.accessToken||!o.user||!o.user.role)throw Error("Invalid response format from server");return o}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),o=localStorage.getItem("firebaseToken"),a=localStorage.getItem("backendToken"),s={"Content-Type":"application/json",...a?{Authorization:"Bearer ".concat(a)}:o?{Authorization:"Bearer ".concat(o)}:{},...t.headers};try{let e=await fetch(r,{...t,headers:s});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let o=e.headers.get("content-type");if(o&&o.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}r.d(t,{K8:()=>a,V7:()=>o,Xw:()=>s,apiCall:()=>n})},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>i});var o=r(95155);r(12115);var a=r(66634),s=r(74466),n=r(59434);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...c}=e,d=l?a.Slot:"button";return(0,o.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:s,className:t})),...c})}},42864:(e,t,r)=>{Promise.resolve().then(r.bind(r,69028))},51790:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>u,A:()=>m,t:()=>h});var o=r(95155),a=r(12115),s=r(16203),n=r(23915);let i=0===(0,n.Dk)().length?(0,n.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,n.Dk)()[0],l=(0,s.xI)(i);var c=r(25731);let d=(0,a.createContext)(void 0),u=e=>{let{children:t}=e,[r,n]=(0,a.useState)(null),[i,u]=(0,a.useState)(null),[m,h]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,s.hg)(l,async e=>{if(n(e),e){let t=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",t),t)u(t);else try{console.log("No role in localStorage, trying to get from backend");let t=await e.getIdToken();localStorage.setItem("firebaseToken",t);let r=await (0,c.K8)();r&&r.user&&r.user.role&&(console.log("Got role from backend:",r.user.role),localStorage.setItem("userRole",r.user.role),u(r.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else u(null);h(!1)});return()=>e()},[]);let g=async(e,t,r)=>{try{let o=await (0,s.eJ)(l,e,t);o.user&&(await (0,s.r7)(o.user,{displayName:r}),await (0,c.V7)(o.user))}catch(e){throw console.error("Error signing up:",e),e}},f=async(e,t)=>{try{let r=await (0,s.x9)(l,e,t);await (0,c.V7)(r.user)}catch(e){throw console.error("Error logging in:",e),e}},x=async()=>{try{let e=new s.HF,t=await (0,s.df)(l,e);await (0,c.V7)(t.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},p=async()=>{try{await (0,s.CI)(l),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},v=async e=>{try{await (0,s.J1)(l,e);try{await fetch("".concat("http://localhost:3000/api","/auth/reset-password-request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},w=async()=>{try{if(!r)throw Error("No authenticated user found");await (0,c.V7)(r);try{let e=await (0,c.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},b=async()=>{try{let e=l.currentUser;e&&(await (0,s.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,o.jsx)(d.Provider,{value:{user:r,userRole:i,loading:m,signUp:g,login:f,loginWithGoogle:x,logout:p,resetPassword:v,setUserRole:e=>{localStorage.setItem("userRole",e),u(e)},handlePasswordResetCompletion:w,deleteAccount:b},children:t})};function m(){let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h(){return(0,a.useContext)(d)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},59434:(e,t,r)=>{"use strict";r.d(t,{b:()=>n,cn:()=>s});var o=r(52596),a=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}function n(e){return new Promise((t,r)=>{let o=new FileReader;o.readAsDataURL(e),o.onload=()=>t(o.result),o.onerror=e=>r(e)})}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var o=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,o.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},69028:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var o=r(95155),a=r(12115),s=r(6874),n=r.n(s),i=r(90221),l=r(62177),c=r(55594),d=r(56671),u=r(30285),m=r(17759),h=r(62523),g=r(51790);let f=c.Ik({email:c.Yj().email({message:"Please enter a valid email address."})});function x(){let{resetPassword:e}=(0,g.A)(),[t,r]=(0,a.useState)(!1),[s,c]=(0,a.useState)(!1),x=(0,l.mN)({resolver:(0,i.u)(f),defaultValues:{email:""}});async function p(t){r(!0);try{await e(t.email),c(!0),d.oR.success("Password reset email sent!")}catch(e){console.error("Password reset error:",e),d.oR.error(e.message||"Failed to send reset email. Please try again.")}finally{r(!1)}}return(0,o.jsxs)("div",{className:"flex min-h-screen",children:[(0,o.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center p-8 bg-white",children:(0,o.jsxs)("div",{className:"w-full max-w-md",children:[(0,o.jsx)("div",{className:"mb-8",children:(0,o.jsxs)("div",{className:"flex items-center",children:[(0,o.jsx)("img",{src:"/logo.svg",alt:"MEDICOS",className:"h-12 w-auto"}),(0,o.jsx)("h1",{className:"ml-2 text-xl font-bold text-gray-900",children:"MEDICOS"})]})}),(0,o.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Reset Password"}),(0,o.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Enter your email address and we'll send you a link to reset your password."}),s?(0,o.jsx)("div",{className:"rounded-md bg-green-50 p-6 border border-green-200",children:(0,o.jsxs)("div",{className:"flex",children:[(0,o.jsx)("div",{className:"flex-shrink-0",children:(0,o.jsx)("svg",{className:"h-6 w-6 text-green-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,o.jsxs)("div",{className:"ml-3",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-green-800",children:"Reset email sent"}),(0,o.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,o.jsx)("p",{children:"Check your email for a link to reset your password. If it doesn't appear within a few minutes, check your spam folder."})}),(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)(n(),{href:"/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Return to login"})})]})]})}):(0,o.jsx)(m.lV,{...x,children:(0,o.jsxs)("form",{onSubmit:x.handleSubmit(p),className:"space-y-6",children:[(0,o.jsx)(m.zB,{control:x.control,name:"email",render:e=>{let{field:t}=e;return(0,o.jsxs)(m.eI,{children:[(0,o.jsx)(m.lR,{className:"text-sm font-medium text-gray-700",children:"Email Address*"}),(0,o.jsx)(m.MJ,{children:(0,o.jsx)(h.p,{placeholder:"<EMAIL>",className:"w-full rounded-md border border-gray-300 py-2 px-3",...t})}),(0,o.jsx)(m.C5,{})]})}}),(0,o.jsx)(u.$,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md",disabled:t,children:t?"Sending...":"Send Reset Link"}),(0,o.jsx)("div",{className:"text-center mt-4",children:(0,o.jsx)(n(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Back to login"})})]})})]})}),(0,o.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-green-800 items-center justify-center p-12 relative",children:[(0,o.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-10"}),(0,o.jsx)("div",{className:"relative z-10 text-center max-w-md",children:(0,o.jsxs)("blockquote",{className:"text-white text-xl font-medium",children:['"Education is the most powerful weapon which you can use to change the world."',(0,o.jsx)("footer",{className:"mt-2 text-white text-opacity-80",children:"– Nelson Mandela"})]})})]})]})}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var o=r(95155);r(12115);var a=r(40968),s=r(59434);function n(e){let{className:t,...r}=e;return(0,o.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7146,4277,6874,6671,685,7117,221,4030,8441,1684,7358],()=>t(42864)),_N_E=e.O()}]);