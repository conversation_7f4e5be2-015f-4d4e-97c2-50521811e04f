"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1141],{6101:(t,e,n)=>{n.d(e,{s:()=>l,t:()=>o});var r=n(12115);function i(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function o(...t){return e=>{let n=!1,r=t.map(t=>{let r=i(t,e);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let e=0;e<r.length;e++){let n=r[e];"function"==typeof n?n():i(t[e],null)}}}}function l(...t){return r.useCallback(o(...t),t)}},19946:(t,e,n)=>{n.d(e,{A:()=>c});var r=n(12115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,n)=>n?n.toUpperCase():e.toLowerCase()),l=t=>{let e=o(t);return e.charAt(0).toUpperCase()+e.slice(1)},f=function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.filter((t,e,n)=>!!t&&""!==t.trim()&&n.indexOf(t)===e).join(" ").trim()};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,r.forwardRef)((t,e)=>{let{color:n="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:a="",children:c,iconNode:s,...d}=t;return(0,r.createElement)("svg",{ref:e,...u,width:i,height:i,stroke:n,strokeWidth:l?24*Number(o)/Number(i):o,className:f("lucide",a),...d},[...s.map(t=>{let[e,n]=t;return(0,r.createElement)(e,n)}),...Array.isArray(c)?c:[c]])}),c=(t,e)=>{let n=(0,r.forwardRef)((n,o)=>{let{className:u,...c}=n;return(0,r.createElement)(a,{ref:o,iconNode:e,className:f("lucide-".concat(i(l(t))),"lucide-".concat(t),u),...c})});return n.displayName=l(t),n}},22475:(t,e,n)=>{n.d(e,{UE:()=>td,ll:()=>tl,rD:()=>th,UU:()=>ta,jD:()=>ts,ER:()=>tp,cY:()=>tf,BN:()=>tu,Ej:()=>tc});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,f=Math.floor,u=t=>({x:t,y:t}),a={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function p(t){return t.split("-")[1]}function h(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}function g(t){return["top","bottom"].includes(d(t))?"y":"x"}function y(t){return t.replace(/start|end/g,t=>c[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>a[t])}function v(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function x(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function b(t,e,n){let r,{reference:i,floating:o}=t,l=g(e),f=h(g(e)),u=m(f),a=d(e),c="y"===l,s=i.x+i.width/2-o.width/2,y=i.y+i.height/2-o.height/2,w=i[u]/2-o[u]/2;switch(a){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:y};break;case"left":r={x:i.x-o.width,y:y};break;default:r={x:i.x,y:i.y}}switch(p(e)){case"start":r[f]-=w*(n&&c?-1:1);break;case"end":r[f]+=w*(n&&c?-1:1)}return r}let R=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(e)),a=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:c,y:s}=b(a,r,u),d=r,p={},h=0;for(let n=0;n<f.length;n++){let{name:o,fn:m}=f[n],{x:g,y:y,data:w,reset:v}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:a,platform:l,elements:{reference:t,floating:e}});c=null!=g?g:c,s=null!=y?y:s,p={...p,[o]:{...p[o],...w}},v&&h<=50&&(h++,"object"==typeof v&&(v.placement&&(d=v.placement),v.rects&&(a=!0===v.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):v.rects),{x:c,y:s}=b(a,d,u)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:p}};async function E(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:f,strategy:u}=t,{boundary:a="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=s(e,t),m=v(h),g=f[p?"floating"===d?"reference":"floating":d],y=x(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:a,rootBoundary:c,strategy:u})),w="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),R=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},E=x(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-E.top+m.top)/R.y,bottom:(E.bottom-y.bottom+m.bottom)/R.y,left:(y.left-E.left+m.left)/R.x,right:(E.right-y.right+m.right)/R.x}}function A(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function L(t){return r.some(e=>t[e]>=0)}async function C(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),f=p(n),u="y"===g(n),a=["left","top"].includes(l)?-1:1,c=o&&u?-1:1,h=s(e,t),{mainAxis:m,crossAxis:y,alignmentAxis:w}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return f&&"number"==typeof w&&(y="end"===f?-1*w:w),u?{x:y*c,y:m*a}:{x:m*a,y:y*c}}function T(){return"undefined"!=typeof window}function O(t){return S(t)?(t.nodeName||"").toLowerCase():"#document"}function k(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function D(t){var e;return null==(e=(S(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function S(t){return!!T()&&(t instanceof Node||t instanceof k(t).Node)}function P(t){return!!T()&&(t instanceof Element||t instanceof k(t).Element)}function N(t){return!!T()&&(t instanceof HTMLElement||t instanceof k(t).HTMLElement)}function j(t){return!!T()&&"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof k(t).ShadowRoot)}function W(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=M(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function F(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function H(t){let e=B(),n=P(t)?M(t):t;return["transform","translate","scale","rotate","perspective"].some(t=>!!n[t]&&"none"!==n[t])||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function B(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(t){return["html","body","#document"].includes(O(t))}function M(t){return k(t).getComputedStyle(t)}function U(t){return P(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function _(t){if("html"===O(t))return t;let e=t.assignedSlot||t.parentNode||j(t)&&t.host||D(t);return j(e)?e.host:e}function $(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=_(e);return V(n)?e.ownerDocument?e.ownerDocument.body:e.body:N(n)&&W(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=k(i);if(o){let t=z(l);return e.concat(l,l.visualViewport||[],W(i)?i:[],t&&n?$(t):[])}return e.concat(i,$(i,[],n))}function z(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function I(t){let e=M(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=N(t),o=i?t.offsetWidth:n,f=i?t.offsetHeight:r,u=l(n)!==o||l(r)!==f;return u&&(n=o,r=f),{width:n,height:r,$:u}}function Y(t){return P(t)?t:t.contextElement}function Z(t){let e=Y(t);if(!N(e))return u(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=I(e),f=(o?l(n.width):n.width)/r,a=(o?l(n.height):n.height)/i;return f&&Number.isFinite(f)||(f=1),a&&Number.isFinite(a)||(a=1),{x:f,y:a}}let q=u(0);function G(t){let e=k(t);return B()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:q}function X(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=Y(t),f=u(1);e&&(r?P(r)&&(f=Z(r)):f=Z(t));let a=(void 0===(i=n)&&(i=!1),r&&(!i||r===k(l))&&i)?G(l):u(0),c=(o.left+a.x)/f.x,s=(o.top+a.y)/f.y,d=o.width/f.x,p=o.height/f.y;if(l){let t=k(l),e=r&&P(r)?k(r):r,n=t,i=z(n);for(;i&&r&&e!==n;){let t=Z(i),e=i.getBoundingClientRect(),r=M(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;c*=t.x,s*=t.y,d*=t.x,p*=t.y,c+=o,s+=l,i=z(n=k(i))}}return x({width:d,height:p,x:c,y:s})}function J(t,e){let n=U(t).scrollLeft;return e?e.left+n:X(D(t)).left+n}function K(t,e,n){void 0===n&&(n=!1);let r=t.getBoundingClientRect();return{x:r.left+e.scrollLeft-(n?0:J(t,r)),y:r.top+e.scrollTop}}function Q(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=k(t),r=D(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,f=0,u=0;if(i){o=i.width,l=i.height;let t=B();(!t||t&&"fixed"===e)&&(f=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:f,y:u}}(t,n);else if("document"===e)r=function(t){let e=D(t),n=U(t),r=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),l=o(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),f=-n.scrollLeft+J(t),u=-n.scrollTop;return"rtl"===M(r).direction&&(f+=o(e.clientWidth,r.clientWidth)-i),{width:i,height:l,x:f,y:u}}(D(t));else if(P(e))r=function(t,e){let n=X(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=N(t)?Z(t):u(1),l=t.clientWidth*o.x,f=t.clientHeight*o.y;return{width:l,height:f,x:i*o.x,y:r*o.y}}(e,n);else{let n=G(t);r={x:e.x-n.x,y:e.y-n.y,width:e.width,height:e.height}}return x(r)}function tt(t){return"static"===M(t).position}function te(t,e){if(!N(t)||"fixed"===M(t).position)return null;if(e)return e(t);let n=t.offsetParent;return D(t)===n&&(n=n.ownerDocument.body),n}function tn(t,e){let n=k(t);if(F(t))return n;if(!N(t)){let e=_(t);for(;e&&!V(e);){if(P(e)&&!tt(e))return e;e=_(e)}return n}let r=te(t,e);for(;r&&["table","td","th"].includes(O(r))&&tt(r);)r=te(r,e);return r&&V(r)&&tt(r)&&!H(r)?n:r||function(t){let e=_(t);for(;N(e)&&!V(e);){if(H(e))return e;if(F(e))break;e=_(e)}return null}(t)||n}let tr=async function(t){let e=this.getOffsetParent||tn,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=N(e),i=D(e),o="fixed"===n,l=X(t,!0,o,e),f={scrollLeft:0,scrollTop:0},a=u(0);if(r||!r&&!o){if(("body"!==O(e)||W(i))&&(f=U(e)),r){let t=X(e,!0,o,e);a.x=t.x+e.clientLeft,a.y=t.y+e.clientTop}else i&&(a.x=J(i))}let c=!i||r||o?u(0):K(i,f);return{x:l.left+f.scrollLeft-a.x-c.x,y:l.top+f.scrollTop-a.y-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ti={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=D(r),f=!!e&&F(e.floating);if(r===l||f&&o)return n;let a={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=N(r);if((d||!d&&!o)&&(("body"!==O(r)||W(l))&&(a=U(r)),N(r))){let t=X(r);c=Z(r),s.x=t.x+r.clientLeft,s.y=t.y+r.clientTop}let p=!l||d||o?u(0):K(l,a,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-a.scrollTop*c.y+s.y+p.y}},getDocumentElement:D,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t,f=[..."clippingAncestors"===n?F(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=$(t,[],!1).filter(t=>P(t)&&"body"!==O(t)),i=null,o="fixed"===M(t).position,l=o?_(t):t;for(;P(l)&&!V(l);){let e=M(l),n=H(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&["absolute","fixed"].includes(i.position)||W(l)&&!n&&function t(e,n){let r=_(e);return!(r===n||!P(r)||V(r))&&("fixed"===M(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=_(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],u=f[0],a=f.reduce((t,n)=>{let r=Q(e,n,l);return t.top=o(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=o(r.left,t.left),t},Q(e,u,l));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:tn,getElementRects:tr,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=I(t);return{width:e,height:n}},getScale:Z,isElement:P,isRTL:function(t){return"rtl"===M(t).direction}};function to(t,e){return t.x===e.x&&t.y===e.y&&t.width===e.width&&t.height===e.height}function tl(t,e,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:a=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=Y(t),h=u||a?[...p?$(p):[],...$(e)]:[];h.forEach(t=>{u&&t.addEventListener("scroll",n,{passive:!0}),a&&t.addEventListener("resize",n)});let m=p&&s?function(t,e){let n,r=null,l=D(t);function u(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function a(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),u();let d=t.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||e(),!m||!g)return;let y=f(h),w=f(l.clientWidth-(p+m)),v={rootMargin:-y+"px "+-w+"px "+-f(l.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:o(0,i(1,s))||1},x=!0;function b(e){let r=e[0].intersectionRatio;if(r!==s){if(!x)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||to(d,t.getBoundingClientRect())||a(),x=!1}try{r=new IntersectionObserver(b,{...v,root:l.ownerDocument})}catch(t){r=new IntersectionObserver(b,v)}r.observe(t)}(!0),u}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===p&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),p&&!d&&y.observe(p),y.observe(e));let w=d?X(t):null;return d&&function e(){let r=X(t);w&&!to(w,r)&&n(),w=r,l=requestAnimationFrame(e)}(),n(),()=>{var t;h.forEach(t=>{u&&t.removeEventListener("scroll",n),a&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(l)}}let tf=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:f}=e,u=await C(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(r=f.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}},tu=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:l}=e,{mainAxis:f=!0,crossAxis:u=!1,limiter:a={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...c}=s(t,e),p={x:n,y:r},m=await E(e,c),y=g(d(l)),w=h(y),v=p[w],x=p[y];if(f){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=v+m[t],r=v-m[e];v=o(n,i(v,r))}if(u){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=x+m[t],r=x-m[e];x=o(n,i(x,r))}let b=a.fn({...e,[w]:v,[y]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[w]:f,[y]:u}}}}}},ta=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:f,middlewareData:u,rects:a,initialPlacement:c,platform:v,elements:x}=e,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:L="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:T=!0,...O}=s(t,e);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=d(f),D=g(c),S=d(c)===c,P=await (null==v.isRTL?void 0:v.isRTL(x.floating)),N=A||(S||!T?[w(c)]:function(t){let e=w(t);return[y(t),e,y(e)]}(c)),j="none"!==C;!A&&j&&N.push(...function(t,e,n,r){let i=p(t),o=function(t,e,n){let r=["left","right"],i=["right","left"];switch(t){case"top":case"bottom":if(n)return e?i:r;return e?r:i;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(d(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(y)))),o}(c,T,C,P));let W=[c,...N],F=await E(e,O),H=[],B=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&H.push(F[k]),R){let t=function(t,e,n){void 0===n&&(n=!1);let r=p(t),i=h(g(t)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(f,a,P);H.push(F[t[0]],F[t[1]])}if(B=[...B,{placement:f,overflows:H}],!H.every(t=>t<=0)){let t=((null==(i=u.flip)?void 0:i.index)||0)+1,e=W[t];if(e)return{data:{index:t,overflows:B},reset:{placement:e}};let n=null==(o=B.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(L){case"bestFit":{let t=null==(l=B.filter(t=>{if(j){let e=g(t.placement);return e===D||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=c}if(f!==n)return{reset:{placement:n}}}return{}}}},tc=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){var n,r;let l,f;let{placement:u,rects:a,platform:c,elements:h}=e,{apply:m=()=>{},...y}=s(t,e),w=await E(e,y),v=d(u),x=p(u),b="y"===g(u),{width:R,height:A}=a.floating;"top"===v||"bottom"===v?(l=v,f=x===(await (null==c.isRTL?void 0:c.isRTL(h.floating))?"start":"end")?"left":"right"):(f=v,l="end"===x?"top":"bottom");let L=A-w.top-w.bottom,C=R-w.left-w.right,T=i(A-w[l],L),O=i(R-w[f],C),k=!e.middlewareData.shift,D=T,S=O;if(null!=(n=e.middlewareData.shift)&&n.enabled.x&&(S=C),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(D=L),k&&!x){let t=o(w.left,0),e=o(w.right,0),n=o(w.top,0),r=o(w.bottom,0);b?S=R-2*(0!==t||0!==e?t+e:o(w.left,w.right)):D=A-2*(0!==n||0!==r?n+r:o(w.top,w.bottom))}await m({...e,availableWidth:S,availableHeight:D});let P=await c.getDimensions(h.floating);return R!==P.width||A!==P.height?{reset:{rects:!0}}:{}}}},ts=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=s(t,e);switch(r){case"referenceHidden":{let t=A(await E(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:L(t)}}}case"escaped":{let t=A(await E(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:L(t)}}}default:return{}}}}},td=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:l,rects:f,platform:u,elements:a,middlewareData:c}=e,{element:d,padding:y=0}=s(t,e)||{};if(null==d)return{};let w=v(y),x={x:n,y:r},b=h(g(l)),R=m(b),E=await u.getDimensions(d),A="y"===b,L=A?"clientHeight":"clientWidth",C=f.reference[R]+f.reference[b]-x[b]-f.floating[R],T=x[b]-f.reference[b],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),k=O?O[L]:0;k&&await (null==u.isElement?void 0:u.isElement(O))||(k=a.floating[L]||f.floating[R]);let D=k/2-E[R]/2-1,S=i(w[A?"top":"left"],D),P=i(w[A?"bottom":"right"],D),N=k-E[R]-P,j=k/2-E[R]/2+(C/2-T/2),W=o(S,i(j,N)),F=!c.arrow&&null!=p(l)&&j!==W&&f.reference[R]/2-(j<S?S:P)-E[R]/2<0,H=F?j<S?j-S:j-N:0;return{[b]:x[b]+H,data:{[b]:W,centerOffset:j-W-H,...F&&{alignmentOffset:H}},reset:F}}}),tp=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:u=!0,crossAxis:a=!0}=s(t,e),c={x:n,y:r},p=g(i),m=h(p),y=c[m],w=c[p],v=s(f,e),x="number"==typeof v?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(u){let t="y"===m?"height":"width",e=o.reference[m]-o.floating[t]+x.mainAxis,n=o.reference[m]+o.reference[t]-x.mainAxis;y<e?y=e:y>n&&(y=n)}if(a){var b,R;let t="y"===m?"width":"height",e=["top","left"].includes(d(i)),n=o.reference[p]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[p])||0)+(e?0:x.crossAxis),r=o.reference[p]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[p])||0)-(e?x.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[m]:y,[p]:w}}}},th=(t,e,n)=>{let r=new Map,i={platform:ti,...n},o={...i.platform,_c:r};return R(t,e,{...i,platform:o})}},52712:(t,e,n)=>{n.d(e,{N:()=>i});var r=n(12115),i=globalThis?.document?r.useLayoutEffect:()=>{}},61285:(t,e,n)=>{n.d(e,{B:()=>u});var r,i=n(12115),o=n(52712),l=(r||(r=n.t(i,2)))["useId".toString()]||(()=>void 0),f=0;function u(t){let[e,n]=i.useState(l());return(0,o.N)(()=>{t||n(t=>t??String(f++))},[t]),t||(e?`radix-${e}`:"")}},63540:(t,e,n)=>{n.d(e,{sG:()=>s,hO:()=>d});var r=n(12115),i=n(47650),o=n(6101),l=n(95155),f=r.forwardRef((t,e)=>{let{children:n,...i}=t,o=r.Children.toArray(n),f=o.find(c);if(f){let t=f.props.children,n=o.map(e=>e!==f?e:r.Children.count(t)>1?r.Children.only(null):r.isValidElement(t)?t.props.children:null);return(0,l.jsx)(u,{...i,ref:e,children:r.isValidElement(t)?r.cloneElement(t,void 0,n):null})}return(0,l.jsx)(u,{...i,ref:e,children:n})});f.displayName="Slot";var u=r.forwardRef((t,e)=>{let{children:n,...i}=t;if(r.isValidElement(n)){let t=function(t){let e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,n=e&&"isReactWarning"in e&&e.isReactWarning;return n?t.ref:(n=(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?t.props.ref:t.props.ref||t.ref}(n),l=function(t,e){let n={...e};for(let r in e){let i=t[r],o=e[r];/^on[A-Z]/.test(r)?i&&o?n[r]=(...t)=>{o(...t),i(...t)}:i&&(n[r]=i):"style"===r?n[r]={...i,...o}:"className"===r&&(n[r]=[i,o].filter(Boolean).join(" "))}return{...t,...n}}(i,n.props);return n.type!==r.Fragment&&(l.ref=e?(0,o.t)(e,t):t),r.cloneElement(n,l)}return r.Children.count(n)>1?r.Children.only(null):null});u.displayName="SlotClone";var a=({children:t})=>(0,l.jsx)(l.Fragment,{children:t});function c(t){return r.isValidElement(t)&&t.type===a}var s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((t,e)=>{let n=r.forwardRef((t,n)=>{let{asChild:r,...i}=t,o=r?f:e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o,{...i,ref:n})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function d(t,e){t&&i.flushSync(()=>t.dispatchEvent(e))}},84945:(t,e,n)=>{n.d(e,{BN:()=>h,ER:()=>m,Ej:()=>y,UE:()=>v,UU:()=>g,cY:()=>p,jD:()=>w,we:()=>s});var r=n(22475),i=n(12115),o=n(47650),l="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function f(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!f(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!f(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function u(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function a(t,e){let n=u(t);return Math.round(e*n)/n}function c(t){let e=i.useRef(t);return l(()=>{e.current=t}),e}function s(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:p,floating:h}={},transform:m=!0,whileElementsMounted:g,open:y}=t,[w,v]=i.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[x,b]=i.useState(s);f(x,s)||b(s);let[R,E]=i.useState(null),[A,L]=i.useState(null),C=i.useCallback(t=>{t!==D.current&&(D.current=t,E(t))},[]),T=i.useCallback(t=>{t!==S.current&&(S.current=t,L(t))},[]),O=p||R,k=h||A,D=i.useRef(null),S=i.useRef(null),P=i.useRef(w),N=null!=g,j=c(g),W=c(d),F=c(y),H=i.useCallback(()=>{if(!D.current||!S.current)return;let t={placement:e,strategy:n,middleware:x};W.current&&(t.platform=W.current),(0,r.rD)(D.current,S.current,t).then(t=>{let e={...t,isPositioned:!1!==F.current};B.current&&!f(P.current,e)&&(P.current=e,o.flushSync(()=>{v(e)}))})},[x,e,n,W,F]);l(()=>{!1===y&&P.current.isPositioned&&(P.current.isPositioned=!1,v(t=>({...t,isPositioned:!1})))},[y]);let B=i.useRef(!1);l(()=>(B.current=!0,()=>{B.current=!1}),[]),l(()=>{if(O&&(D.current=O),k&&(S.current=k),O&&k){if(j.current)return j.current(O,k,H);H()}},[O,k,H,j,N]);let V=i.useMemo(()=>({reference:D,floating:S,setReference:C,setFloating:T}),[C,T]),M=i.useMemo(()=>({reference:O,floating:k}),[O,k]),U=i.useMemo(()=>{let t={position:n,left:0,top:0};if(!M.floating)return t;let e=a(M.floating,w.x),r=a(M.floating,w.y);return m?{...t,transform:"translate("+e+"px, "+r+"px)",...u(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,m,M.floating,w.x,w.y]);return i.useMemo(()=>({...w,update:H,refs:V,elements:M,floatingStyles:U}),[w,H,V,M,U])}let d=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:i}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.UE)({element:n.current,padding:i}).fn(e):{}:n?(0,r.UE)({element:n,padding:i}).fn(e):{}}}),p=(t,e)=>({...(0,r.cY)(t),options:[t,e]}),h=(t,e)=>({...(0,r.BN)(t),options:[t,e]}),m=(t,e)=>({...(0,r.ER)(t),options:[t,e]}),g=(t,e)=>({...(0,r.UU)(t),options:[t,e]}),y=(t,e)=>({...(0,r.Ej)(t),options:[t,e]}),w=(t,e)=>({...(0,r.jD)(t),options:[t,e]}),v=(t,e)=>({...d(t),options:[t,e]})}}]);