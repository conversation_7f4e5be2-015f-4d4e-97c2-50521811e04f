(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7759],{2564:(e,t,s)=>{"use strict";s.d(t,{b:()=>n,s:()=>r});var a=s(12115),i=s(63540),l=s(95155),r=a.forwardRef((e,t)=>(0,l.jsx)(i.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));r.displayName="VisuallyHidden";var n=r},5623:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},28831:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(95155),i=s(12115),l=s(12379),r=s(30285),n=s(62523),c=s(85057),d=s(59409),o=s(66695),u=s(26126),m=s(51154),p=s(84616),h=s(19946);let x=(0,h.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var j=s(54416);let v=(0,h.A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);var f=s(62525),g=s(56671),y=s(50405),b=s(16640);function N(e){let{title:t="Subject & Topic Manager",description:s="Manage subjects and topics in a two-level hierarchy",onDataChange:l,initialSubjects:h=[],initialTopics:N=[]}=e,[w,A]=(0,i.useState)(h),[S,k]=(0,i.useState)(N),[E,C]=(0,i.useState)(""),[z,R]=(0,i.useState)(""),[_,F]=(0,i.useState)(""),[T,M]=(0,i.useState)(""),[O,P]=(0,i.useState)(""),[V,I]=(0,i.useState)(null),[$,D]=(0,i.useState)(null),[W,Z]=(0,i.useState)(""),[L,B]=(0,i.useState)(""),[H,J]=(0,i.useState)(""),[K,q]=(0,i.useState)(""),[G,Q]=(0,i.useState)(!1),[U,Y]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=async()=>{try{Q(!0);let[e,t]=await Promise.all([(0,y.Nj)(),(0,b.Kd)()]),s=e.map(e=>({id:e._id,name:e.name,description:e.description})),a=t.map(e=>{var t;let s="string"==typeof e.subjectId?e.subjectId:(null===(t=e.subjectId)||void 0===t?void 0:t._id)||"";return{id:e._id,name:e.name,subjectId:s,description:e.description}});A(s),k(a),s.length>0&&!E&&C(s[0].id)}catch(e){console.error("Error fetching data:",e),(0,g.oR)({title:"Error",description:"Failed to load subjects and topics",variant:"destructive"})}finally{Q(!1)}};0===h.length&&0===N.length?e():Q(!1)},[]),(0,i.useEffect)(()=>{l&&l(w,S)},[w,S,l]);let X=async()=>{if(!z.trim()){(0,g.oR)({title:"Error",description:"Subject name cannot be empty",variant:"destructive"});return}try{Y(!0);let e={name:z.trim(),description:_.trim()||void 0},t=await (0,y.Fb)(e),s={id:t._id,name:t.name,description:t.description};A(e=>[...e,s]),R(""),F(""),(0,g.oR)({title:"Success",description:"Subject added successfully"})}catch(e){console.error("Error adding subject:",e),(0,g.oR)({title:"Error",description:e.message||"Failed to add subject",variant:"destructive"})}finally{Y(!1)}},ee=async e=>{if(!W.trim()){(0,g.oR)({title:"Error",description:"Subject name cannot be empty",variant:"destructive"});return}try{Y(!0);let t={name:W.trim(),description:L.trim()||void 0};await (0,y.iQ)(e,t),A(t=>t.map(t=>t.id===e?{...t,name:W.trim(),description:L.trim()||void 0}:t)),I(null),Z(""),B(""),(0,g.oR)({title:"Success",description:"Subject updated successfully"})}catch(e){console.error("Error updating subject:",e),(0,g.oR)({title:"Error",description:e.message||"Failed to update subject",variant:"destructive"})}finally{Y(!1)}},et=async e=>{try{Y(!0),await (0,y.gg)(e),A(t=>t.filter(t=>t.id!==e)),setChapters(t=>t.filter(t=>t.subjectId!==e)),E===e&&C(""),(0,g.oR)({title:"Success",description:"Subject deleted successfully"})}catch(e){console.error("Error deleting subject:",e),(0,g.oR)({title:"Error",description:e.message||"Failed to delete subject",variant:"destructive"})}finally{Y(!1)}},es=e=>{I(e.id),Z(e.name),B(e.description||"")},ea=()=>{I(null),Z(""),B("")},ei=e=>S.filter(t=>t.subjectId===e),el=w.find(e=>e.id===E),er=ei(E),en=async()=>{if(T.trim()&&E)try{Y(!0);let e={name:T.trim(),subjectId:E,description:O.trim()||void 0},t=await (0,b.OY)(e),s={id:t._id,name:t.name,subjectId:E,description:t.description};k(e=>[...e,s]),M(""),P(""),(0,g.oR)({title:"Success",description:"Topic added successfully"})}catch(e){console.error("Error adding topic:",e),(0,g.oR)({title:"Error",description:e.message||"Failed to add topic",variant:"destructive"})}finally{Y(!1)}},ec=async e=>{if(H.trim())try{Y(!0);let t={name:H.trim(),description:K.trim()||void 0},s=await (0,b.Tw)(e,t);k(t=>t.map(t=>t.id===e?{...t,name:s.name,description:s.description}:t)),D(null),J(""),q(""),(0,g.oR)({title:"Success",description:"Topic updated successfully"})}catch(e){console.error("Error updating topic:",e),(0,g.oR)({title:"Error",description:e.message||"Failed to update topic",variant:"destructive"})}finally{Y(!1)}},ed=async e=>{try{Y(!0),await (0,b.do)(e),k(t=>t.filter(t=>t.id!==e)),(0,g.oR)({title:"Success",description:"Topic deleted successfully"})}catch(e){console.error("Error deleting topic:",e),(0,g.oR)({title:"Error",description:e.message||"Failed to delete topic",variant:"destructive"})}finally{Y(!1)}},eo=e=>{D(e.id),J(e.name),q(e.description||"")},eu=()=>{D(null),J(""),q("")};return G?(0,a.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading subjects, chapters, and topics..."})]}):(0,a.jsxs)("div",{className:"w-full max-w-6xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:t}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:s})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),"Subjects"]}),(0,a.jsx)(o.BT,{children:"Add and manage subjects"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"new-subject-name",children:"Subject Name"}),(0,a.jsx)(n.p,{id:"new-subject-name",placeholder:"Enter subject name",value:z,onChange:e=>R(e.target.value),onKeyPress:e=>"Enter"===e.key&&X()})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"new-subject-description",children:"Description (Optional)"}),(0,a.jsx)(n.p,{id:"new-subject-description",placeholder:"Enter subject description",value:_,onChange:e=>F(e.target.value),onKeyPress:e=>"Enter"===e.key&&X()})]}),(0,a.jsx)(r.$,{onClick:X,className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:U,children:U?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Add Subject"]})})]}),(0,a.jsxs)("div",{className:"space-y-2 mt-6",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["All Subjects (",w.length,")"]}),0===w.length?(0,a.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:"No subjects added yet"}):(0,a.jsx)("div",{className:"space-y-2 max-h-[300px] overflow-y-auto pr-2",children:w.map(e=>(0,a.jsx)("div",{className:"p-3 border rounded-md cursor-pointer transition-colors ".concat(E===e.id?"border-primary bg-primary/5":"border-border hover:bg-muted/50"),onClick:()=>C(e.id),children:V===e.id?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.p,{value:W,onChange:e=>Z(e.target.value),placeholder:"Subject name",className:"mb-2"}),(0,a.jsx)(n.p,{value:L,onChange:e=>B(e.target.value),placeholder:"Description (optional)",className:"mb-2"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(r.$,{size:"sm",onClick:()=>ee(e.id),className:"flex-1",disabled:U,children:U?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(x,{className:"h-4 w-4"})}),(0,a.jsx)(r.$,{size:"sm",variant:"outline",onClick:ea,className:"flex-1",disabled:U,children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(r.$,{size:"icon",variant:"ghost",onClick:t=>{t.stopPropagation(),es(e)},disabled:U,children:(0,a.jsx)(v,{className:"h-4 w-4"})}),(0,a.jsx)(r.$,{size:"icon",variant:"ghost",onClick:t=>{t.stopPropagation(),et(e.id)},disabled:U,children:(0,a.jsx)(f.A,{className:"h-4 w-4 text-destructive"})})]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description}),(0,a.jsxs)(u.E,{variant:"outline",className:"mt-2",children:[ei(e.id).length," topics"]})]})},e.id))})]})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsxs)(o.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),"Topics"]}),(0,a.jsx)(o.BT,{children:"Manage topics under subjects"})]}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"topic-subject-select",children:"Select Subject"}),(0,a.jsxs)(d.l6,{value:E,onValueChange:C,disabled:0===w.length,children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:0===w.length?"No subjects available":"Select a subject"})}),(0,a.jsx)(d.gC,{children:w.map(e=>(0,a.jsx)(d.eb,{value:e.id,children:e.name},e.id))})]})]}),E&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"new-topic-name",children:"Topic Name"}),(0,a.jsx)(n.p,{id:"new-topic-name",placeholder:"Enter topic name",value:T,onChange:e=>M(e.target.value),onKeyPress:e=>"Enter"===e.key&&en(),disabled:!E})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"new-topic-description",children:"Description (Optional)"}),(0,a.jsx)(n.p,{id:"new-topic-description",placeholder:"Enter topic description",value:O,onChange:e=>P(e.target.value),onKeyPress:e=>"Enter"===e.key&&en(),disabled:!E})]}),(0,a.jsx)(r.$,{onClick:en,className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:!E||U,children:U?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Add Topic"]})})]}),(0,a.jsxs)("div",{className:"space-y-2 mt-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium",children:el?"Topics for ".concat(el.name," (").concat(er.length,")"):"Select a subject to view topics"}),er.length>0?er.map(e=>(0,a.jsx)("div",{className:"p-3 border rounded-lg",children:$===e.id?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.p,{value:H,onChange:e=>J(e.target.value),placeholder:"Topic name",disabled:U}),(0,a.jsx)(n.p,{value:K,onChange:e=>q(e.target.value),placeholder:"Topic description (optional)",disabled:U}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(r.$,{size:"sm",onClick:()=>ec(e.id),className:"flex-1",disabled:U,children:U?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(x,{className:"h-4 w-4"})}),(0,a.jsx)(r.$,{size:"sm",variant:"outline",onClick:eu,className:"flex-1",disabled:U,children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(r.$,{size:"icon",variant:"ghost",onClick:()=>eo(e),disabled:U,children:(0,a.jsx)(v,{className:"h-4 w-4"})}),(0,a.jsx)(r.$,{size:"icon",variant:"ghost",onClick:()=>ed(e.id),disabled:U,children:(0,a.jsx)(f.A,{className:"h-4 w-4 text-destructive"})})]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description})]})},e.id)):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:E?"No topics yet":"Select a subject to manage topics"})]})]})]})]}),w.length>0&&(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{children:"Summary"})}),(0,a.jsx)(o.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:w.map(e=>{let t=ei(e.id);return(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:e.name}),(0,a.jsx)("div",{className:"space-y-2",children:t.length>0?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mb-2",children:[t.length," topics"]}),(0,a.jsx)("div",{className:"space-y-1",children:t.map(e=>(0,a.jsx)(u.E,{variant:"secondary",className:"mr-1 mb-1 text-xs",children:e.name},e.id))})]}):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No topics yet"})})]},e.id)})})})]})]})}let w=()=>(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Add Subjects & Topics"}),(0,a.jsx)(l.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Add Subjects & Topics"}],className:"text-sm mt-1"})]}),(0,a.jsx)("div",{className:"container mx-auto py-10",children:(0,a.jsx)(N,{title:"Add Subjects & Topics",description:"Organize your educational content in a two-level hierarchy",onDataChange:(e,t)=>{console.log("Data updated:",{subjects:e,topics:t})}})})]})})},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>n});var a=s(12115),i=s(63540),l=s(95155),r=a.forwardRef((e,t)=>(0,l.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null===(s=e.onMouseDown)||void 0===s||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));r.displayName="Label";var n=r},51154:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54416:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>o});var a=s(95155);s(12115);var i=s(4011),l=s(66474),r=s(5196),n=s(47863),c=s(59434);function d(e){let{...t}=e;return(0,a.jsx)(i.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,a.jsx)(i.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:r,...n}=e;return(0,a.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[r,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:l="popper",...r}=e;return(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...r,children:[(0,a.jsx)(h,{}),(0,a.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(x,{})]})})}function p(e){let{className:t,children:s,...l}=e;return(0,a.jsxs)(i.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(r.A,{className:"size-4"})})}),(0,a.jsx)(i.p4,{children:s})]})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}function x(e){let{className:t,...s}=e;return(0,a.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}},62525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63730:(e,t,s)=>{Promise.resolve().then(s.bind(s,28831))},66634:(e,t,s)=>{"use strict";s.d(t,{Slot:()=>n,TL:()=>r,Dc:()=>d});var a=s(12115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var l=s(95155);function r(e){let t=function(e){let t=a.forwardRef((e,t)=>{var s,l,r;let n,c;let{children:d,...o}=e,u=function(...e){return a.useCallback(function(...e){return t=>{let s=!1,a=e.map(e=>{let a=i(e,t);return s||"function"!=typeof a||(s=!0),a});if(s)return()=>{for(let t=0;t<a.length;t++){let s=a[t];"function"==typeof s?s():i(e[t],null)}}}}(...e),e)}(a.isValidElement(d)?(c=(n=null===(l=Object.getOwnPropertyDescriptor((s=d).props,"ref"))||void 0===l?void 0:l.get)&&"isReactWarning"in n&&n.isReactWarning)?s.ref:(c=(n=null===(r=Object.getOwnPropertyDescriptor(s,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?s.props.ref:s.props.ref||s.ref:void 0,t);if(a.isValidElement(d)){let e=function(e,t){let s={...t};for(let a in t){let i=e[a],l=t[a];/^on[A-Z]/.test(a)?i&&l?s[a]=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];let a=l(...t);return i(...t),a}:i&&(s[a]=i):"style"===a?s[a]={...i,...l}:"className"===a&&(s[a]=[i,l].filter(Boolean).join(" "))}return{...e,...s}}(o,d.props);return d.type!==a.Fragment&&(e.ref=u),a.cloneElement(d,e)}return a.Children.count(d)>1?a.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),s=a.forwardRef((e,s)=>{let{children:i,...r}=e,n=a.Children.toArray(i),c=n.find(o);if(c){let e=c.props.children,i=n.map(t=>t!==c?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...r,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,i):null})}return(0,l.jsx)(t,{...r,ref:s,children:i})});return s.displayName="".concat(e,".Slot"),s}var n=r("Slot"),c=Symbol("radix.slottable");function d(e){let t=e=>{let{children:t}=e;return(0,l.jsx)(l.Fragment,{children:t})};return t.displayName="".concat(e,".Slottable"),t.__radixId=c,t}function o(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}},74466:(e,t,s)=>{"use strict";s.d(t,{F:()=>r});var a=s(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,r=(e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return l(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:r,defaultVariants:n}=t,c=Object.keys(r).map(e=>{let t=null==s?void 0:s[e],a=null==n?void 0:n[e];if(null===t)return null;let l=i(t)||i(a);return r[e][l]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return l(e,c,null==t?void 0:null===(a=t.compoundVariants)||void 0===a?void 0:a.reduce((e,t)=>{let{class:s,className:a,...i}=t;return Object.entries(i).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...n,...d}[t]):({...n,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},84616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6671,1141,2571,6457,5267,4973,8441,1684,7358],()=>t(63730)),_N_E=e.O()}]);