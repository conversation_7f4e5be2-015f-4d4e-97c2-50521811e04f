(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1857],{19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(12115);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),l=e=>{let t=i(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:u="",children:c,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...s,width:o,height:o,stroke:r,strokeWidth:l?24*Number(i)/Number(o):i,className:a("lucide",u),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:s,...c}=r;return(0,n.createElement)(u,{ref:i,iconNode:t,className:a("lucide-".concat(o(l(e))),"lucide-".concat(e),s),...c})});return r.displayName=l(e),r}},28555:(e,t,r)=>{"use strict";r.d(t,{rc:()=>ex,bm:()=>eT,VY:()=>eg,Kq:()=>ew,bL:()=>eh,hE:()=>eb,LM:()=>eE});var n,o,i=r(12115),l=r.t(i,2),a=r(47650);function s(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function u(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function c(...e){return t=>{let r=!1,n=e.map(e=>{let n=u(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():u(e[t],null)}}}}function d(...e){return i.useCallback(c(...e),e)}var f=r(94971),p=r(95920),v=r(86266),m=r(95155);function y(e,t=[]){let r=[],n=()=>{let t=r.map(e=>i.createContext(e));return function(r){let n=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=i.createContext(n),l=r.length;r=[...r,n];let a=t=>{let{scope:r,children:n,...a}=t,s=r?.[e]?.[l]||o,u=i.useMemo(()=>a,Object.values(a));return(0,m.jsx)(s.Provider,{value:u,children:n})};return a.displayName=t+"Provider",[a,function(r,a){let s=a?.[e]?.[l]||o,u=i.useContext(s);if(u)return u;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}function w(e){let t=function(e){let t=i.forwardRef((e,t)=>{let{children:r,...n}=e;if(i.isValidElement(r)){var o;let e,l;let a=(o=r,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==i.Fragment&&(s.ref=t?c(t,a):a),i.cloneElement(r,s)}return i.Children.count(r)>1?i.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=i.forwardRef((e,r)=>{let{children:n,...o}=e,l=i.Children.toArray(n),a=l.find(h);if(a){let e=a.props.children,n=l.map(t=>t!==a?t:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,m.jsx)(t,{...o,ref:r,children:i.isValidElement(e)?i.cloneElement(e,void 0,n):null})}return(0,m.jsx)(t,{...o,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var E=Symbol("radix.slottable");function h(e){return i.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===E}var b=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=x(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function x(e){return e!=e||0===e?0:Math.trunc(e)}n=new WeakMap;var T=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=w(`Primitive.${t}`),n=i.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,m.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function C(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}function N(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var R="dismissableLayer.update",P=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),O=i.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:c,onInteractOutside:f,onDismiss:p,...v}=e,y=i.useContext(P),[w,E]=i.useState(null),h=null!==(n=null==w?void 0:w.ownerDocument)&&void 0!==n?n:null===(r=globalThis)||void 0===r?void 0:r.document,[,b]=i.useState({}),g=d(t,e=>E(e)),x=Array.from(y.layers),[C]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),O=x.indexOf(C),j=w?x.indexOf(w):-1,D=y.layersWithOutsidePointerEventsDisabled.size>0,A=j>=O,k=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=N(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){S("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...y.branches].some(e=>e.contains(t));!A||r||(null==u||u(e),null==f||f(e),e.defaultPrevented||null==p||p())},h),M=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=N(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&S("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;[...y.branches].some(e=>e.contains(t))||(null==c||c(e),null==f||f(e),e.defaultPrevented||null==p||p())},h);return!function(e,t=globalThis?.document){let r=N(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===y.layers.size-1&&(null==a||a(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},h),i.useEffect(()=>{if(w)return l&&(0===y.layersWithOutsidePointerEventsDisabled.size&&(o=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(w)),y.layers.add(w),L(),()=>{l&&1===y.layersWithOutsidePointerEventsDisabled.size&&(h.body.style.pointerEvents=o)}},[w,h,l,y]),i.useEffect(()=>()=>{w&&(y.layers.delete(w),y.layersWithOutsidePointerEventsDisabled.delete(w),L())},[w,y]),i.useEffect(()=>{let e=()=>b({});return document.addEventListener(R,e),()=>document.removeEventListener(R,e)},[]),(0,m.jsx)(T.div,{...v,ref:g,style:{pointerEvents:D?A?"auto":"none":void 0,...e.style},onFocusCapture:s(e.onFocusCapture,M.onFocusCapture),onBlurCapture:s(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:s(e.onPointerDownCapture,k.onPointerDownCapture)})});O.displayName="DismissableLayer";var j=i.forwardRef((e,t)=>{let r=i.useContext(P),n=i.useRef(null),o=d(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,m.jsx)(T.div,{...e,ref:o})});function L(){let e=new CustomEvent(R);document.dispatchEvent(e)}function S(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?C(i,l):i.dispatchEvent(l)}j.displayName="DismissableLayerBranch";var D=globalThis?.document?i.useLayoutEffect:()=>{},A=i.forwardRef((e,t)=>{var r,n;let{container:o,...l}=e,[s,u]=i.useState(!1);D(()=>u(!0),[]);let c=o||s&&(null===(n=globalThis)||void 0===n?void 0:null===(r=n.document)||void 0===r?void 0:r.body);return c?a.createPortal((0,m.jsx)(T.div,{...l,ref:t}),c):null});A.displayName="Portal";var k=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=i.useState(),l=i.useRef(null),a=i.useRef(e),s=i.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return i.useEffect(()=>{let e=M(l.current);s.current="mounted"===u?e:"none"},[u]),D(()=>{let t=l.current,r=a.current;if(r!==e){let n=s.current,o=M(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),D(()=>{if(n){var e;let t;let r=null!==(e=n.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=M(l.current).includes(e.animationName);if(e.target===n&&o&&(c("ANIMATION_END"),!a.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},i=e=>{e.target===n&&(s.current=M(l.current))};return n.addEventListener("animationstart",i),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",i),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:i.useCallback(e=>{l.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):i.Children.only(r),l=d(n.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof r||n.isPresent?i.cloneElement(o,{ref:l}):null};function M(e){return(null==e?void 0:e.animationName)||"none"}k.displayName="Presence";var _=l[" useInsertionEffect ".trim().toString()]||D,F=(Symbol("RADIX:SYNC_STATE"),Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"})),I=i.forwardRef((e,t)=>(0,m.jsx)(T.span,{...e,ref:t,style:{...F,...e.style}}));I.displayName="VisuallyHidden";var W="ToastProvider",[U,$,K]=function(e){let t=e+"CollectionProvider",[r,n]=y(t),[o,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:r}=e,n=i.useRef(null),l=i.useRef(new Map).current;return(0,m.jsx)(o,{scope:t,itemMap:l,collectionRef:n,children:r})};a.displayName=t;let s=e+"CollectionSlot",u=w(s),c=i.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=d(t,l(s,r).collectionRef);return(0,m.jsx)(u,{ref:o,children:n})});c.displayName=s;let f=e+"CollectionItemSlot",p="data-radix-collection-item",v=w(f),E=i.forwardRef((e,t)=>{let{scope:r,children:n,...o}=e,a=i.useRef(null),s=d(t,a),u=l(f,r);return i.useEffect(()=>(u.itemMap.set(a,{ref:a,...o}),()=>void u.itemMap.delete(a))),(0,m.jsx)(v,{[p]:"",ref:s,children:n})});return E.displayName=f,[{Provider:a,Slot:c,ItemSlot:E},function(t){let r=l(e+"CollectionConsumer",t);return i.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(p,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},n]}("Toast"),[V,z]=y("Toast",[K]),[B,X]=V(W),q=e=>{let{__scopeToast:t,label:r="Notification",duration:n=5e3,swipeDirection:o="right",swipeThreshold:l=50,children:a}=e,[s,u]=i.useState(null),[c,d]=i.useState(0),f=i.useRef(!1),p=i.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(W,"`. Expected non-empty `string`.")),(0,m.jsx)(U.Provider,{scope:t,children:(0,m.jsx)(B,{scope:t,label:r,duration:n,swipeDirection:o,swipeThreshold:l,toastCount:c,viewport:s,onViewportChange:u,onToastAdd:i.useCallback(()=>d(e=>e+1),[]),onToastRemove:i.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:a})})};q.displayName=W;var Y="ToastViewport",H=["F8"],Z="toast.viewportPause",G="toast.viewportResume",J=i.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:n=H,label:o="Notifications ({hotkey})",...l}=e,a=X(Y,r),s=$(r),u=i.useRef(null),c=i.useRef(null),f=i.useRef(null),p=i.useRef(null),v=d(t,p,a.onViewportChange),y=n.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=a.toastCount>0;i.useEffect(()=>{let e=e=>{var t;0!==n.length&&n.every(t=>e[t]||e.code===t)&&(null===(t=p.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[n]),i.useEffect(()=>{let e=u.current,t=p.current;if(w&&e&&t){let r=()=>{if(!a.isClosePausedRef.current){let e=new CustomEvent(Z);t.dispatchEvent(e),a.isClosePausedRef.current=!0}},n=()=>{if(a.isClosePausedRef.current){let e=new CustomEvent(G);t.dispatchEvent(e),a.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},i=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",i),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",i),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[w,a.isClosePausedRef]);let E=i.useCallback(e=>{let{tabbingDirection:t}=e,r=s().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[s]);return i.useEffect(()=>{let e=p.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,i;let r=document.activeElement,l=t.shiftKey;if(t.target===e&&l){null===(n=c.current)||void 0===n||n.focus();return}let a=E({tabbingDirection:l?"backwards":"forwards"}),s=a.findIndex(e=>e===r);ey(a.slice(s+1))?t.preventDefault():l?null===(o=c.current)||void 0===o||o.focus():null===(i=f.current)||void 0===i||i.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[s,E]),(0,m.jsxs)(j,{ref:u,role:"region","aria-label":o.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&(0,m.jsx)(ee,{ref:c,onFocusFromOutsideViewport:()=>{ey(E({tabbingDirection:"forwards"}))}}),(0,m.jsx)(U.Slot,{scope:r,children:(0,m.jsx)(T.ol,{tabIndex:-1,...l,ref:v})}),w&&(0,m.jsx)(ee,{ref:f,onFocusFromOutsideViewport:()=>{ey(E({tabbingDirection:"backwards"}))}})]})});J.displayName=Y;var Q="ToastFocusProxy",ee=i.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...o}=e,i=X(Q,r);return(0,m.jsx)(I,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=i.viewport)||void 0===t?void 0:t.contains(r))||n()}})});ee.displayName=Q;var et="Toast",er=i.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:o,onOpenChange:l,...a}=e,[u,c]=function({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,l,a]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),o=i.useRef(r),l=i.useRef(t);return _(()=>{l.current=t},[t]),i.useEffect(()=>{o.current!==r&&(l.current?.(r),o.current=r)},[r,o]),[r,n,l]}({defaultProp:t,onChange:r}),s=void 0!==e,u=s?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,n])}return[u,i.useCallback(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else l(t)},[s,e,l,a])]}({prop:n,defaultProp:null==o||o,onChange:l,caller:et});return(0,m.jsx)(k,{present:r||u,children:(0,m.jsx)(ei,{open:u,...a,ref:t,onClose:()=>c(!1),onPause:N(e.onPause),onResume:N(e.onResume),onSwipeStart:s(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:s(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:s(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:s(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),c(!1)})})})});er.displayName=et;var[en,eo]=V(et,{onClose(){}}),ei=i.forwardRef((e,t)=>{let{__scopeToast:r,type:n="foreground",duration:o,open:l,onClose:u,onEscapeKeyDown:c,onPause:f,onResume:p,onSwipeStart:v,onSwipeMove:y,onSwipeCancel:w,onSwipeEnd:E,...h}=e,b=X(et,r),[g,x]=i.useState(null),C=d(t,e=>x(e)),R=i.useRef(null),P=i.useRef(null),j=o||b.duration,L=i.useRef(0),S=i.useRef(j),D=i.useRef(0),{onToastAdd:A,onToastRemove:k}=b,M=N(()=>{var e;(null==g?void 0:g.contains(document.activeElement))&&(null===(e=b.viewport)||void 0===e||e.focus()),u()}),_=i.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(D.current),L.current=new Date().getTime(),D.current=window.setTimeout(M,e))},[M]);i.useEffect(()=>{let e=b.viewport;if(e){let t=()=>{_(S.current),null==p||p()},r=()=>{let e=new Date().getTime()-L.current;S.current=S.current-e,window.clearTimeout(D.current),null==f||f()};return e.addEventListener(Z,r),e.addEventListener(G,t),()=>{e.removeEventListener(Z,r),e.removeEventListener(G,t)}}},[b.viewport,j,f,p,_]),i.useEffect(()=>{l&&!b.isClosePausedRef.current&&_(j)},[l,j,b.isClosePausedRef,_]),i.useEffect(()=>(A(),()=>k()),[A,k]);let F=i.useMemo(()=>g?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(g):null,[g]);return b.viewport?(0,m.jsxs)(m.Fragment,{children:[F&&(0,m.jsx)(el,{__scopeToast:r,role:"status","aria-live":"foreground"===n?"assertive":"polite","aria-atomic":!0,children:F}),(0,m.jsx)(en,{scope:r,onClose:M,children:a.createPortal((0,m.jsx)(U.ItemSlot,{scope:r,children:(0,m.jsx)(O,{asChild:!0,onEscapeKeyDown:s(c,()=>{b.isFocusedToastEscapeKeyDownRef.current||M(),b.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,m.jsx)(T.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":b.swipeDirection,...h,ref:C,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:s(e.onKeyDown,e=>{"Escape"!==e.key||(null==c||c(e.nativeEvent),e.nativeEvent.defaultPrevented||(b.isFocusedToastEscapeKeyDownRef.current=!0,M()))}),onPointerDown:s(e.onPointerDown,e=>{0===e.button&&(R.current={x:e.clientX,y:e.clientY})}),onPointerMove:s(e.onPointerMove,e=>{if(!R.current)return;let t=e.clientX-R.current.x,r=e.clientY-R.current.y,n=!!P.current,o=["left","right"].includes(b.swipeDirection),i=["left","up"].includes(b.swipeDirection)?Math.min:Math.max,l=o?i(0,t):0,a=o?0:i(0,r),s="touch"===e.pointerType?10:2,u={x:l,y:a},c={originalEvent:e,delta:u};n?(P.current=u,ev("toast.swipeMove",y,c,{discrete:!1})):em(u,b.swipeDirection,s)?(P.current=u,ev("toast.swipeStart",v,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>s||Math.abs(r)>s)&&(R.current=null)}),onPointerUp:s(e.onPointerUp,e=>{let t=P.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),P.current=null,R.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};em(t,b.swipeDirection,b.swipeThreshold)?ev("toast.swipeEnd",E,n,{discrete:!0}):ev("toast.swipeCancel",w,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),b.viewport)})]}):null}),el=e=>{let{__scopeToast:t,children:r,...n}=e,o=X(et,t),[l,a]=i.useState(!1),[s,u]=i.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=N(e);D(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),i.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),s?null:(0,m.jsx)(A,{asChild:!0,children:(0,m.jsx)(I,{...n,children:l&&(0,m.jsxs)(m.Fragment,{children:[o.label," ",r]})})})},ea=i.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,m.jsx)(T.div,{...n,ref:t})});ea.displayName="ToastTitle";var es=i.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,m.jsx)(T.div,{...n,ref:t})});es.displayName="ToastDescription";var eu="ToastAction",ec=i.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,m.jsx)(ep,{altText:r,asChild:!0,children:(0,m.jsx)(ef,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(eu,"`. Expected non-empty `string`.")),null)});ec.displayName=eu;var ed="ToastClose",ef=i.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,o=eo(ed,r);return(0,m.jsx)(ep,{asChild:!0,children:(0,m.jsx)(T.button,{type:"button",...n,ref:t,onClick:s(e.onClick,o.onClose)})})});ef.displayName=ed;var ep=i.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...o}=e;return(0,m.jsx)(T.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...o,ref:t})});function ev(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.currentTarget,l=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?C(i,l):i.dispatchEvent(l)}var em=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),i=n>o;return"left"===t||"right"===t?i&&n>r:!i&&o>r};function ey(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var ew=q,eE=J,eh=er,eb=ea,eg=es,ex=ec,eT=ef},39249:(e,t,r)=>{"use strict";r.d(t,{Cl:()=>n,Tt:()=>o,fX:()=>i});var n=function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;function i(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,"function"==typeof SuppressedError&&SuppressedError},43969:(e,t,r)=>{"use strict";function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}r.d(t,{_:()=>n})},47017:e=>{e.exports={style:{fontFamily:"'Outfit', 'Outfit Fallback'",fontStyle:"normal"},className:"__className_91870d",variable:"__variable_91870d"}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,s=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==a?void 0:a[e];if(null===t)return null;let i=o(t)||o(n);return l[e][i]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},86266:(e,t,r)=>{"use strict";r.d(t,{_:()=>o});var n=r(43969);function o(e,t,r){var o=(0,n._)(e,t,"set");return!function(e,t,r){if(t.set)t.set.call(e,r);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=r}}(e,o,r),r}},94971:(e,t,r)=>{"use strict";r.d(t,{_:()=>o});var n=r(43969);function o(e,t){var r=(0,n._)(e,t,"get");return r.get?r.get.call(e):r.value}},95920:(e,t,r)=>{"use strict";function n(e,t,r){!function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,r)}r.d(t,{_:()=>n})}}]);