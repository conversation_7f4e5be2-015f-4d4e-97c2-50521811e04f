(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{19349:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var a=r(95155),t=r(12115),l=r(35695),o=r(6874),n=r.n(o),c=r(90221),i=r(62177),d=r(55594),m=r(78749),u=r(92657),x=r(56671),h=r(30285),g=r(17759),p=r(62523),j=r(51790),w=r(25731);let f=d.Ik({name:d.Yj().min(2,{message:"Name must be at least 2 characters."}),email:d.Yj().email({message:"Please enter a valid email address."}),password:d.Yj().min(6,{message:"Password must be at least 6 characters."}),confirmPassword:d.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function b(){let e=(0,l.useRouter)(),{signUp:s,loginWithGoogle:r,deleteAccount:o,logout:d}=(0,j.A)(),[b,y]=(0,t.useState)(!1),[N,v]=(0,t.useState)(!1),[C,k]=(0,t.useState)(!1),[S,P]=(0,t.useState)(!1),I=(0,i.mN)({resolver:(0,c.u)(f),defaultValues:{name:"",email:"",password:"",confirmPassword:""}});async function L(e){y(!0);try{await s(e.email,e.password,e.name);try{let e=await (0,w.K8)();if(!e||!e.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",e.accessToken),e.user&&e.user.role)localStorage.setItem("userRole",e.user.role),A(e.user.role),x.oR.success("Signed up successfully!");else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),x.oR.error(e.message||"Backend authentication failed. Please try again.");try{await o()}catch(e){console.error("Failed to delete Firebase account:",e)}return}}catch(e){console.error("Signup error:",e),x.oR.error(e.message||"Failed to sign up. Please try again.")}finally{y(!1)}}async function R(){v(!0);try{await r();try{let e=await (0,w.K8)();if(!e||!e.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",e.accessToken),e.user&&e.user.role)localStorage.setItem("userRole",e.user.role),A(e.user.role),x.oR.success("Signed up with Google successfully!");else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),x.oR.error(e.message||"Backend authentication failed. Please try again."),await d();return}}catch(e){console.error("Google signup error:",e),x.oR.error(e.message||"Failed to sign up with Google. Please try again.")}finally{v(!1)}}function A(s){switch(s){case"superAdmin":e.push("/admin");break;case"collegeAdmin":e.push("/college");break;case"teacher":e.push("/teacher");break;default:e.push("/")}}return(0,a.jsxs)("div",{className:"flex min-h-screen",children:[(0,a.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center p-8 bg-white",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("img",{src:"/assets/logo/medicos-logo.svg",alt:"MEDICOS",className:"h-[70px] w-auto"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Create Account"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Enter your details to create a new account"}),(0,a.jsxs)("button",{type:"button",onClick:R,disabled:N,className:"w-full flex items-center justify-center gap-2 border border-gray-300 rounded-md py-2 px-4 mb-6 text-gray-700 hover:bg-gray-50 disabled:opacity-70 disabled:cursor-not-allowed",children:[N?(0,a.jsx)("div",{className:"h-5 w-5 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"}):(0,a.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M19.9895 10.1871C19.9895 9.36767 19.9214 8.76973 19.7742 8.14966H10.1992V11.848H15.8195C15.7062 12.7671 15.0943 14.1512 13.7346 15.0813L13.7155 15.2051L16.7429 17.4969L16.9527 17.5174C18.879 15.7789 19.9895 13.221 19.9895 10.1871Z",fill:"#4285F4"}),(0,a.jsx)("path",{d:"M10.1993 19.9313C12.9527 19.9313 15.2643 19.0454 16.9527 17.5174L13.7346 15.0813C12.8734 15.6682 11.7176 16.0779 10.1993 16.0779C7.50243 16.0779 5.21352 14.3395 4.39759 11.9366L4.27799 11.9466L1.13003 14.3273L1.08887 14.4391C2.76588 17.6945 6.21061 19.9313 10.1993 19.9313Z",fill:"#34A853"}),(0,a.jsx)("path",{d:"M4.39748 11.9366C4.18219 11.3166 4.05759 10.6521 4.05759 9.96565C4.05759 9.27909 4.18219 8.61473 4.38615 7.99466L4.38045 7.8626L1.19304 5.44366L1.08875 5.49214C0.397576 6.84305 0.000976562 8.36008 0.000976562 9.96565C0.000976562 11.5712 0.397576 13.0882 1.08875 14.4391L4.39748 11.9366Z",fill:"#FBBC05"}),(0,a.jsx)("path",{d:"M10.1993 3.85336C12.1142 3.85336 13.406 4.66168 14.1425 5.33717L17.0207 2.59107C15.253 0.985496 12.9527 0 10.1993 0C6.2106 0 2.76588 2.23672 1.08887 5.49214L4.38626 7.99466C5.21352 5.59183 7.50242 3.85336 10.1993 3.85336Z",fill:"#EB4335"})]}),N?"Signing up...":"Sign up with Google"]}),(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)("div",{className:"flex-grow border-t border-gray-300"}),(0,a.jsx)("span",{className:"mx-4 text-sm text-gray-500",children:"Or"}),(0,a.jsx)("div",{className:"flex-grow border-t border-gray-300"})]}),(0,a.jsx)(g.lV,{...I,children:(0,a.jsxs)("form",{onSubmit:I.handleSubmit(L),className:"space-y-4",children:[(0,a.jsx)(g.zB,{control:I.control,name:"name",render:e=>{let{field:s}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-sm font-medium text-gray-700",children:"Full Name*"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(p.p,{placeholder:"John Doe",className:"w-full rounded-md border border-gray-300 py-2 px-3",...s})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:I.control,name:"email",render:e=>{let{field:s}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-sm font-medium text-gray-700",children:"Email Address*"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(p.p,{placeholder:"<EMAIL>",className:"w-full rounded-md border border-gray-300 py-2 px-3",...s})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:I.control,name:"password",render:e=>{let{field:s}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-sm font-medium text-gray-700",children:"Password*"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.p,{type:C?"text":"password",className:"w-full rounded-md border border-gray-300 py-2 px-3",...s}),(0,a.jsx)("button",{type:"button",onClick:()=>k(!C),className:"absolute right-3 top-2.5 text-gray-400",children:C?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(u.A,{className:"h-5 w-5"})})]})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:I.control,name:"confirmPassword",render:e=>{let{field:s}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{className:"text-sm font-medium text-gray-700",children:"Confirm Password*"}),(0,a.jsx)(g.MJ,{children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p.p,{type:S?"text":"password",className:"w-full rounded-md border border-gray-300 py-2 px-3",...s}),(0,a.jsx)("button",{type:"button",onClick:()=>P(!S),className:"absolute right-3 top-2.5 text-gray-400",children:S?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(u.A,{className:"h-5 w-5"})})]})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(h.$,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md mt-6",disabled:b,children:b?"Creating account...":"Create Account"}),(0,a.jsx)("div",{className:"text-center mt-4",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,a.jsx)(n(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign In"})]})})]})})]})}),(0,a.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-green-800 items-center justify-center p-12 relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-10"}),(0,a.jsx)("div",{className:"relative z-10 text-center max-w-md",children:(0,a.jsxs)("blockquote",{className:"text-white text-xl font-medium",children:['"Education is the most powerful weapon which you can use to change the world."',(0,a.jsx)("footer",{className:"mt-2 text-white text-opacity-80",children:"– Nelson Mandela"})]})})]})]})}},87947:(e,s,r)=>{Promise.resolve().then(r.bind(r,19349))}},e=>{var s=s=>e(e.s=s);e.O(0,[7146,4277,6874,6671,685,7117,221,8196,3954,8441,1684,7358],()=>s(87947)),_N_E=e.O()}]);