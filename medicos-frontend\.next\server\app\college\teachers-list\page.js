(()=>{var e={};e.id=1389,e.ids=[1389],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7618:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>F});var s=a(60687),r=a(43210),l=a(55533),n=a(63239),o=a(52581),i=a(58869),c=a(62688);let d=(0,c.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),h=(0,c.A)("pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]]);var m=a(88233);function u({data:e=[],title:t="View Teachers list",onEdit:a,onDelete:l,columns:n=["name","department","email","phone","status","actions"],itemsPerPage:o=5,isLoading:c=!1,onRefresh:u,onFilter:p}){let[g,x]=(0,r.useState)([]),[f,y]=(0,r.useState)(1);g?.length;let j=(f-1)*o,b=g?.slice(j,j+o)||[];return(0,s.jsxs)("div",{className:"w-full bg-white rounded-xl shadow border border-gray-200 p-0",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between px-6 pt-6 pb-2",children:[(0,s.jsx)("h2",{className:"text-xl font-bold mb-2 md:mb-0",children:"View Techers list"}),(0,s.jsx)("div",{className:"flex gap-2"})]}),(0,s.jsx)("div",{className:"overflow-x-auto px-6 pb-6",children:(0,s.jsxs)("table",{className:"min-w-full bg-white rounded-xl",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-gray-200 text-gray-500 text-sm",children:[(0,s.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Teacher Name"}),(0,s.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Subject Assigned"}),(0,s.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Email Id"}),(0,s.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Phone number"}),(0,s.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Status"}),(0,s.jsx)("th",{className:"py-3 px-4 text-left font-medium",children:"Actions"})]})}),(0,s.jsx)("tbody",{children:c?(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:6,className:"text-center py-10",children:"Loading..."})}):0===b.length?(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:6,className:"text-center py-10 text-gray-500",children:"No teachers found"})}):b.map(e=>(0,s.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition",children:[(0,s.jsxs)("td",{className:"py-3 px-4 flex items-center gap-3 min-w-[200px]",children:[e.avatar?(0,s.jsx)("img",{src:e.avatar,alt:e.name,className:"w-10 h-10 rounded-full object-cover border"}):(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"w-6 h-6 text-gray-400"})}),(0,s.jsx)("span",{className:"font-medium text-gray-900",children:e.name}),(0,s.jsx)(d,{className:"w-4 h-4 text-blue-500 ml-1",fill:"#3b82f6"})]}),(0,s.jsx)("td",{className:"py-3 px-4 text-gray-700",children:e.department||"-"}),(0,s.jsx)("td",{className:"py-3 px-4 text-gray-700",children:e.email}),(0,s.jsx)("td",{className:"py-3 px-4 font-medium text-green-600",children:e.phone}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsx)("span",{className:`px-3 py-1 rounded-full text-white text-sm font-medium ${"active"===e.status.toLowerCase()?"bg-emerald-500":"bg-red-500"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,s.jsx)("td",{className:"py-3 px-4",children:(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("button",{onClick:()=>a&&a(e.id),className:"rounded-full bg-yellow-100 hover:bg-yellow-200 p-2 text-yellow-700 transition",title:"Edit",children:(0,s.jsx)(h,{className:"w-4 h-4"})}),(0,s.jsx)("button",{onClick:()=>l&&l(e.id),className:"rounded-full bg-red-100 hover:bg-red-200 p-2 text-red-700 transition",title:"Delete",children:(0,s.jsx)(m.A,{className:"w-4 h-4"})})]})})]},e.id))})]})})]})}var p=a(13861),g=a(41862);let x=[{key:"view",label:"View Teachers List",icon:(0,s.jsx)(p.A,{className:"w-4 h-4 mr-2"})},{key:"add",label:"Add Teachers",icon:(0,s.jsx)("span",{className:"font-bold text-xl mr-2",children:"+"})}],f=({activeTab:e,onTabChange:t,collegeIdMissing:a})=>{let[l,n]=(0,r.useState)(null),o=e=>{a||(n(e),setTimeout(()=>{t(e),n(null)},300))};return(0,s.jsx)("div",{className:"border-b mb-6",children:(0,s.jsx)("div",{className:"flex flex-wrap space-x-4",children:x.map(t=>{let r=e===t.key,n=l===t.key;return(0,s.jsxs)("button",{onClick:()=>o(t.key),disabled:a,className:`
                inline-flex items-center px-4 py-3 text-sm font-medium border-b-2 -mb-px transition-all
                ${r?"border-teacher-blue text-teacher-blue font-semibold":"border-transparent text-muted-foreground hover:text-foreground hover:border-border"}
                ${a?"opacity-50 cursor-not-allowed":""}
              `,children:[n?(0,s.jsx)(g.A,{className:"w-4 h-4 mr-2 animate-spin"}):t.icon,t.label]},t.key)})})})};var y=a(12048),j=a(80013),b=a(29523),v=a(41550),N=a(78272),w=a(16189),k=a(45880),S=a(17879),P=a(60478),A=a(31981);let C=k.z.object({name:k.z.string().min(2,{message:"Name must be at least 2 characters"}),email:k.z.string().email({message:"Please enter a valid email address"}),phone:k.z.string().min(10,{message:"Please enter a valid phone number"}),department:k.z.string().optional(),designation:k.z.string().optional()}),$=({onCancel:e,onSuccess:t})=>{(0,w.useParams)();let{user:a,userRole:l}=(0,P.A)(),[n,i]=(0,r.useState)(null);(0,r.useEffect)(()=>{try{let e=null;for(let t of["token","backendToken","authToken","jwtToken"]){let a=localStorage.getItem(t);if(a){console.log(`Found token with key: ${t}`),e=a;break}}if(e){let t=e.split(".");if(3===t.length){let e=JSON.parse(atob(t[1]));console.log("JWT payload:",e),e.collegeId&&(console.log("Found collegeId in JWT:",e.collegeId),i(e.collegeId))}}else console.warn("No token found in localStorage")}catch(e){console.error("Error parsing JWT token:",e)}},[]),(0,r.useEffect)(()=>{console.log("localStorage keys:",Object.keys(localStorage)),Object.keys(localStorage).forEach(e=>{e.toLowerCase().includes("token")&&console.log(`Found potential token in localStorage with key: ${e}`)})},[]);let[c,d]=(0,r.useState)(!1),[h,m]=(0,r.useState)({name:"",email:"",phone:"",department:"",designation:""}),[u,p]=(0,r.useState)(!1),[g,x]=(0,r.useState)({});if((0,r.useEffect)(()=>{n||console.warn("College ID is missing. Form submission will fail.")},[n]),c)return(0,s.jsx)("div",{children:"Loading..."});if(!n)return null;let f=()=>{try{return C.parse(h),x({}),!0}catch(e){if(e instanceof k.z.ZodError){let t={};e.errors.forEach(e=>{t[e.path[0]]=e.message}),x(t)}return!1}},$=async e=>{if(e.preventDefault(),!f())return;let a=n;if(!a)try{for(let e of["token","backendToken","authToken","jwtToken"]){let t=localStorage.getItem(e);if(t)try{let s=JSON.parse(atob(t.split(".")[1]));if(s.collegeId){a=s.collegeId,console.log(`Found collegeId in ${e}:`,a);break}}catch(t){console.error(`Error parsing token from ${e}:`,t)}}}catch(e){console.error("Error getting collegeId from tokens:",e)}if(!a){o.oR.error("College ID is missing. Please try again or contact support.");return}p(!0);try{console.log("Submitting teacher with collegeId:",a);let e=await (0,S.q6)(a,h);(0,A.cY)(e)&&(m({name:"",email:"",phone:"",department:"",designation:""}),t&&t())}catch(e){console.error("Unexpected error adding teacher:",e),o.oR.error("An unexpected error occurred. Please try again.")}finally{p(!1)}},E=e=>{let{name:t,value:a}=e.target;m(e=>({...e,[t]:a})),g[t]&&x(e=>{let a={...e};return delete a[t],a})};return(0,s.jsxs)("form",{onSubmit:$,className:"space-y-6 w-[450px]",children:[(0,s.jsx)("h2",{className:"text-xl font-bold mb-8 md:mb-0",children:"Add Teachers"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(j.J,{htmlFor:"name",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Teacher's Name"}),(0,s.jsx)("span",{className:"text-gray-500 text-sm",children:"Required"})]}),(0,s.jsx)(y.p,{id:"name",name:"name",value:h.name,onChange:E,placeholder:"Enter teacher name",className:`mt-1 w-[450px] h-[46px] ${g.name?"border-red-500":""}`}),g.name&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:g.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(j.J,{htmlFor:"email",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Email"}),(0,s.jsx)("span",{className:"text-gray-500 text-sm",children:"Required"})]}),(0,s.jsxs)("div",{className:"relative mt-1",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(v.A,{className:"h-4 w-4 text-gray-400"})}),(0,s.jsx)(y.p,{id:"email",name:"email",type:"email",value:h.email,onChange:E,placeholder:"Email address",className:`pl-10 w-[450px] h-[46px] ${g.email?"border-red-500":""}`})]}),g.email&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:g.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(j.J,{htmlFor:"phone",className:"text-base font-medium text-gray-700",children:"Phone"}),(0,s.jsx)("span",{className:"text-gray-500 text-sm",children:"Required"})]}),(0,s.jsxs)("div",{className:`flex w-full h-12 border rounded-md overflow-hidden ${g.phone?"border-red-500":"border-gray-200"}`,style:{width:"450px",height:"46px"},children:[(0,s.jsx)("div",{className:"flex items-center justify-center bg-white border-r border-gray-200 w-24 px-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-6 h-4 rounded-sm overflow-hidden",children:(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-b from-orange-500 via-white to-green-600 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-3 h-3 border border-blue-800 rounded-full bg-white flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-blue-800 rounded-full"})})})}),(0,s.jsx)("span",{className:"text-sm",children:"IN"}),(0,s.jsx)(N.A,{className:"h-4 w-4 text-gray-400"})]})}),(0,s.jsx)(y.p,{id:"phone",name:"phone",type:"tel",value:h.phone,onChange:e=>{let t=e.target.value.replace(/\D/g,"");if(0===t.length){m({...h,phone:""}),g.phone&&x(e=>{let t={...e};return delete t.phone,t});return}let a=t;a.startsWith("91")&&(a=a.slice(2)),a.length>10&&(a=a.slice(0,10));let s="";s=0===a.length?"":a.length<=5?`+91 ${a}`:`+91 ${a.slice(0,5)} ${a.slice(5)}`,m({...h,phone:s}),g.phone&&x(e=>{let t={...e};return delete t.phone,t})},placeholder:"+91 00000 00000",className:"border-none flex-1 focus-visible:ring-0 h-full"})]}),g.phone&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:g.phone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(j.J,{htmlFor:"department",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Teacher's Department"}),(0,s.jsx)(y.p,{id:"department",name:"department",value:h.department,onChange:E,placeholder:"Enter teacher department",className:"mt-1 w-[450px] h-[46px]"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(j.J,{htmlFor:"designation",className:"font-family-outfit font-medium text-[15px] leading-[100%] tracking-[0.005em]",children:"Teacher's Designation"}),(0,s.jsx)(y.p,{id:"designation",name:"designation",value:h.designation,onChange:E,placeholder:"Enter teacher designation",className:"mt-1 w-[450px] h-[46px]"})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-6",children:[(0,s.jsx)(b.$,{type:"button",variant:"outline",onClick:e,disabled:u,className:"text-white hover:text-white bg-[#EF4444] hover:bg-red-600 border-none",children:"Cancel"}),(0,s.jsx)(b.$,{type:"button",variant:"outline",onClick:()=>{m({name:"",email:"",phone:"",department:"",designation:""}),x({})},disabled:u,children:"Reset"}),(0,s.jsx)(b.$,{type:"submit",disabled:u,className:"text-white bg-[#05603A] hover:bg-[#04502F] border-none",children:u?"Adding...":"Add Teacher"})]})]})},E=({teacher:e,onCancel:t,onSubmit:a})=>{let[l,n]=(0,r.useState)({phone:"",department:"",designation:"",status:""}),[i,c]=(0,r.useState)(!1),[d,h]=(0,r.useState)({});(0,r.useEffect)(()=>{n({phone:e.phone||"",department:e.department||"",designation:"",status:e.status?.toLowerCase()||"active"})},[e]);let m=()=>{let e={};return l.phone.trim()||(e.phone="Phone number is required"),h(e),0===Object.keys(e).length},u=e=>{let{name:t,value:a}=e.target;n(e=>({...e,[t]:a})),d[t]&&h(e=>({...e,[t]:""}))},p=async e=>{if(e.preventDefault(),m()){c(!0);try{await a({phone:l.phone,department:l.department,designation:l.designation,status:l.status})}catch(e){console.error("Error updating teacher:",e),o.oR.error("Failed to update teacher. Please try again.")}finally{c(!1)}}};return(0,s.jsxs)("form",{onSubmit:p,className:"space-y-6 w-[450px]",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(j.J,{htmlFor:"phone",children:"Phone Number"}),(0,s.jsx)(y.p,{id:"phone",name:"phone",value:l.phone,onChange:u,placeholder:"Enter phone number",className:d.phone?"border-red-500":""}),d.phone&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:d.phone})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(j.J,{htmlFor:"designation",children:"Designation"}),(0,s.jsx)(y.p,{id:"designation",name:"designation",value:l.designation,onChange:u,placeholder:"Enter designation"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(j.J,{htmlFor:"status",children:"Status"}),(0,s.jsxs)("select",{id:"status",name:"status",value:l.status,onChange:e=>n(t=>({...t,status:e.target.value})),className:"w-full p-2 border rounded-md",children:[(0,s.jsx)("option",{value:"active",children:"Active"}),(0,s.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)(b.$,{type:"button",variant:"outline",onClick:t,disabled:i,className:"text-white hover:text-white bg-[#EF4444] hover:bg-red-600 border-none",children:"Cancel"}),(0,s.jsx)(b.$,{type:"submit",disabled:i,className:"text-white bg-[#05603A] hover:bg-[#04502F] border-none",children:i?"Updating...":"Update Teacher"})]})]})};var I=a(63503),T=a(14583);let F=()=>{let[e,t]=(0,r.useState)("view"),[a,i]=(0,r.useState)(null),[c,d]=(0,r.useState)(!1),[h,m]=(0,r.useState)(null),[p,g]=(0,r.useState)(!1),[x,y]=(0,r.useState)({name:"",department:"",status:"all",email:""}),[j,v]=(0,r.useState)([]),[N,w]=(0,r.useState)([]),[k,P]=(0,r.useState)(null),[C,F]=(0,r.useState)(!1),[q,D]=(0,r.useState)(""),[L,z]=(0,r.useState)(1),[U,M]=(0,r.useState)(10),[R,J]=(0,r.useState)(0),[_,O]=(0,r.useState)(1);(0,r.useEffect)(()=>{let e=localStorage.getItem("collegeId");if(e){console.log("Found collegeId in localStorage:",e),P(e);return}try{for(let e of["token","backendToken","authToken","jwtToken"]){let t=localStorage.getItem(e);if(t)try{let a=t.split(".");if(3===a.length){let t=JSON.parse(atob(a[1]));if(console.log("JWT payload:",t),t.collegeId){console.log(`Found collegeId in ${e}:`,t.collegeId),P(t.collegeId),localStorage.setItem("collegeId",t.collegeId);return}}}catch(t){console.error(`Error parsing token from ${e}:`,t)}}console.log("All localStorage keys:",Object.keys(localStorage)),console.error("Could not find collegeId in any token or localStorage")}catch(e){console.error("Error getting collegeId:",e)}},[]);let{data:G=[],isLoading:B,refetch:W,error:Y}=(0,l.I)({queryKey:["teachers",k,L,U,x],queryFn:async()=>{if(!k)return console.log("No collegeId found, returning empty array"),[];try{console.log(`Fetching teachers for collegeId: ${k}, page: ${L}, limit: ${U}`);let e={};x.name&&(e.name=x.name),x.email&&(e.email=x.email),x.department&&"all_departments"!==x.department&&(e.department=x.department),x.status&&"all"!==x.status&&(e.status=x.status);let t=await (0,S.HC)(k,L,U,e);if(console.log("API returned response:",t),!(0,A.cY)(t))return[];let a=t.data,s=[],r=0,l=1;if(Array.isArray(a))s=a,r=a.length,l=1;else{if(!a||!a.teachers)return console.error("API returned invalid data format:",a),[];s=a.teachers,r=a.total||a.teachers.length,l=a.totalPages||Math.ceil(r/U)}return J(r),O(l),s.map(e=>({id:e._id,name:e.displayName||e.name||"Unknown",email:e.email||"No email",department:e.department||"N/A",phone:e.phone||"N/A",status:"active"===e.status?"Active":"Inactive",avatar:e.avatar||null}))}catch(e){return console.error("Failed to fetch teachers:",e),o.oR.error("Failed to load teachers. Please try again."),[]}},enabled:!!k,staleTime:0,refetchOnWindowFocus:!0});(0,r.useEffect)(()=>{Y&&console.error("Query error:",Y)},[Y]),(0,r.useEffect)(()=>{console.log("teachersData changed:",G),console.log("teachersData length:",G?.length||0),G&&G.length>0?(v(Array.from(new Set(G.map(e=>e.department).filter(Boolean)))),w([...G]),console.log("Setting filtered teachers to:",G.length)):console.log("No teachers data to set")},[G]);let V=e=>{console.log("Page received tab change:",e),"edit"!==e&&i(null),setTimeout(()=>{t(e)},100)},H=async()=>{if(h)try{let e=await (0,S.qg)(h);(0,A.cY)(e)&&W()}catch(e){console.error("Unexpected error deleting teacher:",e),o.oR.error("An unexpected error occurred. Please try again.")}finally{d(!1),m(null)}},K=async e=>{if(a)try{let s={phone:e.phone,department:e.department,designation:e.designation,status:e.status.toLowerCase()},r=await (0,S.Sp)(a.id,s);(0,A.cY)(r)&&(W(),t("view"),i(null))}catch(e){console.error("Unexpected error updating teacher:",e),o.oR.error("An unexpected error occurred. Please try again.")}},Q=(N.length>0?N:G).slice((L-1)*U,L*U);return(0,s.jsxs)("div",{className:"container py-6",children:[!k&&(0,s.jsx)("div",{className:"mb-6 p-4 border border-yellow-300 bg-yellow-50 rounded-md",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)("p",{className:"text-yellow-800",children:"College ID not found. Please enter it manually or check your login status."}),C?(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("input",{type:"text",value:q,onChange:e=>D(e.target.value),placeholder:"Enter College ID",className:"px-3 py-2 border rounded-md flex-1"}),(0,s.jsx)(b.$,{onClick:()=>{q.trim()&&(console.log("Setting manual collegeId:",q),P(q),localStorage.setItem("collegeId",q),F(!1),W())},children:"Submit"}),(0,s.jsx)(b.$,{variant:"outline",onClick:()=>F(!1),children:"Cancel"})]}):(0,s.jsx)(b.$,{variant:"outline",onClick:()=>F(!0),children:"Enter College ID Manually"})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold tracking-tight",children:"view"===e?"Teacher's List View":"add"===e?"Add Teachers":"edit"===e?"Edit Teacher":"Teacher Activity Logs"}),(0,s.jsx)(n.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"view"===e?"Teacher list":"add"===e?"Add teachers":"edit"===e?"Edit teacher":"Teacher activity logs"}],className:"mt-2"})]}),(0,s.jsxs)("div",{className:"rounded-lg border bg-card text-card-foreground shadow",children:[(0,s.jsx)("div",{className:"flex justify-between items-center px-6 pt-6",children:(0,s.jsx)(f,{activeTab:"edit"===e?"view":e,onTabChange:V,collegeIdMissing:!k})}),"view"===e&&(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)(u,{data:Q,onEdit:e=>{console.log(`Edit teacher with id: ${e}`);let a=G.find(t=>t.id===e);a&&(i(a),t("edit"))},onDelete:e=>{console.log(`Delete teacher with id: ${e}`),m(e),d(!0)},itemsPerPage:U,isLoading:B,onRefresh:()=>{y({name:"",department:"",status:"all",email:""}),w([]),W()},onFilter:()=>{g(!0)},columns:["name","department","email","phone","status","actions"]}),!B&&G.length>0&&(0,s.jsx)(T.d,{currentPage:L,totalPages:Math.ceil((N.length>0?N.length:R)/U),pageSize:U,totalItems:N.length>0?N.length:R,onPageChange:e=>{z(e)},onPageSizeChange:e=>{M(e),z(1)},pageSizeOptions:[5,10,20,50]})]}),"add"===e&&(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)($,{onCancel:()=>V("view"),onSuccess:()=>{W(),t("view")}})}),"edit"===e&&a&&(0,s.jsx)("div",{className:"p-6",children:(0,s.jsx)(E,{teacher:a,onCancel:()=>V("view"),onSubmit:K})})]})]}),(0,s.jsx)(I.lG,{open:c,onOpenChange:d,children:(0,s.jsxs)(I.Cf,{children:[(0,s.jsxs)(I.c7,{children:[(0,s.jsx)(I.L3,{children:"Confirm Deletion"}),(0,s.jsx)(I.rr,{children:"Are you sure you want to delete this teacher? This action cannot be undone."})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 mt-4",children:[(0,s.jsx)(b.$,{variant:"outline",onClick:()=>d(!1),children:"Cancel"}),(0,s.jsx)(b.$,{variant:"destructive",onClick:H,children:"Delete"})]})]})})]})}},9823:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(60687),r=a(66327),l=a(53355),n=a(99557),o=a(45285);function i({children:e}){return(0,s.jsx)(o.A,{allowedRoles:[n.g.COLLEGE_ADMIN],children:(0,s.jsx)(l.default,{children:(0,s.jsx)(r.N,{role:n.g.COLLEGE_ADMIN,children:e})})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13853:(e,t,a)=>{Promise.resolve().then(a.bind(a,9823))},13861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14583:(e,t,a)=>{"use strict";a.d(t,{d:()=>o});var s=a(60687);a(43210);var r=a(29523),l=a(47033),n=a(14952);function o({currentPage:e,totalPages:t,onPageChange:a,pageSize:o,totalItems:i,onPageSizeChange:c,pageSizeOptions:d=[5,10,20,50]}){let h=Math.min(i,(e-1)*o+1),m=Math.min(i,e*o);return(0,s.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,s.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:i>0?(0,s.jsxs)("p",{children:["Showing ",h," to ",m," of ",i," items"]}):(0,s.jsx)("p",{children:"No items"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[c&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,s.jsx)("select",{className:"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm",value:o,onChange:e=>c(Number(e.target.value)),children:d.map(e=>(0,s.jsx)("option",{value:e,children:e},e))})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>a(e-1),disabled:1===e,className:"h-8 w-8 p-0",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Previous page"})]}),(()=>{let a=[];if(t<=5)for(let e=1;e<=t;e++)a.push(e);else{a.push(1),e>3&&a.push("ellipsis");let s=Math.max(2,e-1),r=Math.min(t-1,e+1);for(let e=s;e<=r;e++)a.push(e);e<t-2&&a.push("ellipsis"),t>1&&a.push(t)}return a})().map((t,l)=>"ellipsis"===t?(0,s.jsx)("span",{className:"px-2",children:"..."},`ellipsis-${l}`):(0,s.jsx)(r.$,{variant:e===t?"default":"outline",size:"sm",onClick:()=>a(t),className:"h-8 w-8 p-0",children:t},`page-${t}`)),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>a(e+1),disabled:e===t||0===t,className:"h-8 w-8 p-0",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Next page"})]})]})]})]})}},17879:(e,t,a)=>{"use strict";a.d(t,{HC:()=>l,P_:()=>i,Rb:()=>c,Sp:()=>n,q6:()=>r,qg:()=>o});var s=a(31981);let r=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,s.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let r={"Content-Type":"application/json",Authorization:`Bearer ${a}`},l=await fetch(`http://localhost:3000/api/colleges/${e}/teachers`,{method:"POST",headers:r,body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${l.status}`,"Failed to add teacher. Please try again.")}let n=await l.json();return(0,s.$y)(n,!0,"Teacher added successfully!")}catch(e){return console.error("Error adding teacher:",e),(0,s.hS)(e.message||"Failed to add teacher. Please try again.","Failed to add teacher. Please try again.")}},l=async(e,t=1,a=10,r={})=>{try{let l=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!l)return console.error("No authentication token found"),(0,s.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let n=new URLSearchParams({page:t.toString(),limit:a.toString(),...r}),o=`http://localhost:3000/api/colleges/${e}/teachers?${n}`;console.log(`Fetching teachers: ${o}`);let i=await fetch(o,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l}`},cache:"no-store"});if(!i.ok){let e=await i.text();try{let t=JSON.parse(e);return(0,s.hS)(t.message||`Error: ${i.status}`,"Failed to load teachers. Please try again.")}catch(e){return(0,s.hS)(`Error: ${i.status} - ${i.statusText}`,"Failed to load teachers. Please try again.")}}let c=await i.json();if(console.log("Raw API response:",c),Array.isArray(c)){console.log("API returned an array, converting to paginated format");let e={teachers:c,total:c.length,page:t,limit:a,totalPages:Math.ceil(c.length/a)};return(0,s.$y)(e)}return(0,s.$y)(c)}catch(e){return console.error("Error fetching college teachers:",e),(0,s.hS)(e.message||"Failed to load teachers. Please try again.","Failed to load teachers. Please try again.")}},n=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,s.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher with data:",t);let r={"Content-Type":"application/json",Authorization:`Bearer ${a}`},l=await fetch(`http://localhost:3000/api/teachers/${e}`,{method:"PUT",headers:r,body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${l.status}`,"Failed to update teacher. Please try again.")}let n=await l.json();return(0,s.$y)(n,!0,"Teacher updated successfully!")}catch(e){return console.error("Error updating teacher:",e),(0,s.hS)(e.message||"Failed to update teacher. Please try again.","Failed to update teacher. Please try again.")}},o=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,s.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let a={"Content-Type":"application/json",Authorization:`Bearer ${t}`},r=await fetch(`http://localhost:3000/api/teachers/${e}`,{method:"DELETE",headers:a});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${r.status}`,"Failed to delete teacher. Please try again.")}let l=await r.json();return(0,s.$y)(l,!0,"Teacher deleted successfully!")}catch(e){return console.error("Error deleting teacher:",e),(0,s.hS)(e.message||"Failed to delete teacher","Failed to delete teacher. Please try again.")}},i=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,s.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher profile with data:",e);let a={"Content-Type":"application/json",Authorization:`Bearer ${t}`},r=await fetch("http://localhost:3000/api/teachers/me",{method:"PUT",headers:a,body:JSON.stringify(e)});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${r.status}`,"Failed to update profile. Please try again.")}let l=await r.json();return(0,s.$y)(l,!0,"Profile updated successfully!")}catch(e){return console.error("Error updating teacher profile:",e),(0,s.hS)(e.message||"Failed to update profile. Please try again.","Failed to update profile. Please try again.")}},c=async()=>{try{let e=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!e)return(0,s.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let t={"Content-Type":"application/json",Authorization:`Bearer ${e}`},a=await fetch("http://localhost:3000/api/users/me",{method:"GET",headers:t});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${a.status}`,"Failed to load profile. Please try again.")}let r=await a.json();return(0,s.$y)(r)}catch(e){return console.error("Error fetching current teacher profile:",e),(0,s.hS)(e.message||"Failed to load profile. Please try again.","Failed to load profile. Please try again.")}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22105:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=a(65239),r=a(48088),l=a(88170),n=a.n(l),o=a(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);a.d(t,i);let c={children:["",{children:["college",{children:["teachers-list",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,90451)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,67053)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,51995)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx"],h={require:a,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/college/teachers-list/page",pathname:"/college/teachers-list",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31981:(e,t,a)=>{"use strict";a.d(t,{$y:()=>l,cY:()=>n,hS:()=>r});var s=a(52581);function r(e,t="An error occurred. Please try again.",a=!0){let l,n=t;return e?.message?n=e.message:"string"==typeof e?n=e:e?.response?.data?.message?n=e.response.data.message:e?.data?.message&&(n=e.data.message),e?.status?l=e.status:e?.response?.status&&(l=e.response.status),n.includes("already exists")||(n.includes("Authentication")||n.includes("Unauthorized")?n="Please log in again to continue. Your session may have expired.":n.includes("Network")||n.includes("fetch")?n="Please check your internet connection and try again.":n.includes("not found")?n="The requested resource was not found.":n.includes("Forbidden")?n="You do not have permission to perform this action.":500===l?n="Server error. Please try again later.":503===l&&(n="Service temporarily unavailable. Please try again later.")),a&&s.oR.error(n),{success:!1,error:n,statusCode:l}}function l(e,t=!1,a){return t&&a&&s.oR.success(a),{success:!0,data:e}}function n(e){return!0===e.success}},33873:e=>{"use strict";e.exports=require("path")},41550:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},41862:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},51995:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});var s=a(37413),r=a(92555);function l(){return(0,s.jsx)(r.W,{message:"Loading college dashboard..."})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,a)=>{"use strict";a.d(t,{A:()=>h});var s=a(60687),r=a(43210),l=a.n(r),n=a(85814),o=a.n(n),i=a(4780),c=a(14952),d=a(93661);let h=({items:e,maxItems:t=4,className:a})=>{let r=l().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,s.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",a),children:(0,s.jsx)("ol",{className:"flex items-center space-x-1",children:r.map((e,t)=>{let a=t===r.length-1;return(0,s.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,s.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):a?(0,s.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,s.jsx)(o(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,s.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},67053:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx","default")},69885:(e,t,a)=>{Promise.resolve().then(a.bind(a,67053))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var s=a(60687);a(43210);var r=a(78148),l=a(4780);function n({className:e,...t}){return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83376:(e,t,a)=>{Promise.resolve().then(a.bind(a,90451))},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},90451:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\teachers-list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx","default")},93104:(e,t,a)=>{Promise.resolve().then(a.bind(a,7618))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[4447,4619,3287,9592,2581,1991,4654,4707,6658],()=>a(22105));module.exports=s})();