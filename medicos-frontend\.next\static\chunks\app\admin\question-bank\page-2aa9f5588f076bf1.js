(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6448],{18125:(e,t,a)=>{"use strict";function s(e){return!!e&&"string"==typeof e&&(!!/^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i.test(e)||e.length>100&&/^[A-Za-z0-9+/]*={0,2}$/.test(e))}function r(e){if(!e)return"";let t=/^(data:image\/[^;]+;base64,)(data:image\/[^;]+;base64,)/;t.test(e)&&(e=e.replace(t,"$2"));let a=e.match(/^(data:image\/[^;]+;base64,)+/);if(a){let t=a[0].length,s=e.substring(t),r=a[0].match(/data:image\/[^;]+;base64,$/);if(r)return r[0]+s}return e.startsWith("data:image/")?e:"data:image/jpeg;base64,".concat(e)}function l(e,t){if(!e)return{cleanText:e,images:[]};let a=[],l=e,i=[...e.matchAll(/<img\s+[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*\/?>/gi)];i.length>0&&i.forEach((e,t)=>{let i=e[0],n=e[1],c=e[2]||"Image ".concat(a.length+1);if((n=n.trim()).includes("base64,")||s(n)){let e=r(n);a.push({id:"extracted-image-html-".concat(t),src:e,alt:c}),l=l.replace(i,"")}});let n=[...l.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g)];n.length>0&&n.forEach((e,i)=>{let n=e[0],c=e[1]||"Image ".concat(a.length+1),o=e[2];if((o=o.trim()).includes("base64,")||s(o)){let e=r(o);a.push({id:"extracted-image-markdown-".concat(i),src:e,alt:c}),l=l.replace(n,"")}else if(t){let e=function(e,t){if(t[e])return e;for(let a of[e,e.replace(".jpeg","").replace(".jpg","").replace(".png",""),"img-".concat(e),"image-".concat(e),e.replace("img-","").replace("image-","")])for(let e of Object.keys(t))if(e.includes(a)||a.includes(e))return e;let a=e.match(/\d+/g);if(a){for(let e of a)for(let a of Object.keys(t))if(a.includes(e))return a}return null}(o,t);if(e&&t[e]){let s=r(t[e]);a.push({id:"extracted-image-ref-".concat(i),src:s,alt:c}),l=l.replace(n,"")}else l=l.replace(n,"[Missing Image: ".concat(o,"]"))}else l=l.replace(n,"[Image: ".concat(o,"]"))});let c=l.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g);c&&c.forEach((e,t)=>{if(s(e)){let s=r(e);a.push({id:"extracted-image-dataurl-".concat(t),src:s,alt:"Extracted image ".concat(a.length+1)}),l=l.replace(e,"")}});let o=l.match(/\b[A-Za-z0-9+/]{200,}={0,2}\b/g);return o&&o.forEach((e,t)=>{if(s(e)){let s=r(e);a.push({id:"extracted-image-raw-".concat(t),src:s,alt:"Extracted image ".concat(a.length+1)}),l=l.replace(e,"")}}),{cleanText:l=l.replace(/\s+/g," ").trim(),images:a}}function i(e){if(!e||!s(e))return null;try{return r(e)}catch(e){return console.warn("Invalid base64 image source:",e),null}}a.d(t,{B_:()=>i,UF:()=>r,XI:()=>s,Xw:()=>l})},19983:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>R});var s=a(95155),r=a(12115),l=a(12379),i=a(30285),n=a(84616),c=a(59409),o=a(62523),d=a(47924),m=a(27213),h=a(54416),u=a(66695),x=a(26126),g=a(13717),p=a(62525),f=a(5040),j=a(47863),b=a(66474),v=a(70463),N=a(18125),y=a(59434);function w(e){let{src:t,alt:a="Image",className:l,maxWidth:i=300,maxHeight:n=200,onClick:c}=e,[o,d]=(0,r.useState)(!1),[m,h]=(0,r.useState)(!0),u=(0,N.B_)(t);return!u||o?(0,s.jsx)("div",{className:(0,y.cn)("flex items-center justify-center bg-gray-100 border border-gray-200 rounded-md text-gray-500 text-sm",l),style:{maxWidth:i,maxHeight:Math.min(n,100)},children:o?"Failed to load image":"Invalid image"}):(0,s.jsxs)("div",{className:(0,y.cn)("relative inline-block",l),children:[m&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 border border-gray-200 rounded-md",style:{maxWidth:i,maxHeight:n},children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"})}),(0,s.jsx)("img",{src:u,alt:a,className:(0,y.cn)("rounded-md border border-gray-200 object-contain",c&&"cursor-pointer hover:opacity-80 transition-opacity",m&&"opacity-0"),style:{maxWidth:i,maxHeight:n,display:m?"none":"block"},onLoad:()=>h(!1),onError:()=>{d(!0),h(!1)},onClick:c})]})}var S=a(2603);function E(e){let{text:t,className:a}=e;if(!t)return null;let r=function(e){let t;let a=[],r=0,l=0,i=e.replace(/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g,e=>{try{let t=e.trim(),a=(t=t.replace(/<br\s*\/?>/gi," ")).split("|").map(e=>e.trim()).filter(e=>e);if(a.length<4)return e;let s=[],r=[];for(let e=0;e<a.length;e++){let t=a[e];if(t.match(/^:?-+:?$/)){r.length>0&&(s.push("| "+r.join(" | ")+" |"),r=[]);let e=Array(Math.max(2,r.length||2)).fill(":--");s.push("| "+e.join(" | ")+" |");continue}t.match(/^\d+/)&&r.length>=2?(r.length>0&&s.push("| "+r.join(" | ")+" |"),r=[t]):r.push(t)}if(r.length>0&&s.push("| "+r.join(" | ")+" |"),s.length>=3)return"\n\n"+s.join("\n")+"\n\n";return e}catch(t){return console.warn("Error fixing malformed table:",t),e}}),n=/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g,c=/<table[\s\S]*?<\/table>/gi,o=[];for(;null!==(t=n.exec(i));)o.push({start:t.index,end:t.index+t[0].length,content:t[0],type:"markdown"});for(;null!==(t=c.exec(e));)o.push({start:t.index,end:t.index+t[0].length,content:t[0],type:"html"});for(let t of(o.sort((e,t)=>e.start-t.start),o)){if(r<t.start){let s=I(e.slice(r,t.start),l);a.push(...s.elements),l=s.nextKey}if("markdown"===t.type){let e=function(e,t){let a=function(e){try{let t=e.trim().split("\n").filter(e=>e.trim());if(t.length<2)return null;let a=t[0].split("|").map(e=>e.trim()).filter(e=>e),s=t[1].split("|").map(e=>{let t=e.trim();return t.startsWith(":")&&t.endsWith(":")?"center":t.endsWith(":")?"right":"left"}).filter((e,t)=>t<a.length),r=[];for(let e=2;e<t.length;e++){let s=t[e].split("|").map(e=>e.trim()).filter(e=>e);if(s.length>0){for(;s.length<a.length;)s.push("");r.push(s.slice(0,a.length))}}return{headers:a,rows:r,alignments:s}}catch(e){return console.warn("Error parsing markdown table:",e),null}}(e);if(!a)return null;let{headers:r,rows:l,alignments:i}=a;return(0,s.jsx)("div",{className:"my-4 overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsx)("tr",{children:r.map((e,t)=>(0,s.jsx)("th",{className:(0,y.cn)("border border-gray-300 px-4 py-2 font-semibold text-gray-900","center"===i[t]&&"text-center","right"===i[t]&&"text-right","left"===i[t]&&"text-left"),children:e},t))})}),(0,s.jsx)("tbody",{children:l.map((e,t)=>(0,s.jsx)("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:e.map((e,t)=>(0,s.jsx)("td",{className:(0,y.cn)("border border-gray-300 px-4 py-2 text-gray-700","center"===i[t]&&"text-center","right"===i[t]&&"text-right","left"===i[t]&&"text-left"),children:(0,s.jsx)(E,{text:e})},t))},t))})]})},t)}(t.content,l++);e&&a.push(e)}else{var d,m;let e=(d=t.content,m=l++,(0,s.jsx)("div",{className:"my-4 overflow-x-auto",dangerouslySetInnerHTML:{__html:d.replace(/<table/g,'<table class="min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg"').replace(/<th/g,'<th class="border border-gray-300 px-4 py-2 font-semibold text-gray-900 bg-gray-50"').replace(/<td/g,'<td class="border border-gray-300 px-4 py-2 text-gray-700"')}},m));e&&a.push(e)}r=t.end}if(r<e.length){let t=I(e.slice(r),l);a.push(...t.elements)}return a}(t);return(0,s.jsx)("div",{className:(0,y.cn)("enhanced-text-renderer",a),children:r})}function I(e,t){let a=[],l=t;for(let t of e.split(/(\$\$[\s\S]*?\$\$|\$[^$]*?\$)/))if(t.startsWith("$$")&&t.endsWith("$$")){let e=t.slice(2,-2).trim();try{a.push((0,s.jsx)("div",{className:"my-2 katex-isolated",children:(0,s.jsx)(S.BlockMath,{math:e,errorColor:"#dc2626",renderError:e=>(0,s.jsxs)("div",{className:"p-2 bg-red-50 border border-red-200 rounded text-red-700",children:["Error rendering math: ",e.message]})})},l++))}catch(t){console.warn("Error rendering block math:",t),a.push((0,s.jsxs)("div",{className:"my-2 p-2 bg-red-50 border border-red-200 rounded text-red-700",children:["Error rendering math: ",e]},l++))}}else if(t.startsWith("$")&&t.endsWith("$")&&t.length>2){let e=t.slice(1,-1).trim();try{a.push((0,s.jsx)(S.InlineMath,{math:e,errorColor:"#dc2626",renderError:e=>(0,s.jsxs)("span",{className:"px-1 bg-red-50 border border-red-200 rounded text-red-700",children:["Error: ",e.message]})},l++))}catch(t){console.warn("Error rendering inline math:",t),a.push((0,s.jsxs)("span",{className:"px-1 bg-red-50 border border-red-200 rounded text-red-700",children:["Error: ",e]},l++))}}else if(t.trim()){let e=t.split("\n").map((e,t,a)=>(0,s.jsxs)(r.Fragment,{children:[e,t<a.length-1&&(0,s.jsx)("br",{})]},"".concat(l,"-line-").concat(t)));a.push((0,s.jsx)("span",{children:e},l++))}return{elements:a,nextKey:l}}function C(e){let{text:t,className:a,imageClassName:r,maxImageWidth:l=300,maxImageHeight:i=200,questionImages:n}=e,{cleanText:c,images:o}=(0,N.Xw)(t,n);return(0,s.jsxs)("div",{className:(0,y.cn)("space-y-3",a),children:[c&&(0,s.jsx)("div",{className:"text-base",children:(0,s.jsx)(E,{text:c})}),o.length>0&&(0,s.jsx)("div",{className:"space-y-2",children:o.map(e=>(0,s.jsx)("div",{className:"flex flex-col space-y-1",children:(0,s.jsx)(w,{src:e.src,alt:e.alt,className:r,maxWidth:l,maxHeight:i})},e.id))})]})}a(31491);var k=a(75478);function A(e){let{text:t,images:a={},className:l,imageClassName:n,maxImageWidth:c=400,maxImageHeight:o=300,showImageToggle:d=!0}=e,[m,h]=(0,r.useState)(!0),[u,x]=(0,r.useState)(new Set),{cleanText:g,imageRefs:p}=(e=>{let t;if(!e)return{cleanText:"",imageRefs:[]};let s=e,r=[],l=/\[CHEMICAL_IMAGE_(\d+)_([^\]]+)\]/g;for(;null!==(t=l.exec(e));){let[e,l,i]=t,n="chemical_img_".concat(l,"_").concat(i);a[n]?(r.push(n),s=s.replace(e,"[IMAGE_PLACEHOLDER_".concat(n,"]"))):s=s.replace(e,"")}return{cleanText:s=s.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,t,s)=>{let l=Object.keys(a).find(e=>e.includes(s)||s.includes(e.replace("chemical_img_","")));return l?(r.push(l),"[IMAGE_PLACEHOLDER_".concat(l,"]")):"[Missing Image: ".concat(s,"]")}),imageRefs:[...new Set(r)]}})(t),f=p.length>0,v=e=>{x(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})};return(0,s.jsxs)("div",{className:(0,y.cn)("space-y-3",l),children:[f&&d&&(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),p.length," Chemical Structure",1!==p.length?"s":""]}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>h(!m),className:"h-7 px-3 text-xs",children:m?"Hide Images":"Show Images"})]}),(0,s.jsx)("div",{className:"text-base leading-relaxed",children:g?g.split(/(\[IMAGE_PLACEHOLDER_[^\]]+\])/).map((e,t)=>{let r=e.match(/\[IMAGE_PLACEHOLDER_([^\]]+)\]/);if(r){let e=r[1],l=a[e];if(l&&m){let a=u.has(e);return(0,s.jsxs)("div",{className:"my-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-blue-700",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),"Chemical Structure"]}),(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>v(e),className:"h-6 px-2 text-blue-600 hover:text-blue-700",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Collapse"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),"Expand"]})})]}),(0,s.jsx)(w,{src:l,alt:"Chemical structure ".concat(e),className:(0,y.cn)("border border-blue-300 rounded-md transition-all duration-200",n,a?"cursor-zoom-out":"cursor-zoom-in"),maxWidth:a?1.5*c:c,maxHeight:a?1.5*o:o,onClick:()=>v(e)})]},t)}return(0,s.jsxs)("div",{className:"my-2 p-2 bg-gray-100 border border-gray-200 rounded text-sm text-gray-600",children:["[Chemical Structure - ",e,"]"]},t)}return e?(0,s.jsx)("span",{children:(0,s.jsx)(E,{text:e})},t):null}):null}),m&&p.length>0&&(0,s.jsx)("div",{className:"space-y-3",children:p.map(e=>{if(g.includes("[IMAGE_PLACEHOLDER_".concat(e,"]")))return null;let t=a[e];if(!t)return null;let r=u.has(e);return(0,s.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-green-700",children:[(0,s.jsx)(k.A,{className:"h-4 w-4"}),"Chemical Structure - ",e.replace("chemical_img_","").replace(/_/g," ")]}),(0,s.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>v(e),className:"h-6 px-2 text-green-600 hover:text-green-700",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}),"Collapse"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),"Expand"]})})]}),(0,s.jsx)(w,{src:t,alt:"Chemical structure ".concat(e),className:(0,y.cn)("border border-green-300 rounded-md transition-all duration-200",n,r?"cursor-zoom-out":"cursor-zoom-in"),maxWidth:r?1.5*c:c,maxHeight:r?1.5*o:o,onClick:()=>v(e)})]},e)})})]})}function _(e){let{option:t,images:a={},isCorrect:r=!1,className:l}=e,i=t.imageUrl&&"string"==typeof t.imageUrl&&a[t.imageUrl]||t.imageUrl;return(0,s.jsxs)("div",{className:(0,y.cn)("flex items-start p-3 rounded-md border transition-colors",r?"border-green-500 bg-green-50":"border-gray-200 hover:border-green-300",l),children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:(0,s.jsx)("span",{className:"text-sm font-medium",children:t.label})}),(0,s.jsxs)("div",{className:"flex-1",children:[t.text&&!t.isImageOption&&(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsx)(A,{text:t.text,images:a,maxImageWidth:200,maxImageHeight:150,showImageToggle:!1})}),i&&(0,s.jsx)("div",{className:t.isImageOption?"":"mt-2",children:(0,s.jsxs)("div",{className:"p-2 bg-green-50 border border-green-200 rounded-md",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2 text-xs text-green-700",children:[(0,s.jsx)(k.A,{className:"h-3 w-3"}),"Chemical Structure Option"]}),(0,s.jsx)(w,{src:i,alt:"Option ".concat(t.label," - Chemical Structure"),maxWidth:200,maxHeight:150,className:"border border-green-300 rounded"})]})}),t.isImageOption&&!i&&(0,s.jsx)("div",{className:"text-gray-500 italic text-sm",children:"Chemical structure option (image not available)"})]})]})}var U=a(90010),$=a(17809),q=a(55097),P=a(88262),z=a(35695);function O(e){let{questions:t,onDifficultyChange:a,onReviewStatusChange:l,onQuestionDeleted:n}=e,o=(0,z.useRouter)(),[d,m]=(0,r.useState)(!1),[h,N]=(0,r.useState)(null),[y,S]=(0,r.useState)(!1),[E,I]=(0,r.useState)(new Set),[k,O]=(0,r.useState)(new Set),H=e=>{I(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},W=e=>{O(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},F=e=>{N(e),m(!0)},M=e=>{o.push("/admin/edit-question/".concat(e))},D=async()=>{if(h)try{S(!0);let e=await (0,$.ul)(h);(0,q.cY)(e)&&n&&n(h)}catch(e){console.error("Unexpected error deleting question:",e),(0,P.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{S(!1),m(!1),N(null)}};return(0,s.jsxs)("div",{className:"space-y-4",children:[t.map(e=>(0,s.jsx)(u.Zp,{className:"overflow-hidden",children:(0,s.jsx)(u.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[(0,s.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,s.jsx)(x.E,{variant:"outline",className:"font-normal",children:e.subject}),(0,s.jsx)(x.E,{variant:"outline",className:"font-normal",children:e.chapter}),e.reviewStatus&&(0,s.jsx)(x.E,{className:"approved"===e.reviewStatus?"bg-green-100 text-green-800 hover:bg-green-100":"rejected"===e.reviewStatus?"bg-red-100 text-red-800 hover:bg-red-100":"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:e.reviewStatus.charAt(0).toUpperCase()+e.reviewStatus.slice(1)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(c.l6,{defaultValue:e.difficulty.toLowerCase(),onValueChange:t=>a(e.id,t),children:[(0,s.jsx)(c.bq,{className:"w-[110px]",children:(0,s.jsx)(c.yv,{placeholder:"Difficulty"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"easy",children:"Easy"}),(0,s.jsx)(c.eb,{value:"medium",children:"Medium"}),(0,s.jsx)(c.eb,{value:"hard",children:"Hard"})]})]}),(0,s.jsxs)(c.l6,{defaultValue:e.reviewStatus.toLowerCase(),onValueChange:t=>l(e.id,t),children:[(0,s.jsx)(c.bq,{className:"w-[110px]",children:(0,s.jsx)(c.yv,{placeholder:"Review Status"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"pending",children:"Pending"}),(0,s.jsx)(c.eb,{value:"approved",children:"Approved"}),(0,s.jsx)(c.eb,{value:"rejected",children:"Rejected"})]})]}),(0,s.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>M(e.id),title:"Edit question",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>F(e.id),title:"Delete question",className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,s.jsx)("div",{className:"font-medium",children:e.isChemical&&(e.chemicalImages||e.imageData)?(0,s.jsx)(A,{text:e.text,images:e.imageData||e.chemicalImages,maxImageWidth:400,maxImageHeight:300}):(0,s.jsx)(C,{text:e.text,maxImageWidth:400,maxImageHeight:300,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:e.options.map((t,a)=>e.isChemical&&(e.chemicalImages||e.imageData)?(0,s.jsx)(_,{option:t,images:e.imageData||e.chemicalImages,isCorrect:t.text===e.correctAnswer},a):(0,s.jsxs)("div",{className:"flex items-start p-3 rounded-md border ".concat(t.text===e.correctAnswer?"border-green-500 bg-green-50":"border-gray-200"),children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:(0,s.jsx)("span",{className:"text-sm",children:t.label})}),(0,s.jsxs)("div",{className:"flex-1",children:[t.text&&!t.isImageOption&&(0,s.jsx)("div",{className:"mb-2",children:(0,s.jsx)(C,{text:t.text,maxImageWidth:200,maxImageHeight:150,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})}),t.imageUrl&&(0,s.jsx)("div",{className:t.isImageOption?"":"mt-2",children:(0,s.jsx)(w,{src:t.imageUrl,alt:"Option ".concat(t.label),maxWidth:200,maxHeight:150,className:"border-0"})}),t.isImageOption&&!t.imageUrl&&(0,s.jsx)("div",{className:"text-gray-500 italic",children:"Image option"})]})]},a))}),e.solution&&(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsxs)(i.$,{variant:"ghost",onClick:()=>H(e.id),className:"flex items-center gap-2 p-0 h-auto font-medium text-blue-600 hover:text-blue-700",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),"Solution",E.has(e.id)?(0,s.jsx)(j.A,{className:"h-4 w-4"}):(0,s.jsx)(b.A,{className:"h-4 w-4"})]}),E.has(e.id)&&(0,s.jsxs)("div",{className:"mt-3 space-y-3 bg-blue-50 p-4 rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-1",children:"Methodology:"}),(0,s.jsx)("p",{className:"text-sm text-gray-700",children:e.solution.methodology})]}),e.solution.key_concepts&&e.solution.key_concepts.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-1",children:"Key Concepts:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-1",children:e.solution.key_concepts.map((e,t)=>(0,s.jsx)(x.E,{variant:"secondary",className:"text-xs",children:e},t))})]}),e.solution.steps&&e.solution.steps.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-2",children:"Solution Steps:"}),(0,s.jsx)("div",{className:"space-y-2",children:e.solution.steps.map((t,a)=>(0,s.jsx)("div",{className:"text-sm text-gray-700",children:(0,s.jsx)(C,{text:t,maxImageWidth:300,maxImageHeight:200,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})},a))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-1",children:"Final Explanation:"}),(0,s.jsx)("div",{className:"text-sm text-gray-700",children:(0,s.jsx)(C,{text:e.solution.final_explanation,maxImageWidth:300,maxImageHeight:200,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})})]})]})]}),e.hints&&e.hints.length>0&&(0,s.jsxs)("div",{className:"border-t pt-4",children:[(0,s.jsxs)(i.$,{variant:"ghost",onClick:()=>W(e.id),className:"flex items-center gap-2 p-0 h-auto font-medium text-amber-600 hover:text-amber-700",children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"Hints (",e.hints.length,")",k.has(e.id)?(0,s.jsx)(j.A,{className:"h-4 w-4"}):(0,s.jsx)(b.A,{className:"h-4 w-4"})]}),k.has(e.id)&&(0,s.jsx)("div",{className:"mt-3 space-y-2 bg-amber-50 p-4 rounded-lg",children:e.hints.map((e,t)=>(0,s.jsx)("div",{className:"text-sm text-gray-700",children:(0,s.jsx)(C,{text:e,maxImageWidth:300,maxImageHeight:200})},t))})]})]})})},e.id)),(0,s.jsx)(U.Lt,{open:d,onOpenChange:m,children:(0,s.jsxs)(U.EO,{children:[(0,s.jsxs)(U.wd,{children:[(0,s.jsx)(U.r7,{children:"Are you sure you want to delete this question?"}),(0,s.jsx)(U.$v,{children:"This action cannot be undone. This will permanently delete the question and all associated data."})]}),(0,s.jsxs)(U.ck,{children:[(0,s.jsx)(U.Zr,{children:"Cancel"}),(0,s.jsx)(U.Rx,{onClick:D,className:"bg-red-600 hover:bg-red-700",disabled:y,children:y?"Deleting...":"Delete"})]})]})})]})}var H=a(29797),W=a(68856);function F(){return(0,s.jsx)(u.Zp,{children:(0,s.jsx)(u.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(W.E,{className:"h-6 w-20"}),(0,s.jsx)(W.E,{className:"h-6 w-20"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(W.E,{className:"h-8 w-24"}),(0,s.jsx)(W.E,{className:"h-8 w-8"}),(0,s.jsx)(W.E,{className:"h-8 w-8"})]})]}),(0,s.jsx)(W.E,{className:"h-6 w-full"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,s.jsx)(W.E,{className:"h-16 w-full"}),(0,s.jsx)(W.E,{className:"h-16 w-full"}),(0,s.jsx)(W.E,{className:"h-16 w-full"}),(0,s.jsx)(W.E,{className:"h-16 w-full"})]})]})})})}function M(){let[e,t]=(0,r.useState)([]),[a,l]=(0,r.useState)([]),[n,u]=(0,r.useState)("all_subjects"),[x,g]=(0,r.useState)("all_chapters"),[p,f]=(0,r.useState)(""),[j,b]=(0,r.useState)(void 0),[v,y]=(0,r.useState)([]),[w,S]=(0,r.useState)({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:10}),[E,I]=(0,r.useState)(10),[C,k]=(0,r.useState)(!0),[A,_]=(0,r.useState)(0);(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("".concat("http://localhost:3000/api","/subjects/with-topics"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("backendToken"))}});if(!e.ok)throw Error("Failed to fetch subjects: ".concat(e.status));let a=await e.json();t(a)}catch(e){console.error("Error fetching subjects:",e),(0,P.o)({title:"Error",description:"Failed to load subjects. Please try again.",variant:"destructive"})}})()},[]),(0,r.useEffect)(()=>{if(n&&"all_subjects"!==n){let t=e.find(e=>e._id===n);t&&t.chapters?l(t.chapters):t&&t.topics?l(t.topics):l([]),g("all_chapters")}else l([])},[n,e]),(0,r.useEffect)(()=>{(async()=>{k(!0);try{let e=new URLSearchParams;n&&"all_subjects"!==n&&e.append("subjectId",n),x&&"all_chapters"!==x&&e.append("chapterId",x),p&&e.append("search",p),void 0!==j&&e.append("hasImages",j.toString()),e.append("page",w.currentPage.toString()),e.append("limit",E.toString());let t=await fetch("".concat("http://localhost:3000/api","/questions?").concat(e.toString()),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("backendToken"))}});if(!t.ok)throw Error("Failed to fetch questions: ".concat(t.status));let a=await t.json();y(a.questions),S(a.pagination)}catch(e){console.error("Error fetching questions:",e),(0,P.o)({title:"Error",description:"Failed to load questions. Please try again.",variant:"destructive"})}finally{k(!1)}})()},[n,x,p,j,w.currentPage,E]);let U=async(e,t)=>{try{let a=await fetch("".concat("http://localhost:3000/api","/questions/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("backendToken"))},body:JSON.stringify({difficulty:t})});if(!a.ok)throw Error("Failed to update question: ".concat(a.status));y(a=>a.map(a=>a._id===e?{...a,difficulty:t}:a)),(0,P.o)({title:"Success",description:"Question difficulty updated successfully"})}catch(e){console.error("Error updating question difficulty:",e),(0,P.o)({title:"Error",description:"Failed to update question difficulty",variant:"destructive"})}},q=async(e,t)=>{try{await (0,$.v5)(e,t),y(a=>a.map(a=>a._id===e?{...a,reviewStatus:t}:a)),(0,P.o)({title:"Success",description:"Question ".concat(t," successfully")})}catch(e){console.error("Error updating question review status:",e),(0,P.o)({title:"Error",description:"Failed to update question review status",variant:"destructive"})}},z=(e,t)=>{if(!e||!t||0===t.length)return e||"";let a=e;return!(a=(a=a.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,a)=>t.length>0?'<img src="'.concat(t[0],'" alt="').concat(a||"Question Image",'" style="max-width: 300px; height: auto; display: block; margin: 10px auto;" />'):e)).replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi,e=>t.length>0?'<img src="'.concat(t[0],'" alt="Question Image" style="max-width: 300px; height: auto; display: block; margin: 10px auto;" />'):e)).includes("<img")&&t.length>0&&/image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(a)&&(a+='\n<img src="'.concat(t[0],'" alt="Question Image" style="max-width: 300px; height: auto; display: block; margin: 10px auto;" />')),a},W=v.map(e=>{var t,a,s,r,l;try{let s=[],r=Array.isArray(e.options)?e.options.filter(e=>null!=e):[];return r.length>0?s="string"==typeof r[0]?1===r.length&&r[0].includes(",")?r[0].split(",").map((e,t)=>{let a=e.trim();return(0,N.XI)(a)?{label:String.fromCharCode(97+t),text:"",imageUrl:(0,N.UF)(a),isImageOption:!0}:{label:String.fromCharCode(97+t),text:a}}):r.map((e,t)=>{let a=e.trim();return(0,N.XI)(a)?{label:String.fromCharCode(97+t),text:"",imageUrl:(0,N.UF)(a),isImageOption:!0}:{label:String.fromCharCode(97+t),text:a}}):r.map((e,t)=>({label:String.fromCharCode(97+t),text:"string"==typeof e?e:e&&e.text||"",imageUrl:"object"==typeof e&&e?e.imageUrl:void 0})):(console.warn("Question ".concat(e._id," has no valid options:"),e.options),s=[{label:"a",text:"No options available"},{label:"b",text:"No options available"},{label:"c",text:"No options available"},{label:"d",text:"No options available"}]),{id:e._id,subject:e.subjectId.name,chapter:(null===(t=e.chapterId)||void 0===t?void 0:t.name)||(null===(a=e.topicId)||void 0===a?void 0:a.name)||"No Chapter",text:z(e.content,e.imageUrls||[]),options:s,difficulty:e.difficulty.charAt(0).toUpperCase()+e.difficulty.slice(1),correctAnswer:e.answer,reviewStatus:e.reviewStatus,solution:e.solution,hints:e.hints}}catch(t){return console.error("Error formatting question ".concat(e._id,":"),t,e),{id:e._id||"unknown",subject:(null===(s=e.subjectId)||void 0===s?void 0:s.name)||"Unknown Subject",chapter:(null===(r=e.chapterId)||void 0===r?void 0:r.name)||(null===(l=e.topicId)||void 0===l?void 0:l.name)||"No Chapter",text:z(e.content||"Error loading question content",e.imageUrls||[]),options:[{label:"a",text:"Error loading options"},{label:"b",text:"Error loading options"},{label:"c",text:"Error loading options"},{label:"d",text:"Error loading options"}],difficulty:e.difficulty||"Unknown",correctAnswer:e.answer||"a",reviewStatus:e.reviewStatus||"pending",solution:e.solution,hints:e.hints}}});return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Subject"}),(0,s.jsxs)(c.l6,{value:n,onValueChange:u,children:[(0,s.jsx)(c.bq,{children:(0,s.jsx)(c.yv,{placeholder:"Select Subject"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"all_subjects",children:"All Subjects"}),e.map(e=>(0,s.jsx)(c.eb,{value:e._id,children:e.name},e._id))]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Chapter"}),(0,s.jsxs)(c.l6,{value:x,onValueChange:g,disabled:"all_subjects"===n||0===a.length,children:[(0,s.jsx)(c.bq,{children:(0,s.jsx)(c.yv,{placeholder:"all_subjects"!==n?"Select Chapter":"Select Subject First"})}),(0,s.jsxs)(c.gC,{children:[(0,s.jsx)(c.eb,{value:"all_chapters",children:"All Chapters"}),a.map(e=>(0,s.jsx)(c.eb,{value:e._id,children:e.name},e._id))]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Search"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(d.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(o.p,{type:"text",placeholder:"Search questions...",className:"pl-8",value:p,onChange:e=>f(e.target.value)})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("label",{className:"text-sm font-medium",children:"Filter by images:"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(i.$,{variant:!0===j?"default":"outline",size:"sm",onClick:()=>b(!0),className:"flex items-center gap-1",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),"With Images"]}),(0,s.jsxs)(i.$,{variant:!1===j?"default":"outline",size:"sm",onClick:()=>b(!1),className:"flex items-center gap-1",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"Without Images"]}),(0,s.jsx)(i.$,{variant:void 0===j?"default":"outline",size:"sm",onClick:()=>b(void 0),children:"All Questions"})]})]})]}),C?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(F,{}),(0,s.jsx)(F,{}),(0,s.jsx)(F,{})]}):W.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(O,{questions:W,onDifficultyChange:U,onReviewStatusChange:q,onQuestionDeleted:e=>{y(t=>t.filter(t=>t._id!==e)),S(e=>({...e,totalItems:e.totalItems-1,totalPages:Math.ceil((e.totalItems-1)/E)}))}}),w.totalItems>0&&(0,s.jsx)(H.d,{currentPage:w.currentPage,totalPages:w.totalPages,pageSize:E,totalItems:w.totalItems,onPageChange:e=>{S(t=>({...t,currentPage:e}))},onPageSizeChange:e=>{I(e),S(t=>({...t,currentPage:1,itemsPerPage:e}))},pageSizeOptions:[5,10,20,50]})]}):(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No questions found. Try adjusting your filters."})})]})}var D=a(6874),L=a.n(D);let R=()=>(0,s.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Question Bank"}),(0,s.jsx)(l.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Question Bank"}],className:"text-sm mt-1"})]}),(0,s.jsx)(L(),{href:"/admin/add-question",children:(0,s.jsxs)(i.$,{className:"bg-[#05603A] hover:bg-[#04502F] text-white",children:["Add Questions",(0,s.jsx)(n.A,{className:"w-4 h-4 ml-2"})]})})]}),(0,s.jsx)("div",{className:"container mx-auto py-10",children:(0,s.jsx)(M,{})})]})})},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>c});var s=a(95155);a(12115);var r=a(66634),l=a(74466),i=a(59434);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:a,asChild:l=!1,...c}=e,o=l?r.Slot:"span";return(0,s.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(n({variant:a}),t),...c})}},29797:(e,t,a)=>{"use strict";a.d(t,{d:()=>n});var s=a(95155);a(12115);var r=a(30285),l=a(42355),i=a(13052);function n(e){let{currentPage:t,totalPages:a,onPageChange:n,pageSize:c,totalItems:o,onPageSizeChange:d,pageSizeOptions:m=[5,10,20,50]}=e,h=Math.min(o,(t-1)*c+1),u=Math.min(o,t*c);return(0,s.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,s.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:o>0?(0,s.jsxs)("p",{children:["Showing ",h," to ",u," of ",o," items"]}):(0,s.jsx)("p",{children:"No items"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[d&&(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,s.jsx)("select",{className:"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm",value:c,onChange:e=>d(Number(e.target.value)),children:m.map(e=>(0,s.jsx)("option",{value:e,children:e},e))})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>n(t-1),disabled:1===t,className:"h-8 w-8 p-0",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Previous page"})]}),(()=>{let e=[];if(a<=5)for(let t=1;t<=a;t++)e.push(t);else{e.push(1),t>3&&e.push("ellipsis");let s=Math.max(2,t-1),r=Math.min(a-1,t+1);for(let t=s;t<=r;t++)e.push(t);t<a-2&&e.push("ellipsis"),a>1&&e.push(a)}return e})().map((e,a)=>"ellipsis"===e?(0,s.jsx)("span",{className:"px-2",children:"..."},"ellipsis-".concat(a)):(0,s.jsx)(r.$,{variant:t===e?"default":"outline",size:"sm",onClick:()=>n(e),className:"h-8 w-8 p-0",children:e},"page-".concat(e))),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>n(t+1),disabled:t===a||0===a,className:"h-8 w-8 p-0",children:[(0,s.jsx)(i.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Next page"})]})]})]})]})}},68856:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(95155),r=a(59434);function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,r.cn)("bg-accent animate-pulse rounded-md",t),...a})}},87781:(e,t,a)=>{Promise.resolve().then(a.bind(a,19983))},90010:(e,t,a)=>{"use strict";a.d(t,{$v:()=>x,EO:()=>d,Lt:()=>n,Rx:()=>g,Zr:()=>p,ck:()=>h,r7:()=>u,wd:()=>m});var s=a(95155);a(12115);var r=a(35563),l=a(59434),i=a(30285);function n(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"alert-dialog",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...t})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(o,{}),(0,s.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function u(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.rc,{className:(0,l.cn)((0,i.r)(),t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.ZD,{className:(0,l.cn)((0,i.r)({variant:"outline"}),t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[562,1913,4277,6874,6671,1141,2571,6457,5631,5267,9237,8852,8441,1684,7358],()=>t(87781)),_N_E=e.O()}]);