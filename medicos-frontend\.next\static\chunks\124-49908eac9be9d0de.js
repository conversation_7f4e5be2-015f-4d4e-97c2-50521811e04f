"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[124],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5623:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15312:(e,t,n)=>{n.d(t,{C1:()=>N,bL:()=>w});var r=n(12115),o=n(95155);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}n(47650);var l=Symbol("radix.slottable");function a(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var l;let e,a;let u=(l=n,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?function(...e){return t=>{let n=!1,r=e.map(e=>{let r=i(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():i(e[t],null)}}}}(t,u):u),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:i,...l}=e,u=r.Children.toArray(i),s=u.find(a);if(s){let e=s.props.children,i=u.map(t=>t!==s?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...l,ref:n,children:i})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:i,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?n:t,{...l,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{}),s="Progress",[d,c]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[a]||l,d=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:d,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}(s),[f,p]=d(s),v=r.forwardRef((e,t)=>{var n,r,i,l;let{__scopeProgress:a,value:s=null,max:d,getValueLabel:c=y,...p}=e;(d||0===d)&&!b(d)&&console.error((n="".concat(d),r="Progress","Invalid prop `max` of value `".concat(n,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let v=b(d)?d:100;null===s||E(s,v)||console.error((i="".concat(s),l="Progress","Invalid prop `value` of value `".concat(i,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let m=E(s,v)?s:null,h=x(m)?c(m,v):void 0;return(0,o.jsx)(f,{scope:a,value:m,max:v,children:(0,o.jsx)(u.div,{"aria-valuemax":v,"aria-valuemin":0,"aria-valuenow":x(m)?m:void 0,"aria-valuetext":h,role:"progressbar","data-state":g(m,v),"data-value":null!=m?m:void 0,"data-max":v,...p,ref:t})})});v.displayName=s;var m="ProgressIndicator",h=r.forwardRef((e,t)=>{var n;let{__scopeProgress:r,...i}=e,l=p(m,r);return(0,o.jsx)(u.div,{"data-state":g(l.value,l.max),"data-value":null!==(n=l.value)&&void 0!==n?n:void 0,"data-max":l.max,...i,ref:t})});function y(e,t){return"".concat(Math.round(e/t*100),"%")}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function b(e){return x(e)&&!isNaN(e)&&e>0}function E(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=m;var w=v,N=h},27213:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},67140:(e,t,n)=>{n.d(t,{UC:()=>eT,ZL:()=>eL,bL:()=>ek,l9:()=>eD});var r,o=n(12115),i=n.t(o,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let n=!1,r=e.map(e=>{let r=a(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():a(e[t],null)}}}}function s(...e){return o.useCallback(u(...e),e)}var d=n(95155);function c(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,u=n?.[e]?.[l]||i,s=o.useMemo(()=>a,Object.values(a));return(0,d.jsx)(u.Provider,{value:s,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var f=n(47650);function p(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){var i;let e,l;let a=(i=n,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props);return n.type!==o.Fragment&&(s.ref=t?u(t,a):a),o.cloneElement(n,s)}return o.Children.count(n)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=o.forwardRef((e,n)=>{let{children:r,...i}=e,l=o.Children.toArray(r),a=l.find(m);if(a){let e=a.props.children,r=l.map(t=>t!==a?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,d.jsx)(t,{...i,ref:n,children:o.isValidElement(e)?o.cloneElement(e,void 0,r):null})}return(0,d.jsx)(t,{...i,ref:n,children:r})});return n.displayName=`${e}.Slot`,n}var v=Symbol("radix.slottable");function m(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===v}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=p(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?n:t,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function y(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var g="dismissableLayer.update",x=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{var n,i;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:c,onFocusOutside:f,onInteractOutside:p,onDismiss:v,...m}=e,b=o.useContext(x),[N,C]=o.useState(null),P=null!==(i=null==N?void 0:N.ownerDocument)&&void 0!==i?i:null===(n=globalThis)||void 0===n?void 0:n.document,[,A]=o.useState({}),R=s(t,e=>C(e)),O=Array.from(b.layers),[j]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),S=O.indexOf(j),k=N?O.indexOf(N):-1,D=b.layersWithOutsidePointerEventsDisabled.size>0,L=k>=S,T=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=y(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){w("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!L||n||(null==c||c(e),null==p||p(e),e.defaultPrevented||null==v||v())},P),_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=y(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&w("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(null==f||f(e),null==p||p(e),e.defaultPrevented||null==v||v())},P);return!function(e,t=globalThis?.document){let n=y(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{k===b.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},P),o.useEffect(()=>{if(N)return a&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(N)),b.layers.add(N),E(),()=>{a&&1===b.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[N,P,a,b]),o.useEffect(()=>()=>{N&&(b.layers.delete(N),b.layersWithOutsidePointerEventsDisabled.delete(N),E())},[N,b]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,d.jsx)(h.div,{...m,ref:R,style:{pointerEvents:D?L?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,_.onFocusCapture),onBlurCapture:l(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,T.onPointerDownCapture)})});function E(){let e=new CustomEvent(g);document.dispatchEvent(e)}function w(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),o)i&&f.flushSync(()=>i.dispatchEvent(l));else i.dispatchEvent(l)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(x),r=o.useRef(null),i=s(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,d.jsx)(h.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var N=0;function C(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},O=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...a}=e,[u,c]=o.useState(null),f=y(i),p=y(l),v=o.useRef(null),m=s(t,e=>c(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(g.paused||!u)return;let t=e.target;u.contains(t)?v.current=t:k(v.current,{select:!0})},t=function(e){if(g.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||k(v.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&k(u)});return u&&n.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,u,g.paused]),o.useEffect(()=>{if(u){D.add(g);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(P,R);u.addEventListener(P,f),u.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(k(r,{select:t}),document.activeElement!==n)return}(j(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&k(u))}return()=>{u.removeEventListener(P,f),setTimeout(()=>{let t=new CustomEvent(A,R);u.addEventListener(A,p),u.dispatchEvent(t),t.defaultPrevented||k(null!=e?e:document.body,{select:!0}),u.removeEventListener(A,p),D.remove(g)},0)}}},[u,f,p,g]);let x=o.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=j(e);return[S(t,e),S(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&k(i,{select:!0})):(e.preventDefault(),n&&k(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,d.jsx)(h.div,{tabIndex:-1,...a,ref:m,onKeyDown:x})});function j(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function S(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function k(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}O.displayName="FocusScope";var D=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=L(e,t)).unshift(t)},remove(t){var n;null===(n=(e=L(e,t))[0])||void 0===n||n.resume()}}}();function L(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var T=globalThis?.document?o.useLayoutEffect:()=>{},_=i[" useId ".trim().toString()]||(()=>void 0),M=0,F=n(84945),I=n(22475),W=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,d.jsx)(h.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});W.displayName="Arrow";var U="Popper",[$,z]=c(U),[B,H]=$(U),V=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,d.jsx)(B,{scope:t,anchor:r,onAnchorChange:i,children:n})};V.displayName=U;var K="PopperAnchor",q=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=H(K,n),a=o.useRef(null),u=s(t,a);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||a.current)}),r?null:(0,d.jsx)(h.div,{...i,ref:u})});q.displayName=K;var Y="PopperContent",[Z,X]=$(Y),G=o.forwardRef((e,t)=>{var n,r,i,l,a,u,c,f;let{__scopePopper:p,side:v="bottom",sideOffset:m=0,align:g="center",alignOffset:x=0,arrowPadding:b=0,avoidCollisions:E=!0,collisionBoundary:w=[],collisionPadding:N=0,sticky:C="partial",hideWhenDetached:P=!1,updatePositionStrategy:A="optimized",onPlaced:R,...O}=e,j=H(Y,p),[S,k]=o.useState(null),D=s(t,e=>k(e)),[L,_]=o.useState(null),M=function(e){let[t,n]=o.useState(void 0);return T(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(L),W=null!==(c=null==M?void 0:M.width)&&void 0!==c?c:0,U=null!==(f=null==M?void 0:M.height)&&void 0!==f?f:0,$="number"==typeof N?N:{top:0,right:0,bottom:0,left:0,...N},z=Array.isArray(w)?w:[w],B=z.length>0,V={padding:$,boundary:z.filter(et),altBoundary:B},{refs:K,floatingStyles:q,placement:X,isPositioned:G,middlewareData:J}=(0,F.we)({strategy:"fixed",placement:v+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,I.ll)(...t,{animationFrame:"always"===A})},elements:{reference:j.anchor},middleware:[(0,F.cY)({mainAxis:m+U,alignmentAxis:x}),E&&(0,F.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===C?(0,F.ER)():void 0,...V}),E&&(0,F.UU)({...V}),(0,F.Ej)({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),L&&(0,F.UE)({element:L,padding:b}),en({arrowWidth:W,arrowHeight:U}),P&&(0,F.jD)({strategy:"referenceHidden",...V})]}),[Q,ee]=er(X),eo=y(R);T(()=>{G&&(null==eo||eo())},[G,eo]);let ei=null===(n=J.arrow)||void 0===n?void 0:n.x,el=null===(r=J.arrow)||void 0===r?void 0:r.y,ea=(null===(i=J.arrow)||void 0===i?void 0:i.centerOffset)!==0,[eu,es]=o.useState();return T(()=>{S&&es(window.getComputedStyle(S).zIndex)},[S]),(0,d.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...q,transform:G?q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eu,"--radix-popper-transform-origin":[null===(l=J.transformOrigin)||void 0===l?void 0:l.x,null===(a=J.transformOrigin)||void 0===a?void 0:a.y].join(" "),...(null===(u=J.hide)||void 0===u?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(Z,{scope:p,placedSide:Q,onArrowChange:_,arrowX:ei,arrowY:el,shouldHideArrow:ea,children:(0,d.jsx)(h.div,{"data-side":Q,"data-align":ee,...O,ref:D,style:{...O.style,animation:G?void 0:"none"}})})})});G.displayName=Y;var J="PopperArrow",Q={top:"bottom",right:"left",bottom:"top",left:"right"},ee=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=X(J,n),i=Q[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(W,{...r,ref:t,style:{...r.style,display:"block"}})})});function et(e){return null!==e}ee.displayName=J;var en=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,d=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=er(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(i=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+c/2,y=(null!==(l=null===(o=s.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,g="",x="";return"bottom"===p?(g=d?m:"".concat(h,"px"),x="".concat(-f,"px")):"top"===p?(g=d?m:"".concat(h,"px"),x="".concat(u.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),x=d?m:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+f,"px"),x=d?m:"".concat(y,"px")),{data:{x:g,y:x}}}});function er(e){let[t,n="center"]=e.split("-");return[t,n]}var eo=o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,u]=o.useState(!1);T(()=>u(!0),[]);let s=i||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return s?f.createPortal((0,d.jsx)(h.div,{...l,ref:t}),s):null});eo.displayName="Portal";var ei=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef({}),a=o.useRef(e),u=o.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=el(l.current);u.current="mounted"===s?e:"none"},[s]),T(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=el(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),T(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=el(l.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=el(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),l=s(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||r.isPresent?o.cloneElement(i,{ref:l}):null};function el(e){return(null==e?void 0:e.animationName)||"none"}ei.displayName="Presence";var ea=n(38168),eu=n(31114),es="Popover",[ed,ec]=c(es,[z]),ef=z(),[ep,ev]=ed(es),em=e=>{let{__scopePopover:t,children:n,open:r,defaultOpen:i,onOpenChange:l,modal:a=!1}=e,u=ef(t),s=o.useRef(null),[c,f]=o.useState(!1),[p=!1,v]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[r,i]=function({defaultProp:e,onChange:t}){let n=o.useState(e),[r]=n,i=o.useRef(r),l=y(t);return o.useEffect(()=>{i.current!==r&&(l(r),i.current=r)},[r,i,l]),n}({defaultProp:t,onChange:n}),l=void 0!==e,a=l?e:r,u=y(n);return[a,o.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&u(n)}else i(t)},[l,e,i,u])]}({prop:r,defaultProp:i,onChange:l});return(0,d.jsx)(V,{...u,children:(0,d.jsx)(ep,{scope:t,contentId:function(e){let[t,n]=o.useState(_());return T(()=>{n(e=>e??String(M++))},[void 0]),e||(t?`radix-${t}`:"")}(),triggerRef:s,open:p,onOpenChange:v,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),hasCustomAnchor:c,onCustomAnchorAdd:o.useCallback(()=>f(!0),[]),onCustomAnchorRemove:o.useCallback(()=>f(!1),[]),modal:a,children:n})})};em.displayName=es;var eh="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=ev(eh,n),l=ef(n),{onCustomAnchorAdd:a,onCustomAnchorRemove:u}=i;return o.useEffect(()=>(a(),()=>u()),[a,u]),(0,d.jsx)(q,{...l,...r,ref:t})}).displayName=eh;var ey="PopoverTrigger",eg=o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=ev(ey,n),i=ef(n),a=s(t,o.triggerRef),u=(0,d.jsx)(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eS(o.open),...r,ref:a,onClick:l(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?u:(0,d.jsx)(q,{asChild:!0,...i,children:u})});eg.displayName=ey;var ex="PopoverPortal",[eb,eE]=ed(ex,{forceMount:void 0}),ew=e=>{let{__scopePopover:t,forceMount:n,children:r,container:o}=e,i=ev(ex,t);return(0,d.jsx)(eb,{scope:t,forceMount:n,children:(0,d.jsx)(ei,{present:n||i.open,children:(0,d.jsx)(eo,{asChild:!0,container:o,children:r})})})};ew.displayName=ex;var eN="PopoverContent",eC=o.forwardRef((e,t)=>{let n=eE(eN,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,i=ev(eN,e.__scopePopover);return(0,d.jsx)(ei,{present:r||i.open,children:i.modal?(0,d.jsx)(eA,{...o,ref:t}):(0,d.jsx)(eR,{...o,ref:t})})});eC.displayName=eN;var eP=p("PopoverContent.RemoveScroll"),eA=o.forwardRef((e,t)=>{let n=ev(eN,e.__scopePopover),r=o.useRef(null),i=s(t,r),a=o.useRef(!1);return o.useEffect(()=>{let e=r.current;if(e)return(0,ea.Eq)(e)},[]),(0,d.jsx)(eu.A,{as:eP,allowPinchZoom:!0,children:(0,d.jsx)(eO,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),a.current||null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;a.current=2===t.button||n},{checkForDefaultPrevented:!1}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),eR=o.forwardRef((e,t)=>{let n=ev(eN,e.__scopePopover),r=o.useRef(!1),i=o.useRef(!1);return(0,d.jsx)(eO,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,l;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{var o,l;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eO=o.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:l,disableOutsidePointerEvents:a,onEscapeKeyDown:u,onPointerDownOutside:s,onFocusOutside:c,onInteractOutside:f,...p}=e,v=ev(eN,n),m=ef(n);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:C()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:C()),N++,()=>{1===N&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),N--}},[]),(0,d.jsx)(O,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,d.jsx)(b,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:f,onEscapeKeyDown:u,onPointerDownOutside:s,onFocusOutside:c,onDismiss:()=>v.onOpenChange(!1),children:(0,d.jsx)(G,{"data-state":eS(v.open),role:"dialog",id:v.contentId,...m,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),ej="PopoverClose";function eS(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=ev(ej,n);return(0,d.jsx)(h.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})}).displayName=ej,o.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=ef(n);return(0,d.jsx)(ee,{...o,...r,ref:t})}).displayName="PopoverArrow";var ek=em,eD=eg,eL=ew,eT=eC},99890:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])}}]);