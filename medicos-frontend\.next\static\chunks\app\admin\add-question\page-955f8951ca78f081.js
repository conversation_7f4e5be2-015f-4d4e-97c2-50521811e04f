(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5489],{13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17313:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>o,av:()=>c,j7:()=>l,tU:()=>i});var a=r(95155);r(12115);var s=r(60704),n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",t),...r})}},17759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>g,MJ:()=>j,Rr:()=>f,eI:()=>x,lR:()=>p,lV:()=>c,zB:()=>u});var a=r(95155),s=r(12115),n=r(66634),i=r(62177),l=r(59434),o=r(85057);let c=i.Op,d=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},m=()=>{let e=s.useContext(d),t=s.useContext(h),{getFieldState:r}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},h=s.createContext({});function x(e){let{className:t,...r}=e,n=s.useId();return(0,a.jsx)(h.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",t),...r})})}function p(e){let{className:t,...r}=e,{error:s,formItemId:n}=m();return(0,a.jsx)(o.J,{"data-slot":"form-label","data-error":!!s,className:(0,l.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...r})}function j(e){let{...t}=e,{error:r,formItemId:s,formDescriptionId:i,formMessageId:l}=m();return(0,a.jsx)(n.Slot,{"data-slot":"form-control",id:s,"aria-describedby":r?"".concat(i," ").concat(l):"".concat(i),"aria-invalid":!!r,...t})}function f(e){let{className:t,...r}=e,{formDescriptionId:s}=m();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,l.cn)("text-muted-foreground text-sm",t),...r})}function g(e){var t;let{className:r,...s}=e,{error:n,formMessageId:i}=m(),o=n?String(null!==(t=null==n?void 0:n.message)&&void 0!==t?t:""):s.children;return o?(0,a.jsx)("p",{"data-slot":"form-message",id:i,className:(0,l.cn)("text-destructive text-sm",r),...s,children:o}):null}},25731:(e,t,r)=>{"use strict";async function a(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function s(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let r=await t.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function n(e,t){try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let a=await r.json();if(!a||!a.accessToken||!a.user||!a.user.role)throw Error("Invalid response format from server");return a}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),a=localStorage.getItem("firebaseToken"),s=localStorage.getItem("backendToken"),n={"Content-Type":"application/json",...s?{Authorization:"Bearer ".concat(s)}:a?{Authorization:"Bearer ".concat(a)}:{},...t.headers};try{let e=await fetch(r,{...t,headers:n});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let a=e.headers.get("content-type");if(a&&a.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}r.d(t,{K8:()=>s,V7:()=>a,Xw:()=>n,apiCall:()=>i})},30356:(e,t,r)=>{"use strict";r.d(t,{C:()=>o,z:()=>l});var a=r(95155);r(12115);var s=r(81575),n=r(9428),i=r(59434);function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)(s.q7,{"data-slot":"radio-group-item",className:(0,i.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,a.jsx)(s.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},46102:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>i,ZI:()=>c,k$:()=>o,m_:()=>l});var a=r(95155);r(12115);var s=r(78082),n=r(59434);function i(e){let{delayDuration:t=0,...r}=e;return(0,a.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...r})}function l(e){let{...t}=e;return(0,a.jsx)(i,{children:(0,a.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function o(e){let{...t}=e;return(0,a.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function c(e){let{className:t,sideOffset:r=0,children:i,...l}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:r,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...l,children:[i,(0,a.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},50405:(e,t,r)=>{"use strict";r.d(t,{CG:()=>n,Fb:()=>o,Nj:()=>s,getSubjectsWithTopics:()=>l,gg:()=>d,iQ:()=>c,py:()=>i});var a=r(25731);let s=async()=>{try{return await (0,a.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},n=async()=>{try{return(await (0,a.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},i=async()=>{try{return await (0,a.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},l=async()=>{try{return await (0,a.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},o=async e=>{try{return await (0,a.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},c=async(e,t)=>{try{return await (0,a.apiCall)("/subjects/".concat(e),{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error("Error updating subject ".concat(e,":"),t),Error(t.message||"Failed to update subject")}},d=async e=>{try{await (0,a.apiCall)("/subjects/".concat(e),{method:"DELETE"})}catch(t){throw console.error("Error deleting subject ".concat(e,":"),t),Error(t.message||"Failed to delete subject")}}},57434:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},60704:(e,t,r)=>{"use strict";r.d(t,{B8:()=>E,UC:()=>z,bL:()=>I,l9:()=>D});var a=r(12115),s=r(85185),n=r(46081),i=r(89196),l=r(28905),o=r(63540),c=r(94315),d=r(5845),u=r(61285),m=r(95155),h="Tabs",[x,p]=(0,n.A)(h,[i.RG]),j=(0,i.RG)(),[f,g]=x(h),v=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,onValueChange:s,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:h="automatic",...x}=e,p=(0,c.jH)(l),[j,g]=(0,d.i)({prop:a,onChange:s,defaultProp:n});return(0,m.jsx)(f,{scope:r,baseId:(0,u.B)(),value:j,onValueChange:g,orientation:i,dir:p,activationMode:h,children:(0,m.jsx)(o.sG.div,{dir:p,"data-orientation":i,...x,ref:t})})});v.displayName=h;var b="TabsList",y=a.forwardRef((e,t)=>{let{__scopeTabs:r,loop:a=!0,...s}=e,n=g(b,r),l=j(r);return(0,m.jsx)(i.bL,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:a,children:(0,m.jsx)(o.sG.div,{role:"tablist","aria-orientation":n.orientation,...s,ref:t})})});y.displayName=b;var w="TabsTrigger",N=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,disabled:n=!1,...l}=e,c=g(w,r),d=j(r),u=F(c.baseId,a),h=k(c.baseId,a),x=a===c.value;return(0,m.jsx)(i.q7,{asChild:!0,...d,focusable:!n,active:x,children:(0,m.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":h,"data-state":x?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...l,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,s.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,s.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;x||n||!e||c.onValueChange(a)})})})});N.displayName=w;var C="TabsContent",A=a.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,forceMount:n,children:i,...c}=e,d=g(C,r),u=F(d.baseId,s),h=k(d.baseId,s),x=s===d.value,p=a.useRef(x);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(l.C,{present:n||x,children:r=>{let{present:a}=r;return(0,m.jsx)(o.sG.div,{"data-state":x?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:h,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&i})}})});function F(e,t){return"".concat(e,"-trigger-").concat(t)}function k(e,t){return"".concat(e,"-content-").concat(t)}A.displayName=C;var I=v,E=y,D=N,z=A},73624:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var a=r(95155),s=r(12115),n=r(12379),i=r(66766),l=r(90221),o=r(62177),c=r(55594),d=r(13717),u=r(57434),m=r(81284),h=r(29869),x=r(54416),p=r(51154),j=r(30285),f=r(66695),g=r(17759),v=r(62523),b=r(30356),y=r(59409),w=r(88539),N=r(46102),C=r(88262),A=r(17313),F=r(50405),k=r(17809),I=r(55097);let E=c.z.object({subject:c.z.string().min(1,{message:"Please select a subject"}),chapter:c.z.string().min(1,{message:"Please select a chapter"}),questionText:c.z.string().min(5,{message:"Question must be at least 5 characters"}),optionA:c.z.string().optional(),optionB:c.z.string().optional(),optionC:c.z.string().optional(),optionD:c.z.string().optional(),correctAnswer:c.z.enum(["A","B","C","D"],{required_error:"Please select the correct answer"}),explanation:c.z.string().optional(),difficulty:c.z.enum(["Easy","Medium","Hard"],{required_error:"Please select a difficulty level"})}),D=c.z.object({subject:c.z.string().min(1,{message:"Please select a subject"}),chapter:c.z.string().optional(),aiProvider:c.z.enum(["mistral","gemini"]).default("gemini")});function z(){let[e,t]=(0,s.useState)("manual"),[r,n]=(0,s.useState)(!1),[c,z]=(0,s.useState)([]),[P,R]=(0,s.useState)(!0),[S,B]=(0,s.useState)(null),[T,q]=(0,s.useState)({A:null,B:null,C:null,D:null}),[M,_]=(0,s.useState)([]),[L,V]=(0,s.useState)({A:!1,B:!1,C:!1,D:!1}),[O,J]=(0,s.useState)([]),[G,U]=(0,s.useState)(null),[$,H]=(0,s.useState)(!1);(0,s.useEffect)(()=>{(async()=>{try{R(!0);let e=await (0,F.CG)();z(e)}catch(e){console.error("Error fetching subjects and chapters:",e),(0,C.o)({title:"Error",description:e.message||"Failed to load subjects and chapters",variant:"destructive"})}finally{R(!1)}})()},[]);let K=(0,s.useRef)(null),Q={A:(0,s.useRef)(null),B:(0,s.useRef)(null),C:(0,s.useRef)(null),D:(0,s.useRef)(null)},Z=(0,o.mN)({resolver:(0,l.u)(E),defaultValues:{subject:"",chapter:"",questionText:"",optionA:"",optionB:"",optionC:"",optionD:"",correctAnswer:"",explanation:"",difficulty:""}}),W=(0,o.mN)({resolver:(0,l.u)(D),defaultValues:{subject:"",chapter:"",aiProvider:"gemini"}}),X=e=>{Z.setValue("subject",e),Z.setValue("chapter","");let t=c.find(t=>t._id===e);t?_(t.topics||[]):_([])},Y=e=>{W.setValue("subject",e),W.setValue("chapter","");let t=c.find(t=>t._id===e);t?J(t.topics||[]):J([])},ee=(e,t)=>{var r;let a=null===(r=e.target.files)||void 0===r?void 0:r[0];if(!a)return;let s=new FileReader;s.onload=e=>{if("question"===t){var r;B(null===(r=e.target)||void 0===r?void 0:r.result)}else q(r=>{var a;return{...r,[t]:null===(a=e.target)||void 0===a?void 0:a.result}}),V(e=>({...e,[t]:!1}))},s.readAsDataURL(a)},et=e=>{if("question"===e)B(null),K.current&&(K.current.value="");else{var t;q(t=>({...t,[e]:null})),(null===(t=Q[e])||void 0===t?void 0:t.current)&&(Q[e].current.value=""),Z.clearErrors("option".concat(e)),V(t=>({...t,[e]:!1}))}},er=e=>{let t=[],r={};for(let a of["A","B","C","D"]){let s=e["option".concat(a)]&&""!==e["option".concat(a)].trim(),n=null!==T[a];s||n?r[a]=!1:(t.push("Option ".concat(a," must have either text or an image")),r[a]=!0)}return V(r),t},ea=async e=>{n(!0);try{var t,r,a,s;let i;console.log("Manual form data:",e);let l=er(e);if(l.length>0){(0,C.o)({title:"Validation Error",description:l.join(", "),variant:"destructive"}),n(!1);return}let o=[(null===(t=e.optionA)||void 0===t?void 0:t.trim())||T.A||"",(null===(r=e.optionB)||void 0===r?void 0:r.trim())||T.B||"",(null===(a=e.optionC)||void 0===a?void 0:a.trim())||T.C||"",(null===(s=e.optionD)||void 0===s?void 0:s.trim())||T.D||""],c=o[({A:0,B:1,C:2,D:3})[e.correctAnswer]],d=e.difficulty.toLowerCase(),u=localStorage.getItem("userData");try{if(u){let e=JSON.parse(u);i=e._id||e.id}}catch(e){console.error("Error parsing user data:",e)}let m={content:e.questionText,options:o,answer:c,subjectId:e.subject,topicId:e.chapter,difficulty:d,type:"multiple-choice"};i&&(m.createdBy=i);let h=e.explanation&&""!==e.explanation.trim()?{...m,explanation:e.explanation}:m,x={...h};S&&(x.content="".concat(h.content,"\n").concat(S));let p=await (0,k.hG)(x);(0,I.cY)(p)&&en()}catch(e){console.error("Unexpected error adding question:",e),(0,C.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{n(!1)}},es=async e=>{if(!G){(0,C.o)({title:"Error",description:"Please select a PDF file to upload.",variant:"destructive"});return}n(!0);try{console.log("PDF form data:",e,"Chemical extraction:",$);let t=$?await (0,k.N_)(G,e.subject,e.chapter||void 0,e.aiProvider):await (0,k.ly)(G,e.subject,e.chapter||void 0,e.aiProvider),r=$?"chemical questions with molecular structures":"questions",a=t.questionsAdded||t.questionsCreated||"questions";(0,C.o)({title:"PDF Upload Successful",description:"Successfully uploaded ".concat(a," ").concat(r," from PDF.")}),ei()}catch(e){console.error("Error uploading PDF:",e),(0,C.o)({title:"Error",description:e.message||"Failed to upload PDF. Please try again.",variant:"destructive"})}finally{n(!1)}},en=()=>{Z.reset({subject:"",chapter:"",questionText:"",optionA:"",optionB:"",optionC:"",optionD:"",correctAnswer:"",explanation:"",difficulty:""}),B(null),q({A:null,B:null,C:null,D:null}),_([]),V({A:!1,B:!1,C:!1,D:!1})},ei=()=>{W.reset({subject:"",chapter:"",aiProvider:"gemini"}),U(null),J([])};return(0,a.jsxs)(f.Zp,{className:"w-full",children:[(0,a.jsx)(f.aR,{children:(0,a.jsx)(f.ZB,{children:"Add Questions"})}),(0,a.jsx)(f.Wu,{children:(0,a.jsxs)(A.tU,{value:e,onValueChange:t,className:"w-full",children:[(0,a.jsxs)(A.j7,{className:"grid w-full grid-cols-2",children:[(0,a.jsxs)(A.Xi,{value:"manual",className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"Manual Entry"]}),(0,a.jsxs)(A.Xi,{value:"pdf",className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Upload PDF"]})]}),(0,a.jsx)(A.av,{value:"manual",className:"space-y-6",children:(0,a.jsx)(g.lV,{...Z,children:(0,a.jsxs)("form",{onSubmit:Z.handleSubmit(ea),className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(g.zB,{control:Z.control,name:"subject",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Subject *"}),(0,a.jsxs)(y.l6,{onValueChange:X,value:t.value,children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(y.bq,{children:(0,a.jsx)(y.yv,{placeholder:P?"Loading subjects...":"Select a subject"})})}),(0,a.jsx)(y.gC,{children:P?(0,a.jsx)(y.eb,{value:"loading",disabled:!0,children:"Loading subjects..."}):c.map(e=>(0,a.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:Z.control,name:"chapter",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Chapter *"}),(0,a.jsxs)(y.l6,{onValueChange:t.onChange,value:t.value,disabled:0===M.length,children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(y.bq,{children:(0,a.jsx)(y.yv,{placeholder:P?"Loading chapters...":M.length>0?"Select a chapter":"Select a subject first"})})}),(0,a.jsx)(y.gC,{children:M.map(e=>(0,a.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,a.jsx)(g.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(g.zB,{control:Z.control,name:"questionText",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Question Text *"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(w.T,{placeholder:"Enter your question here...",className:"min-h-[100px]",...t})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Question Image (Optional)"}),(0,a.jsx)(N.Bc,{children:(0,a.jsxs)(N.m_,{children:[(0,a.jsx)(N.k$,{asChild:!0,children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,a.jsx)(N.ZI,{children:(0,a.jsx)("p",{children:"Upload an image to accompany your question"})})]})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(j.$,{type:"button",variant:"outline",size:"sm",className:"h-9",onClick:()=>{var e;return null===(e=K.current)||void 0===e?void 0:e.click()},children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Upload Image"]}),(0,a.jsx)("input",{type:"file",ref:K,className:"hidden",accept:"image/*",onChange:e=>ee(e,"question")}),S&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.default,{src:S||"/placeholder.svg",alt:"Question image",width:100,height:100,className:"object-cover rounded-md border h-[100px] w-[100px]"}),(0,a.jsx)(j.$,{type:"button",variant:"destructive",size:"icon",className:"h-6 w-6 absolute -top-2 -right-2 rounded-full",onClick:()=>et("question"),children:(0,a.jsx)(x.A,{className:"h-3 w-3"})})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Answer Options"}),["A","B","C","D"].map(e=>(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0",children:[(0,a.jsx)(g.zB,{control:Z.control,name:"option".concat(e),render:t=>{let{field:r}=t;return(0,a.jsxs)(g.eI,{children:[(0,a.jsxs)(g.lR,{children:["Option ",e,T[e]&&(0,a.jsx)("span",{className:"text-sm text-green-600 ml-2",children:"(Image uploaded)"})]}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(v.p,{placeholder:T[e]?"Option ".concat(e," text (optional - image uploaded)"):"Enter option ".concat(e," text or upload an image..."),...r})}),(0,a.jsx)(g.C5,{}),L[e]&&(0,a.jsxs)("p",{className:"text-sm text-red-600",children:["Option ",e," requires either text or an image"]})]})}}),(0,a.jsx)("div",{className:"space-y-2 mt-8 md:mt-0",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(j.$,{type:"button",variant:"outline",size:"sm",className:"h-9 text-xs",onClick:()=>{var t;return null===(t=Q[e].current)||void 0===t?void 0:t.click()},children:[(0,a.jsx)(h.A,{className:"h-3 w-3 mr-1"}),"Image"]}),(0,a.jsx)("input",{type:"file",ref:Q[e],className:"hidden",accept:"image/*",onChange:t=>ee(t,e)}),T[e]&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.default,{src:T[e]||"/placeholder.svg",alt:"Option ".concat(e," image"),width:60,height:60,className:"object-cover rounded-md border h-[60px] w-[60px]"}),(0,a.jsx)(j.$,{type:"button",variant:"destructive",size:"icon",className:"h-5 w-5 absolute -top-2 -right-2 rounded-full",onClick:()=>et(e),children:(0,a.jsx)(x.A,{className:"h-3 w-3"})})]})]})})]},e))]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(g.zB,{control:Z.control,name:"correctAnswer",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{className:"space-y-3",children:[(0,a.jsx)(g.lR,{children:"Correct Answer *"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(b.z,{onValueChange:t.onChange,value:t.value||"",className:"flex space-x-4",children:["A","B","C","D"].map(e=>(0,a.jsxs)(g.eI,{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(b.C,{value:e,id:"manual-option-".concat(e)})}),(0,a.jsx)(g.lR,{className:"font-normal",htmlFor:"manual-option-".concat(e),children:e})]},e))})}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:Z.control,name:"difficulty",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{className:"space-y-3",children:[(0,a.jsx)(g.lR,{children:"Difficulty Level *"}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(b.z,{onValueChange:t.onChange,value:t.value||"",className:"flex space-x-4",children:["Easy","Medium","Hard"].map(e=>(0,a.jsxs)(g.eI,{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(b.C,{value:e,id:"manual-level-".concat(e)})}),(0,a.jsx)(g.lR,{className:"font-normal",htmlFor:"manual-level-".concat(e),children:e})]},e))})}),(0,a.jsx)(g.C5,{})]})}})]}),(0,a.jsx)(g.zB,{control:Z.control,name:"explanation",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.lR,{children:"Explanation (Optional)"}),(0,a.jsx)(N.Bc,{children:(0,a.jsxs)(N.m_,{children:[(0,a.jsx)(N.k$,{asChild:!0,children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,a.jsx)(N.ZI,{children:(0,a.jsx)("p",{children:"Provide an explanation for the correct answer"})})]})})]}),(0,a.jsx)(g.MJ,{children:(0,a.jsx)(w.T,{placeholder:"Explain why the correct answer is right...",className:"min-h-[80px]",...t})}),(0,a.jsx)(g.Rr,{children:"This will be shown to students after they answer the question."}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-2",children:[(0,a.jsx)(j.$,{type:"submit",className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:r,children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding Question..."]}):"Add Question"}),(0,a.jsx)(j.$,{type:"button",variant:"outline",className:"w-full sm:w-auto",onClick:en,disabled:r,children:"Reset Form"})]})]})})}),(0,a.jsx)(A.av,{value:"pdf",className:"space-y-6",children:(0,a.jsx)(g.lV,{...W,children:(0,a.jsxs)("form",{onSubmit:W.handleSubmit(es),className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(g.zB,{control:W.control,name:"subject",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Subject *"}),(0,a.jsxs)(y.l6,{onValueChange:Y,value:t.value,children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(y.bq,{children:(0,a.jsx)(y.yv,{placeholder:P?"Loading subjects...":"Select a subject"})})}),(0,a.jsx)(y.gC,{children:P?(0,a.jsx)(y.eb,{value:"loading",disabled:!0,children:"Loading subjects..."}):c.map(e=>(0,a.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:W.control,name:"chapter",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"Chapter (Optional)"}),(0,a.jsxs)(y.l6,{onValueChange:t.onChange,value:t.value,disabled:0===O.length,children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(y.bq,{children:(0,a.jsx)(y.yv,{placeholder:P?"Loading chapters...":O.length>0?"Select a chapter (optional)":"Select a subject first"})})}),(0,a.jsx)(y.gC,{children:O.map(e=>(0,a.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,a.jsx)(g.C5,{})]})}}),(0,a.jsx)(g.zB,{control:W.control,name:"aiProvider",render:e=>{let{field:t}=e;return(0,a.jsxs)(g.eI,{children:[(0,a.jsx)(g.lR,{children:"AI Provider"}),(0,a.jsxs)(y.l6,{onValueChange:t.onChange,value:t.value,children:[(0,a.jsx)(g.MJ,{children:(0,a.jsx)(y.bq,{children:(0,a.jsx)(y.yv,{placeholder:"Select AI provider"})})}),(0,a.jsxs)(y.gC,{children:[(0,a.jsx)(y.eb,{value:"gemini",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:"\uD83E\uDD16"}),(0,a.jsx)("span",{children:"Gemini (Recommended)"})]})}),(0,a.jsx)(y.eb,{value:"mistral",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{children:"⚡"}),(0,a.jsx)("span",{children:"Mistral"})]})})]})]}),(0,a.jsx)(g.Rr,{children:"Choose the AI provider for question extraction. Gemini is recommended for better accuracy."}),(0,a.jsx)(g.C5,{})]})}})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(g.lR,{children:"PDF File *"}),(0,a.jsx)(N.Bc,{children:(0,a.jsxs)(N.m_,{children:[(0,a.jsx)(N.k$,{asChild:!0,children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,a.jsx)(N.ZI,{children:(0,a.jsx)("p",{children:"Upload a PDF file containing questions to be extracted and added to the question bank"})})]})})]}),(0,a.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("label",{htmlFor:"pdf-upload",className:"cursor-pointer",children:[(0,a.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:G?G.name:"Choose PDF file or drag and drop"}),(0,a.jsx)("span",{className:"mt-1 block text-xs text-gray-500",children:"PDF up to 50MB"})]}),(0,a.jsx)("input",{id:"pdf-upload",type:"file",className:"sr-only",accept:".pdf",onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];if(r){if("application/pdf"!==r.type){(0,C.o)({title:"Invalid File Type",description:"Please select a PDF file.",variant:"destructive"});return}if(r.size>0x3200000){(0,C.o)({title:"File Too Large",description:"File size must be less than 50MB.",variant:"destructive"});return}U(r)}}})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>{var e;return null===(e=document.getElementById("pdf-upload"))||void 0===e?void 0:e.click()},children:"Select PDF File"})})]})}),G&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-sm text-green-800",children:G.name}),(0,a.jsxs)("span",{className:"text-xs text-green-600",children:["(",(G.size/1024/1024).toFixed(2)," MB)"]})]}),(0,a.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>U(null),children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"chemical-extraction",checked:$,onChange:e=>H(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"chemical-extraction",className:"text-sm font-medium text-gray-700",children:"Use Chemical Extraction (for Chemistry PDFs with molecular structures)"})]}),$&&(0,a.jsx)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-xs text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Chemical Extraction Mode"}),(0,a.jsx)("p",{children:"This mode is optimized for chemistry PDFs containing:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-0.5",children:[(0,a.jsx)("li",{children:"Molecular structures and chemical diagrams"}),(0,a.jsx)("li",{children:"Chemical equations and formulas"}),(0,a.jsx)("li",{children:"Reaction mechanisms"}),(0,a.jsx)("li",{children:"Complex chemical images"})]}),(0,a.jsx)("p",{className:"mt-2 text-blue-700",children:"Processing may take longer but provides better accuracy for chemical content."})]})]})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-2",children:[(0,a.jsx)(j.$,{type:"submit",className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:r||!G,children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uploading PDF..."]}):"Upload PDF"}),(0,a.jsx)(j.$,{type:"button",variant:"outline",className:"w-full sm:w-auto",onClick:ei,disabled:r,children:"Reset Form"})]})]})})})]})})]})}let P=()=>(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Add Question"}),(0,a.jsx)(n.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Add Question"}],className:"text-sm mt-1"})]}),(0,a.jsx)("div",{className:"container mx-auto py-10",children:(0,a.jsx)(z,{})})]})})},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(95155);r(12115);var s=r(40968),n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}},89196:(e,t,r)=>{"use strict";r.d(t,{RG:()=>y,bL:()=>D,q7:()=>z});var a=r(12115),s=r(85185),n=r(76589),i=r(6101),l=r(46081),o=r(61285),c=r(63540),d=r(39033),u=r(5845),m=r(94315),h=r(95155),x="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},j="RovingFocusGroup",[f,g,v]=(0,n.N)(j),[b,y]=(0,l.A)(j,[v]),[w,N]=b(j),C=a.forwardRef((e,t)=>(0,h.jsx)(f.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(f.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(A,{...e,ref:t})})}));C.displayName=j;var A=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:n,loop:l=!1,dir:o,currentTabStopId:j,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:v,onEntryFocus:b,preventScrollOnEntryFocus:y=!1,...N}=e,C=a.useRef(null),A=(0,i.s)(t,C),F=(0,m.jH)(o),[k=null,I]=(0,u.i)({prop:j,defaultProp:f,onChange:v}),[D,z]=a.useState(!1),P=(0,d.c)(b),R=g(r),S=a.useRef(!1),[B,T]=a.useState(0);return a.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(x,P),()=>e.removeEventListener(x,P)},[P]),(0,h.jsx)(w,{scope:r,orientation:n,dir:F,loop:l,currentTabStopId:k,onItemFocus:a.useCallback(e=>I(e),[I]),onItemShiftTab:a.useCallback(()=>z(!0),[]),onFocusableItemAdd:a.useCallback(()=>T(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>T(e=>e-1),[]),children:(0,h.jsx)(c.sG.div,{tabIndex:D||0===B?-1:0,"data-orientation":n,...N,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,s.m)(e.onMouseDown,()=>{S.current=!0}),onFocus:(0,s.m)(e.onFocus,e=>{let t=!S.current;if(e.target===e.currentTarget&&t&&!D){let t=new CustomEvent(x,p);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=R().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),y)}}S.current=!1}),onBlur:(0,s.m)(e.onBlur,()=>z(!1))})})}),F="RovingFocusGroupItem",k=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:n=!0,active:i=!1,tabStopId:l,...d}=e,u=(0,o.B)(),m=l||u,x=N(F,r),p=x.currentTabStopId===m,j=g(r),{onFocusableItemAdd:v,onFocusableItemRemove:b}=x;return a.useEffect(()=>{if(n)return v(),()=>b()},[n,v,b]),(0,h.jsx)(f.ItemSlot,{scope:r,id:m,focusable:n,active:i,children:(0,h.jsx)(c.sG.span,{tabIndex:p?0:-1,"data-orientation":x.orientation,...d,ref:t,onMouseDown:(0,s.m)(e.onMouseDown,e=>{n?x.onItemFocus(m):e.preventDefault()}),onFocus:(0,s.m)(e.onFocus,()=>x.onItemFocus(m)),onKeyDown:(0,s.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){x.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var a;let s=(a=e.key,"rtl"!==r?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(s)))return I[s]}(e,x.orientation,x.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=j().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let a=r.indexOf(e.currentTarget);r=x.loop?function(e,t){return e.map((r,a)=>e[(t+a)%e.length])}(r,a+1):r.slice(a+1)}setTimeout(()=>E(r))}})})})});k.displayName=F;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let a of e)if(a===r||(a.focus({preventScroll:t}),document.activeElement!==r))return}var D=C,z=k},94344:(e,t,r)=>{Promise.resolve().then(r.bind(r,73624))}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6671,1141,2571,6457,7117,221,8377,5267,8661,9140,8852,8441,1684,7358],()=>t(94344)),_N_E=e.O()}]);