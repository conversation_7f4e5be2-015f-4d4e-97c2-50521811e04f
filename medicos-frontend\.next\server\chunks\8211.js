"use strict";exports.id=8211,exports.ids=[8211],exports.modules={9005:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},14819:(e,t,r)=>{r.d(t,{C1:()=>N,bL:()=>w});var n=r(43210),o=r(60687);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}r(51215);var a=Symbol("radix.slottable");function l(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var a;let e,l;let u=(a=r,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(s.ref=t?function(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}(t,u):u),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,u=n.Children.toArray(i),s=u.find(l);if(s){let e=s.props.children,i=u.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(i?r:t,{...a,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),s="Progress",[d,c]=function(e,t=[]){let r=[],i=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return i.scopeName=e,[function(t,i){let a=n.createContext(i),l=r.length;r=[...r,i];let u=t=>{let{scope:r,children:i,...u}=t,s=r?.[e]?.[l]||a,d=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:d,children:i})};return u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e]?.[l]||a,s=n.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(i,...t)]}(s),[f,p]=d(s),m=n.forwardRef((e,t)=>{var r,n;let{__scopeProgress:i,value:a=null,max:l,getValueLabel:s=y,...d}=e;(l||0===l)&&!b(l)&&console.error((r=`${l}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=b(l)?l:100;null===a||E(a,c)||console.error((n=`${a}`,`Invalid prop \`value\` of value \`${n}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=E(a,c)?a:null,m=x(p)?s(p,c):void 0;return(0,o.jsx)(f,{scope:i,value:p,max:c,children:(0,o.jsx)(u.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":x(p)?p:void 0,"aria-valuetext":m,role:"progressbar","data-state":g(p,c),"data-value":p??void 0,"data-max":c,...d,ref:t})})});m.displayName=s;var v="ProgressIndicator",h=n.forwardRef((e,t)=>{let{__scopeProgress:r,...n}=e,i=p(v,r);return(0,o.jsx)(u.div,{"data-state":g(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...n,ref:t})});function y(e,t){return`${Math.round(e/t*100)}%`}function g(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function x(e){return"number"==typeof e}function b(e){return x(e)&&!isNaN(e)&&e>0}function E(e,t){return x(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=v;var w=m,N=h},14952:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},36141:(e,t,r)=>{r.d(t,{UC:()=>e_,ZL:()=>eT,bL:()=>eD,l9:()=>eL});var n,o=r(43210),i=r.t(o,2);function a(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function s(...e){return o.useCallback(u(...e),e)}var d=r(60687);function c(e,t=[]){let r=[],n=()=>{let t=r.map(e=>o.createContext(e));return function(r){let n=r?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let i=o.createContext(n),a=r.length;r=[...r,n];let l=t=>{let{scope:r,children:n,...l}=t,u=r?.[e]?.[a]||i,s=o.useMemo(()=>l,Object.values(l));return(0,d.jsx)(u.Provider,{value:s,children:n})};return l.displayName=t+"Provider",[l,function(r,l){let u=l?.[e]?.[a]||i,s=o.useContext(u);if(s)return s;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}var f=r(51215);function p(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){var i;let e,a;let l=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(s.ref=t?u(t,l):l),o.cloneElement(r,s)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:n,...i}=e,a=o.Children.toArray(n),l=a.find(v);if(l){let e=l.props.children,n=a.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,d.jsx)(t,{...i,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,d.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var m=Symbol("radix.slottable");function v(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=p(`Primitive.${t}`),n=o.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(o?r:t,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function y(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var g="dismissableLayer.update",x=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:u,onInteractOutside:c,onDismiss:f,...p}=e,m=o.useContext(x),[v,b]=o.useState(null),N=v?.ownerDocument??globalThis?.document,[,C]=o.useState({}),P=s(t,e=>b(e)),A=Array.from(m.layers),[R]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),O=A.indexOf(R),j=v?A.indexOf(v):-1,S=m.layersWithOutsidePointerEventsDisabled.size>0,D=j>=O,L=function(e,t=globalThis?.document){let r=y(e),n=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){w("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=n,t.addEventListener("click",i.current,{once:!0})):n()}else t.removeEventListener("click",i.current);n.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...m.branches].some(e=>e.contains(t));!D||r||(l?.(e),c?.(e),e.defaultPrevented||f?.())},N),T=function(e,t=globalThis?.document){let r=y(e),n=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!n.current&&w("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...m.branches].some(e=>e.contains(t))||(u?.(e),c?.(e),e.defaultPrevented||f?.())},N);return function(e,t=globalThis?.document){let r=y(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{j===m.layers.size-1&&(i?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},N),o.useEffect(()=>{if(v)return r&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(n=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(v)),m.layers.add(v),E(),()=>{r&&1===m.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=n)}},[v,N,r,m]),o.useEffect(()=>()=>{v&&(m.layers.delete(v),m.layersWithOutsidePointerEventsDisabled.delete(v),E())},[v,m]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,d.jsx)(h.div,{...p,ref:P,style:{pointerEvents:S?D?"auto":"none":void 0,...e.style},onFocusCapture:a(e.onFocusCapture,T.onFocusCapture),onBlurCapture:a(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:a(e.onPointerDownCapture,L.onPointerDownCapture)})});function E(){let e=new CustomEvent(g);document.dispatchEvent(e)}function w(e,t,r,{discrete:n}){let o=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&o.addEventListener(e,t,{once:!0}),n)o&&f.flushSync(()=>o.dispatchEvent(i));else o.dispatchEvent(i)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(x),n=o.useRef(null),i=s(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,d.jsx)(h.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var N=0;function C(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var P="focusScope.autoFocusOnMount",A="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},O=o.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...l}=e,[u,c]=o.useState(null),f=y(i),p=y(a),m=o.useRef(null),v=s(t,e=>c(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(n){let e=function(e){if(g.paused||!u)return;let t=e.target;u.contains(t)?m.current=t:D(m.current,{select:!0})},t=function(e){if(g.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||D(m.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&D(u)});return u&&r.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,u,g.paused]),o.useEffect(()=>{if(u){L.add(g);let e=document.activeElement;if(!u.contains(e)){let t=new CustomEvent(P,R);u.addEventListener(P,f),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(D(n,{select:t}),document.activeElement!==r)return}(j(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&D(u))}return()=>{u.removeEventListener(P,f),setTimeout(()=>{let t=new CustomEvent(A,R);u.addEventListener(A,p),u.dispatchEvent(t),t.defaultPrevented||D(e??document.body,{select:!0}),u.removeEventListener(A,p),L.remove(g)},0)}}},[u,f,p,g]);let x=o.useCallback(e=>{if(!r&&!n||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,i]=function(e){let t=j(e);return[S(t,e),S(t.reverse(),e)]}(t);n&&i?e.shiftKey||o!==i?e.shiftKey&&o===n&&(e.preventDefault(),r&&D(i,{select:!0})):(e.preventDefault(),r&&D(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,g.paused]);return(0,d.jsx)(h.div,{tabIndex:-1,...l,ref:v,onKeyDown:x})});function j(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function S(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function D(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}O.displayName="FocusScope";var L=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=T(e,t)).unshift(t)},remove(t){e=T(e,t),e[0]?.resume()}}}();function T(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var _=globalThis?.document?o.useLayoutEffect:()=>{},k=i[" useId ".trim().toString()]||(()=>void 0),F=0,M=r(4503),$=r(25605),I=o.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,d.jsx)(h.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,d.jsx)("polygon",{points:"0,0 30,0 15,10"})})});I.displayName="Arrow";var W="Popper",[U,z]=c(W),[B,H]=U(W),V=e=>{let{__scopePopper:t,children:r}=e,[n,i]=o.useState(null);return(0,d.jsx)(B,{scope:t,anchor:n,onAnchorChange:i,children:r})};V.displayName=W;var K="PopperAnchor",q=o.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:n,...i}=e,a=H(K,r),l=o.useRef(null),u=s(t,l);return o.useEffect(()=>{a.onAnchorChange(n?.current||l.current)}),n?null:(0,d.jsx)(h.div,{...i,ref:u})});q.displayName=K;var Y="PopperContent",[Z,X]=U(Y),G=o.forwardRef((e,t)=>{let{__scopePopper:r,side:n="bottom",sideOffset:i=0,align:a="center",alignOffset:l=0,arrowPadding:u=0,avoidCollisions:c=!0,collisionBoundary:f=[],collisionPadding:p=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:x,...b}=e,E=H(Y,r),[w,N]=o.useState(null),C=s(t,e=>N(e)),[P,A]=o.useState(null),R=function(e){let[t,r]=o.useState(void 0);return _(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(P),O=R?.width??0,j=R?.height??0,S="number"==typeof p?p:{top:0,right:0,bottom:0,left:0,...p},D=Array.isArray(f)?f:[f],L=D.length>0,T={padding:S,boundary:D.filter(et),altBoundary:L},{refs:k,floatingStyles:F,placement:I,isPositioned:W,middlewareData:U}=(0,M.we)({strategy:"fixed",placement:n+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(0,$.ll)(...e,{animationFrame:"always"===g}),elements:{reference:E.anchor},middleware:[(0,M.cY)({mainAxis:i+j,alignmentAxis:l}),c&&(0,M.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?(0,M.ER)():void 0,...T}),c&&(0,M.UU)({...T}),(0,M.Ej)({...T,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),P&&(0,M.UE)({element:P,padding:u}),er({arrowWidth:O,arrowHeight:j}),v&&(0,M.jD)({strategy:"referenceHidden",...T})]}),[z,B]=en(I),V=y(x);_(()=>{W&&V?.()},[W,V]);let K=U.arrow?.x,q=U.arrow?.y,X=U.arrow?.centerOffset!==0,[G,J]=o.useState();return _(()=>{w&&J(window.getComputedStyle(w).zIndex)},[w]),(0,d.jsx)("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:W?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:G,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,d.jsx)(Z,{scope:r,placedSide:z,onArrowChange:A,arrowX:K,arrowY:q,shouldHideArrow:X,children:(0,d.jsx)(h.div,{"data-side":z,"data-align":B,...b,ref:C,style:{...b.style,animation:W?void 0:"none"}})})})});G.displayName=Y;var J="PopperArrow",Q={top:"bottom",right:"left",bottom:"top",left:"right"},ee=o.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=X(J,r),i=Q[o.placedSide];return(0,d.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,d.jsx)(I,{...n,ref:t,style:{...n.style,display:"block"}})})});function et(e){return null!==e}ee.displayName=J;var er=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[u,s]=en(r),d={start:"0%",center:"50%",end:"100%"}[s],c=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+l/2,p="",m="";return"bottom"===u?(p=i?d:`${c}px`,m=`${-l}px`):"top"===u?(p=i?d:`${c}px`,m=`${n.floating.height+l}px`):"right"===u?(p=`${-l}px`,m=i?d:`${f}px`):"left"===u&&(p=`${n.floating.width+l}px`,m=i?d:`${f}px`),{data:{x:p,y:m}}}});function en(e){let[t,r="center"]=e.split("-");return[t,r]}var eo=o.forwardRef((e,t)=>{let{container:r,...n}=e,[i,a]=o.useState(!1);_(()=>a(!0),[]);let l=r||i&&globalThis?.document?.body;return l?f.createPortal((0,d.jsx)(h.div,{...n,ref:t}),l):null});eo.displayName="Portal";var ei=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,i]=o.useState(),a=o.useRef({}),l=o.useRef(e),u=o.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>r[e][t]??e,t));return o.useEffect(()=>{let e=ea(a.current);u.current="mounted"===s?e:"none"},[s]),_(()=>{let t=a.current,r=l.current;if(r!==e){let n=u.current,o=ea(t);e?d("MOUNT"):"none"===o||t?.display==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),_(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let o=ea(a.current).includes(r.animationName);if(r.target===n&&o&&(d("ANIMATION_END"),!l.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},o=e=>{e.target===n&&(u.current=ea(a.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof r?r({present:n.isPresent}):o.Children.only(r),a=s(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||n.isPresent?o.cloneElement(i,{ref:a}):null};function ea(e){return e?.animationName||"none"}ei.displayName="Presence";var el=r(63376),eu=r(11490),es="Popover",[ed,ec]=c(es,[z]),ef=z(),[ep,em]=ed(es),ev=e=>{let{__scopePopover:t,children:r,open:n,defaultOpen:i,onOpenChange:a,modal:l=!1}=e,u=ef(t),s=o.useRef(null),[c,f]=o.useState(!1),[p=!1,m]=function({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,i]=function({defaultProp:e,onChange:t}){let r=o.useState(e),[n]=r,i=o.useRef(n),a=y(t);return o.useEffect(()=>{i.current!==n&&(a(n),i.current=n)},[n,i,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,l=a?e:n,u=y(r);return[l,o.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else i(t)},[a,e,i,u])]}({prop:n,defaultProp:i,onChange:a});return(0,d.jsx)(V,{...u,children:(0,d.jsx)(ep,{scope:t,contentId:function(e){let[t,r]=o.useState(k());return _(()=>{r(e=>e??String(F++))},[void 0]),e||(t?`radix-${t}`:"")}(),triggerRef:s,open:p,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:c,onCustomAnchorAdd:o.useCallback(()=>f(!0),[]),onCustomAnchorRemove:o.useCallback(()=>f(!1),[]),modal:l,children:r})})};ev.displayName=es;var eh="PopoverAnchor";o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=em(eh,r),a=ef(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:u}=i;return o.useEffect(()=>(l(),()=>u()),[l,u]),(0,d.jsx)(q,{...a,...n,ref:t})}).displayName=eh;var ey="PopoverTrigger",eg=o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=em(ey,r),i=ef(r),l=s(t,o.triggerRef),u=(0,d.jsx)(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eS(o.open),...n,ref:l,onClick:a(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?u:(0,d.jsx)(q,{asChild:!0,...i,children:u})});eg.displayName=ey;var ex="PopoverPortal",[eb,eE]=ed(ex,{forceMount:void 0}),ew=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,i=em(ex,t);return(0,d.jsx)(eb,{scope:t,forceMount:r,children:(0,d.jsx)(ei,{present:r||i.open,children:(0,d.jsx)(eo,{asChild:!0,container:o,children:n})})})};ew.displayName=ex;var eN="PopoverContent",eC=o.forwardRef((e,t)=>{let r=eE(eN,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,i=em(eN,e.__scopePopover);return(0,d.jsx)(ei,{present:n||i.open,children:i.modal?(0,d.jsx)(eA,{...o,ref:t}):(0,d.jsx)(eR,{...o,ref:t})})});eC.displayName=eN;var eP=p("PopoverContent.RemoveScroll"),eA=o.forwardRef((e,t)=>{let r=em(eN,e.__scopePopover),n=o.useRef(null),i=s(t,n),l=o.useRef(!1);return o.useEffect(()=>{let e=n.current;if(e)return(0,el.Eq)(e)},[]),(0,d.jsx)(eu.A,{as:eP,allowPinchZoom:!0,children:(0,d.jsx)(eO,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:a(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||r.triggerRef.current?.focus()}),onPointerDownOutside:a(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;l.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:a(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),eR=o.forwardRef((e,t)=>{let r=em(eN,e.__scopePopover),n=o.useRef(!1),i=o.useRef(!1);return(0,d.jsx)(eO,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eO=o.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:a,disableOutsidePointerEvents:l,onEscapeKeyDown:u,onPointerDownOutside:s,onFocusOutside:c,onInteractOutside:f,...p}=e,m=em(eN,r),v=ef(r);return o.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??C()),document.body.insertAdjacentElement("beforeend",e[1]??C()),N++,()=>{1===N&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),N--}},[]),(0,d.jsx)(O,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,d.jsx)(b,{asChild:!0,disableOutsidePointerEvents:l,onInteractOutside:f,onEscapeKeyDown:u,onPointerDownOutside:s,onFocusOutside:c,onDismiss:()=>m.onOpenChange(!1),children:(0,d.jsx)(G,{"data-state":eS(m.open),role:"dialog",id:m.contentId,...v,...p,ref:t,style:{...p.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),ej="PopoverClose";function eS(e){return e?"open":"closed"}o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=em(ej,r);return(0,d.jsx)(h.button,{type:"button",...n,ref:t,onClick:a(e.onClick,()=>o.onOpenChange(!1))})}).displayName=ej,o.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=ef(r);return(0,d.jsx)(ee,{...o,...n,ref:t})}).displayName="PopoverArrow";var eD=ev,eL=eg,eT=ew,e_=eC},43649:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},78464:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},93661:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])}};