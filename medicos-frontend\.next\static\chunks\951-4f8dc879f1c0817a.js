"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[951],{25731:(e,r,t)=>{async function o(e){try{let r=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",r),r}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function a(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let t=await r.json();if(!t||!t.accessToken||!t.user||!t.user.role)throw Error("Invalid response format from server");return t}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function n(e,r){try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let o=await t.json();if(!o||!o.accessToken||!o.user||!o.user.role)throw Error("Invalid response format from server");return o}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),o=localStorage.getItem("firebaseToken"),a=localStorage.getItem("backendToken"),n={"Content-Type":"application/json",...a?{Authorization:"Bearer ".concat(a)}:o?{Authorization:"Bearer ".concat(o)}:{},...r.headers};try{let e=await fetch(t,{...r,headers:n});if(!e.ok){let r=await e.json().catch(()=>({}));throw Error(r.message||"API error: ".concat(e.status))}let o=e.headers.get("content-type");if(o&&o.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}t.d(r,{K8:()=>a,V7:()=>o,Xw:()=>n,apiCall:()=>s})},35695:(e,r,t)=>{var o=t(18999);t.o(o,"useParams")&&t.d(r,{useParams:function(){return o.useParams}}),t.o(o,"usePathname")&&t.d(r,{usePathname:function(){return o.usePathname}}),t.o(o,"useRouter")&&t.d(r,{useRouter:function(){return o.useRouter}})},39249:(e,r,t)=>{t.d(r,{Cl:()=>o,Tt:()=>a,fX:()=>n});var o=function(){return(o=Object.assign||function(e){for(var r,t=1,o=arguments.length;t<o;t++)for(var a in r=arguments[t])Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a]);return e}).apply(this,arguments)};function a(e,r){var t={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>r.indexOf(o)&&(t[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>r.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(t[o[a]]=e[o[a]]);return t}Object.create;function n(e,r,t){if(t||2==arguments.length)for(var o,a=0,n=r.length;a<n;a++)!o&&a in r||(o||(o=Array.prototype.slice.call(r,0,a)),o[a]=r[a]);return e.concat(o||Array.prototype.slice.call(r))}Object.create,"function"==typeof SuppressedError&&SuppressedError},51790:(e,r,t)=>{t.d(r,{AuthProvider:()=>h,A:()=>d,t:()=>f});var o=t(95155),a=t(12115),n=t(16203),s=t(23915);let c=0===(0,s.Dk)().length?(0,s.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,s.Dk)()[0],l=(0,n.xI)(c);var i=t(25731);let u=(0,a.createContext)(void 0),h=e=>{let{children:r}=e,[t,s]=(0,a.useState)(null),[c,h]=(0,a.useState)(null),[d,f]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,n.hg)(l,async e=>{if(s(e),e){let r=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",r),r)h(r);else try{console.log("No role in localStorage, trying to get from backend");let r=await e.getIdToken();localStorage.setItem("firebaseToken",r);let t=await (0,i.K8)();t&&t.user&&t.user.role&&(console.log("Got role from backend:",t.user.role),localStorage.setItem("userRole",t.user.role),h(t.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else h(null);f(!1)});return()=>e()},[]);let g=async(e,r,t)=>{try{let o=await (0,n.eJ)(l,e,r);o.user&&(await (0,n.r7)(o.user,{displayName:t}),await (0,i.V7)(o.user))}catch(e){throw console.error("Error signing up:",e),e}},p=async(e,r)=>{try{let t=await (0,n.x9)(l,e,r);await (0,i.V7)(t.user)}catch(e){throw console.error("Error logging in:",e),e}},w=async()=>{try{let e=new n.HF,r=await (0,n.df)(l,e);await (0,i.V7)(r.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},m=async()=>{try{await (0,n.CI)(l),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},y=async e=>{try{await (0,n.J1)(l,e);try{await fetch("".concat("http://localhost:3000/api","/auth/reset-password-request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},b=async()=>{try{if(!t)throw Error("No authenticated user found");await (0,i.V7)(t);try{let e=await (0,i.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},v=async()=>{try{let e=l.currentUser;e&&(await (0,n.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,o.jsx)(u.Provider,{value:{user:t,userRole:c,loading:d,signUp:g,login:p,loginWithGoogle:w,logout:m,resetPassword:y,setUserRole:e=>{localStorage.setItem("userRole",e),h(e)},handlePasswordResetCompletion:b,deleteAccount:v},children:r})};function d(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function f(){return(0,a.useContext)(u)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},61029:(e,r,t)=>{t.d(r,{A:()=>c});var o=t(95155),a=t(12115),n=t(35695),s=t(51790);function c(e){let{children:r,allowedRoles:t,redirectTo:c="/login"}=e,{user:l,userRole:i,loading:u}=(0,s.A)(),h=(0,n.useRouter)(),[d,f]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{u||(l?i&&t.includes(i)?f(!0):h.push("/admin"):h.push(c))},[l,i,u,h,c,t]),u||!d)?(0,o.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):(0,o.jsx)(o.Fragment,{children:r})}},71987:(e,r,t)=>{t.d(r,{g:()=>o});var o=function(e){return e.SUPER_ADMIN="superAdmin",e.COLLEGE_ADMIN="collegeAdmin",e.TEACHER="teacher",e}({})}}]);