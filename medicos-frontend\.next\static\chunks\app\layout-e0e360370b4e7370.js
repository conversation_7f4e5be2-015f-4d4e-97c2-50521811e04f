(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{25731:(e,t,r)=>{"use strict";async function o(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function a(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let r=await t.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function s(e,t){try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let o=await r.json();if(!o||!o.accessToken||!o.user||!o.user.role)throw Error("Invalid response format from server");return o}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),o=localStorage.getItem("firebaseToken"),a=localStorage.getItem("backendToken"),s={"Content-Type":"application/json",...a?{Authorization:"Bearer ".concat(a)}:o?{Authorization:"Bearer ".concat(o)}:{},...t.headers};try{let e=await fetch(r,{...t,headers:s});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let o=e.headers.get("content-type");if(o&&o.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}r.d(t,{K8:()=>a,V7:()=>o,Xw:()=>s,apiCall:()=>n})},30347:()=>{},36065:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var o=r(95155),a=r(432),s=r(26715),n=r(50192),i=r(12115);function l(e){let{children:t}=e,[r]=(0,i.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4}}}));return(0,o.jsxs)(s.Ht,{client:r,children:[t,(0,o.jsx)(n.E,{initialIsOpen:!1})]})}},51790:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>u,A:()=>f,t:()=>h});var o=r(95155),a=r(12115),s=r(16203),n=r(23915);let i=0===(0,n.Dk)().length?(0,n.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,n.Dk)()[0],l=(0,s.xI)(i);var c=r(25731);let d=(0,a.createContext)(void 0),u=e=>{let{children:t}=e,[r,n]=(0,a.useState)(null),[i,u]=(0,a.useState)(null),[f,h]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,s.hg)(l,async e=>{if(n(e),e){let t=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",t),t)u(t);else try{console.log("No role in localStorage, trying to get from backend");let t=await e.getIdToken();localStorage.setItem("firebaseToken",t);let r=await (0,c.K8)();r&&r.user&&r.user.role&&(console.log("Got role from backend:",r.user.role),localStorage.setItem("userRole",r.user.role),u(r.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else u(null);h(!1)});return()=>e()},[]);let g=async(e,t,r)=>{try{let o=await (0,s.eJ)(l,e,t);o.user&&(await (0,s.r7)(o.user,{displayName:r}),await (0,c.V7)(o.user))}catch(e){throw console.error("Error signing up:",e),e}},m=async(e,t)=>{try{let r=await (0,s.x9)(l,e,t);await (0,c.V7)(r.user)}catch(e){throw console.error("Error logging in:",e),e}},p=async()=>{try{let e=new s.HF,t=await (0,s.df)(l,e);await (0,c.V7)(t.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},w=async()=>{try{await (0,s.CI)(l),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},v=async e=>{try{await (0,s.J1)(l,e);try{await fetch("".concat("http://localhost:3000/api","/auth/reset-password-request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},y=async()=>{try{if(!r)throw Error("No authenticated user found");await (0,c.V7)(r);try{let e=await (0,c.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},b=async()=>{try{let e=l.currentUser;e&&(await (0,s.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,o.jsx)(d.Provider,{value:{user:r,userRole:i,loading:f,signUp:g,login:m,loginWithGoogle:p,logout:w,resetPassword:v,setUserRole:e=>{localStorage.setItem("userRole",e),u(e)},handlePasswordResetCompletion:y,deleteAccount:b},children:t})};function f(){let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function h(){return(0,a.useContext)(d)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},52558:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>v});var o=r(95155),a=r(12115),s=r(28555),n=r(74466),i=r(54416),l=r(59434);let c=s.Kq,d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)(s.LM,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...a})});d.displayName=s.LM.displayName;let u=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),f=a.forwardRef((e,t)=>{let{className:r,variant:a,...n}=e;return(0,o.jsx)(s.bL,{ref:t,className:(0,l.cn)(u({variant:a}),r),...n})});f.displayName=s.bL.displayName,a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)(s.rc,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...a})}).displayName=s.rc.displayName;let h=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)(s.bm,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...a,children:(0,o.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=s.bm.displayName;let g=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)(s.hE,{ref:t,className:(0,l.cn)("text-sm font-semibold",r),...a})});g.displayName=s.hE.displayName;let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,o.jsx)(s.VY,{ref:t,className:(0,l.cn)("text-sm opacity-90",r),...a})});m.displayName=s.VY.displayName;var p=r(88262),w=r(51790);function v(){let{toasts:e,dismiss:t}=(0,p.d)();return(0,w.t)(),(0,o.jsxs)(c,{children:[e.map(e=>{let{id:r,title:a,description:s,action:n,...i}=e;return(0,o.jsxs)(f,{...i,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[a&&(0,o.jsx)(g,{children:a}),s&&(0,o.jsx)(m,{children:s})]}),n,(0,o.jsx)(h,{onClick:()=>r&&t(r)})]},r)}),(0,o.jsx)(d,{})]})}},59434:(e,t,r)=>{"use strict";r.d(t,{b:()=>n,cn:()=>s});var o=r(52596),a=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}function n(e){return new Promise((t,r)=>{let o=new FileReader;o.readAsDataURL(e),o.onload=()=>t(o.result),o.onerror=e=>r(e)})}},88262:(e,t,r)=>{"use strict";r.d(t,{d:()=>c,o:()=>d});var o=r(12115);new EventTarget;let a=[],s=[];function n(){s.forEach(e=>e([...a]))}function i(e){let t=e.id||Math.random().toString(36).substring(2,9),r={...e,id:t};return a=[...a,r],n(),setTimeout(()=>{l(t)},5e3),t}function l(e){a=a.filter(t=>t.id!==e),n()}function c(){let[e,t]=o.useState(a);return o.useEffect(()=>(s.push(t),t([...a]),()=>{s=s.filter(e=>e!==t)}),[]),{toast:e=>i(e),dismiss:e=>{e?l(e):a.forEach(e=>e.id&&l(e.id))},toasts:e}}let d=e=>i(e)},89252:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,47017,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,52558)),Promise.resolve().then(r.bind(r,51790)),Promise.resolve().then(r.bind(r,36065))}},e=>{var t=t=>e(e.s=t);e.O(0,[2060,7690,7146,4277,685,6967,2465,1857,8441,1684,7358],()=>t(89252)),_N_E=e.O()}]);