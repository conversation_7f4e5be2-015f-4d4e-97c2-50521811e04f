(()=>{var e={};e.id=2162,e.ids=[2162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3416:(e,r,t)=>{"use strict";t.d(r,{sG:()=>u,hO:()=>m});var s=t(43210),n=t(51215),i=t(98599),a=t(60687),o=s.forwardRef((e,r)=>{let{children:t,...n}=e,i=s.Children.toArray(t),o=i.find(c);if(o){let e=o.props.children,t=i.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,a.jsx)(l,{...n,ref:r,children:t})});o.displayName="Slot";var l=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),a=function(e,r){let t={...r};for(let s in r){let n=e[s],i=r[s];/^on[A-Z]/.test(s)?n&&i?t[s]=(...e)=>{i(...e),n(...e)}:n&&(t[s]=n):"style"===s?t[s]={...n,...i}:"className"===s&&(t[s]=[n,i].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(a.ref=r?(0,i.t)(r,e):e),s.cloneElement(t,a)}return s.Children.count(t)>1?s.Children.only(null):null});l.displayName="SlotClone";var d=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===d}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...n}=e,i=s?o:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i,{...n,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function m(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},10038:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(60687),n=t(43210),i=t(85814),a=t.n(i),o=t(63442),l=t(27605),d=t(45880),c=t(52581),u=t(29523),m=t(71669),p=t(12048),f=t(60478);let x=d.Ik({email:d.Yj().email({message:"Please enter a valid email address."})});function h(){let{resetPassword:e}=(0,f.A)(),[r,t]=(0,n.useState)(!1),[i,d]=(0,n.useState)(!1),h=(0,l.mN)({resolver:(0,o.u)(x),defaultValues:{email:""}});async function g(r){t(!0);try{await e(r.email),d(!0),c.oR.success("Password reset email sent!")}catch(e){console.error("Password reset error:",e),c.oR.error(e.message||"Failed to send reset email. Please try again.")}finally{t(!1)}}return(0,s.jsxs)("div",{className:"flex min-h-screen",children:[(0,s.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center p-8 bg-white",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("img",{src:"/logo.svg",alt:"MEDICOS",className:"h-12 w-auto"}),(0,s.jsx)("h1",{className:"ml-2 text-xl font-bold text-gray-900",children:"MEDICOS"})]})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Reset Password"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Enter your email address and we'll send you a link to reset your password."}),i?(0,s.jsx)("div",{className:"rounded-md bg-green-50 p-6 border border-green-200",children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-green-500",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-green-800",children:"Reset email sent"}),(0,s.jsx)("div",{className:"mt-2 text-sm text-green-700",children:(0,s.jsx)("p",{children:"Check your email for a link to reset your password. If it doesn't appear within a few minutes, check your spam folder."})}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(a(),{href:"/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Return to login"})})]})]})}):(0,s.jsx)(m.lV,{...h,children:(0,s.jsxs)("form",{onSubmit:h.handleSubmit(g),className:"space-y-6",children:[(0,s.jsx)(m.zB,{control:h.control,name:"email",render:({field:e})=>(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{className:"text-sm font-medium text-gray-700",children:"Email Address*"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(p.p,{placeholder:"<EMAIL>",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e})}),(0,s.jsx)(m.C5,{})]})}),(0,s.jsx)(u.$,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md",disabled:r,children:r?"Sending...":"Send Reset Link"}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsx)(a(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Back to login"})})]})})]})}),(0,s.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-green-800 items-center justify-center p-12 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-10"}),(0,s.jsx)("div",{className:"relative z-10 text-center max-w-md",children:(0,s.jsxs)("blockquote",{className:"text-white text-xl font-medium",children:['"Education is the most powerful weapon which you can use to change the world."',(0,s.jsx)("footer",{className:"mt-2 text-white text-opacity-80",children:"– Nelson Mandela"})]})})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12048:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var s=t(60687);t(43210);var n=t(4780);function i({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},13379:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),n=t(48088),i=t(88170),a=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36200)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>o});var s=t(60687);t(43210);var n=t(11329),i=t(24224),a=t(4780);let o=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:i=!1,...l}){let d=i?n.Slot:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,a.cn)(o({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},36200:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx","default")},45672:(e,r,t)=>{Promise.resolve().then(t.bind(t,10038))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,r,t)=>{"use strict";t.d(r,{C5:()=>v,MJ:()=>h,Rr:()=>g,eI:()=>f,lR:()=>x,lV:()=>d,zB:()=>u});var s=t(60687),n=t(43210),i=t(11329),a=t(27605),o=t(4780),l=t(80013);let d=a.Op,c=n.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(a.xI,{...e})}),m=()=>{let e=n.useContext(c),r=n.useContext(p),{getFieldState:t}=(0,a.xW)(),s=(0,a.lN)({name:e.name}),i=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=r;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...i}},p=n.createContext({});function f({className:e,...r}){let t=n.useId();return(0,s.jsx)(p.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",e),...r})})}function x({className:e,...r}){let{error:t,formItemId:n}=m();return(0,s.jsx)(l.J,{"data-slot":"form-label","data-error":!!t,className:(0,o.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...r})}function h({...e}){let{error:r,formItemId:t,formDescriptionId:n,formMessageId:a}=m();return(0,s.jsx)(i.Slot,{"data-slot":"form-control",id:t,"aria-describedby":r?`${n} ${a}`:`${n}`,"aria-invalid":!!r,...e})}function g({className:e,...r}){let{formDescriptionId:t}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:t,className:(0,o.cn)("text-muted-foreground text-sm",e),...r})}function v({className:e,...r}){let{error:t,formMessageId:n}=m(),i=t?String(t?.message??""):r.children;return i?(0,s.jsx)("p",{"data-slot":"form-message",id:n,className:(0,o.cn)("text-destructive text-sm",e),...r,children:i}):null}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>a});var s=t(60687);t(43210);var n=t(78148),i=t(4780);function a({className:e,...r}){return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},82120:(e,r,t)=>{Promise.resolve().then(t.bind(t,36200))},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>a,t:()=>i});var s=t(43210);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function i(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}function a(...e){return s.useCallback(i(...e),e)}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4619,3287,2581,1991,3442,4707],()=>t(13379));module.exports=s})();