exports.id=4707,exports.ids=[4707],exports.modules={2604:(e,t,r)=>{Promise.resolve().then(r.bind(r,79737)),Promise.resolve().then(r.bind(r,51966)),Promise.resolve().then(r.bind(r,32957))},4780:(e,t,r)=>{"use strict";r.d(t,{b:()=>a,cn:()=>n});var o=r(49384),s=r(82348);function n(...e){return(0,s.QP)((0,o.$)(e))}function a(e){return new Promise((t,r)=>{let o=new FileReader;o.readAsDataURL(e),o.onload=()=>t(o.result),o.onerror=e=>r(e)})}},14947:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>w});var o=r(60687),s=r(43210),n=r(89667),a=r(24224),i=r(11860),l=r(4780);let c=n.Kq,d=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)(n.LM,{ref:r,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));d.displayName=n.LM.displayName;let u=(0,a.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),h=s.forwardRef(({className:e,variant:t,...r},s)=>(0,o.jsx)(n.bL,{ref:s,className:(0,l.cn)(u({variant:t}),e),...r}));h.displayName=n.bL.displayName,s.forwardRef(({className:e,...t},r)=>(0,o.jsx)(n.rc,{ref:r,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.rc.displayName;let f=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)(n.bm,{ref:r,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,o.jsx)(i.A,{className:"h-4 w-4"})}));f.displayName=n.bm.displayName;let m=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)(n.hE,{ref:r,className:(0,l.cn)("text-sm font-semibold",e),...t}));m.displayName=n.hE.displayName;let p=s.forwardRef(({className:e,...t},r)=>(0,o.jsx)(n.VY,{ref:r,className:(0,l.cn)("text-sm opacity-90",e),...t}));p.displayName=n.VY.displayName;var v=r(20140),g=r(60478);function w(){let{toasts:e,dismiss:t}=(0,v.d)();return(0,g.t)(),(0,o.jsxs)(c,{children:[e.map(({id:e,title:r,description:s,action:n,...a})=>(0,o.jsxs)(h,{...a,children:[(0,o.jsxs)("div",{className:"grid gap-1",children:[r&&(0,o.jsx)(m,{children:r}),s&&(0,o.jsx)(p,{children:s})]}),n,(0,o.jsx)(f,{onClick:()=>e&&t(e)})]},e)),(0,o.jsx)(d,{})]})}},20140:(e,t,r)=>{"use strict";r.d(t,{d:()=>c,o:()=>d});var o=r(43210);let s=[],n=[];function a(){n.forEach(e=>e([...s]))}function i(e){let t=e.id||Math.random().toString(36).substring(2,9),r={...e,id:t};return s=[...s,r],a(),setTimeout(()=>{l(t)},5e3),t}function l(e){s=s.filter(t=>t.id!==e),a()}function c(){let[e,t]=o.useState(s);return o.useEffect(()=>(n.push(t),t([...s]),()=>{n=n.filter(e=>e!==t)}),[]),{toast:e=>i(e),dismiss:e=>{e?l(e):s.forEach(e=>e.id&&l(e.id))},toasts:e}}let d=e=>i(e)},32957:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\lib\\\\ReactQueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\ReactQueryProvider.tsx","default")},51966:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var o=r(12907);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx","AuthProvider");(0,o.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx","useAuth"),(0,o.registerClientReference)(function(){throw Error("Attempted to call useSafeAuth() from the server but useSafeAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx","useSafeAuth")},53355:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var o=r(60687),s=r(25217),n=r(8693),a=r(9124),i=r(43210);function l({children:e}){let[t]=(0,i.useState)(()=>new s.E({defaultOptions:{queries:{staleTime:6e4}}}));return(0,o.jsxs)(n.Ht,{client:t,children:[e,(0,o.jsx)(a.E,{initialIsOpen:!1})]})}},60478:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>u,A:()=>h,t:()=>f});var o=r(60687),s=r(43210),n=r(4656),a=r(67989);let i=0===(0,a.Dk)().length?(0,a.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,a.Dk)()[0],l=(0,n.xI)(i);var c=r(62185);let d=(0,s.createContext)(void 0),u=({children:e})=>{let[t,r]=(0,s.useState)(null),[a,i]=(0,s.useState)(null),[u,h]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=(0,n.hg)(l,async e=>{if(r(e),e){let t=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",t),t)i(t);else try{console.log("No role in localStorage, trying to get from backend");let t=await e.getIdToken();localStorage.setItem("firebaseToken",t);let r=await (0,c.K8)();r&&r.user&&r.user.role&&(console.log("Got role from backend:",r.user.role),localStorage.setItem("userRole",r.user.role),i(r.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else i(null);h(!1)});return()=>e()},[]);let f=async(e,t,r)=>{try{let o=await (0,n.eJ)(l,e,t);o.user&&(await (0,n.r7)(o.user,{displayName:r}),await (0,c.V7)(o.user))}catch(e){throw console.error("Error signing up:",e),e}},m=async(e,t)=>{try{let r=await (0,n.x9)(l,e,t);await (0,c.V7)(r.user)}catch(e){throw console.error("Error logging in:",e),e}},p=async()=>{try{let e=new n.HF,t=await (0,n.df)(l,e);await (0,c.V7)(t.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},v=async()=>{try{await (0,n.CI)(l),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},g=async e=>{try{await (0,n.J1)(l,e);try{await fetch("http://localhost:3000/api/auth/reset-password-request",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},w=async()=>{try{if(!t)throw Error("No authenticated user found");await (0,c.V7)(t);try{let e=await (0,c.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},b=async()=>{try{let e=l.currentUser;e&&(await (0,n.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,o.jsx)(d.Provider,{value:{user:t,userRole:a,loading:u,signUp:f,login:m,loginWithGoogle:p,logout:v,resetPassword:g,setUserRole:e=>{localStorage.setItem("userRole",e),i(e)},handlePasswordResetCompletion:w,deleteAccount:b},children:e})};function h(){let e=(0,s.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function f(){return(0,s.useContext)(d)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},61135:()=>{},62185:(e,t,r)=>{"use strict";async function o(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function s(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("http://localhost:3000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||`API error: ${t.status}`)}let r=await t.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function n(e,t){try{let r=await fetch("http://localhost:3000/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||`API error: ${r.status}`)}let o=await r.json();if(!o||!o.accessToken||!o.user||!o.user.role)throw Error("Invalid response format from server");return o}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function a(e,t={}){let r=`http://localhost:3000/api${e.startsWith("/")?e:`/${e}`}`,o=localStorage.getItem("firebaseToken"),s=localStorage.getItem("backendToken"),n={"Content-Type":"application/json",...s?{Authorization:`Bearer ${s}`}:o?{Authorization:`Bearer ${o}`}:{},...t.headers};try{let e=await fetch(r,{...t,headers:n});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||`API error: ${e.status}`)}let o=e.headers.get("content-type");if(o&&o.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}r.d(t,{K8:()=>s,V7:()=>o,Xw:()=>n,apiCall:()=>a})},62483:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},67393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var o=r(37413),s=r(92555);function n(){return(0,o.jsx)(s.W,{message:"Loading application..."})}},78335:()=>{},79737:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toaster.tsx","Toaster")},89452:(e,t,r)=>{Promise.resolve().then(r.bind(r,14947)),Promise.resolve().then(r.bind(r,60478)),Promise.resolve().then(r.bind(r,53355))},92555:(e,t,r)=>{"use strict";r.d(t,{W:()=>n});var o=r(37413);r(61120);var s=r(63420);function n({message:e="Loading..."}){return(0,o.jsx)("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex flex-col items-center justify-center",children:(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center gap-4 p-6 rounded-lg bg-white shadow-lg",children:[(0,o.jsx)("div",{className:"relative h-16 w-16",children:(0,o.jsx)(s.A,{className:"h-16 w-16 animate-spin text-primary"})}),(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h3",{className:"text-lg font-medium text-primary",children:e}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Please wait while we load your content"})]})]})})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>i});var o=r(37413);r(61135);var s=r(79737),n=r(51966),a=r(32957);let i={title:"Medicos",description:"Generated by create next app"};function l({children:e}){return(0,o.jsx)("html",{lang:"en",children:(0,o.jsx)("body",{children:(0,o.jsx)(n.AuthProvider,{children:(0,o.jsxs)(a.default,{children:[e,(0,o.jsx)(s.Toaster,{})]})})})})}},96487:()=>{},99435:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))}};