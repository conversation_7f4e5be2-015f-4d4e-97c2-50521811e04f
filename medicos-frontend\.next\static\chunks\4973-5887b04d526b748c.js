"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4973],{12379:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(95155),o=r(12115),s=r(6874),n=r.n(s),i=r(59434),c=r(13052),l=r(5623);let d=e=>{let{items:t,maxItems:r=4,className:s}=e,d=o.useMemo(()=>t.length<=r?t:[t[0],{label:"..."},...t.slice(-2)],[t,r]);return(0,a.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",s),children:(0,a.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let r=t===d.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}):r?(0,a.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,a.jsx)(n(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},16640:(e,t,r)=>{r.d(t,{Kd:()=>o,OY:()=>s,Tw:()=>n,do:()=>i});var a=r(25731);let o=async(e,t)=>{try{let r=new URLSearchParams;e&&r.append("chapterId",e),t&&r.append("subjectId",t);let o=r.toString();return await (0,a.apiCall)(o?"/topics?".concat(o):"/topics")}catch(e){throw console.error("Error fetching topics:",e),Error(e.message||"Failed to fetch topics")}},s=async e=>{try{return await (0,a.apiCall)("/topics",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating topic:",e),Error(e.message||"Failed to create topic")}},n=async(e,t)=>{try{return await (0,a.apiCall)("/topics/".concat(e),{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error("Error updating topic ".concat(e,":"),t),Error(t.message||"Failed to update topic")}},i=async e=>{try{await (0,a.apiCall)("/topics/".concat(e),{method:"DELETE"})}catch(t){throw console.error("Error deleting topic ".concat(e,":"),t),Error(t.message||"Failed to delete topic")}}},25731:(e,t,r)=>{async function a(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function o(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let r=await t.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function s(e,t){try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let a=await r.json();if(!a||!a.accessToken||!a.user||!a.user.role)throw Error("Invalid response format from server");return a}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),a=localStorage.getItem("firebaseToken"),o=localStorage.getItem("backendToken"),s={"Content-Type":"application/json",...o?{Authorization:"Bearer ".concat(o)}:a?{Authorization:"Bearer ".concat(a)}:{},...t.headers};try{let e=await fetch(r,{...t,headers:s});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let a=e.headers.get("content-type");if(a&&a.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}r.d(t,{K8:()=>o,V7:()=>a,Xw:()=>s,apiCall:()=>n})},26126:(e,t,r)=>{r.d(t,{E:()=>c});var a=r(95155);r(12115);var o=r(66634),s=r(74466),n=r(59434);let i=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,asChild:s=!1,...c}=e,l=s?o.Slot:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(i({variant:r}),t),...c})}},30285:(e,t,r)=>{r.d(t,{$:()=>c,r:()=>i});var a=r(95155);r(12115);var o=r(66634),s=r(74466),n=r(59434);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:s,asChild:c=!1,...l}=e,d=c?o.Slot:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:s,className:t})),...l})}},50405:(e,t,r)=>{r.d(t,{CG:()=>s,Fb:()=>c,Nj:()=>o,getSubjectsWithTopics:()=>i,gg:()=>d,iQ:()=>l,py:()=>n});var a=r(25731);let o=async()=>{try{return await (0,a.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},s=async()=>{try{return(await (0,a.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},n=async()=>{try{return await (0,a.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},i=async()=>{try{return await (0,a.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},c=async e=>{try{return await (0,a.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},l=async(e,t)=>{try{return await (0,a.apiCall)("/subjects/".concat(e),{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error("Error updating subject ".concat(e,":"),t),Error(t.message||"Failed to update subject")}},d=async e=>{try{await (0,a.apiCall)("/subjects/".concat(e),{method:"DELETE"})}catch(t){throw console.error("Error deleting subject ".concat(e,":"),t),Error(t.message||"Failed to delete subject")}}},59434:(e,t,r)=>{r.d(t,{b:()=>n,cn:()=>s});var a=r(52596),o=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}function n(e){return new Promise((t,r)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>t(a.result),a.onerror=e=>r(e)})}},62523:(e,t,r)=>{r.d(t,{p:()=>s});var a=r(95155);r(12115);var o=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},66695:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>i,Zp:()=>s,aR:()=>n});var a=r(95155);r(12115);var o=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...r})}},85057:(e,t,r)=>{r.d(t,{J:()=>n});var a=r(95155);r(12115);var o=r(40968),s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)(o.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}}}]);