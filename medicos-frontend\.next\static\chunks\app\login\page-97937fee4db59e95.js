(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{9690:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var l=r(95155),t=r(12115),a=r(35695),o=r(6874),n=r.n(o),i=r(90221),c=r(62177),d=r(55594),m=r(78749),u=r(92657),g=r(56671),x=r(30285),h=r(17759),p=r(62523),f=r(51790),w=r(25731);let b=d.Ik({email:d.Yj().email({message:"Please enter a valid email address."}),password:d.Yj().min(6,{message:"Password must be at least 6 characters."})});function j(){let e=(0,a.useRouter)(),{login:s,loginWithGoogle:r,logout:o}=(0,f.A)(),[d,j]=(0,t.useState)(!1),[y,N]=(0,t.useState)(!1),[v,k]=(0,t.useState)(!1),C=(0,c.mN)({resolver:(0,i.u)(b),defaultValues:{email:"",password:""}});async function S(r){j(!0);try{await s(r.email,r.password);try{let s=await (0,w.Xw)(r.email,r.password);if(!s||!s.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",s.accessToken),s.user&&s.user.role){console.log("User role from backend:",s.user.role),localStorage.setItem("userRole",s.user.role);let r=localStorage.getItem("userRole");console.log("Role stored in localStorage:",r),r?(L(r),g.oR.success("Logged in successfully!")):(console.error("Role not properly stored in localStorage"),g.oR.error("Login successful but role not properly set. Please try again."),e.push("/"))}else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),g.oR.error(e.message||"Backend authentication failed. Please try again."),await o();return}}catch(e){console.error("Login error:",e),g.oR.error(e.message||"Failed to login. Please try again.")}finally{j(!1)}}async function R(){N(!0);try{await r();try{let s=await (0,w.K8)();if(!s||!s.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",s.accessToken),s.user&&s.user.role){console.log("User role from backend:",s.user.role),localStorage.setItem("userRole",s.user.role);let r=localStorage.getItem("userRole");console.log("Role stored in localStorage:",r),r?(L(r),g.oR.success("Logged in with Google successfully!")):(console.error("Role not properly stored in localStorage"),g.oR.error("Login successful but role not properly set. Please try again."),e.push("/"))}else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),g.oR.error(e.message||"Backend authentication failed. Please try again."),await o();return}}catch(e){console.error("Google login error:",e),g.oR.error(e.message||"Failed to login with Google. Please try again.")}finally{N(!1)}}function L(s){console.log("Redirecting based on role:",s);let r=s.toLowerCase();r.includes("superadmin")?e.push("/admin"):r.includes("collegeadmin")?e.push("/college"):r.includes("teacher")?e.push("/teacher"):(console.warn("Unknown role for redirection:",s),e.push("/"))}return(0,l.jsxs)("div",{className:"flex min-h-screen",children:[(0,l.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center p-8 bg-white",children:(0,l.jsxs)("div",{className:"w-full max-w-md",children:[(0,l.jsx)("div",{className:"mb-8",children:(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("img",{src:"/assets/logo/medicos-logo.svg",alt:"MEDICOS",className:"h-[70px] w-auto"})})}),(0,l.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Sign In"}),(0,l.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Enter your email and password to sign in!"}),(0,l.jsxs)("button",{type:"button",onClick:R,disabled:y,className:"w-full flex items-center justify-center gap-2 border border-gray-300 rounded-md py-2 px-4 mb-6 text-gray-700 hover:bg-gray-50 disabled:opacity-70 disabled:cursor-not-allowed",children:[y?(0,l.jsx)("div",{className:"h-5 w-5 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"}):(0,l.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)("path",{d:"M19.9895 10.1871C19.9895 9.36767 19.9214 8.76973 19.7742 8.14966H10.1992V11.848H15.8195C15.7062 12.7671 15.0943 14.1512 13.7346 15.0813L13.7155 15.2051L16.7429 17.4969L16.9527 17.5174C18.879 15.7789 19.9895 13.221 19.9895 10.1871Z",fill:"#4285F4"}),(0,l.jsx)("path",{d:"M10.1993 19.9313C12.9527 19.9313 15.2643 19.0454 16.9527 17.5174L13.7346 15.0813C12.8734 15.6682 11.7176 16.0779 10.1993 16.0779C7.50243 16.0779 5.21352 14.3395 4.39759 11.9366L4.27799 11.9466L1.13003 14.3273L1.08887 14.4391C2.76588 17.6945 6.21061 19.9313 10.1993 19.9313Z",fill:"#34A853"}),(0,l.jsx)("path",{d:"M4.39748 11.9366C4.18219 11.3166 4.05759 10.6521 4.05759 9.96565C4.05759 9.27909 4.18219 8.61473 4.38615 7.99466L4.38045 7.8626L1.19304 5.44366L1.08875 5.49214C0.397576 6.84305 0.000976562 8.36008 0.000976562 9.96565C0.000976562 11.5712 0.397576 13.0882 1.08875 14.4391L4.39748 11.9366Z",fill:"#FBBC05"}),(0,l.jsx)("path",{d:"M10.1993 3.85336C12.1142 3.85336 13.406 4.66168 14.1425 5.33717L17.0207 2.59107C15.253 0.985496 12.9527 0 10.1993 0C6.2106 0 2.76588 2.23672 1.08887 5.49214L4.38626 7.99466C5.21352 5.59183 7.50242 3.85336 10.1993 3.85336Z",fill:"#EB4335"})]}),y?"Signing in...":"Sign in with Google"]}),(0,l.jsxs)("div",{className:"flex items-center mb-6",children:[(0,l.jsx)("div",{className:"flex-grow border-t border-gray-300"}),(0,l.jsx)("span",{className:"mx-4 text-sm text-gray-500",children:"Or"}),(0,l.jsx)("div",{className:"flex-grow border-t border-gray-300"})]}),(0,l.jsx)(h.lV,{...C,children:(0,l.jsxs)("form",{onSubmit:C.handleSubmit(S),className:"space-y-4",children:[(0,l.jsx)(h.zB,{control:C.control,name:"email",render:e=>{let{field:s}=e;return(0,l.jsxs)(h.eI,{children:[(0,l.jsx)(h.lR,{className:"text-sm font-medium text-gray-700",children:"Username*"}),(0,l.jsx)(h.MJ,{children:(0,l.jsx)(p.p,{placeholder:"Enter your username",className:"w-full rounded-md border border-gray-300 py-2 px-3",...s})}),(0,l.jsx)(h.C5,{})]})}}),(0,l.jsx)(h.zB,{control:C.control,name:"password",render:e=>{let{field:s}=e;return(0,l.jsxs)(h.eI,{children:[(0,l.jsx)(h.lR,{className:"text-sm font-medium text-gray-700",children:"Password*"}),(0,l.jsx)(h.MJ,{children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(p.p,{type:v?"text":"password",className:"w-full rounded-md border border-gray-300 py-2 px-3",...s}),(0,l.jsx)("button",{type:"button",onClick:()=>k(!v),className:"absolute right-3 top-2.5 text-gray-400",children:v?(0,l.jsx)(m.A,{className:"h-5 w-5"}):(0,l.jsx)(u.A,{className:"h-5 w-5"})})]})}),(0,l.jsx)(h.C5,{})]})}}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,l.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Keep me logged in"})]}),(0,l.jsx)("div",{className:"text-sm",children:(0,l.jsx)(n(),{href:"/forgot-password",className:"font-medium text-green-600 hover:text-green-500",children:"Forgot password?"})})]}),(0,l.jsx)(x.$,{type:"submit",className:"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md",disabled:d,children:d?"Signing in...":"Sign In"}),(0,l.jsx)("div",{className:"text-center mt-4",children:(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,l.jsx)(n(),{href:"/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign Up"})]})})]})})]})}),(0,l.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-green-800 items-center justify-center p-12 relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-10"}),(0,l.jsx)("div",{className:"relative z-10 text-center max-w-md",children:(0,l.jsxs)("blockquote",{className:"text-white text-xl font-medium",children:['"Education is the most powerful weapon which you can use to change the world."',(0,l.jsx)("footer",{className:"mt-2 text-white text-opacity-80",children:"– Nelson Mandela"})]})})]})]})}},90082:(e,s,r)=>{Promise.resolve().then(r.bind(r,9690))}},e=>{var s=s=>e(e.s=s);e.O(0,[7146,4277,6874,6671,685,7117,221,8196,3954,8441,1684,7358],()=>s(90082)),_N_E=e.O()}]);