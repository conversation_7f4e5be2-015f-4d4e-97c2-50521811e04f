(()=>{var e={};e.id=7514,e.ids=[7514],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4557:(e,s,r)=>{Promise.resolve().then(r.bind(r,29488))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29488:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-college\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},48805:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>P});var t=r(60687),a=r(43210),n=r(63239),o=r(63442),i=r(27605),l=r(45880),d=r(87834),c=r(29523),m=r(71669),p=r(12048),u=r(34729),x=r(78632),h=r(20140),g=r(16189),j=r(26423),f=r(31981),v=r(4780);let b=["image/jpeg","image/jpg","image/png","image/svg+xml"],y=l.Ik({collegeName:l.Yj().min(2,{message:"College name must be at least 2 characters."}),phone:l.Yj().min(10,{message:"Phone number must be valid."}),email:l.Yj().email({message:"Please enter a valid email address."}),address:l.Yj().max(100,{message:"Address must not exceed 100 characters."}),logo:l.bz().optional().refine(e=>!e||0===e.length||e[0]?.size<=0x3200000,"Max file size is 50MB.").refine(e=>!e||0===e.length||b.includes(e[0]?.type),"Only .jpg, .jpeg, .png and .svg formats are supported.")});function C(){let[e,s]=(0,a.useState)([]),[r,n]=(0,a.useState)(!1),l=(0,g.useRouter)(),C=(0,i.mN)({resolver:(0,o.u)(y),defaultValues:{collegeName:"",phone:"",email:"",address:""}});async function P(s){n(!0);try{let r="";e.length>0&&(r=await (0,v.b)(e[0]));let t={name:s.collegeName,address:s.address,contactPhone:s.phone,contactEmail:s.email,logoUrl:r},a=await (0,j.M0)(t);(0,f.cY)(a)&&(q(),l.push("/admin/college"))}catch(e){console.error("Unexpected error adding college:",e),(0,h.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{n(!1)}}function q(){C.reset(),s([])}return(0,t.jsx)(m.lV,{...C,children:(0,t.jsxs)("form",{onSubmit:C.handleSubmit(P),className:"space-y-8 mx-auto",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.zB,{control:C.control,name:"collegeName",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"College name"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(p.p,{placeholder:"",...e})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:C.control,name:"phone",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsxs)(m.lR,{children:["Phone",(0,t.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Required"})]}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(x.L,{...e})}),(0,t.jsx)(m.C5,{})]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(m.zB,{control:C.control,name:"email",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsx)(m.lR,{children:"Email address"}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(p.p,{placeholder:"",...e})}),(0,t.jsx)(m.Rr,{children:"We'll never share your details."}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsx)(m.zB,{control:C.control,name:"address",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(m.lR,{children:"Address details"}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.value.length,"/100"]})]}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(u.T,{placeholder:"",className:"resize-none",maxLength:100,...e})}),(0,t.jsx)(m.C5,{})]})})]}),(0,t.jsx)(m.zB,{control:C.control,name:"logo",render:({field:e})=>(0,t.jsxs)(m.eI,{children:[(0,t.jsxs)(m.lR,{children:["Upload logo",(0,t.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Optional"})]}),(0,t.jsx)(m.MJ,{children:(0,t.jsx)(d.l,{value:e.value,onChange:r=>{e.onChange(r),s(Array.from(r||[]))},maxSize:0x3200000,acceptedTypes:b})}),(0,t.jsx)(m.C5,{})]})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)(c.$,{type:"submit",className:"bg-[#05603A] hover:bg-[#04502F]",disabled:r,children:r?"Adding...":"Add college"}),(0,t.jsx)(c.$,{type:"button",variant:"destructive",onClick:()=>C.reset(),disabled:r,children:"Cancel"}),(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:q,disabled:r,children:"Reset"})]})]})})}let P=()=>(0,t.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Add Colleges"}),(0,t.jsx)(n.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Add Colleges"}],className:"text-sm mt-1"})]}),(0,t.jsx)("div",{className:"container mx-auto py-10",children:(0,t.jsx)(C,{})})]})})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70507:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let d={children:["",{children:["admin",{children:["add-college",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29488)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/add-college/page",pathname:"/admin/add-college",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85997:(e,s,r)=>{Promise.resolve().then(r.bind(r,48805))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,4619,3287,9592,2581,1991,3442,8211,4707,6658,1037],()=>r(70507));module.exports=t})();