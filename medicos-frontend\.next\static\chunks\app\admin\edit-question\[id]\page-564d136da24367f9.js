(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5400],{17759:(e,t,r)=>{"use strict";r.d(t,{C5:()=>j,MJ:()=>g,Rr:()=>f,eI:()=>m,lR:()=>x,lV:()=>c,zB:()=>u});var a=r(95155),s=r(12115),n=r(66634),i=r(62177),o=r(59434),l=r(85057);let c=i.Op,d=s.createContext({}),u=e=>{let{...t}=e;return(0,a.jsx)(d.Provider,{value:{name:t.name},children:(0,a.jsx)(i.xI,{...t})})},h=()=>{let e=s.useContext(d),t=s.useContext(p),{getFieldState:r}=(0,i.xW)(),a=(0,i.lN)({name:e.name}),n=r(e.name,a);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...n}},p=s.createContext({});function m(e){let{className:t,...r}=e,n=s.useId();return(0,a.jsx)(p.Provider,{value:{id:n},children:(0,a.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...r})})}function x(e){let{className:t,...r}=e,{error:s,formItemId:n}=h();return(0,a.jsx)(l.J,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...r})}function g(e){let{...t}=e,{error:r,formItemId:s,formDescriptionId:i,formMessageId:o}=h();return(0,a.jsx)(n.Slot,{"data-slot":"form-control",id:s,"aria-describedby":r?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!r,...t})}function f(e){let{className:t,...r}=e,{formDescriptionId:s}=h();return(0,a.jsx)("p",{"data-slot":"form-description",id:s,className:(0,o.cn)("text-muted-foreground text-sm",t),...r})}function j(e){var t;let{className:r,...s}=e,{error:n,formMessageId:i}=h(),l=n?String(null!==(t=null==n?void 0:n.message)&&void 0!==t?t:""):s.children;return l?(0,a.jsx)("p",{"data-slot":"form-message",id:i,className:(0,o.cn)("text-destructive text-sm",r),...s,children:l}):null}},18125:(e,t,r)=>{"use strict";function a(e){return!!e&&"string"==typeof e&&(!!/^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i.test(e)||e.length>100&&/^[A-Za-z0-9+/]*={0,2}$/.test(e))}function s(e){if(!e)return"";let t=/^(data:image\/[^;]+;base64,)(data:image\/[^;]+;base64,)/;t.test(e)&&(e=e.replace(t,"$2"));let r=e.match(/^(data:image\/[^;]+;base64,)+/);if(r){let t=r[0].length,a=e.substring(t),s=r[0].match(/data:image\/[^;]+;base64,$/);if(s)return s[0]+a}return e.startsWith("data:image/")?e:"data:image/jpeg;base64,".concat(e)}function n(e,t){if(!e)return{cleanText:e,images:[]};let r=[],n=e,i=[...e.matchAll(/<img\s+[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*\/?>/gi)];i.length>0&&i.forEach((e,t)=>{let i=e[0],o=e[1],l=e[2]||"Image ".concat(r.length+1);if((o=o.trim()).includes("base64,")||a(o)){let e=s(o);r.push({id:"extracted-image-html-".concat(t),src:e,alt:l}),n=n.replace(i,"")}});let o=[...n.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g)];o.length>0&&o.forEach((e,i)=>{let o=e[0],l=e[1]||"Image ".concat(r.length+1),c=e[2];if((c=c.trim()).includes("base64,")||a(c)){let e=s(c);r.push({id:"extracted-image-markdown-".concat(i),src:e,alt:l}),n=n.replace(o,"")}else if(t){let e=function(e,t){if(t[e])return e;for(let r of[e,e.replace(".jpeg","").replace(".jpg","").replace(".png",""),"img-".concat(e),"image-".concat(e),e.replace("img-","").replace("image-","")])for(let e of Object.keys(t))if(e.includes(r)||r.includes(e))return e;let r=e.match(/\d+/g);if(r){for(let e of r)for(let r of Object.keys(t))if(r.includes(e))return r}return null}(c,t);if(e&&t[e]){let a=s(t[e]);r.push({id:"extracted-image-ref-".concat(i),src:a,alt:l}),n=n.replace(o,"")}else n=n.replace(o,"[Missing Image: ".concat(c,"]"))}else n=n.replace(o,"[Image: ".concat(c,"]"))});let l=n.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g);l&&l.forEach((e,t)=>{if(a(e)){let a=s(e);r.push({id:"extracted-image-dataurl-".concat(t),src:a,alt:"Extracted image ".concat(r.length+1)}),n=n.replace(e,"")}});let c=n.match(/\b[A-Za-z0-9+/]{200,}={0,2}\b/g);return c&&c.forEach((e,t)=>{if(a(e)){let a=s(e);r.push({id:"extracted-image-raw-".concat(t),src:a,alt:"Extracted image ".concat(r.length+1)}),n=n.replace(e,"")}}),{cleanText:n=n.replace(/\s+/g," ").trim(),images:r}}function i(e){if(!e||!a(e))return null;try{return s(e)}catch(e){return console.warn("Invalid base64 image source:",e),null}}r.d(t,{B_:()=>i,UF:()=>s,XI:()=>a,Xw:()=>n})},19576:(e,t,r)=>{Promise.resolve().then(r.bind(r,45027))},25731:(e,t,r)=>{"use strict";async function a(e){try{let t=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",t),t}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function s(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let r=await t.json();if(!r||!r.accessToken||!r.user||!r.user.role)throw Error("Invalid response format from server");return r}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function n(e,t){try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let a=await r.json();if(!a||!a.accessToken||!a.user||!a.user.role)throw Error("Invalid response format from server");return a}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),a=localStorage.getItem("firebaseToken"),s=localStorage.getItem("backendToken"),n={"Content-Type":"application/json",...s?{Authorization:"Bearer ".concat(s)}:a?{Authorization:"Bearer ".concat(a)}:{},...t.headers};try{let e=await fetch(r,{...t,headers:n});if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"API error: ".concat(e.status))}let a=e.headers.get("content-type");if(a&&a.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}r.d(t,{K8:()=>s,V7:()=>a,Xw:()=>n,apiCall:()=>i})},30356:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,z:()=>o});var a=r(95155);r(12115);var s=r(81575),n=r(9428),i=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)(s.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)(s.q7,{"data-slot":"radio-group-item",className:(0,i.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,a.jsx)(s.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},45027:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var a=r(95155),s=r(12115),n=r(12379),i=r(66766),o=r(35695),l=r(90221),c=r(62177),d=r(55594),u=r(29869),h=r(54416),p=r(81284),m=r(51154),x=r(30285),g=r(66695),f=r(17759),j=r(62523),b=r(30356),v=r(59409),y=r(88539),w=r(46102),C=r(88262),N=r(50405),E=r(17809),I=r(18125);let A=["image/jpeg","image/jpg","image/png","image/svg+xml"],k=d.z.object({subject:d.z.string().min(1,{message:"Please select a subject"}),topic:d.z.string().min(1,{message:"Please select a chapter"}),questionText:d.z.string().min(5,{message:"Question must be at least 5 characters"}),optionA:d.z.string().optional(),optionB:d.z.string().optional(),optionC:d.z.string().optional(),optionD:d.z.string().optional(),correctAnswer:d.z.enum(["A","B","C","D"],{required_error:"Please select the correct answer"}),explanation:d.z.string().optional(),difficulty:d.z.enum(["Easy","Medium","Hard"],{required_error:"Please select a difficulty level"})});function z(e){let{questionData:t,questionId:r}=e,n=(0,o.useRouter)(),[d,z]=(0,s.useState)(!1),[S,q]=(0,s.useState)(null),[T,B]=(0,s.useState)({A:null,B:null,C:null,D:null}),[F,P]=(0,s.useState)([]),[R,O]=(0,s.useState)([]),[_,D]=(0,s.useState)(!0),[J,M]=(0,s.useState)({A:!1,B:!1,C:!1,D:!1}),Q=(0,s.useRef)(null),V={A:(0,s.useRef)(null),B:(0,s.useRef)(null),C:(0,s.useRef)(null),D:(0,s.useRef)(null)},$=(0,c.mN)({resolver:(0,l.u)(k),defaultValues:{subject:"",topic:"",questionText:"",optionA:"",optionB:"",optionC:"",optionD:"",correctAnswer:"",explanation:"",difficulty:""}});(0,s.useEffect)(()=>{if(t&&F.length>0){var e,r;let a=(()=>{if(!t.options||0===t.options.length)return["","","",""];if("string"==typeof t.options[0]){if(!(1===t.options.length&&t.options[0].includes(",")))return t.options.concat(Array(4-t.options.length).fill("")).slice(0,4);{let e=t.options[0].split(",");return e.map(e=>e.trim()).concat(Array(4-e.length).fill("")).slice(0,4)}}return["","","",""]})(),s={A:null,B:null,C:null,D:null};a.forEach((e,t)=>{let r=String.fromCharCode(65+t);"string"==typeof e&&(0,I.XI)(e)&&(s[r]=(0,I.UF)(e),a[t]="")}),B(s);let n=null!==(r=null===(e=t.options)||void 0===e?void 0:e.findIndex(e=>e===t.answer))&&void 0!==r?r:-1,i=n>=0?String.fromCharCode(65+n):"A",o=t.topicId?"string"==typeof t.topicId?t.topicId:t.topicId._id:"";console.log("Question data:",t),console.log("Topic ID extracted:",o),console.log("Available subjects:",F);let l=F.find(e=>e._id===t.subjectId._id);console.log("Selected subject:",l),l&&(O(l.chapters||[]),console.log("Set chapters:",l.chapters)),$.reset({subject:t.subjectId._id,topic:o,questionText:t.content,optionA:a[0]||"",optionB:a[1]||"",optionC:a[2]||"",optionD:a[3]||"",correctAnswer:i,explanation:t.explanation||"",difficulty:t.difficulty.charAt(0).toUpperCase()+t.difficulty.slice(1)}),console.log("Form reset with chapter:",o)}},[t,F,$]),(0,s.useEffect)(()=>{(async()=>{try{D(!0);let e=await (0,N.CG)();P(e)}catch(e){console.error("Error fetching subjects and chapters:",e),(0,C.o)({title:"Error",description:e.message||"Failed to load subjects and chapters",variant:"destructive"})}finally{D(!1)}})()},[]);let U=e=>{$.setValue("subject",e),$.setValue("topic","");let t=F.find(t=>t._id===e);t?O(t.chapters||[]):O([])},L=(e,t)=>{var r;let a=null===(r=e.target.files)||void 0===r?void 0:r[0];if(!a)return;let s=new FileReader;s.onload=e=>{if("question"===t){var r;q(null===(r=e.target)||void 0===r?void 0:r.result)}else B(r=>{var a;return{...r,[t]:null===(a=e.target)||void 0===a?void 0:a.result}}),M(e=>({...e,[t]:!1}))},s.readAsDataURL(a)},W=e=>{if("question"===e)q(null),Q.current&&(Q.current.value="");else{var t;B(t=>({...t,[e]:null})),(null===(t=V[e])||void 0===t?void 0:t.current)&&(V[e].current.value=""),$.clearErrors("option".concat(e)),M(t=>({...t,[e]:!1}))}},Z=e=>{let t=[],r={};for(let a of["A","B","C","D"]){let s=e["option".concat(a)]&&""!==e["option".concat(a)].trim(),n=null!==T[a];s||n?r[a]=!1:(t.push("Option ".concat(a," must have either text or an image")),r[a]=!0)}return M(r),t},H=async e=>{z(!0);try{var t,a,s,i;console.log("Form data:",e);let o=Z(e);if(o.length>0){(0,C.o)({title:"Validation Error",description:o.join(", "),variant:"destructive"}),z(!1);return}let l=[(null===(t=e.optionA)||void 0===t?void 0:t.trim())||T.A||"",(null===(a=e.optionB)||void 0===a?void 0:a.trim())||T.B||"",(null===(s=e.optionC)||void 0===s?void 0:s.trim())||T.C||"",(null===(i=e.optionD)||void 0===i?void 0:i.trim())||T.D||""],c=l[({A:0,B:1,C:2,D:3})[e.correctAnswer]],d=e.difficulty.toLowerCase(),u={content:e.questionText,options:l,answer:c,subjectId:e.subject,chapterId:e.topic,difficulty:d,type:"multiple-choice"},h=e.explanation&&""!==e.explanation.trim()?{...u,explanation:e.explanation}:u;console.log("Prepared question data:",h),console.log("Updating question with embedded base64 images");let p={...h};S&&(p.content="".concat(h.content,"\n").concat(S)),await (0,E.sr)(r,p),(0,C.o)({title:"Question Updated",description:"Your question has been successfully updated."}),n.push("/admin/question-bank")}catch(e){console.error("Error updating question:",e),(0,C.o)({title:"Error",description:e.message||"Failed to update question. Please try again.",variant:"destructive"})}finally{z(!1)}};return _?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,a.jsx)(w.Bc,{children:(0,a.jsx)(g.Zp,{className:"w-full max-w-4xl mx-auto",children:(0,a.jsx)(g.Wu,{className:"p-6",children:(0,a.jsx)(f.lV,{...$,children:(0,a.jsxs)("form",{onSubmit:$.handleSubmit(H),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(f.zB,{control:$.control,name:"subject",render:e=>{let{field:t}=e;return(0,a.jsxs)(f.eI,{children:[(0,a.jsx)(f.lR,{children:"Subject *"}),(0,a.jsxs)(v.l6,{onValueChange:U,value:t.value,children:[(0,a.jsx)(f.MJ,{children:(0,a.jsx)(v.bq,{children:(0,a.jsx)(v.yv,{placeholder:"Select a subject"})})}),(0,a.jsx)(v.gC,{children:F.map(e=>(0,a.jsx)(v.eb,{value:e._id,children:e.name},e._id))})]}),(0,a.jsx)(f.C5,{})]})}}),(0,a.jsx)(f.zB,{control:$.control,name:"topic",render:e=>{let{field:t}=e;return(0,a.jsxs)(f.eI,{children:[(0,a.jsx)(f.lR,{children:"Chapter *"}),(0,a.jsxs)(v.l6,{onValueChange:t.onChange,value:t.value,disabled:0===R.length,children:[(0,a.jsx)(f.MJ,{children:(0,a.jsx)(v.bq,{children:(0,a.jsx)(v.yv,{placeholder:0===R.length?"Select a subject first":"Select a chapter"})})}),(0,a.jsx)(v.gC,{children:R.map(e=>(0,a.jsx)(v.eb,{value:e._id,children:e.name},e._id))})]}),(0,a.jsx)(f.C5,{})]})}})]}),(0,a.jsx)(f.zB,{control:$.control,name:"questionText",render:e=>{let{field:t}=e;return(0,a.jsxs)(f.eI,{children:[(0,a.jsx)(f.lR,{children:"Question Text *"}),(0,a.jsx)(f.MJ,{children:(0,a.jsx)(y.T,{placeholder:"Enter your question here...",className:"min-h-[100px]",...t})}),(0,a.jsx)(f.C5,{})]})}}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.lR,{children:"Question Image (Optional)"}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)(x.$,{type:"button",variant:"outline",onClick:()=>{var e;return null===(e=Q.current)||void 0===e?void 0:e.click()},className:"flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),"Upload Image"]}),(0,a.jsx)("input",{ref:Q,type:"file",accept:A.join(","),onChange:e=>L(e,"question"),className:"hidden"}),S&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.default,{src:S,alt:"Question",width:100,height:100,className:"rounded-md object-cover"}),(0,a.jsx)(x.$,{type:"button",variant:"destructive",size:"icon",className:"absolute -top-2 -right-2 h-6 w-6",onClick:()=>W("question"),children:(0,a.jsx)(h.A,{className:"h-3 w-3"})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(f.lR,{children:"Answer Options *"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:["A","B","C","D"].map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.zB,{control:$.control,name:"option".concat(e),render:t=>{let{field:r}=t;return(0,a.jsxs)(f.eI,{children:[(0,a.jsxs)(f.lR,{children:["Option ",e,T[e]&&(0,a.jsx)("span",{className:"text-sm text-green-600 ml-2",children:"(Image uploaded)"})]}),(0,a.jsx)(f.MJ,{children:(0,a.jsx)(j.p,{placeholder:T[e]?"Option ".concat(e," text (optional - image uploaded)"):"Enter option ".concat(e," text or upload an image..."),...r})}),(0,a.jsx)(f.C5,{}),J[e]&&(0,a.jsxs)("p",{className:"text-sm text-red-600",children:["Option ",e," requires either text or an image"]})]})}}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(x.$,{type:"button",variant:"outline",size:"sm",onClick:()=>{var t,r;return null===(r=V[e])||void 0===r?void 0:null===(t=r.current)||void 0===t?void 0:t.click()},className:"flex items-center gap-1",children:[(0,a.jsx)(u.A,{className:"h-3 w-3"}),"Image"]}),(0,a.jsx)("input",{ref:V[e],type:"file",accept:A.join(","),onChange:t=>L(t,e),className:"hidden"}),T[e]&&(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.default,{src:T[e],alt:"Option ".concat(e),width:60,height:60,className:"rounded-md object-cover"}),(0,a.jsx)(x.$,{type:"button",variant:"destructive",size:"icon",className:"absolute -top-1 -right-1 h-4 w-4",onClick:()=>W(e),children:(0,a.jsx)(h.A,{className:"h-2 w-2"})})]})]})]},e))})]}),(0,a.jsx)(f.zB,{control:$.control,name:"correctAnswer",render:e=>{let{field:t}=e;return(0,a.jsxs)(f.eI,{className:"space-y-3",children:[(0,a.jsx)(f.lR,{children:"Correct Answer *"}),(0,a.jsx)(f.MJ,{children:(0,a.jsx)(b.z,{onValueChange:t.onChange,value:t.value,className:"flex flex-row space-x-6",children:["A","B","C","D"].map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(b.C,{value:e,id:e}),(0,a.jsxs)(f.lR,{htmlFor:e,children:["Option ",e]})]},e))})}),(0,a.jsx)(f.C5,{})]})}}),(0,a.jsx)(f.zB,{control:$.control,name:"difficulty",render:e=>{let{field:t}=e;return(0,a.jsxs)(f.eI,{children:[(0,a.jsx)(f.lR,{children:"Difficulty Level *"}),(0,a.jsxs)(v.l6,{onValueChange:t.onChange,value:t.value,children:[(0,a.jsx)(f.MJ,{children:(0,a.jsx)(v.bq,{children:(0,a.jsx)(v.yv,{placeholder:"Select difficulty level"})})}),(0,a.jsxs)(v.gC,{children:[(0,a.jsx)(v.eb,{value:"Easy",children:"Easy"}),(0,a.jsx)(v.eb,{value:"Medium",children:"Medium"}),(0,a.jsx)(v.eb,{value:"Hard",children:"Hard"})]})]}),(0,a.jsx)(f.C5,{})]})}}),(0,a.jsx)(f.zB,{control:$.control,name:"explanation",render:e=>{let{field:t}=e;return(0,a.jsxs)(f.eI,{children:[(0,a.jsxs)(f.lR,{children:["Explanation (Optional)",(0,a.jsxs)(w.m_,{children:[(0,a.jsx)(w.k$,{asChild:!0,children:(0,a.jsx)(p.A,{className:"inline h-4 w-4 ml-1 text-muted-foreground"})}),(0,a.jsx)(w.ZI,{children:(0,a.jsx)("p",{children:"Provide an explanation for the correct answer"})})]})]}),(0,a.jsx)(f.MJ,{children:(0,a.jsx)(y.T,{placeholder:"Explain why this is the correct answer...",className:"min-h-[80px]",...t})}),(0,a.jsx)(f.C5,{})]})}}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,a.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>{n.push("/admin/question-bank")},disabled:d,children:"Cancel"}),(0,a.jsx)(x.$,{type:"submit",disabled:d,children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update Question"})]})]})})})})})}let S=()=>{let e=(0,o.useParams)(),t=(0,o.useRouter)(),r=e.id,[i,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{let e=async()=>{try{d(!0);let e=await (0,E.zi)(r);l(e)}catch(e){console.error("Error fetching question:",e),(0,C.o)({title:"Error",description:e.message||"Failed to load question data",variant:"destructive"}),t.push("/admin/question-bank")}finally{d(!1)}};r&&e()},[r,t]),(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Edit Question"}),(0,a.jsx)(n.A,{items:[{label:"Home",href:"/"},{label:"Question Bank",href:"/admin/question-bank"},{label:"Edit Question"}],className:"text-sm mt-1"})]}),(0,a.jsx)("div",{className:"container mx-auto py-10",children:c?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):i?(0,a.jsx)(z,{questionData:i,questionId:r}):(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"Question not found"})})})]})})}},46102:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>i,ZI:()=>c,k$:()=>l,m_:()=>o});var a=r(95155);r(12115);var s=r(78082),n=r(59434);function i(e){let{delayDuration:t=0,...r}=e;return(0,a.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...r})}function o(e){let{...t}=e;return(0,a.jsx)(i,{children:(0,a.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function l(e){let{...t}=e;return(0,a.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function c(e){let{className:t,sideOffset:r=0,children:i,...o}=e;return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:r,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...o,children:[i,(0,a.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},50405:(e,t,r)=>{"use strict";r.d(t,{CG:()=>n,Fb:()=>l,Nj:()=>s,getSubjectsWithTopics:()=>o,gg:()=>d,iQ:()=>c,py:()=>i});var a=r(25731);let s=async()=>{try{return await (0,a.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},n=async()=>{try{return(await (0,a.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},i=async()=>{try{return await (0,a.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},o=async()=>{try{return await (0,a.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},l=async e=>{try{return await (0,a.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},c=async(e,t)=>{try{return await (0,a.apiCall)("/subjects/".concat(e),{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error("Error updating subject ".concat(e,":"),t),Error(t.message||"Failed to update subject")}},d=async e=>{try{await (0,a.apiCall)("/subjects/".concat(e),{method:"DELETE"})}catch(t){throw console.error("Error deleting subject ".concat(e,":"),t),Error(t.message||"Failed to delete subject")}}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(95155);r(12115);var s=r(40968),n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6671,1141,2571,6457,7117,221,8377,5267,8661,9140,8852,8441,1684,7358],()=>t(19576)),_N_E=e.O()}]);