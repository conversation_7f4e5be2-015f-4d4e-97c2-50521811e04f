"use strict";exports.id=6822,exports.ids=[6822],exports.modules={3589:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},17403:(e,t,r)=>{r.d(t,{UC:()=>eM,In:()=>eI,q7:()=>eA,VF:()=>eH,p4:()=>eB,ZL:()=>eD,bL:()=>eN,wn:()=>e_,PP:()=>eV,l9:()=>eE,WT:()=>eP,LM:()=>eL});var n=r(43210),l=r(51215);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(70569),i=r(72031),s=r(98599),d=r(11273),u=r(43),c=r(31355),p=r(1359),f=r(32547),h=r(96963),v=r(29172),m=r(25028),g=r(3416),w=r(60687),x=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),a=o.find(b);if(a){let e=a.props.children,r=o.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,w.jsx)(y,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,w.jsx)(y,{...l,ref:t,children:r})});x.displayName="Slot";var y=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),o=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==n.Fragment&&(o.ref=t?(0,s.t)(t,e):e),n.cloneElement(r,o)}return n.Children.count(r)>1?n.Children.only(null):null});y.displayName="SlotClone";var S=({children:e})=>(0,w.jsx)(w.Fragment,{children:e});function b(e){return n.isValidElement(e)&&e.type===S}var C=r(13495),j=r(65551),R=r(66156),T=r(69024),k=r(63376),N=r(11490),E=[" ","Enter","ArrowUp","ArrowDown"],P=[" ","Enter"],I="Select",[D,M,L]=(0,i.N)(I),[A,B]=(0,d.A)(I,[L,v.Bk]),H=(0,v.Bk)(),[V,_]=A(I),[O,F]=A(I),W=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:d,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:x}=e,y=H(t),[S,b]=n.useState(null),[C,R]=n.useState(null),[T,k]=n.useState(!1),N=(0,u.jH)(c),[E=!1,P]=(0,j.i)({prop:l,defaultProp:o,onChange:a}),[I,M]=(0,j.i)({prop:i,defaultProp:s,onChange:d}),L=n.useRef(null),A=!S||x||!!S.closest("form"),[B,_]=n.useState(new Set),F=Array.from(B).map(e=>e.props.value).join(";");return(0,w.jsx)(v.bL,{...y,children:(0,w.jsxs)(V,{required:g,scope:t,trigger:S,onTriggerChange:b,valueNode:C,onValueNodeChange:R,valueNodeHasChildren:T,onValueNodeHasChildrenChange:k,contentId:(0,h.B)(),value:I,onValueChange:M,open:E,onOpenChange:P,dir:N,triggerPointerDownPosRef:L,disabled:m,children:[(0,w.jsx)(D.Provider,{scope:t,children:(0,w.jsx)(O,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{_(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,w.jsxs)(eR,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:I,onChange:e=>M(e.target.value),disabled:m,form:x,children:[void 0===I?(0,w.jsx)("option",{value:""}):null,Array.from(B)]},F):null]})})};W.displayName=I;var G="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=H(r),d=_(G,r),u=d.disabled||l,c=(0,s.s)(t,d.onTriggerChange),p=M(r),f=n.useRef("touch"),[h,m,x]=eT(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===d.value),n=ek(t,e,r);void 0!==n&&d.onValueChange(n.value)}),y=e=>{u||(d.onOpenChange(!0),x()),e&&(d.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,w.jsx)(v.Mz,{asChild:!0,...i,children:(0,w.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":d.contentId,"aria-expanded":d.open,"aria-required":d.required,"aria-autocomplete":"none",dir:d.dir,"data-state":d.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":ej(d.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&y(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&E.includes(e.key)&&(y(),e.preventDefault())})})})});K.displayName=G;var U="SelectValue",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,d=_(U,r),{onValueNodeHasChildrenChange:u}=d,c=void 0!==o,p=(0,s.s)(t,d.onValueNodeChange);return(0,R.N)(()=>{u(c)},[u,c]),(0,w.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ej(d.value)?(0,w.jsx)(w.Fragment,{children:a}):o})});z.displayName=U;var q=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});q.displayName="SelectIcon";var Z=e=>(0,w.jsx)(m.Z,{asChild:!0,...e});Z.displayName="SelectPortal";var X="SelectContent",Y=n.forwardRef((e,t)=>{let r=_(X,e.__scopeSelect),[o,a]=n.useState();return((0,R.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,w.jsx)($,{...e,ref:t}):o?l.createPortal((0,w.jsx)(J,{scope:e.__scopeSelect,children:(0,w.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});Y.displayName=X;var[J,Q]=A(X),$=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:d,side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:S,sticky:b,hideWhenDetached:C,avoidCollisions:j,...R}=e,T=_(X,r),[E,P]=n.useState(null),[I,D]=n.useState(null),L=(0,s.s)(t,e=>P(e)),[A,B]=n.useState(null),[H,V]=n.useState(null),O=M(r),[F,W]=n.useState(!1),G=n.useRef(!1);n.useEffect(()=>{if(E)return(0,k.Eq)(E)},[E]),(0,p.Oh)();let K=n.useCallback(e=>{let[t,...r]=O().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&I&&(I.scrollTop=0),r===n&&I&&(I.scrollTop=I.scrollHeight),r?.focus(),document.activeElement!==l))return},[O,I]),U=n.useCallback(()=>K([A,E]),[K,A,E]);n.useEffect(()=>{F&&U()},[F,U]);let{onOpenChange:z,triggerPointerDownPosRef:q}=T;n.useEffect(()=>{if(E){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(q.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(q.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():E.contains(r.target)||z(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[E,z,q]),n.useEffect(()=>{let e=()=>z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[z]);let[Z,Y]=eT(e=>{let t=O().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=ek(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),Q=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==T.value&&T.value===t||n)&&(B(e),n&&(G.current=!0))},[T.value]),$=n.useCallback(()=>E?.focus(),[E]),er=n.useCallback((e,t,r)=>{let n=!G.current&&!r;(void 0!==T.value&&T.value===t||n)&&V(e)},[T.value]),en="popper"===l?et:ee,el=en===et?{side:u,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:S,sticky:b,hideWhenDetached:C,avoidCollisions:j}:{};return(0,w.jsx)(J,{scope:r,content:E,viewport:I,onViewportChange:D,itemRefCallback:Q,selectedItem:A,onItemLeave:$,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:H,position:l,isPositioned:F,searchRef:Z,children:(0,w.jsx)(N.A,{as:x,allowPinchZoom:!0,children:(0,w.jsx)(f.n,{asChild:!0,trapped:T.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{T.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:d,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>T.onOpenChange(!1),children:(0,w.jsx)(en,{role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:e=>e.preventDefault(),...R,...el,onPlaced:()=>W(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,a.m)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(X,r),d=Q(X,r),[u,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.s)(t,e=>f(e)),v=M(r),m=n.useRef(!1),x=n.useRef(!0),{viewport:y,selectedItem:S,selectedItemText:b,focusSelectedItem:C}=d,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&u&&p&&y&&S&&b){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,d=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-d)]);u.style.minWidth=s+"px",u.style.right=c+"px"}let a=v(),s=window.innerHeight-20,d=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+h+d+parseInt(c.paddingBottom,10)+g,x=Math.min(5*S.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,k=S.offsetHeight/2,N=f+h+(S.offsetTop+k);if(N<=T){let e=a.length>0&&S===a[a.length-1].ref.current;u.style.bottom="0px";let t=Math.max(s-T,k+(e?R:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+g);u.style.height=N+t+"px"}else{let e=a.length>0&&S===a[0].ref.current;u.style.top="0px";let t=Math.max(T,f+y.offsetTop+(e?j:0)+k);u.style.height=t+(w-N)+"px",y.scrollTop=N-T+y.offsetTop}u.style.margin="10px 0",u.style.minHeight=x+"px",u.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,u,p,y,S,b,i.dir,l]);(0,R.N)(()=>j(),[j]);let[T,k]=n.useState();(0,R.N)(()=>{p&&k(window.getComputedStyle(p).zIndex)},[p]);let N=n.useCallback(e=>{e&&!0===x.current&&(j(),C?.(),x.current=!1)},[j,C]);return(0,w.jsx)(er,{scope:r,contentWrapper:u,shouldExpandOnScrollRef:m,onScrollButtonChange:N,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:T},children:(0,w.jsx)(g.sG.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});ee.displayName="SelectItemAlignedPosition";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=H(r);return(0,w.jsx)(v.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName="SelectPopperPosition";var[er,en]=A(X,{}),el="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Q(el,r),d=en(el,r),u=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(D.Slot,{scope:r,children:(0,w.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:u,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=d;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=el;var ea="SelectGroup",[ei,es]=A(ea);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.B)();return(0,w.jsx)(ei,{scope:r,id:l,children:(0,w.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=ea;var ed="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=es(ed,r);return(0,w.jsx)(g.sG.div,{id:l.id,...n,ref:t})}).displayName=ed;var eu="SelectItem",[ec,ep]=A(eu),ef=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...d}=e,u=_(eu,r),c=Q(eu,r),p=u.value===l,[f,v]=n.useState(i??""),[m,x]=n.useState(!1),y=(0,s.s)(t,e=>c.itemRefCallback?.(e,l,o)),S=(0,h.B)(),b=n.useRef("touch"),C=()=>{o||(u.onValueChange(l),u.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(ec,{scope:r,value:l,disabled:o,textId:S,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,w.jsx)(D.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,w.jsx)(g.sG.div,{role:"option","aria-labelledby":S,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...d,ref:y,onFocus:(0,a.m)(d.onFocus,()=>x(!0)),onBlur:(0,a.m)(d.onBlur,()=>x(!1)),onClick:(0,a.m)(d.onClick,()=>{"mouse"!==b.current&&C()}),onPointerUp:(0,a.m)(d.onPointerUp,()=>{"mouse"===b.current&&C()}),onPointerDown:(0,a.m)(d.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(d.onPointerMove,e=>{b.current=e.pointerType,o?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(d.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.m)(d.onKeyDown,e=>{(c.searchRef?.current===""||" "!==e.key)&&(P.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ef.displayName=eu;var eh="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,d=_(eh,r),u=Q(eh,r),c=ep(eh,r),p=F(eh,r),[f,h]=n.useState(null),v=(0,s.s)(t,e=>h(e),c.onItemTextChange,e=>u.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,x=n.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:S}=p;return(0,R.N)(()=>(y(x),()=>S(x)),[y,S,x]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(g.sG.span,{id:c.textId,...i,ref:v}),c.isSelected&&d.valueNode&&!d.valueNodeHasChildren?l.createPortal(i.children,d.valueNode):null]})});ev.displayName=eh;var em="SelectItemIndicator",eg=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ep(em,r).isSelected?(0,w.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});eg.displayName=em;var ew="SelectScrollUpButton",ex=n.forwardRef((e,t)=>{let r=Q(ew,e.__scopeSelect),l=en(ew,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,R.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ex.displayName=ew;var ey="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=Q(ey,e.__scopeSelect),l=en(ey,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,R.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(eb,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=ey;var eb=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Q("SelectScrollButton",r),s=n.useRef(null),d=M(r),u=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>u(),[u]),(0,R.N)(()=>{let e=d().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[d]),(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{u()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,w.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eC="SelectArrow";function ej(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=H(r),o=_(eC,r),a=Q(eC,r);return o.open&&"popper"===a.position?(0,w.jsx)(v.i3,{...l,...n,ref:t}):null}).displayName=eC;var eR=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),a=(0,s.s)(t,o),i=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[i,r]),(0,w.jsx)(T.s,{asChild:!0,children:(0,w.jsx)("select",{...l,ref:a,defaultValue:r})})});function eT(e){let t=(0,C.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function ek(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eR.displayName="BubbleSelect";var eN=W,eE=K,eP=z,eI=q,eD=Z,eM=Y,eL=eo,eA=ef,eB=ev,eH=eg,eV=ex,e_=eS}};