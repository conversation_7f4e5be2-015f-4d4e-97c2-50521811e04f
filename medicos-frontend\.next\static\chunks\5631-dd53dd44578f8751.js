"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5631],{4033:(e,t,n)=>{n.d(t,{bm:()=>eR,UC:()=>eC,VY:()=>ex,hJ:()=>eN,ZL:()=>ew,bL:()=>eb,hE:()=>eD,l9:()=>eh,G$:()=>ep,Hs:()=>K});var r,o=n(12115),u=n.t(o,2);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return o.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}(...e),e)}var a=n(95155),c=globalThis?.document?o.useLayoutEffect:()=>{},d=u[" useId ".trim().toString()]||(()=>void 0),f=0;function v(e){let[t,n]=o.useState(d());return c(()=>{e||n(e=>e??String(f++))},[e]),e||(t?`radix-${t}`:"")}var m=u[" useInsertionEffect ".trim().toString()]||c,p=(Symbol("RADIX:SYNC_STATE"),n(47650)),E=n(66634),y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,E.TL)(`Primitive.${t}`),r=o.forwardRef((e,r)=>{let{asChild:o,...u}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...u,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function g(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}var b="dismissableLayer.update",h=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=o.forwardRef((e,t)=>{var n,u;let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:v,onDismiss:m,...p}=e,E=o.useContext(h),[w,D]=o.useState(null),x=null!==(u=null==w?void 0:w.ownerDocument)&&void 0!==u?u:null===(n=globalThis)||void 0===n?void 0:n.document,[,R]=o.useState({}),O=s(t,e=>D(e)),P=Array.from(E.layers),[T]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),L=P.indexOf(T),I=w?P.indexOf(w):-1,S=E.layersWithOutsidePointerEventsDisabled.size>0,j=I>=L,F=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=g(e),u=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!u.current){let t=function(){C("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);u.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>u.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!j||n||(null==d||d(e),null==v||v(e),e.defaultPrevented||null==m||m())},x),_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=g(e),u=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!u.current&&C("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>u.current=!0,onBlurCapture:()=>u.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==f||f(e),null==v||v(e),e.defaultPrevented||null==m||m())},x);return!function(e,t=globalThis?.document){let n=g(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{I===E.layers.size-1&&(null==c||c(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},x),o.useEffect(()=>{if(w)return l&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(w)),E.layers.add(w),N(),()=>{l&&1===E.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=r)}},[w,x,l,E]),o.useEffect(()=>()=>{w&&(E.layers.delete(w),E.layersWithOutsidePointerEventsDisabled.delete(w),N())},[w,E]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(b,e),()=>document.removeEventListener(b,e)},[]),(0,a.jsx)(y.div,{...p,ref:O,style:{pointerEvents:S?j?"auto":"none":void 0,...e.style},onFocusCapture:i(e.onFocusCapture,_.onFocusCapture),onBlurCapture:i(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:i(e.onPointerDownCapture,F.onPointerDownCapture)})});function N(){let e=new CustomEvent(b);document.dispatchEvent(e)}function C(e,t,n,r){let{discrete:o}=r,u=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&u.addEventListener(e,t,{once:!0}),o)u&&p.flushSync(()=>u.dispatchEvent(i));else u.dispatchEvent(i)}w.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(h),r=o.useRef(null),u=s(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,a.jsx)(y.div,{...e,ref:u})}).displayName="DismissableLayerBranch";var D="focusScope.autoFocusOnMount",x="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},O=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:u,onUnmountAutoFocus:i,...l}=e,[c,d]=o.useState(null),f=g(u),v=g(i),m=o.useRef(null),p=s(t,e=>d(e)),E=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(E.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:L(m.current,{select:!0})},t=function(e){if(E.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||L(m.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&L(c)});return c&&n.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,c,E.paused]),o.useEffect(()=>{if(c){I.add(E);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(D,R);c.addEventListener(D,f),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(L(r,{select:t}),document.activeElement!==n)return}(P(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&L(c))}return()=>{c.removeEventListener(D,f),setTimeout(()=>{let t=new CustomEvent(x,R);c.addEventListener(x,v),c.dispatchEvent(t),t.defaultPrevented||L(null!=e?e:document.body,{select:!0}),c.removeEventListener(x,v),I.remove(E)},0)}}},[c,f,v,E]);let b=o.useCallback(e=>{if(!n&&!r||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,u]=function(e){let t=P(e);return[T(t,e),T(t.reverse(),e)]}(t);r&&u?e.shiftKey||o!==u?e.shiftKey&&o===r&&(e.preventDefault(),n&&L(u,{select:!0})):(e.preventDefault(),n&&L(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,E.paused]);return(0,a.jsx)(y.div,{tabIndex:-1,...l,ref:p,onKeyDown:b})});function P(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function T(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function L(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}O.displayName="FocusScope";var I=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=S(e,t)).unshift(t)},remove(t){var n;null===(n=(e=S(e,t))[0])||void 0===n||n.resume()}}}();function S(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var j=o.forwardRef((e,t)=>{var n,r;let{container:u,...i}=e,[l,s]=o.useState(!1);c(()=>s(!0),[]);let d=u||l&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return d?p.createPortal((0,a.jsx)(y.div,{...i,ref:t}),d):null});j.displayName="Portal";var F=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,u]=o.useState(),i=o.useRef(null),l=o.useRef(e),s=o.useRef("none"),[a,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=_(i.current);s.current="mounted"===a?e:"none"},[a]),c(()=>{let t=i.current,n=l.current;if(n!==e){let r=s.current,o=_(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),c(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=_(i.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!l.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},u=e=>{e.target===r&&(s.current=_(i.current))};return r.addEventListener("animationstart",u),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",u),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,u(e)},[])}}(t),u="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),i=s(r.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||r.isPresent?o.cloneElement(u,{ref:i}):null};function _(e){return(null==e?void 0:e.animationName)||"none"}F.displayName="Presence";var A=0;function M(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var k=n(31114),U=n(38168),W="Dialog",[$,K]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let u=o.createContext(r),i=n.length;n=[...n,r];let l=t=>{let{scope:n,children:r,...l}=t,s=n?.[e]?.[i]||u,c=o.useMemo(()=>l,Object.values(l));return(0,a.jsx)(s.Provider,{value:c,children:r})};return l.displayName=t+"Provider",[l,function(n,l){let s=l?.[e]?.[i]||u,a=o.useContext(s);if(a)return a;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(W),[B,q]=$(W),z=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:u,onOpenChange:i,modal:l=!0}=e,s=o.useRef(null),c=o.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[u,i,l]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),u=o.useRef(n),i=o.useRef(t);return m(()=>{i.current=t},[t]),o.useEffect(()=>{u.current!==n&&(i.current?.(n),u.current=n)},[n,u]),[n,r,i]}({defaultProp:t,onChange:n}),s=void 0!==e,a=s?e:u;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[a,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&l.current?.(n)}else i(t)},[s,e,i,l])]}({prop:r,defaultProp:null!=u&&u,onChange:i,caller:W});return(0,a.jsx)(B,{scope:t,triggerRef:s,contentRef:c,contentId:v(),titleId:v(),descriptionId:v(),open:d,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:l,children:n})};z.displayName=W;var H="DialogTrigger",V=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=q(H,n),u=s(t,o.triggerRef);return(0,a.jsx)(y.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ev(o.open),...r,ref:u,onClick:i(e.onClick,o.onOpenToggle)})});V.displayName=H;var Y="DialogPortal",[Z,G]=$(Y,{forceMount:void 0}),J=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:u}=e,i=q(Y,t);return(0,a.jsx)(Z,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,a.jsx)(F,{present:n||i.open,children:(0,a.jsx)(j,{asChild:!0,container:u,children:e})}))})};J.displayName=Y;var X="DialogOverlay",Q=o.forwardRef((e,t)=>{let n=G(X,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,u=q(X,e.__scopeDialog);return u.modal?(0,a.jsx)(F,{present:r||u.open,children:(0,a.jsx)(et,{...o,ref:t})}):null});Q.displayName=X;var ee=(0,E.TL)("DialogOverlay.RemoveScroll"),et=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=q(X,n);return(0,a.jsx)(k.A,{as:ee,allowPinchZoom:!0,shards:[o.contentRef],children:(0,a.jsx)(y.div,{"data-state":ev(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),en="DialogContent",er=o.forwardRef((e,t)=>{let n=G(en,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,u=q(en,e.__scopeDialog);return(0,a.jsx)(F,{present:r||u.open,children:u.modal?(0,a.jsx)(eo,{...o,ref:t}):(0,a.jsx)(eu,{...o,ref:t})})});er.displayName=en;var eo=o.forwardRef((e,t)=>{let n=q(en,e.__scopeDialog),r=o.useRef(null),u=s(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,U.Eq)(e)},[]),(0,a.jsx)(ei,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:i(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:i(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:i(e.onFocusOutside,e=>e.preventDefault())})}),eu=o.forwardRef((e,t)=>{let n=q(en,e.__scopeDialog),r=o.useRef(!1),u=o.useRef(!1);return(0,a.jsx)(ei,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,i;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1,u.current=!1},onInteractOutside:t=>{var o,i;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(u.current=!0));let l=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&u.current&&t.preventDefault()}})}),ei=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:u,onCloseAutoFocus:i,...l}=e,c=q(en,n),d=o.useRef(null),f=s(t,d);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:M()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:M()),A++,()=>{1===A&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),A--}},[]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(O,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:u,onUnmountAutoFocus:i,children:(0,a.jsx)(w,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":ev(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ey,{titleId:c.titleId}),(0,a.jsx)(eg,{contentRef:d,descriptionId:c.descriptionId})]})]})}),el="DialogTitle",es=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=q(el,n);return(0,a.jsx)(y.h2,{id:o.titleId,...r,ref:t})});es.displayName=el;var ea="DialogDescription",ec=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=q(ea,n);return(0,a.jsx)(y.p,{id:o.descriptionId,...r,ref:t})});ec.displayName=ea;var ed="DialogClose",ef=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=q(ed,n);return(0,a.jsx)(y.button,{type:"button",...r,ref:t,onClick:i(e.onClick,()=>o.onOpenChange(!1))})});function ev(e){return e?"open":"closed"}ef.displayName=ed;var em="DialogTitleWarning",[ep,eE]=function(e,t){let n=o.createContext(t),r=e=>{let{children:t,...r}=e,u=o.useMemo(()=>r,Object.values(r));return(0,a.jsx)(n.Provider,{value:u,children:t})};return r.displayName=e+"Provider",[r,function(r){let u=o.useContext(n);if(u)return u;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(em,{contentName:en,titleName:el,docsSlug:"dialog"}),ey=e=>{let{titleId:t}=e,n=eE(em),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},eg=e=>{let{contentRef:t,descriptionId:n}=e,r=eE("DialogDescriptionWarning"),u="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(u)},[u,t,n]),null},eb=z,eh=V,ew=J,eN=Q,eC=er,eD=es,ex=ec,eR=ef},35695:(e,t,n)=>{var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})}}]);