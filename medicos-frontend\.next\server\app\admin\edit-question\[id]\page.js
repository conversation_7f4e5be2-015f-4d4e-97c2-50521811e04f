(()=>{var e={};e.id=5400,e.ids=[5400],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7295:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),l=r(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c={children:["",{children:["admin",{children:["edit-question",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96236)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/edit-question/[id]/page",pathname:"/admin/edit-question/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7397:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(60687),a=r(43210),i=r(63239),n=r(30474),l=r(16189),o=r(63442),c=r(27605),d=r(45880),u=r(16023),p=r(11860),m=r(96882),h=r(41862),x=r(29523),g=r(44493),j=r(71669),f=r(12048),b=r(50812),v=r(15079),y=r(34729),w=r(76242),C=r(20140);r(82013);var N=r(94487);r(29045);let q=["image/jpeg","image/jpg","image/png","image/svg+xml"],E=d.z.object({subject:d.z.string().min(1,{message:"Please select a subject"}),topic:d.z.string().min(1,{message:"Please select a chapter"}),questionText:d.z.string().min(5,{message:"Question must be at least 5 characters"}),optionA:d.z.string().optional(),optionB:d.z.string().optional(),optionC:d.z.string().optional(),optionD:d.z.string().optional(),correctAnswer:d.z.enum(["A","B","C","D"],{required_error:"Please select the correct answer"}),explanation:d.z.string().optional(),difficulty:d.z.enum(["Easy","Medium","Hard"],{required_error:"Please select a difficulty level"})});function $({questionData:e,questionId:t}){let r=(0,l.useRouter)(),[i,d]=(0,a.useState)(!1),[$,A]=(0,a.useState)(null),[k,z]=(0,a.useState)({A:null,B:null,C:null,D:null}),[P,I]=(0,a.useState)([]),[F,D]=(0,a.useState)([]),[R,_]=(0,a.useState)(!0),[B,S]=(0,a.useState)({A:!1,B:!1,C:!1,D:!1}),M=(0,a.useRef)(null),O={A:(0,a.useRef)(null),B:(0,a.useRef)(null),C:(0,a.useRef)(null),D:(0,a.useRef)(null)},T=(0,c.mN)({resolver:(0,o.u)(E),defaultValues:{subject:"",topic:"",questionText:"",optionA:"",optionB:"",optionC:"",optionD:"",correctAnswer:"",explanation:"",difficulty:""}}),U=e=>{T.setValue("subject",e),T.setValue("topic","");let t=P.find(t=>t._id===e);t?D(t.chapters||[]):D([])},L=(e,t)=>{let r=e.target.files?.[0];if(!r)return;let s=new FileReader;s.onload=e=>{"question"===t?A(e.target?.result):(z(r=>({...r,[t]:e.target?.result})),S(e=>({...e,[t]:!1})))},s.readAsDataURL(r)},J=e=>{"question"===e?(A(null),M.current&&(M.current.value="")):(z(t=>({...t,[e]:null})),O[e]?.current&&(O[e].current.value=""),T.clearErrors(`option${e}`),S(t=>({...t,[e]:!1})))},Q=e=>{let t=[],r={};for(let s of["A","B","C","D"]){let a=e[`option${s}`]&&""!==e[`option${s}`].trim(),i=null!==k[s];a||i?r[s]=!1:(t.push(`Option ${s} must have either text or an image`),r[s]=!0)}return S(r),t},V=async e=>{d(!0);try{console.log("Form data:",e);let s=Q(e);if(s.length>0){(0,C.o)({title:"Validation Error",description:s.join(", "),variant:"destructive"}),d(!1);return}let a=[e.optionA?.trim()||k.A||"",e.optionB?.trim()||k.B||"",e.optionC?.trim()||k.C||"",e.optionD?.trim()||k.D||""],i=a[({A:0,B:1,C:2,D:3})[e.correctAnswer]],n=e.difficulty.toLowerCase(),l={content:e.questionText,options:a,answer:i,subjectId:e.subject,chapterId:e.topic,difficulty:n,type:"multiple-choice"},o=e.explanation&&""!==e.explanation.trim()?{...l,explanation:e.explanation}:l;console.log("Prepared question data:",o),console.log("Updating question with embedded base64 images");let c={...o};$&&(c.content=`${o.content}
${$}`),await (0,N.sr)(t,c),(0,C.o)({title:"Question Updated",description:"Your question has been successfully updated."}),r.push("/admin/question-bank")}catch(e){console.error("Error updating question:",e),(0,C.o)({title:"Error",description:e.message||"Failed to update question. Please try again.",variant:"destructive"})}finally{d(!1)}};return R?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,s.jsx)(w.Bc,{children:(0,s.jsx)(g.Zp,{className:"w-full max-w-4xl mx-auto",children:(0,s.jsx)(g.Wu,{className:"p-6",children:(0,s.jsx)(j.lV,{...T,children:(0,s.jsxs)("form",{onSubmit:T.handleSubmit(V),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsx)(j.zB,{control:T.control,name:"subject",render:({field:e})=>(0,s.jsxs)(j.eI,{children:[(0,s.jsx)(j.lR,{children:"Subject *"}),(0,s.jsxs)(v.l6,{onValueChange:U,value:e.value,children:[(0,s.jsx)(j.MJ,{children:(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{placeholder:"Select a subject"})})}),(0,s.jsx)(v.gC,{children:P.map(e=>(0,s.jsx)(v.eb,{value:e._id,children:e.name},e._id))})]}),(0,s.jsx)(j.C5,{})]})}),(0,s.jsx)(j.zB,{control:T.control,name:"topic",render:({field:e})=>(0,s.jsxs)(j.eI,{children:[(0,s.jsx)(j.lR,{children:"Chapter *"}),(0,s.jsxs)(v.l6,{onValueChange:e.onChange,value:e.value,disabled:0===F.length,children:[(0,s.jsx)(j.MJ,{children:(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{placeholder:0===F.length?"Select a subject first":"Select a chapter"})})}),(0,s.jsx)(v.gC,{children:F.map(e=>(0,s.jsx)(v.eb,{value:e._id,children:e.name},e._id))})]}),(0,s.jsx)(j.C5,{})]})})]}),(0,s.jsx)(j.zB,{control:T.control,name:"questionText",render:({field:e})=>(0,s.jsxs)(j.eI,{children:[(0,s.jsx)(j.lR,{children:"Question Text *"}),(0,s.jsx)(j.MJ,{children:(0,s.jsx)(y.T,{placeholder:"Enter your question here...",className:"min-h-[100px]",...e})}),(0,s.jsx)(j.C5,{})]})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(j.lR,{children:"Question Image (Optional)"}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsxs)(x.$,{type:"button",variant:"outline",onClick:()=>M.current?.click(),className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),"Upload Image"]}),(0,s.jsx)("input",{ref:M,type:"file",accept:q.join(","),onChange:e=>L(e,"question"),className:"hidden"}),$&&(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.default,{src:$,alt:"Question",width:100,height:100,className:"rounded-md object-cover"}),(0,s.jsx)(x.$,{type:"button",variant:"destructive",size:"icon",className:"absolute -top-2 -right-2 h-6 w-6",onClick:()=>J("question"),children:(0,s.jsx)(p.A,{className:"h-3 w-3"})})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(j.lR,{children:"Answer Options *"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:["A","B","C","D"].map(e=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(j.zB,{control:T.control,name:`option${e}`,render:({field:t})=>(0,s.jsxs)(j.eI,{children:[(0,s.jsxs)(j.lR,{children:["Option ",e,k[e]&&(0,s.jsx)("span",{className:"text-sm text-green-600 ml-2",children:"(Image uploaded)"})]}),(0,s.jsx)(j.MJ,{children:(0,s.jsx)(f.p,{placeholder:k[e]?`Option ${e} text (optional - image uploaded)`:`Enter option ${e} text or upload an image...`,...t})}),(0,s.jsx)(j.C5,{}),B[e]&&(0,s.jsxs)("p",{className:"text-sm text-red-600",children:["Option ",e," requires either text or an image"]})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(x.$,{type:"button",variant:"outline",size:"sm",onClick:()=>O[e]?.current?.click(),className:"flex items-center gap-1",children:[(0,s.jsx)(u.A,{className:"h-3 w-3"}),"Image"]}),(0,s.jsx)("input",{ref:O[e],type:"file",accept:q.join(","),onChange:t=>L(t,e),className:"hidden"}),k[e]&&(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.default,{src:k[e],alt:`Option ${e}`,width:60,height:60,className:"rounded-md object-cover"}),(0,s.jsx)(x.$,{type:"button",variant:"destructive",size:"icon",className:"absolute -top-1 -right-1 h-4 w-4",onClick:()=>J(e),children:(0,s.jsx)(p.A,{className:"h-2 w-2"})})]})]})]},e))})]}),(0,s.jsx)(j.zB,{control:T.control,name:"correctAnswer",render:({field:e})=>(0,s.jsxs)(j.eI,{className:"space-y-3",children:[(0,s.jsx)(j.lR,{children:"Correct Answer *"}),(0,s.jsx)(j.MJ,{children:(0,s.jsx)(b.z,{onValueChange:e.onChange,value:e.value,className:"flex flex-row space-x-6",children:["A","B","C","D"].map(e=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(b.C,{value:e,id:e}),(0,s.jsxs)(j.lR,{htmlFor:e,children:["Option ",e]})]},e))})}),(0,s.jsx)(j.C5,{})]})}),(0,s.jsx)(j.zB,{control:T.control,name:"difficulty",render:({field:e})=>(0,s.jsxs)(j.eI,{children:[(0,s.jsx)(j.lR,{children:"Difficulty Level *"}),(0,s.jsxs)(v.l6,{onValueChange:e.onChange,value:e.value,children:[(0,s.jsx)(j.MJ,{children:(0,s.jsx)(v.bq,{children:(0,s.jsx)(v.yv,{placeholder:"Select difficulty level"})})}),(0,s.jsxs)(v.gC,{children:[(0,s.jsx)(v.eb,{value:"Easy",children:"Easy"}),(0,s.jsx)(v.eb,{value:"Medium",children:"Medium"}),(0,s.jsx)(v.eb,{value:"Hard",children:"Hard"})]})]}),(0,s.jsx)(j.C5,{})]})}),(0,s.jsx)(j.zB,{control:T.control,name:"explanation",render:({field:e})=>(0,s.jsxs)(j.eI,{children:[(0,s.jsxs)(j.lR,{children:["Explanation (Optional)",(0,s.jsxs)(w.m_,{children:[(0,s.jsx)(w.k$,{asChild:!0,children:(0,s.jsx)(m.A,{className:"inline h-4 w-4 ml-1 text-muted-foreground"})}),(0,s.jsx)(w.ZI,{children:(0,s.jsx)("p",{children:"Provide an explanation for the correct answer"})})]})]}),(0,s.jsx)(j.MJ,{children:(0,s.jsx)(y.T,{placeholder:"Explain why this is the correct answer...",className:"min-h-[80px]",...e})}),(0,s.jsx)(j.C5,{})]})}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6",children:[(0,s.jsx)(x.$,{type:"button",variant:"outline",onClick:()=>{r.push("/admin/question-bank")},disabled:i,children:"Cancel"}),(0,s.jsx)(x.$,{type:"submit",disabled:i,children:i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update Question"})]})]})})})})})}let A=()=>{let e=(0,l.useParams)(),t=(0,l.useRouter)(),r=e.id,[n,o]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let e=async()=>{try{d(!0);let e=await (0,N.zi)(r);o(e)}catch(e){console.error("Error fetching question:",e),(0,C.o)({title:"Error",description:e.message||"Failed to load question data",variant:"destructive"}),t.push("/admin/question-bank")}finally{d(!1)}};r&&e()},[r,t]),(0,s.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Edit Question"}),(0,s.jsx)(i.A,{items:[{label:"Home",href:"/"},{label:"Question Bank",href:"/admin/question-bank"},{label:"Edit Question"}],className:"text-sm mt-1"})]}),(0,s.jsx)("div",{className:"container mx-auto py-10",children:c?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):n?(0,s.jsx)($,{questionData:n,questionId:r}):(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Question not found"})})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},16160:(e,t,r)=>{Promise.resolve().then(r.bind(r,96236))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29045:(e,t,r)=>{"use strict";function s(e){return!!e&&"string"==typeof e&&(!!/^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i.test(e)||e.length>100&&/^[A-Za-z0-9+/]*={0,2}$/.test(e))}function a(e){if(!e)return"";let t=/^(data:image\/[^;]+;base64,)(data:image\/[^;]+;base64,)/;t.test(e)&&(e=e.replace(t,"$2"));let r=e.match(/^(data:image\/[^;]+;base64,)+/);if(r){let t=r[0].length,s=e.substring(t),a=r[0].match(/data:image\/[^;]+;base64,$/);if(a)return a[0]+s}return e.startsWith("data:image/")?e:`data:image/jpeg;base64,${e}`}function i(e,t){if(!e)return{cleanText:e,images:[]};let r=[],i=e,n=[...e.matchAll(/<img\s+[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*\/?>/gi)];n.length>0&&n.forEach((e,t)=>{let n=e[0],l=e[1],o=e[2]||`Image ${r.length+1}`;if((l=l.trim()).includes("base64,")||s(l)){let e=`extracted-image-html-${t}`,s=a(l);r.push({id:e,src:s,alt:o}),i=i.replace(n,"")}});let l=[...i.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g)];l.length>0&&l.forEach((e,n)=>{let l=e[0],o=e[1]||`Image ${r.length+1}`,c=e[2];if((c=c.trim()).includes("base64,")||s(c)){let e=`extracted-image-markdown-${n}`,t=a(c);r.push({id:e,src:t,alt:o}),i=i.replace(l,"")}else if(t){let e=function(e,t){if(t[e])return e;for(let r of[e,e.replace(".jpeg","").replace(".jpg","").replace(".png",""),`img-${e}`,`image-${e}`,e.replace("img-","").replace("image-","")])for(let e of Object.keys(t))if(e.includes(r)||r.includes(e))return e;let r=e.match(/\d+/g);if(r){for(let e of r)for(let r of Object.keys(t))if(r.includes(e))return r}return null}(c,t);if(e&&t[e]){let s=`extracted-image-ref-${n}`,c=a(t[e]);r.push({id:s,src:c,alt:o}),i=i.replace(l,"")}else i=i.replace(l,`[Missing Image: ${c}]`)}else i=i.replace(l,`[Image: ${c}]`)});let o=i.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g);o&&o.forEach((e,t)=>{if(s(e)){let s=`extracted-image-dataurl-${t}`,n=a(e);r.push({id:s,src:n,alt:`Extracted image ${r.length+1}`}),i=i.replace(e,"")}});let c=i.match(/\b[A-Za-z0-9+/]{200,}={0,2}\b/g);return c&&c.forEach((e,t)=>{if(s(e)){let s=`extracted-image-raw-${t}`,n=a(e);r.push({id:s,src:n,alt:`Extracted image ${r.length+1}`}),i=i.replace(e,"")}}),{cleanText:i=i.replace(/\s+/g," ").trim(),images:r}}function n(e){if(!e||!s(e))return null;try{return a(e)}catch(e){return console.warn("Invalid base64 image source:",e),null}}r.d(t,{B_:()=>n,UF:()=>a,XI:()=>s,Xw:()=>i})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},50812:(e,t,r)=>{"use strict";r.d(t,{C:()=>o,z:()=>l});var s=r(60687);r(43210);var a=r(74797),i=r(65822),n=r(4780);function l({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"radio-group",className:(0,n.cn)("grid gap-3",e),...t})}function o({className:e,...t}){return(0,s.jsx)(a.q7,{"data-slot":"radio-group-item",className:(0,n.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71669:(e,t,r)=>{"use strict";r.d(t,{C5:()=>f,MJ:()=>g,Rr:()=>j,eI:()=>h,lR:()=>x,lV:()=>c,zB:()=>u});var s=r(60687),a=r(43210),i=r(11329),n=r(27605),l=r(4780),o=r(80013);let c=n.Op,d=a.createContext({}),u=({...e})=>(0,s.jsx)(d.Provider,{value:{name:e.name},children:(0,s.jsx)(n.xI,{...e})}),p=()=>{let e=a.useContext(d),t=a.useContext(m),{getFieldState:r}=(0,n.xW)(),s=(0,n.lN)({name:e.name}),i=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},m=a.createContext({});function h({className:e,...t}){let r=a.useId();return(0,s.jsx)(m.Provider,{value:{id:r},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...t})})}function x({className:e,...t}){let{error:r,formItemId:a}=p();return(0,s.jsx)(o.J,{"data-slot":"form-label","data-error":!!r,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...t})}function g({...e}){let{error:t,formItemId:r,formDescriptionId:a,formMessageId:n}=p();return(0,s.jsx)(i.Slot,{"data-slot":"form-control",id:r,"aria-describedby":t?`${a} ${n}`:`${a}`,"aria-invalid":!!t,...e})}function j({className:e,...t}){let{formDescriptionId:r}=p();return(0,s.jsx)("p",{"data-slot":"form-description",id:r,className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}function f({className:e,...t}){let{error:r,formMessageId:a}=p(),i=r?String(r?.message??""):t.children;return i?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,l.cn)("text-destructive text-sm",e),...t,children:i}):null}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79712:(e,t,r)=>{Promise.resolve().then(r.bind(r,7397))},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(60687);r(43210);var a=r(78148),i=r(4780);function n({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},82013:(e,t,r)=>{"use strict";r.d(t,{CG:()=>i,Fb:()=>o,Nj:()=>a,getSubjectsWithTopics:()=>l,gg:()=>d,iQ:()=>c,py:()=>n});var s=r(62185);let a=async()=>{try{return await (0,s.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},i=async()=>{try{return(await (0,s.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},n=async()=>{try{return await (0,s.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},l=async()=>{try{return await (0,s.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},o=async e=>{try{return await (0,s.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},c=async(e,t)=>{try{return await (0,s.apiCall)(`/subjects/${e}`,{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error(`Error updating subject ${e}:`,t),Error(t.message||"Failed to update subject")}},d=async e=>{try{await (0,s.apiCall)(`/subjects/${e}`,{method:"DELETE"})}catch(t){throw console.error(`Error deleting subject ${e}:`,t),Error(t.message||"Failed to delete subject")}}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},96236:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\edit-question\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,4619,3287,9592,2581,1991,3442,6822,5361,694,4707,6658,5091],()=>r(7295));module.exports=s})();