(()=>{var e={};e.id=879,e.ids=[879],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3416:(e,r,t)=>{"use strict";t.d(r,{sG:()=>u,hO:()=>m});var s=t(43210),a=t(51215),n=t(98599),o=t(60687),i=s.forwardRef((e,r)=>{let{children:t,...a}=e,n=s.Children.toArray(t),i=n.find(c);if(i){let e=i.props.children,t=n.map(r=>r!==i?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,o.jsx)(l,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,o.jsx)(l,{...a,ref:r,children:t})});i.displayName="Slot";var l=s.forwardRef((e,r)=>{let{children:t,...a}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),o=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{n(...e),a(...e)}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==s.Fragment&&(o.ref=r?(0,n.t)(r,e):e),s.cloneElement(t,o)}return s.Children.count(t)>1?s.Children.only(null):null});l.displayName="SlotClone";var d=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===d}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...a}=e,n=s?i:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(n,{...a,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function m(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12048:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(60687);t(43210);var a=t(4780);function n({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20205:(e,r,t)=>{Promise.resolve().then(t.bind(t,33221))},22413:(e,r,t)=>{Promise.resolve().then(t.bind(t,38255))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>i});var s=t(60687);t(43210);var a=t(11329),n=t(24224),o=t(4780);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:n=!1,...l}){let d=n?a.Slot:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:t,className:e})),...l})}},33221:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var s=t(60687),a=t(43210),n=t(16189),o=t(85814),i=t.n(o),l=t(63442),d=t(27605),c=t(45880),u=t(12597),m=t(13861),p=t(52581),x=t(29523),f=t(71669),h=t(12048),g=t(60478),b=t(62185);let v=c.Ik({name:c.Yj().min(2,{message:"Name must be at least 2 characters."}),email:c.Yj().email({message:"Please enter a valid email address."}),password:c.Yj().min(6,{message:"Password must be at least 6 characters."}),confirmPassword:c.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function y(){let e=(0,n.useRouter)(),{signUp:r,loginWithGoogle:t,deleteAccount:o,logout:c}=(0,g.A)(),[y,w]=(0,a.useState)(!1),[j,N]=(0,a.useState)(!1),[k,C]=(0,a.useState)(!1),[P,R]=(0,a.useState)(!1),S=(0,d.mN)({resolver:(0,l.u)(v),defaultValues:{name:"",email:"",password:"",confirmPassword:""}});async function A(e){w(!0);try{await r(e.email,e.password,e.name);try{let e=await (0,b.K8)();if(!e||!e.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",e.accessToken),e.user&&e.user.role)localStorage.setItem("userRole",e.user.role),M(e.user.role),p.oR.success("Signed up successfully!");else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),p.oR.error(e.message||"Backend authentication failed. Please try again.");try{await o()}catch(e){console.error("Failed to delete Firebase account:",e)}return}}catch(e){console.error("Signup error:",e),p.oR.error(e.message||"Failed to sign up. Please try again.")}finally{w(!1)}}async function E(){N(!0);try{await t();try{let e=await (0,b.K8)();if(!e||!e.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",e.accessToken),e.user&&e.user.role)localStorage.setItem("userRole",e.user.role),M(e.user.role),p.oR.success("Signed up with Google successfully!");else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),p.oR.error(e.message||"Backend authentication failed. Please try again."),await c();return}}catch(e){console.error("Google signup error:",e),p.oR.error(e.message||"Failed to sign up with Google. Please try again.")}finally{N(!1)}}function M(r){switch(r){case"superAdmin":e.push("/admin");break;case"collegeAdmin":e.push("/college");break;case"teacher":e.push("/teacher");break;default:e.push("/")}}return(0,s.jsxs)("div",{className:"flex min-h-screen",children:[(0,s.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center p-8 bg-white",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("img",{src:"/assets/logo/medicos-logo.svg",alt:"MEDICOS",className:"h-[70px] w-auto"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Create Account"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Enter your details to create a new account"}),(0,s.jsxs)("button",{type:"button",onClick:E,disabled:j,className:"w-full flex items-center justify-center gap-2 border border-gray-300 rounded-md py-2 px-4 mb-6 text-gray-700 hover:bg-gray-50 disabled:opacity-70 disabled:cursor-not-allowed",children:[j?(0,s.jsx)("div",{className:"h-5 w-5 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"}):(0,s.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M19.9895 10.1871C19.9895 9.36767 19.9214 8.76973 19.7742 8.14966H10.1992V11.848H15.8195C15.7062 12.7671 15.0943 14.1512 13.7346 15.0813L13.7155 15.2051L16.7429 17.4969L16.9527 17.5174C18.879 15.7789 19.9895 13.221 19.9895 10.1871Z",fill:"#4285F4"}),(0,s.jsx)("path",{d:"M10.1993 19.9313C12.9527 19.9313 15.2643 19.0454 16.9527 17.5174L13.7346 15.0813C12.8734 15.6682 11.7176 16.0779 10.1993 16.0779C7.50243 16.0779 5.21352 14.3395 4.39759 11.9366L4.27799 11.9466L1.13003 14.3273L1.08887 14.4391C2.76588 17.6945 6.21061 19.9313 10.1993 19.9313Z",fill:"#34A853"}),(0,s.jsx)("path",{d:"M4.39748 11.9366C4.18219 11.3166 4.05759 10.6521 4.05759 9.96565C4.05759 9.27909 4.18219 8.61473 4.38615 7.99466L4.38045 7.8626L1.19304 5.44366L1.08875 5.49214C0.397576 6.84305 0.000976562 8.36008 0.000976562 9.96565C0.000976562 11.5712 0.397576 13.0882 1.08875 14.4391L4.39748 11.9366Z",fill:"#FBBC05"}),(0,s.jsx)("path",{d:"M10.1993 3.85336C12.1142 3.85336 13.406 4.66168 14.1425 5.33717L17.0207 2.59107C15.253 0.985496 12.9527 0 10.1993 0C6.2106 0 2.76588 2.23672 1.08887 5.49214L4.38626 7.99466C5.21352 5.59183 7.50242 3.85336 10.1993 3.85336Z",fill:"#EB4335"})]}),j?"Signing up...":"Sign up with Google"]}),(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"flex-grow border-t border-gray-300"}),(0,s.jsx)("span",{className:"mx-4 text-sm text-gray-500",children:"Or"}),(0,s.jsx)("div",{className:"flex-grow border-t border-gray-300"})]}),(0,s.jsx)(f.lV,{...S,children:(0,s.jsxs)("form",{onSubmit:S.handleSubmit(A),className:"space-y-4",children:[(0,s.jsx)(f.zB,{control:S.control,name:"name",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-sm font-medium text-gray-700",children:"Full Name*"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"John Doe",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S.control,name:"email",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-sm font-medium text-gray-700",children:"Email Address*"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(h.p,{placeholder:"<EMAIL>",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S.control,name:"password",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-sm font-medium text-gray-700",children:"Password*"}),(0,s.jsx)(f.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.p,{type:k?"text":"password",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e}),(0,s.jsx)("button",{type:"button",onClick:()=>C(!k),className:"absolute right-3 top-2.5 text-gray-400",children:k?(0,s.jsx)(u.A,{className:"h-5 w-5"}):(0,s.jsx)(m.A,{className:"h-5 w-5"})})]})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:S.control,name:"confirmPassword",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-sm font-medium text-gray-700",children:"Confirm Password*"}),(0,s.jsx)(f.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.p,{type:P?"text":"password",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e}),(0,s.jsx)("button",{type:"button",onClick:()=>R(!P),className:"absolute right-3 top-2.5 text-gray-400",children:P?(0,s.jsx)(u.A,{className:"h-5 w-5"}):(0,s.jsx)(m.A,{className:"h-5 w-5"})})]})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(x.$,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md mt-6",disabled:y,children:y?"Creating account...":"Create Account"}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(i(),{href:"/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign In"})]})})]})})]})}),(0,s.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-green-800 items-center justify-center p-12 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-10"}),(0,s.jsx)("div",{className:"relative z-10 text-center max-w-md",children:(0,s.jsxs)("blockquote",{className:"text-white text-xl font-medium",children:['"Education is the most powerful weapon which you can use to change the world."',(0,s.jsx)("footer",{className:"mt-2 text-white text-opacity-80",children:"– Nelson Mandela"})]})})]})]})}},33873:e=>{"use strict";e.exports=require("path")},38255:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx","default")},51037:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38255)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,r,t)=>{"use strict";t.d(r,{C5:()=>b,MJ:()=>h,Rr:()=>g,eI:()=>x,lR:()=>f,lV:()=>d,zB:()=>u});var s=t(60687),a=t(43210),n=t(11329),o=t(27605),i=t(4780),l=t(80013);let d=o.Op,c=a.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(o.xI,{...e})}),m=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:t}=(0,o.xW)(),s=(0,o.lN)({name:e.name}),n=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...n}},p=a.createContext({});function x({className:e,...r}){let t=a.useId();return(0,s.jsx)(p.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",e),...r})})}function f({className:e,...r}){let{error:t,formItemId:a}=m();return(0,s.jsx)(l.J,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...r})}function h({...e}){let{error:r,formItemId:t,formDescriptionId:a,formMessageId:o}=m();return(0,s.jsx)(n.Slot,{"data-slot":"form-control",id:t,"aria-describedby":r?`${a} ${o}`:`${a}`,"aria-invalid":!!r,...e})}function g({className:e,...r}){let{formDescriptionId:t}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:t,className:(0,i.cn)("text-muted-foreground text-sm",e),...r})}function b({className:e,...r}){let{error:t,formMessageId:a}=m(),n=t?String(t?.message??""):r.children;return n?(0,s.jsx)("p",{"data-slot":"form-message",id:a,className:(0,i.cn)("text-destructive text-sm",e),...r,children:n}):null}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var s=t(60687);t(43210);var a=t(78148),n=t(4780);function o({className:e,...r}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>o,t:()=>n});var s=t(43210);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function o(...e){return s.useCallback(n(...e),e)}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4619,3287,2581,1991,3442,4707],()=>t(51037));module.exports=s})();