"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6106],{47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},66474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},77740:(e,t,r)=>{r.d(t,{uB:()=>P});var n=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,a=/[\s-]/,u=/[\s-]/g;function i(e){return e.toLowerCase().replace(u," ")}var c=r(4033),o=r(12115),d=r(63540),s=r(61285),f=r(6101),v='[cmdk-group=""]',m='[cmdk-group-items=""]',h='[cmdk-item=""]',p="".concat(h,':not([aria-disabled="true"])'),g="cmdk-item-select",b="data-value",E=(e,t,r)=>(function(e,t,r){return function e(t,r,i,c,o,d,s){if(d===r.length)return o===t.length?1:.99;var f=`${o},${d}`;if(void 0!==s[f])return s[f];for(var v,m,h,p,g=c.charAt(d),b=i.indexOf(g,o),E=0;b>=0;)(v=e(t,r,i,c,b+1,d+1,s))>E&&(b===o?v*=1:n.test(t.charAt(b-1))?(v*=.8,(h=t.slice(o,b-1).match(l))&&o>0&&(v*=Math.pow(.999,h.length))):a.test(t.charAt(b-1))?(v*=.9,(p=t.slice(o,b-1).match(u))&&o>0&&(v*=Math.pow(.999,p.length))):(v*=.17,o>0&&(v*=Math.pow(.999,b-o))),t.charAt(b)!==r.charAt(d)&&(v*=.9999)),(v<.1&&i.charAt(b-1)===c.charAt(d+1)||c.charAt(d+1)===c.charAt(d)&&i.charAt(b-1)!==c.charAt(d))&&.1*(m=e(t,r,i,c,b+1,d+2,s))>v&&(v=.1*m),v>E&&(E=v),b=i.indexOf(g,b+1);return s[f]=E,E}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,i(e),i(t),0,0,{})})(e,t,r),w=o.createContext(void 0),k=()=>o.useContext(w),y=o.createContext(void 0),S=()=>o.useContext(y),A=o.createContext(void 0),C=o.forwardRef((e,t)=>{let r=L(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=L(()=>new Set),l=L(()=>new Map),a=L(()=>new Map),u=L(()=>new Set),i=q(e),{label:c,children:f,value:k,onValueChange:S,filter:A,shouldFilter:C,loop:I,disablePointerSelection:x=!1,vimBindings:R=!0,...M}=e,D=(0,s.B)(),B=(0,s.B)(),P=(0,s.B)(),F=o.useRef(null),K=O();G(()=>{if(void 0!==k){let e=k.trim();r.current.value=e,N.emit()}},[k]),G(()=>{K(6,J)},[]);let N=o.useMemo(()=>({subscribe:e=>(u.current.add(e),()=>u.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var l,a,u,c;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)H(),U(),K(1,$);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(P);e?e.focus():null==(l=document.getElementById(D))||l.focus()}if(K(7,()=>{var e;r.current.selectedItemId=null==(e=W())?void 0:e.id,N.emit()}),n||K(5,J),(null==(a=i.current)?void 0:a.value)!==void 0){null==(c=(u=i.current).onValueChange)||c.call(u,null!=t?t:"");return}}N.emit()}},emit:()=>{u.current.forEach(e=>e())}}),[]),_=o.useMemo(()=>({value:(e,t,n)=>{var l;t!==(null==(l=a.current.get(e))?void 0:l.value)&&(a.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,z(t,n)),K(2,()=>{U(),N.emit()}))},item:(e,t)=>(n.current.add(e),t&&(l.current.has(t)?l.current.get(t).add(e):l.current.set(t,new Set([e]))),K(3,()=>{H(),U(),r.current.value||$(),N.emit()}),()=>{a.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=W();K(4,()=>{H(),(null==t?void 0:t.getAttribute("id"))===e&&$(),N.emit()})}),group:e=>(l.current.has(e)||l.current.set(e,new Set),()=>{a.current.delete(e),l.current.delete(e)}),filter:()=>i.current.shouldFilter,label:c||e["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:D,inputId:P,labelId:B,listInnerRef:F}),[]);function z(e,t){var n,l;let a=null!=(l=null==(n=i.current)?void 0:n.filter)?l:E;return e?a(e,r.current.search,t):0}function U(){if(!r.current.search||!1===i.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=l.current.get(r),a=0;n.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let n=F.current;Z().sort((t,r)=>{var n,l;let a=t.getAttribute("id"),u=r.getAttribute("id");return(null!=(n=e.get(u))?n:0)-(null!=(l=e.get(a))?l:0)}).forEach(e=>{let t=e.closest(m);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(m," > *"))):n.appendChild(e.parentElement===n?e:e.closest("".concat(m," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=F.current)?void 0:t.querySelector("".concat(v,"[").concat(b,'="').concat(encodeURIComponent(e[0]),'"]'));null==r||r.parentElement.appendChild(r)})}function $(){let e=Z().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);N.setState("value",t||void 0)}function H(){var e,t,u,c;if(!r.current.search||!1===i.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let o=0;for(let l of n.current){let n=z(null!=(t=null==(e=a.current.get(l))?void 0:e.value)?t:"",null!=(c=null==(u=a.current.get(l))?void 0:u.keywords)?c:[]);r.current.filtered.items.set(l,n),n>0&&o++}for(let[e,t]of l.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=o}function J(){var e,t,r;let n=W();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(v))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function W(){var e;return null==(e=F.current)?void 0:e.querySelector("".concat(h,'[aria-selected="true"]'))}function Z(){var e;return Array.from((null==(e=F.current)?void 0:e.querySelectorAll(p))||[])}function Q(e){let t=Z()[e];t&&N.setState("value",t.getAttribute(b))}function T(e){var t;let r=W(),n=Z(),l=n.findIndex(e=>e===r),a=n[l+e];null!=(t=i.current)&&t.loop&&(a=l+e<0?n[n.length-1]:l+e===n.length?n[0]:n[l+e]),a&&N.setState("value",a.getAttribute(b))}function X(e){let t=W(),r=null==t?void 0:t.closest(v),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,v):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,v))?void 0:r.querySelector(p);n?N.setState("value",n.getAttribute(b)):T(e)}let Y=()=>Q(Z().length-1),ee=e=>{e.preventDefault(),e.metaKey?Y():e.altKey?X(1):T(1)},et=e=>{e.preventDefault(),e.metaKey?Q(0):e.altKey?X(-1):T(-1)};return o.createElement(d.sG.div,{ref:t,tabIndex:-1,...M,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=M.onKeyDown)||t.call(M,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":R&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":R&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),Q(0);break;case"End":e.preventDefault(),Y();break;case"Enter":{e.preventDefault();let t=W();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},o.createElement("label",{"cmdk-label":"",htmlFor:_.inputId,id:_.labelId,style:j},c),V(e,e=>o.createElement(y.Provider,{value:N},o.createElement(w.Provider,{value:_},e))))}),I=o.forwardRef((e,t)=>{var r,n;let l=(0,s.B)(),a=o.useRef(null),u=o.useContext(A),i=k(),c=q(e),v=null!=(n=null==(r=c.current)?void 0:r.forceMount)?n:null==u?void 0:u.forceMount;G(()=>{if(!v)return i.item(l,null==u?void 0:u.id)},[v]);let m=K(l,a,[e.value,e.children,a],e.keywords),h=S(),p=F(e=>e.value&&e.value===m.current),b=F(e=>!!v||!1===i.filter()||!e.search||e.filtered.items.get(l)>0);function E(){var e,t;w(),null==(t=(e=c.current).onSelect)||t.call(e,m.current)}function w(){h.setState("value",m.current,!0)}if(o.useEffect(()=>{let t=a.current;if(!(!t||e.disabled))return t.addEventListener(g,E),()=>t.removeEventListener(g,E)},[b,e.onSelect,e.disabled]),!b)return null;let{disabled:y,value:C,onSelect:I,forceMount:x,keywords:R,...M}=e;return o.createElement(d.sG.div,{ref:(0,f.t)(a,t),...M,id:l,"cmdk-item":"",role:"option","aria-disabled":!!y,"aria-selected":!!p,"data-disabled":!!y,"data-selected":!!p,onPointerMove:y||i.getDisablePointerSelection()?void 0:w,onClick:y?void 0:E},e.children)}),x=o.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:l,...a}=e,u=(0,s.B)(),i=o.useRef(null),c=o.useRef(null),v=(0,s.B)(),m=k(),h=F(e=>!!l||!1===m.filter()||!e.search||e.filtered.groups.has(u));G(()=>m.group(u),[]),K(u,i,[e.value,e.heading,c]);let p=o.useMemo(()=>({id:u,forceMount:l}),[l]);return o.createElement(d.sG.div,{ref:(0,f.t)(i,t),...a,"cmdk-group":"",role:"presentation",hidden:!h||void 0},r&&o.createElement("div",{ref:c,"cmdk-group-heading":"","aria-hidden":!0,id:v},r),V(e,e=>o.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?v:void 0},o.createElement(A.Provider,{value:p},e))))}),R=o.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,l=o.useRef(null),a=F(e=>!e.search);return r||a?o.createElement(d.sG.div,{ref:(0,f.t)(l,t),...n,"cmdk-separator":"",role:"separator"}):null}),M=o.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,l=null!=e.value,a=S(),u=F(e=>e.search),i=F(e=>e.selectedItemId),c=k();return o.useEffect(()=>{null!=e.value&&a.setState("search",e.value)},[e.value]),o.createElement(d.sG.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.listId,"aria-labelledby":c.labelId,"aria-activedescendant":i,id:c.inputId,type:"text",value:l?e.value:u,onChange:e=>{l||a.setState("search",e.target.value),null==r||r(e.target.value)}})}),D=o.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...l}=e,a=o.useRef(null),u=o.useRef(null),i=F(e=>e.selectedItemId),c=k();return o.useEffect(()=>{if(u.current&&a.current){let e=u.current,t=a.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),o.createElement(d.sG.div,{ref:(0,f.t)(a,t),...l,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":i,"aria-label":n,id:c.listId},V(e,e=>o.createElement("div",{ref:(0,f.t)(u,c.listInnerRef),"cmdk-list-sizer":""},e)))}),B=o.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:l,contentClassName:a,container:u,...i}=e;return o.createElement(c.bL,{open:r,onOpenChange:n},o.createElement(c.ZL,{container:u},o.createElement(c.hJ,{"cmdk-overlay":"",className:l}),o.createElement(c.UC,{"aria-label":e.label,"cmdk-dialog":"",className:a},o.createElement(C,{ref:t,...i}))))}),P=Object.assign(C,{List:D,Item:I,Input:M,Group:x,Separator:R,Dialog:B,Empty:o.forwardRef((e,t)=>F(e=>0===e.filtered.count)?o.createElement(d.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:o.forwardRef((e,t)=>{let{progress:r,children:n,label:l="Loading...",...a}=e;return o.createElement(d.sG.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":l},V(e,e=>o.createElement("div",{"aria-hidden":!0},e)))})});function q(e){let t=o.useRef(e);return G(()=>{t.current=e}),t}var G="undefined"==typeof window?o.useEffect:o.useLayoutEffect;function L(e){let t=o.useRef();return void 0===t.current&&(t.current=e()),t}function F(e){let t=S(),r=()=>e(t.snapshot());return o.useSyncExternalStore(t.subscribe,r,r)}function K(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],l=o.useRef(),a=k();return G(()=>{var u;let i=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():l.current}})(),c=n.map(e=>e.trim());a.value(e,i,c),null==(u=t.current)||u.setAttribute(b,i),l.current=i}),l}var O=()=>{let[e,t]=o.useState(),r=L(()=>new Map);return G(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function V(e,t){let r,{asChild:n,children:l}=e;return n&&o.isValidElement(l)?o.cloneElement("function"==typeof(r=l.type)?r(l.props):"render"in r?r.render(l.props):l,{ref:l.ref},t(l.props.children)):t(l)}var j={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}}}]);