(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1297],{5623:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5914:(e,t,a)=>{Promise.resolve().then(a.bind(a,59913))},11723:(e,t,a)=>{"use strict";a.d(t,{HC:()=>o,P_:()=>l,Rb:()=>c,Sp:()=>s,q6:()=>n,qg:()=>i});var r=a(55097);let n=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let n=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e,"/teachers"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to add teacher. Please try again.")}let o=await n.json();return(0,r.$y)(o,!0,"Teacher added successfully!")}catch(e){return console.error("Error adding teacher:",e),(0,r.hS)(e.message||"Failed to add teacher. Please try again.","Failed to add teacher. Please try again.")}},o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};try{let o=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!o)return console.error("No authentication token found"),(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let s=new URLSearchParams({page:t.toString(),limit:a.toString(),...n}),i="".concat("http://localhost:3000/api","/colleges/").concat(e,"/teachers?").concat(s);console.log("Fetching teachers: ".concat(i));let l=await fetch(i,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o)},cache:"no-store"});if(!l.ok){let e=await l.text();try{let t=JSON.parse(e);return(0,r.hS)(t.message||"Error: ".concat(l.status),"Failed to load teachers. Please try again.")}catch(e){return(0,r.hS)("Error: ".concat(l.status," - ").concat(l.statusText),"Failed to load teachers. Please try again.")}}let c=await l.json();if(console.log("Raw API response:",c),Array.isArray(c)){console.log("API returned an array, converting to paginated format");let e={teachers:c,total:c.length,page:t,limit:a,totalPages:Math.ceil(c.length/a)};return(0,r.$y)(e)}return(0,r.$y)(c)}catch(e){return console.error("Error fetching college teachers:",e),(0,r.hS)(e.message||"Failed to load teachers. Please try again.","Failed to load teachers. Please try again.")}},s=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher with data:",t);let n=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to update teacher. Please try again.")}let o=await n.json();return(0,r.$y)(o,!0,"Teacher updated successfully!")}catch(e){return console.error("Error updating teacher:",e),(0,r.hS)(e.message||"Failed to update teacher. Please try again.","Failed to update teacher. Please try again.")}},i=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let a=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to delete teacher. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"Teacher deleted successfully!")}catch(e){return console.error("Error deleting teacher:",e),(0,r.hS)(e.message||"Failed to delete teacher","Failed to delete teacher. Please try again.")}},l=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher profile with data:",e);let a=await fetch("".concat("http://localhost:3000/api","/teachers/me"),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to update profile. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"Profile updated successfully!")}catch(e){return console.error("Error updating teacher profile:",e),(0,r.hS)(e.message||"Failed to update profile. Please try again.","Failed to update profile. Please try again.")}},c=async()=>{try{let e=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let t=await fetch("".concat("http://localhost:3000/api","/users/me"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(t.status),"Failed to load profile. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching current teacher profile:",e),(0,r.hS)(e.message||"Failed to load profile. Please try again.","Failed to load profile. Please try again.")}}},12379:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(95155),n=a(12115),o=a(6874),s=a.n(o),i=a(59434),l=a(13052),c=a(5623);let d=e=>{let{items:t,maxItems:a=4,className:o}=e,d=n.useMemo(()=>t.length<=a?t:[t[0],{label:"..."},...t.slice(-2)],[t,a]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",o),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let a=t===d.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(l.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}):a?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(s(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},13052:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19946:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,a)=>a?a.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return t.filter((e,t,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)((e,t)=>{let{color:a="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:s,className:c="",children:d,iconNode:u,...h}=e;return(0,r.createElement)("svg",{ref:t,...l,width:n,height:n,stroke:a,strokeWidth:s?24*Number(o)/Number(n):o,className:i("lucide",c),...h},[...u.map(e=>{let[t,a]=e;return(0,r.createElement)(t,a)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let a=(0,r.forwardRef)((a,o)=>{let{className:l,...d}=a;return(0,r.createElement)(c,{ref:o,iconNode:t,className:i("lucide-".concat(n(s(e))),"lucide-".concat(e),l),...d})});return a.displayName=s(e),a}},40124:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});let r=a(23464).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}});async function n(){try{let e=localStorage.getItem("backendToken");console.log("Token being sent:",e);let t=await fetch("http://localhost:3000/api/users/me",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json",...e?{Authorization:"Bearer ".concat(e)}:{}}});if(!t.ok)return console.error("Failed to fetch current user:",t.statusText),null;return await t.json()}catch(e){return console.error("Error fetching current user:",e),null}}r.interceptors.request.use(e=>{{let t=localStorage.getItem("token");if(!t)return window.location.href="/login",Promise.reject("No token found");e.headers.Authorization="Bearer ".concat(t)}return e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)})},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55097:(e,t,a)=>{"use strict";a.d(t,{$y:()=>o,cY:()=>s,hS:()=>n});var r=a(56671);function n(e){var t,a,n,o;let s,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",l=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=i;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)&&(c=e.data.message),(null==e?void 0:e.status)?s=e.status:(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)&&(s=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===s?c="Server error. Please try again later.":503===s&&(c="Service temporarily unavailable. Please try again later.")),l&&r.oR.error(c),{success:!1,error:c,statusCode:s}}function o(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&r.oR.success(a),{success:!0,data:e}}function s(e){return!0===e.success}},59434:(e,t,a)=>{"use strict";a.d(t,{b:()=>s,cn:()=>o});var r=a(52596),n=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function s(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},59913:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(95155),n=a(12115),o=a(12379),s=a(66695),i=a(71007);function l(e){let{profileData:t,onEdit:a}=e;return(0,r.jsxs)("div",{className:"w-full mx-auto space-y-6 py-10",children:[(0,r.jsxs)(s.Zp,{children:[(0,r.jsx)(s.aR,{children:(0,r.jsx)(s.ZB,{className:"text-lg font-medium",children:"My Profile"})}),(0,r.jsx)(s.Wu,{className:"pb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"relative h-16 w-16 rounded-full overflow-hidden",children:t.profileImage?(0,r.jsx)("img",{src:t.profileImage,alt:t.firstName,className:"w-10 h-10 rounded-full object-cover border"}):(0,r.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"w-8 h-8 text-gray-400"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-base",children:"".concat(t.firstName," ").concat(t.lastName)}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex flex-col sm:flex-row sm:gap-2",children:[(0,r.jsx)("span",{children:t.designation}),t.location&&(0,r.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,r.jsx)("span",{children:t.location})]})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-2"})]})})]}),(0,r.jsxs)(s.Zp,{children:[(0,r.jsx)(s.aR,{className:"flex flex-row items-center justify-between",children:(0,r.jsx)(s.ZB,{className:"text-lg font-medium",children:"Personal Information"})}),(0,r.jsx)(s.Wu,{className:"pb-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"First Name"}),(0,r.jsx)("p",{className:"font-medium",children:t.firstName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Last Name"}),(0,r.jsx)("p",{className:"font-medium",children:t.lastName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Email address"}),(0,r.jsx)("p",{className:"font-medium",children:t.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Designation"}),(0,r.jsx)("p",{className:"font-medium",children:t.designation})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Department"}),(0,r.jsx)("p",{className:"font-medium",children:t.department})]})]})})]})]})}a(11723);let c=async e=>{try{let t=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")||localStorage.getItem("backendToken"))}});if(!t.ok)throw Error("Failed to fetch teacher");return await t.json()}catch(e){throw console.error("Error fetching teacher by ID:",e),e}};var d=a(40124),u=a(88262),h=a(51154);let m=()=>{let[e,t]=(0,n.useState)(null),[a,s]=(0,n.useState)(!0),[i,m]=(0,n.useState)(null);return(0,n.useEffect)(()=>{(async()=>{try{s(!0);let e=await (0,d.p)();if(!e||!e._id)throw Error("Unable to retrieve user information");let a=await c(e._id);if(!a)throw Error("Unable to retrieve teacher profile");t({firstName:a.firstName||"",lastName:a.lastName||"",occupation:"Teacher",location:a.cityState||"",email:a.email||"",gender:a.gender||"",designation:a.designation||"",dateOfBirth:a.dateOfBirth||"",phone:a.phone||"",country:a.country||"",cityState:a.cityState||"",postalCode:a.postalCode||"",taxId:a.taxId||"",profileImage:a.profileImageUrl||"",department:a.department||""})}catch(e){console.error("Error fetching profile:",e),m(e.message||"Failed to load profile data"),(0,u.o)({title:"Error",description:e.message||"Failed to load profile data",variant:"destructive"})}finally{s(!1)}})()},[]),(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-black",children:"User Profile"}),(0,r.jsx)(o.A,{items:[{label:"Home",href:"/teacher"},{label:"...",href:"#"},{label:"User Profile"}],className:"text-sm mt-1"})]}),a?(0,r.jsxs)("div",{className:"flex justify-center items-center py-20",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading profile..."})]}):i?(0,r.jsxs)("div",{className:"text-center py-20 text-destructive",children:[(0,r.jsx)("p",{children:i}),(0,r.jsx)("button",{className:"mt-4 px-4 py-2 bg-primary text-white rounded-md",onClick:()=>window.location.reload(),children:"Retry"})]}):e?(0,r.jsx)(l,{profileData:e,onEdit:e=>{console.log("Edit ".concat(e," section"))}}):(0,r.jsx)("div",{className:"text-center py-20 text-muted-foreground",children:"No profile data available"})]})})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>s});var r=a(95155);a(12115);var n=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}},88262:(e,t,a)=>{"use strict";a.d(t,{d:()=>c,o:()=>d});var r=a(12115);new EventTarget;let n=[],o=[];function s(){o.forEach(e=>e([...n]))}function i(e){let t=e.id||Math.random().toString(36).substring(2,9),a={...e,id:t};return n=[...n,a],s(),setTimeout(()=>{l(t)},5e3),t}function l(e){n=n.filter(t=>t.id!==e),s()}function c(){let[e,t]=r.useState(n);return r.useEffect(()=>(o.push(t),t([...n]),()=>{o=o.filter(e=>e!==t)}),[]),{toast:e=>i(e),dismiss:e=>{e?l(e):n.forEach(e=>e.id&&l(e.id))},toasts:e}}let d=e=>i(e)}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6671,9641,506,8441,1684,7358],()=>t(5914)),_N_E=e.O()}]);