(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6935],{71542:(e,l,a)=>{"use strict";a.r(l),a.d(l,{default:()=>C});var s=a(95155),r=a(12115),t=a(12379),n=a(90221),o=a(62177),c=a(55594),i=a(46104),d=a(30285),m=a(17759),h=a(62523),u=a(88539),x=a(95984),g=a(88262),j=a(35695),p=a(74125),f=a(55097);async function v(e){let l=localStorage.getItem("backendToken");if(!l)throw Error("Authentication required");try{let a=new FormData;a.append("file",e);let s=await fetch("".concat("http://localhost:3000/api","/upload"),{method:"POST",headers:{Authorization:"Bearer ".concat(l)},body:a});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(s.status))}return(await s.json()).url}catch(e){throw console.error("Error uploading file:",e),e}}let N=["image/jpeg","image/jpg","image/png","image/svg+xml"],b=c.Ik({collegeName:c.Yj().min(2,{message:"College name must be at least 2 characters."}),phone:c.Yj().min(10,{message:"Phone number must be valid."}),email:c.Yj().email({message:"Please enter a valid email address."}),address:c.Yj().max(100,{message:"Address must not exceed 100 characters."}),logo:c.bz().optional().refine(e=>{var l;return!e||0===e.length||(null===(l=e[0])||void 0===l?void 0:l.size)<=0x3200000},"Max file size is 50MB.").refine(e=>{var l;return!e||0===e.length||N.includes(null===(l=e[0])||void 0===l?void 0:l.type)},"Only .jpg, .jpeg, .png and .svg formats are supported.")});function y(e){let{collegeData:l,collegeId:a}=e,[t,c]=(0,r.useState)([]),[y,C]=(0,r.useState)(!1),[w,E]=(0,r.useState)(null),k=(0,j.useRouter)(),z=(0,o.mN)({resolver:(0,n.u)(b),defaultValues:{collegeName:"",phone:"",email:"",address:""}});async function P(e){C(!0);try{let l=w;t.length>0&&(l=await v(t[0]));let s={name:e.collegeName,address:e.address,contactPhone:e.phone,contactEmail:e.email,logoUrl:l||void 0},r=await (0,p.N0)(a,s);(0,f.cY)(r)&&k.push("/admin/college")}catch(e){console.error("Unexpected error updating college:",e),(0,g.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{C(!1)}}return(0,r.useEffect)(()=>{l&&(z.reset({collegeName:l.name||"",phone:l.contactPhone||"",email:l.contactEmail||"",address:l.address||""}),l.logoUrl&&E(l.logoUrl))},[l,z]),(0,s.jsx)(m.lV,{...z,children:(0,s.jsxs)("form",{onSubmit:z.handleSubmit(P),className:"space-y-8 mx-auto",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(m.zB,{control:z.control,name:"collegeName",render:e=>{let{field:l}=e;return(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"College name"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(h.p,{placeholder:"",...l})}),(0,s.jsx)(m.C5,{})]})}}),(0,s.jsx)(m.zB,{control:z.control,name:"phone",render:e=>{let{field:l}=e;return(0,s.jsxs)(m.eI,{children:[(0,s.jsxs)(m.lR,{children:["Phone",(0,s.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Required"})]}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(x.L,{...l})}),(0,s.jsx)(m.C5,{})]})}})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(m.zB,{control:z.control,name:"email",render:e=>{let{field:l}=e;return(0,s.jsxs)(m.eI,{children:[(0,s.jsx)(m.lR,{children:"Email address"}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(h.p,{placeholder:"",...l})}),(0,s.jsx)(m.Rr,{children:"We'll never share your details."}),(0,s.jsx)(m.C5,{})]})}}),(0,s.jsx)(m.zB,{control:z.control,name:"address",render:e=>{let{field:l}=e;return(0,s.jsxs)(m.eI,{children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(m.lR,{children:"Address details"}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:[l.value.length,"/100"]})]}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(u.T,{placeholder:"",className:"resize-none",maxLength:100,...l})}),(0,s.jsx)(m.C5,{})]})}})]}),(0,s.jsx)(m.zB,{control:z.control,name:"logo",render:e=>{let{field:l}=e;return(0,s.jsxs)(m.eI,{children:[(0,s.jsxs)(m.lR,{children:["Upload logo",(0,s.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Optional"})]}),w&&(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Current logo:"}),(0,s.jsx)("div",{className:"w-20 h-20 bg-gray-100 rounded-md p-2 flex items-center justify-center",children:(0,s.jsx)("img",{src:w,alt:"Current logo",className:"max-w-full max-h-full object-contain"})})]}),(0,s.jsx)(m.MJ,{children:(0,s.jsx)(i.l,{value:l.value,onChange:e=>{l.onChange(e),c(Array.from(e||[]))},maxSize:0x3200000,acceptedTypes:N})}),(0,s.jsx)(m.Rr,{children:"Upload a new logo to replace the current one, or leave empty to keep the existing logo."}),(0,s.jsx)(m.C5,{})]})}}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(d.$,{type:"submit",className:"bg-[#05603A] hover:bg-[#04502F]",disabled:y,children:y?"Updating...":"Update college"}),(0,s.jsx)(d.$,{type:"button",variant:"outline",onClick:function(){k.push("/admin/college")},disabled:y,children:"Cancel"})]})]})})}let C=()=>{let[e,l]=(0,r.useState)(null),[a,n]=(0,r.useState)(!0),o=(0,j.useParams)(),c=(0,j.useRouter)(),i=o.id;return(0,r.useEffect)(()=>{let e=async()=>{try{let e=await (0,p.qk)(i);l(e)}catch(e){console.error("Failed to fetch college:",e),(0,g.o)({title:"Error",description:e.message||"Failed to load college data. Please try again.",variant:"destructive"}),c.push("/admin/college")}finally{n(!1)}};i&&e()},[i,c]),(0,s.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Edit College"}),(0,s.jsx)(t.A,{items:[{label:"Home",href:"/"},{label:"College Management",href:"/admin/college"},{label:"Edit College"}],className:"text-sm mt-1"})]}),(0,s.jsx)("div",{className:"container mx-auto py-10",children:a?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#05603A]"})}):(0,s.jsx)(y,{collegeData:e,collegeId:i})})]})})}},87137:(e,l,a)=>{Promise.resolve().then(a.bind(a,71542))}},e=>{var l=l=>e(e.s=l);e.O(0,[4277,6874,6671,1141,2571,7117,5631,221,6106,124,9078,8441,1684,7358],()=>l(87137)),_N_E=e.O()}]);