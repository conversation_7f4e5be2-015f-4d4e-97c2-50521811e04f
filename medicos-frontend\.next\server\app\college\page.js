(()=>{var e={};e.id=2856,e.ids=[2856],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9823:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(60687),s=r(66327),o=r(53355),n=r(99557),l=r(45285);function i({children:e}){return(0,a.jsx)(l.A,{allowedRoles:[n.g.COLLEGE_ADMIN],children:(0,a.jsx)(o.default,{children:(0,a.jsx)(s.N,{role:n.g.COLLEGE_ADMIN,children:e})})})}},10082:(e,t,r)=>{Promise.resolve().then(r.bind(r,62768))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13853:(e,t,r)=>{Promise.resolve().then(r.bind(r,9823))},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>h,l6:()=>c,yv:()=>d});var a=r(60687);r(43210);var s=r(17403),o=r(78272),n=r(13964),l=r(3589),i=r(4780);function c({...e}){return(0,a.jsx)(s.bL,{"data-slot":"select",...e})}function d({...e}){return(0,a.jsx)(s.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...n}){return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function h({className:e,children:t,position:r="popper",...o}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...o,children:[(0,a.jsx)(g,{}),(0,a.jsx)(s.LM,{className:(0,i.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(p,{})]})})}function m({className:e,children:t,...r}){return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:t})]})}function g({className:e,...t}){return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function p({className:e,...t}){return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22315:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),n=r.n(o),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let c={children:["",{children:["college",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43954)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,67053)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,51995)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/college/page",pathname:"/college",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},26423:(e,t,r)=>{"use strict";r.d(t,{$T:()=>u,JY:()=>o,M0:()=>s,N0:()=>i,mS:()=>c,mi:()=>d,pZ:()=>n,qk:()=>l});var a=r(31981);async function s(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch("http://localhost:3000/api/colleges",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${r.status}`,"Failed to create college. Please try again.")}let s=await r.json();return(0,a.$y)(s,!0,"College created successfully!")}catch(e){return console.error("Error creating college:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to create college. Please try again.","Failed to create college. Please try again.")}}async function o(){let e=localStorage.getItem("backendToken");if(!e)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let t=await fetch("http://localhost:3000/api/colleges",{method:"GET",headers:{Authorization:`Bearer ${e}`}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${t.status}`,"Failed to load colleges. Please try again.")}let r=await t.json();return(0,a.$y)(r)}catch(e){return console.error("Error fetching colleges:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to load colleges. Please try again.","Failed to load colleges. Please try again.")}}async function n(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${r.status}`,"Failed to delete college. Please try again.")}let s=await r.json();return(0,a.$y)(s,!0,"College deleted successfully!")}catch(e){return console.error("Error deleting college:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to delete college. Please try again.","Failed to delete college. Please try again.")}}async function l(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${r.status}`,"Failed to load college. Please try again.")}let s=await r.json();return(0,a.$y)(s)}catch(e){return console.error("Error fetching college:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to load college. Please try again.","Failed to load college. Please try again.")}}async function i(e,t){let r=localStorage.getItem("backendToken");if(!r)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let s=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(t)});if(!s.ok){let e=await s.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${s.status}`,"Failed to update college. Please try again.")}let o=await s.json();return(0,a.$y)(o,!0,"College updated successfully!")}catch(e){return console.error("Error updating college:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to update college. Please try again.","Failed to update college. Please try again.")}}async function c(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let r=await fetch(`http://localhost:3000/api/analytics/college/${e}/summary`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||`Error: ${r.status}`)}return await r.json()}catch(e){throw console.error("Error fetching college:",e),e}}async function d(e,t,r){let a=localStorage.getItem("backendToken");if(!a)throw Error("Authentication required");try{let s=`http://localhost:3000/api/analytics/college/${e}/question-papers?startDate=${encodeURIComponent(t)}&endDate=${encodeURIComponent(r)}`,o=await fetch(s,{method:"GET",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(!o.ok){let e=await o.json().catch(()=>({}));throw Error(e.message||`Error: ${o.status}`)}return await o.json()}catch(e){throw console.error("Error fetching question paper stats:",e),e}}async function u(e,t=10){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let a=`http://localhost:3000/api/analytics/college/${e}/top-teachers?limit=${t}`,s=await fetch(a,{method:"GET",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||`Error: ${s.status}`)}return await s.json()}catch(e){throw console.error("Error fetching top teachers:",e),e}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31981:(e,t,r)=>{"use strict";r.d(t,{$y:()=>o,cY:()=>n,hS:()=>s});var a=r(52581);function s(e,t="An error occurred. Please try again.",r=!0){let o,n=t;return e?.message?n=e.message:"string"==typeof e?n=e:e?.response?.data?.message?n=e.response.data.message:e?.data?.message&&(n=e.data.message),e?.status?o=e.status:e?.response?.status&&(o=e.response.status),n.includes("already exists")||(n.includes("Authentication")||n.includes("Unauthorized")?n="Please log in again to continue. Your session may have expired.":n.includes("Network")||n.includes("fetch")?n="Please check your internet connection and try again.":n.includes("not found")?n="The requested resource was not found.":n.includes("Forbidden")?n="You do not have permission to perform this action.":500===o?n="Server error. Please try again later.":503===o&&(n="Service temporarily unavailable. Please try again later.")),r&&a.oR.error(n),{success:!1,error:n,statusCode:o}}function o(e,t=!1,r){return t&&r&&a.oR.success(r),{success:!0,data:e}}function n(e){return!0===e.success}},33873:e=>{"use strict";e.exports=require("path")},43954:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx","default")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>n});var a=r(60687);r(43210);var s=r(4780);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},46708:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(60687);r(43210);var s=r(4780),o=r(65668),n=r(93613),l=r(44493),i=r(85726);let c=({icon:e=(0,a.jsx)(o.A,{className:"h-5 w-5 text-muted-foreground"}),label:t,value:r,className:c,iconClassName:d,labelClassName:u,valueClassName:h,loading:m=!1,error:g=!1})=>(0,a.jsx)(l.Zp,{className:(0,s.cn)("p-6",c),children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:(0,s.cn)("inline-flex h-10 w-10 items-center justify-center rounded-lg bg-muted",d),children:g?(0,a.jsx)(n.A,{className:"h-5 w-5 text-destructive"}):e}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:(0,s.cn)("text-sm font-medium text-muted-foreground",u),children:t}),m?(0,a.jsx)(i.E,{className:"h-9 w-24"}):g?(0,a.jsx)("p",{className:(0,s.cn)("text-sm font-medium text-destructive"),children:"Failed to load"}):(0,a.jsx)("p",{className:(0,s.cn)("text-3xl font-bold",h),children:r})]})]})})},51995:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(37413),s=r(92555);function o(){return(0,a.jsx)(s.W,{message:"Loading college dashboard..."})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62768:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>z});var a=r(60687),s=r(43210),o=r.n(s),n=r(46708),l=r(41312);let i=(0,r(62688).A)("user-check",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var c=r(10022),d=r(42405),u=r(31158),h=r(44493),m=r(15079),g=r(48482),p=r(38246),f=r(57359),x=r(4780);let v={light:"",dark:".dark"},y=s.createContext(null);function j({id:e,className:t,children:r,config:o,...n}){let l=s.useId(),i=`chart-${e||l.replace(/:/g,"")}`;return(0,a.jsx)(y.Provider,{value:{config:o},children:(0,a.jsxs)("div",{"data-slot":"chart","data-chart":i,className:(0,x.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",t),...n,children:[(0,a.jsx)(b,{id:i,config:o}),(0,a.jsx)(g.u,{children:r})]})})}let b=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(v).map(([t,a])=>`
${a} [data-chart=${e}] {
${r.map(([e,r])=>{let a=r.theme?.[t]||r.color;return a?`  --color-${e}: ${a};`:null}).join("\n")}
}
`).join("\n")}}):null};function w({active:e,payload:t,className:r,indicator:o="dot",hideLabel:n=!1,hideIndicator:l=!1,label:i,labelFormatter:c,labelClassName:d,formatter:u,color:h,nameKey:m,labelKey:g}){let{config:p}=function(){let e=s.useContext(y);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),f=s.useMemo(()=>{if(n||!t?.length)return null;let[e]=t,r=`${g||e?.dataKey||e?.name||"value"}`,s=N(p,e,r),o=g||"string"!=typeof i?s?.label:p[i]?.label||i;return c?(0,a.jsx)("div",{className:(0,x.cn)("font-medium",d),children:c(o,t)}):o?(0,a.jsx)("div",{className:(0,x.cn)("font-medium",d),children:o}):null},[i,c,t,n,d,p,g]);if(!e||!t?.length)return null;let v=1===t.length&&"dot"!==o;return(0,a.jsxs)("div",{className:(0,x.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",r),children:[v?null:f,(0,a.jsx)("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${m||e.name||e.dataKey||"value"}`,s=N(p,e,r),n=h||e.payload.fill||e.color;return(0,a.jsx)("div",{className:(0,x.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===o&&"items-center"),children:u&&e?.value!==void 0&&e.name?u(e.value,e.name,e,t,e.payload):(0,a.jsxs)(a.Fragment,{children:[s?.icon?(0,a.jsx)(s.icon,{}):!l&&(0,a.jsx)("div",{className:(0,x.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===o,"w-1":"line"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"my-0.5":v&&"dashed"===o}),style:{"--color-bg":n,"--color-border":n}}),(0,a.jsxs)("div",{className:(0,x.cn)("flex flex-1 justify-between leading-none",v?"items-end":"items-center"),children:[(0,a.jsxs)("div",{className:"grid gap-1.5",children:[v?f:null,(0,a.jsx)("span",{className:"text-muted-foreground",children:s?.label||e.name})]}),e.value&&(0,a.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function N(e,t,r){if("object"!=typeof t||null===t)return;let a="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,s=r;return r in t&&"string"==typeof t[r]?s=t[r]:a&&r in a&&"string"==typeof a[r]&&(s=a[r]),s in e?e[s]:e[r]}p.m,f.s;var k=r(2041),C=r(85168),P=r(27747),A=r(9920),E=r(90812),S=r(26423);let _=({title:e,collegeId:t,subjectColors:r,timeRanges:o=["Daily","Weekly","Monthly","Yearly"],defaultTimeRange:n="Monthly",className:l})=>{let[i,c]=(0,s.useState)(n),[d,u]=(0,s.useState)([]),[f,x]=(0,s.useState)(!1),v=r.reduce((e,{subject:t,color:r})=>({...e,[t]:{color:r}}),{}),y=e=>{let t=new Date,r=new Date(t),a=new Date(t);switch(e){case"Daily":a.setDate(t.getDate()-7);break;case"Weekly":a.setDate(t.getDate()-30);break;case"Monthly":default:a.setMonth(t.getMonth()-6);break;case"Yearly":a.setFullYear(t.getFullYear()-1)}return{startDate:a.toISOString(),endDate:r.toISOString()}},b=async()=>{if(!t)return;let{startDate:e,endDate:r}=y(i);x(!0);try{let a=await (0,S.mi)(t,e,r);if(!Array.isArray(a)){console.warn("API did not return an array. Got:",a),u([]);return}let s=a.map(e=>({name:e.dateLabel,...e.subjectCounts}));u(s)}catch(e){console.error("Failed to fetch chart data:",e),u([])}finally{x(!1)}};return(0,s.useEffect)(()=>{b()},[t,i]),(0,a.jsxs)(h.Zp,{className:`p-4 h-full ${l||""}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:e}),(0,a.jsxs)(m.l6,{value:i,onValueChange:e=>c(e),children:[(0,a.jsx)(m.bq,{className:"w-[120px]",children:(0,a.jsx)(m.yv,{placeholder:i})}),(0,a.jsx)(m.gC,{children:o.map(e=>(0,a.jsx)(m.eb,{value:e,children:e},e))})]})]}),(0,a.jsx)("div",{className:"space-y-2 mb-2",children:(0,a.jsx)("div",{className:"flex flex-wrap gap-4",children:r.map(({subject:e,color:t})=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:t}}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:e})]},e))})}),(0,a.jsx)("div",{className:"h-[calc(100%-80px)] min-h-[250px] w-full",children:(0,a.jsx)(j,{config:v,children:(0,a.jsx)(g.u,{width:"98%",height:"100%",children:(0,a.jsxs)(k.E,{data:d.length?d:[{name:"No Data"}],margin:{top:5,right:10,left:10,bottom:20},barGap:4,layout:"horizontal",children:[(0,a.jsx)(C.d,{strokeDasharray:"3 3",vertical:!1}),(0,a.jsx)(P.W,{dataKey:"name",axisLine:!1,tickLine:!1,tick:{fontSize:10},dy:8,height:20}),(0,a.jsx)(A.h,{axisLine:!1,tickLine:!1,tick:{fontSize:10},domain:[0,"dataMax"],width:40,tickFormatter:e=>e>=1e3?`${(e/1e3).toFixed(1)}K`:e.toString(),padding:{top:10}}),(0,a.jsx)(p.m,{content:e=>e.active&&e.payload&&e.payload.length?(0,a.jsx)(w,{indicator:"line",payload:e.payload}):null,cursor:{fill:"rgba(0, 0, 0, 0.05)"}}),r.map(({subject:e,color:t})=>(0,a.jsx)(E.y,{dataKey:e,fill:t,radius:[2,2,0,0],maxBarSize:25},e))]})})})})]})};var $=r(12048),q=r(99270),T=r(58869);let F=({title:e,teachers:t,className:r,onSearch:s,onTeacherClick:n})=>{let[l,i]=o().useState("");return(0,a.jsxs)(h.Zp,{className:`p-6 ${r}`,children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-6",children:e}),(0,a.jsxs)("div",{className:"relative mb-6",children:[(0,a.jsx)(q.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)($.p,{placeholder:"Search...",className:"pl-9",value:l,onChange:e=>{i(e.target.value),s&&s(e.target.value)}})]}),(0,a.jsx)("div",{className:"space-y-4",children:t.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer hover:bg-muted/50 rounded-md p-2 transition-colors",onClick:()=>n?.(e),children:[(0,a.jsxs)("div",{className:"relative",children:[e.avatar?(0,a.jsx)("img",{src:e.avatar,alt:e.name,className:"w-10 h-10 rounded-full object-cover"}):(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)(T.A,{className:"w-6 h-6 text-gray-400"})}),e.status&&(0,a.jsx)("div",{className:`w-2.5 h-2.5 rounded-full absolute right-0 bottom-0 border-2 border-background ${"online"===e.status?"bg-green-500":"away"===e.status?"bg-amber-500":"bg-gray-300"}`})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e.name}),e.meta&&(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.meta})]})]},e.id))})]})};function z(){let[e,t]=(0,s.useState)(null),[r,o]=(0,s.useState)([]),[h,m]=(0,s.useState)(!1),[g,p]=(0,s.useState)(null),[f,x]=(0,s.useState)(null),[v,y]=(0,s.useState)(!1),[j,b]=(0,s.useState)(null);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5",children:[(0,a.jsx)(n.A,{icon:(0,a.jsx)(l.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Teachers",value:f?.totalTeachers??0,loading:v,error:!!j}),(0,a.jsx)(n.A,{icon:(0,a.jsx)(i,{className:"h-5 w-5 text-muted-foreground"}),label:"Active Teachers",value:f?.activeTeachers??0,loading:v,error:!!j,iconClassName:"bg-green-100",valueClassName:"text-green-600"}),(0,a.jsx)(n.A,{icon:(0,a.jsx)(c.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Question Papers",value:f?.totalQuestionPapers??0,loading:v,error:!!j,iconClassName:"bg-green-100",valueClassName:"text-green-600"}),(0,a.jsx)(n.A,{icon:(0,a.jsx)(d.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Questions",value:f?.totalQuestions??0,loading:v,error:!!j,iconClassName:"bg-amber-100",valueClassName:"text-amber-600"}),(0,a.jsx)(n.A,{icon:(0,a.jsx)(u.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Downloads",value:f?.totalDownloads??0,loading:v,error:!!j,iconClassName:"bg-purple-100",valueClassName:"text-purple-600"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3 mb-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2 overflow-hidden",children:(0,a.jsx)(_,{title:"Questions created per subject",collegeId:e||"",subjectColors:[{subject:"Math",color:"#4F46E5"},{subject:"Physics",color:"#10B981"},{subject:"Chemistry",color:"#F59E0B"},{subject:"Biology",color:"#EC4899"}],defaultTimeRange:"Monthly",className:"w-full h-full"})}),(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsx)(F,{title:"Top Teachers Generating Papers",teachers:h?[]:r,onTeacherClick:e=>{"online"===e.status||"offline"===e.status?console.log("Clicked on teacher:",e.name):console.warn("Teacher status is not clickable:",e.status??"unknown")}}),h&&(0,a.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,a.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading top teachers..."})}),g&&(0,a.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"text-sm text-red-500",children:["Error: ",g]})})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67053:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\college\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx","default")},69885:(e,t,r)=>{Promise.resolve().then(r.bind(r,67053))},69914:(e,t,r)=>{Promise.resolve().then(r.bind(r,43954))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,4619,3287,9592,2581,6822,9575,4707,6658],()=>r(22315));module.exports=a})();