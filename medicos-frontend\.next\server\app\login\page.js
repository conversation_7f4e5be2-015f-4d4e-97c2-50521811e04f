(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3416:(e,r,t)=>{"use strict";t.d(r,{sG:()=>u,hO:()=>m});var s=t(43210),n=t(51215),o=t(98599),a=t(60687),i=s.forwardRef((e,r)=>{let{children:t,...n}=e,o=s.Children.toArray(t),i=o.find(c);if(i){let e=i.props.children,t=o.map(r=>r!==i?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(l,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,t):null})}return(0,a.jsx)(l,{...n,ref:r,children:t})});i.displayName="Slot";var l=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){let e=function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),a=function(e,r){let t={...r};for(let s in r){let n=e[s],o=r[s];/^on[A-Z]/.test(s)?n&&o?t[s]=(...e)=>{o(...e),n(...e)}:n&&(t[s]=n):"style"===s?t[s]={...n,...o}:"className"===s&&(t[s]=[n,o].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(a.ref=r?(0,o.t)(r,e):e),s.cloneElement(t,a)}return s.Children.count(t)>1?s.Children.only(null):null});l.displayName="SlotClone";var d=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===d}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,r)=>{let t=s.forwardRef((e,t)=>{let{asChild:s,...n}=e,o=s?i:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o,{...n,ref:t})});return t.displayName=`Primitive.${r}`,{...e,[r]:t}},{});function m(e,r){e&&n.flushSync(()=>e.dispatchEvent(r))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11054:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},12048:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(60687);t(43210);var n=t(4780);function o({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},16189:(e,r,t)=>{"use strict";var s=t(65773);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>i});var s=t(60687);t(43210);var n=t(11329),o=t(24224),a=t(4780);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:o=!1,...l}){let d=o?n.Slot:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,a.cn)(i({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},38971:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),n=t(48088),o=t(88170),a=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},50806:(e,r,t)=>{Promise.resolve().then(t.bind(t,69488))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>y});var s=t(60687),n=t(43210),o=t(16189),a=t(85814),i=t.n(a),l=t(63442),d=t(27605),c=t(45880),u=t(12597),m=t(13861),p=t(52581),g=t(29523),f=t(71669),x=t(12048),h=t(60478),v=t(62185);let b=c.Ik({email:c.Yj().email({message:"Please enter a valid email address."}),password:c.Yj().min(6,{message:"Password must be at least 6 characters."})});function y(){let e=(0,o.useRouter)(),{login:r,loginWithGoogle:t,logout:a}=(0,h.A)(),[c,y]=(0,n.useState)(!1),[w,j]=(0,n.useState)(!1),[k,N]=(0,n.useState)(!1),C=(0,d.mN)({resolver:(0,l.u)(b),defaultValues:{email:"",password:""}});async function P(t){y(!0);try{await r(t.email,t.password);try{let r=await (0,v.Xw)(t.email,t.password);if(!r||!r.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",r.accessToken),r.user&&r.user.role){console.log("User role from backend:",r.user.role),localStorage.setItem("userRole",r.user.role);let t=localStorage.getItem("userRole");console.log("Role stored in localStorage:",t),t?(S(t),p.oR.success("Logged in successfully!")):(console.error("Role not properly stored in localStorage"),p.oR.error("Login successful but role not properly set. Please try again."),e.push("/"))}else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),p.oR.error(e.message||"Backend authentication failed. Please try again."),await a();return}}catch(e){console.error("Login error:",e),p.oR.error(e.message||"Failed to login. Please try again.")}finally{y(!1)}}async function R(){j(!0);try{await t();try{let r=await (0,v.K8)();if(!r||!r.accessToken)throw Error("Invalid response from server. Missing access token.");if(localStorage.setItem("backendToken",r.accessToken),r.user&&r.user.role){console.log("User role from backend:",r.user.role),localStorage.setItem("userRole",r.user.role);let t=localStorage.getItem("userRole");console.log("Role stored in localStorage:",t),t?(S(t),p.oR.success("Logged in with Google successfully!")):(console.error("Role not properly stored in localStorage"),p.oR.error("Login successful but role not properly set. Please try again."),e.push("/"))}else throw Error("User role not provided in response")}catch(e){console.error("Backend authentication failed:",e),p.oR.error(e.message||"Backend authentication failed. Please try again."),await a();return}}catch(e){console.error("Google login error:",e),p.oR.error(e.message||"Failed to login with Google. Please try again.")}finally{j(!1)}}function S(r){console.log("Redirecting based on role:",r);let t=r.toLowerCase();t.includes("superadmin")?e.push("/admin"):t.includes("collegeadmin")?e.push("/college"):t.includes("teacher")?e.push("/teacher"):(console.warn("Unknown role for redirection:",r),e.push("/"))}return(0,s.jsxs)("div",{className:"flex min-h-screen",children:[(0,s.jsx)("div",{className:"w-full md:w-1/2 flex items-center justify-center p-8 bg-white",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("img",{src:"/assets/logo/medicos-logo.svg",alt:"MEDICOS",className:"h-[70px] w-auto"})})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Sign In"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-6",children:"Enter your email and password to sign in!"}),(0,s.jsxs)("button",{type:"button",onClick:R,disabled:w,className:"w-full flex items-center justify-center gap-2 border border-gray-300 rounded-md py-2 px-4 mb-6 text-gray-700 hover:bg-gray-50 disabled:opacity-70 disabled:cursor-not-allowed",children:[w?(0,s.jsx)("div",{className:"h-5 w-5 animate-spin rounded-full border-b-2 border-t-2 border-blue-500"}):(0,s.jsxs)("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M19.9895 10.1871C19.9895 9.36767 19.9214 8.76973 19.7742 8.14966H10.1992V11.848H15.8195C15.7062 12.7671 15.0943 14.1512 13.7346 15.0813L13.7155 15.2051L16.7429 17.4969L16.9527 17.5174C18.879 15.7789 19.9895 13.221 19.9895 10.1871Z",fill:"#4285F4"}),(0,s.jsx)("path",{d:"M10.1993 19.9313C12.9527 19.9313 15.2643 19.0454 16.9527 17.5174L13.7346 15.0813C12.8734 15.6682 11.7176 16.0779 10.1993 16.0779C7.50243 16.0779 5.21352 14.3395 4.39759 11.9366L4.27799 11.9466L1.13003 14.3273L1.08887 14.4391C2.76588 17.6945 6.21061 19.9313 10.1993 19.9313Z",fill:"#34A853"}),(0,s.jsx)("path",{d:"M4.39748 11.9366C4.18219 11.3166 4.05759 10.6521 4.05759 9.96565C4.05759 9.27909 4.18219 8.61473 4.38615 7.99466L4.38045 7.8626L1.19304 5.44366L1.08875 5.49214C0.397576 6.84305 0.000976562 8.36008 0.000976562 9.96565C0.000976562 11.5712 0.397576 13.0882 1.08875 14.4391L4.39748 11.9366Z",fill:"#FBBC05"}),(0,s.jsx)("path",{d:"M10.1993 3.85336C12.1142 3.85336 13.406 4.66168 14.1425 5.33717L17.0207 2.59107C15.253 0.985496 12.9527 0 10.1993 0C6.2106 0 2.76588 2.23672 1.08887 5.49214L4.38626 7.99466C5.21352 5.59183 7.50242 3.85336 10.1993 3.85336Z",fill:"#EB4335"})]}),w?"Signing in...":"Sign in with Google"]}),(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)("div",{className:"flex-grow border-t border-gray-300"}),(0,s.jsx)("span",{className:"mx-4 text-sm text-gray-500",children:"Or"}),(0,s.jsx)("div",{className:"flex-grow border-t border-gray-300"})]}),(0,s.jsx)(f.lV,{...C,children:(0,s.jsxs)("form",{onSubmit:C.handleSubmit(P),className:"space-y-4",children:[(0,s.jsx)(f.zB,{control:C.control,name:"email",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-sm font-medium text-gray-700",children:"Username*"}),(0,s.jsx)(f.MJ,{children:(0,s.jsx)(x.p,{placeholder:"Enter your username",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsx)(f.zB,{control:C.control,name:"password",render:({field:e})=>(0,s.jsxs)(f.eI,{children:[(0,s.jsx)(f.lR,{className:"text-sm font-medium text-gray-700",children:"Password*"}),(0,s.jsx)(f.MJ,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(x.p,{type:k?"text":"password",className:"w-full rounded-md border border-gray-300 py-2 px-3",...e}),(0,s.jsx)("button",{type:"button",onClick:()=>N(!k),className:"absolute right-3 top-2.5 text-gray-400",children:k?(0,s.jsx)(u.A,{className:"h-5 w-5"}):(0,s.jsx)(m.A,{className:"h-5 w-5"})})]})}),(0,s.jsx)(f.C5,{})]})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-green-600 focus:ring-green-500"}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Keep me logged in"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(i(),{href:"/forgot-password",className:"font-medium text-green-600 hover:text-green-500",children:"Forgot password?"})})]}),(0,s.jsx)(g.$,{type:"submit",className:"w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md",disabled:c,children:c?"Signing in...":"Sign In"}),(0,s.jsx)("div",{className:"text-center mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(i(),{href:"/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign Up"})]})})]})})]})}),(0,s.jsxs)("div",{className:"hidden md:flex md:w-1/2 bg-green-800 items-center justify-center p-12 relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-grid-pattern opacity-10"}),(0,s.jsx)("div",{className:"relative z-10 text-center max-w-md",children:(0,s.jsxs)("blockquote",{className:"text-white text-xl font-medium",children:['"Education is the most powerful weapon which you can use to change the world."',(0,s.jsx)("footer",{className:"mt-2 text-white text-opacity-80",children:"– Nelson Mandela"})]})})]})]})}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71669:(e,r,t)=>{"use strict";t.d(r,{C5:()=>v,MJ:()=>x,Rr:()=>h,eI:()=>g,lR:()=>f,lV:()=>d,zB:()=>u});var s=t(60687),n=t(43210),o=t(11329),a=t(27605),i=t(4780),l=t(80013);let d=a.Op,c=n.createContext({}),u=({...e})=>(0,s.jsx)(c.Provider,{value:{name:e.name},children:(0,s.jsx)(a.xI,{...e})}),m=()=>{let e=n.useContext(c),r=n.useContext(p),{getFieldState:t}=(0,a.xW)(),s=(0,a.lN)({name:e.name}),o=t(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...o}},p=n.createContext({});function g({className:e,...r}){let t=n.useId();return(0,s.jsx)(p.Provider,{value:{id:t},children:(0,s.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",e),...r})})}function f({className:e,...r}){let{error:t,formItemId:n}=m();return(0,s.jsx)(l.J,{"data-slot":"form-label","data-error":!!t,className:(0,i.cn)("data-[error=true]:text-destructive",e),htmlFor:n,...r})}function x({...e}){let{error:r,formItemId:t,formDescriptionId:n,formMessageId:a}=m();return(0,s.jsx)(o.Slot,{"data-slot":"form-control",id:t,"aria-describedby":r?`${n} ${a}`:`${n}`,"aria-invalid":!!r,...e})}function h({className:e,...r}){let{formDescriptionId:t}=m();return(0,s.jsx)("p",{"data-slot":"form-description",id:t,className:(0,i.cn)("text-muted-foreground text-sm",e),...r})}function v({className:e,...r}){let{error:t,formMessageId:n}=m(),o=t?String(t?.message??""):r.children;return o?(0,s.jsx)("p",{"data-slot":"form-message",id:n,className:(0,i.cn)("text-destructive text-sm",e),...r,children:o}):null}},79551:e=>{"use strict";e.exports=require("url")},80013:(e,r,t)=>{"use strict";t.d(r,{J:()=>a});var s=t(60687);t(43210);var n=t(78148),o=t(4780);function a({className:e,...r}){return(0,s.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx","default")},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>a,t:()=>o});var s=t(43210);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function o(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}function a(...e){return s.useCallback(o(...e),e)}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4619,3287,2581,1991,3442,4707],()=>t(38971));module.exports=s})();