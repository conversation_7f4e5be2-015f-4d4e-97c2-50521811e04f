(()=>{var e={};e.id=6448,e.ids=[6448],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6601:function(e){var t;"undefined"!=typeof self&&self,e.exports=function(){"use strict";var e,t,r={};r.d=function(e,t){for(var l in t)r.o(t,l)&&!r.o(e,l)&&Object.defineProperty(e,l,{enumerable:!0,get:t[l]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var l={};r.d(l,{default:function(){return lu}});class n{constructor(e,t){let r,l;this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;let s="KaTeX parse error: "+e,a=t&&t.loc;if(a&&a.start<=a.end){let e;let t=a.lexer.input;r=a.start,l=a.end,r===t.length?s+=" at end of input: ":s+=" at position "+(r+1)+": ";let n=t.slice(r,l).replace(/[^]/g,"$&̲");s+=(r>15?"…"+t.slice(r-15,r):t.slice(0,r))+n+(l+15<t.length?t.slice(l,l+15)+"…":t.slice(l))}let i=Error(s);return i.name="ParseError",i.__proto__=n.prototype,i.position=r,null!=r&&null!=l&&(i.length=l-r),i.rawMessage=e,i}}n.prototype.__proto__=Error.prototype;let s=/([A-Z])/g,a={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},i=/[&><"']/g,o=function(e){return"ordgroup"===e.type||"color"===e.type?1===e.body.length?o(e.body[0]):e:"font"===e.type?o(e.body):e},h=function(e){if(!e)throw Error("Expected non-null, but got "+String(e));return e};var m={contains:function(e,t){return -1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(i,e=>a[e])},hyphenate:function(e){return e.replace(s,"-$1").toLowerCase()},getBaseElem:o,isCharacterBox:function(e){let t=o(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type},protocolFromUrl:function(e){let t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?":"===t[2]&&/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?t[1].toLowerCase():null:"_relative"}};let c={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:e=>"#"+e},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(e,t)=>(t.push(e),t)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:e=>Math.max(0,e),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:e=>Math.max(0,e),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:e=>Math.max(0,e),cli:"-e, --max-expand <n>",cliProcessor:e=>"Infinity"===e?1/0:parseInt(e)},globalGroup:{type:"boolean",cli:!1}};class d{constructor(e){for(let t in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},c)if(c.hasOwnProperty(t)){let r=c[t];this[t]=void 0!==e[t]?r.processor?r.processor(e[t]):e[t]:function(e){if(e.default)return e.default;let t=e.type,r=Array.isArray(t)?t[0]:t;if("string"!=typeof r)return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}(r)}}reportNonstrict(e,t,r){let l=this.strict;if("function"==typeof l&&(l=l(e,t,r)),l&&"ignore"!==l){if(!0===l||"error"===l)throw new n("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" [")+e+"]",r);"warn"===l?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" [")+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+l+"': "+t+" [")+e+"]")}}useStrictBehavior(e,t,r){let l=this.strict;if("function"==typeof l)try{l=l(e,t,r)}catch(e){l="error"}return!!l&&"ignore"!==l&&(!0===l||"error"===l||("warn"===l?("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" [")+e+"]"),!1):("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+l+"': "+t+" [")+e+"]"),!1)))}isTrusted(e){if(e.url&&!e.protocol){let t=m.protocolFromUrl(e.url);if(null==t)return!1;e.protocol=t}return!!("function"==typeof this.trust?this.trust(e):this.trust)}}class u{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return p[g[this.id]]}sub(){return p[f[this.id]]}fracNum(){return p[x[this.id]]}fracDen(){return p[b[this.id]]}cramp(){return p[y[this.id]]}text(){return p[w[this.id]]}isTight(){return this.size>=2}}let p=[new u(0,0,!1),new u(1,0,!0),new u(2,1,!1),new u(3,1,!0),new u(4,2,!1),new u(5,2,!0),new u(6,3,!1),new u(7,3,!0)],g=[4,5,4,5,6,7,6,7],f=[5,5,5,5,7,7,7,7],x=[2,3,4,5,6,7,6,7],b=[3,3,5,5,7,7,7,7],y=[1,1,3,3,5,5,7,7],w=[0,1,2,3,2,3,2,3];var v={DISPLAY:p[0],TEXT:p[2],SCRIPT:p[4],SCRIPTSCRIPT:p[6]};let k=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}],S=[];function M(e){for(let t=0;t<S.length;t+=2)if(e>=S[t]&&e<=S[t+1])return!0;return!1}k.forEach(e=>e.blocks.forEach(e=>S.push(...e)));let z=function(e,t,r){var l,n,s,a,i,o;t*=1e3;let h="";switch(e){case"sqrtMain":h="M95,"+(622+(l=t)+80)+"\nc-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14\nc0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54\nc44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10\ns173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429\nc69,-144,104.5,-217.7,106.5,-221\nl"+l/2.075+" -"+l+"\nc5.3,-9.3,12,-14,20,-14\nH400000v"+(40+l)+"H845.2724\ns-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7\nc-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z\nM"+(834+l)+" 80h400000v"+(40+l)+"h-400000z";break;case"sqrtSize1":h="M263,"+(601+(n=t)+80)+"c0.7,0,18,39.7,52,119\nc34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120\nc340,-704.7,510.7,-1060.3,512,-1067\nl"+n/2.084+" -"+n+"\nc4.7,-7.3,11,-11,19,-11\nH40000v"+(40+n)+"H1012.3\ns-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232\nc-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1\ns-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26\nc-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z\nM"+(1001+n)+" 80h400000v"+(40+n)+"h-400000z";break;case"sqrtSize2":h="M983 "+(10+(s=t)+80)+"\nl"+s/3.13+" -"+s+"\nc4,-6.7,10,-10,18,-10 H400000v"+(40+s)+"\nH1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7\ns-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744\nc-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30\nc26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722\nc56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5\nc53.7,-170.3,84.5,-266.8,92.5,-289.5z\nM"+(1001+s)+" 80h400000v"+(40+s)+"h-400000z";break;case"sqrtSize3":h="M424,"+(2398+(a=t)+80)+"\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl"+a/4.223+" -"+a+"c4,-6.7,10,-10,18,-10 H400000\nv"+(40+a)+"H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M"+(1001+a)+" 80\nh400000v"+(40+a)+"h-400000z";break;case"sqrtSize4":h="M473,"+(2713+(i=t)+80)+"\nc339.3,-1799.3,509.3,-2700,510,-2702 l"+i/5.298+" -"+i+"\nc3.3,-7.3,9.3,-11,18,-11 H400000v"+(40+i)+"H1017.7\ns-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200\nc0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26\ns76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,\n606zM"+(1001+i)+" 80h400000v"+(40+i)+"H1017.7z";break;case"sqrtTall":h="M702 "+((o=t)+80)+"H400000"+(40+o)+"\nH742v"+(r-54-80-o)+"l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1\nh-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170\nc-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667\n219 661 l218 661zM702 80H400000v"+(40+o)+"H742z"}return h},A=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V")+t+" H367z";case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V")+t+" H478z";default:return""}},T={doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"},N=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v1759 h347 v-84\nH403z M403 1759 V0 H319 V1759 v"+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v1759 H0 v84 H347z\nM347 1759 V0 H263 V1759 v"+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z\nM367 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M410 15 H367 v585 v"+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+" v1715 h263 v84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+" v1799 H0 v-84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v602 h84z\nM403 1759 V0 H319 V1759 v"+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v602 h84z\nM347 1759 V0 h-84 V1759 v"+t+" v602 h84z";case"lparen":return"M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1\nc-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,\n-36,557 l0,"+(t+84)+"c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,\n949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9\nc0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,\n-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189\nl0,-"+(t+92)+"c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,\n-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z";case"rparen":return"M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,\n63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5\nc11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,"+(t+9)+"\nc-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664\nc-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11\nc0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17\nc242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558\nl0,-"+(t+144)+"c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,\n-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z";default:throw Error("Unknown stretchy delimiter.")}};class C{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return m.contains(this.classes,e)}toNode(){let e=document.createDocumentFragment();for(let t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){let e="";for(let t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){return this.children.map(e=>e.toText()).join("")}}var j={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}};let q={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},B={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function I(e,t,r){if(!j[t])throw Error("Font metrics not found for font: "+t+".");let l=e.charCodeAt(0),n=j[t][l];if(!n&&e[0]in B&&(l=B[e[0]].charCodeAt(0),n=j[t][l]),!n&&"text"===r&&M(l)&&(n=j[t][77]),n)return{depth:n[0],height:n[1],italic:n[2],skew:n[3],width:n[4]}}let E={},O=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],H=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],R=function(e,t){return t.size<2?e:O[e-1][t.size-1]};class L{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||L.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=H[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){let t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(let r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new L(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:R(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:H[e-1]})}havingBaseStyle(e){e=e||this.style.text();let t=R(L.BASESIZE,e);return this.size===t&&this.textSize===L.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){let e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==L.BASESIZE?["sizing","reset-size"+this.size,"size"+L.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){let t;if(!E[t=e>=5?0:e>=3?1:2]){let e=E[t]={cssEmPerMu:q.quad[t]/18};for(let r in q)q.hasOwnProperty(r)&&(e[r]=q[r][t])}return E[t]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}L.BASESIZE=6;let P={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},D={ex:!0,em:!0,mu:!0},F=function(e){return"string"!=typeof e&&(e=e.unit),e in P||e in D||"ex"===e},V=function(e,t){let r;if(e.unit in P)r=P[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)r=t.fontMetrics().cssEmPerMu;else{let l;if(l=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)r=l.fontMetrics().xHeight;else if("em"===e.unit)r=l.fontMetrics().quad;else throw new n("Invalid unit: '"+e.unit+"'");l!==t&&(r*=l.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},_=function(e){return+e.toFixed(4)+"em"},U=function(e){return e.filter(e=>e).join(" ")},$=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");let e=t.getColor();e&&(this.style.color=e)}},G=function(e){let t=document.createElement(e);for(let e in t.className=U(this.classes),this.style)this.style.hasOwnProperty(e)&&(t.style[e]=this.style[e]);for(let e in this.attributes)this.attributes.hasOwnProperty(e)&&t.setAttribute(e,this.attributes[e]);for(let e=0;e<this.children.length;e++)t.appendChild(this.children[e].toNode());return t},W=/[\s"'>/=\x00-\x1f]/,X=function(e){let t="<"+e;this.classes.length&&(t+=' class="'+m.escape(U(this.classes))+'"');let r="";for(let e in this.style)this.style.hasOwnProperty(e)&&(r+=m.hyphenate(e)+":"+this.style[e]+";");for(let e in r&&(t+=' style="'+m.escape(r)+'"'),this.attributes)if(this.attributes.hasOwnProperty(e)){if(W.test(e))throw new n("Invalid attribute name '"+e+"'");t+=" "+e+'="'+m.escape(this.attributes[e])+'"'}t+=">";for(let e=0;e<this.children.length;e++)t+=this.children[e].toMarkup();return t+("</"+e+">")};class Y{constructor(e,t,r,l){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,$.call(this,e,r,l),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return m.contains(this.classes,e)}toNode(){return G.call(this,"span")}toMarkup(){return X.call(this,"span")}}class Z{constructor(e,t,r,l){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,$.call(this,t,l),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return m.contains(this.classes,e)}toNode(){return G.call(this,"a")}toMarkup(){return X.call(this,"a")}}class K{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return m.contains(this.classes,e)}toNode(){let e=document.createElement("img");for(let t in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){let e='<img src="'+m.escape(this.src)+'" alt="'+m.escape(this.alt)+'"',t="";for(let e in this.style)this.style.hasOwnProperty(e)&&(t+=m.hyphenate(e)+":"+this.style[e]+";");return t&&(e+=' style="'+m.escape(t)+'"'),e+="'/>"}}let Q={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class J{constructor(e,t,r,l,n,s,a,i){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=l||0,this.skew=n||0,this.width=s||0,this.classes=a||[],this.style=i||{},this.maxFontSize=0;let o=function(e){for(let t=0;t<k.length;t++){let r=k[t];for(let t=0;t<r.blocks.length;t++){let l=r.blocks[t];if(e>=l[0]&&e<=l[1])return r.name}}return null}(this.text.charCodeAt(0));o&&this.classes.push(o+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=Q[this.text])}hasClass(e){return m.contains(this.classes,e)}toNode(){let e=document.createTextNode(this.text),t=null;for(let e in this.italic>0&&((t=document.createElement("span")).style.marginRight=_(this.italic)),this.classes.length>0&&((t=t||document.createElement("span")).className=U(this.classes)),this.style)this.style.hasOwnProperty(e)&&((t=t||document.createElement("span")).style[e]=this.style[e]);return t?(t.appendChild(e),t):e}toMarkup(){let e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=m.escape(U(this.classes)),t+='"');let r="";for(let e in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(e)&&(r+=m.hyphenate(e)+":"+this.style[e]+";");r&&(e=!0,t+=' style="'+m.escape(r)+'"');let l=m.escape(this.text);return e?(t+=">",t+=l,t+="</span>"):l}}class ee{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){let e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(let t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){let e='<svg xmlns="http://www.w3.org/2000/svg"';for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+m.escape(this.attributes[t])+'"');e+=">";for(let t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e+"</svg>"}}class et{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){let e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",T[this.pathName]),e}toMarkup(){return this.alternate?'<path d="'+m.escape(this.alternate)+'"/>':'<path d="'+m.escape(T[this.pathName])+'"/>'}}class er{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){let e=document.createElementNS("http://www.w3.org/2000/svg","line");for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e}toMarkup(){let e="<line";for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+m.escape(this.attributes[t])+'"');return e+"/>"}}function el(e){if(e instanceof J)return e;throw Error("Expected symbolNode but got "+String(e)+".")}let en={bin:1,close:1,inner:1,open:1,punct:1,rel:1},es={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},ea={math:{},text:{}};function ei(e,t,r,l,n,s){ea[e][n]={font:t,group:r,replace:l},s&&l&&(ea[e][l]=ea[e][n])}let eo="math",eh="text",em="main",ec="accent-token",ed="close",eu="inner",ep="mathord",eg="op-token",ef="open",ex="punct",eb="spacing",ey="textord";ei(eo,em,"rel","≡","\\equiv",!0),ei(eo,em,"rel","≺","\\prec",!0),ei(eo,em,"rel","≻","\\succ",!0),ei(eo,em,"rel","∼","\\sim",!0),ei(eo,em,"rel","⊥","\\perp"),ei(eo,em,"rel","⪯","\\preceq",!0),ei(eo,em,"rel","⪰","\\succeq",!0),ei(eo,em,"rel","≃","\\simeq",!0),ei(eo,em,"rel","∣","\\mid",!0),ei(eo,em,"rel","≪","\\ll",!0),ei(eo,em,"rel","≫","\\gg",!0),ei(eo,em,"rel","≍","\\asymp",!0),ei(eo,em,"rel","∥","\\parallel"),ei(eo,em,"rel","⋈","\\bowtie",!0),ei(eo,em,"rel","⌣","\\smile",!0),ei(eo,em,"rel","⊑","\\sqsubseteq",!0),ei(eo,em,"rel","⊒","\\sqsupseteq",!0),ei(eo,em,"rel","≐","\\doteq",!0),ei(eo,em,"rel","⌢","\\frown",!0),ei(eo,em,"rel","∋","\\ni",!0),ei(eo,em,"rel","∝","\\propto",!0),ei(eo,em,"rel","⊢","\\vdash",!0),ei(eo,em,"rel","⊣","\\dashv",!0),ei(eo,em,"rel","∋","\\owns"),ei(eo,em,ex,".","\\ldotp"),ei(eo,em,ex,"⋅","\\cdotp"),ei(eo,em,ey,"#","\\#"),ei(eh,em,ey,"#","\\#"),ei(eo,em,ey,"&","\\&"),ei(eh,em,ey,"&","\\&"),ei(eo,em,ey,"ℵ","\\aleph",!0),ei(eo,em,ey,"∀","\\forall",!0),ei(eo,em,ey,"ℏ","\\hbar",!0),ei(eo,em,ey,"∃","\\exists",!0),ei(eo,em,ey,"∇","\\nabla",!0),ei(eo,em,ey,"♭","\\flat",!0),ei(eo,em,ey,"ℓ","\\ell",!0),ei(eo,em,ey,"♮","\\natural",!0),ei(eo,em,ey,"♣","\\clubsuit",!0),ei(eo,em,ey,"℘","\\wp",!0),ei(eo,em,ey,"♯","\\sharp",!0),ei(eo,em,ey,"♢","\\diamondsuit",!0),ei(eo,em,ey,"ℜ","\\Re",!0),ei(eo,em,ey,"♡","\\heartsuit",!0),ei(eo,em,ey,"ℑ","\\Im",!0),ei(eo,em,ey,"♠","\\spadesuit",!0),ei(eo,em,ey,"\xa7","\\S",!0),ei(eh,em,ey,"\xa7","\\S"),ei(eo,em,ey,"\xb6","\\P",!0),ei(eh,em,ey,"\xb6","\\P"),ei(eo,em,ey,"†","\\dag"),ei(eh,em,ey,"†","\\dag"),ei(eh,em,ey,"†","\\textdagger"),ei(eo,em,ey,"‡","\\ddag"),ei(eh,em,ey,"‡","\\ddag"),ei(eh,em,ey,"‡","\\textdaggerdbl"),ei(eo,em,ed,"⎱","\\rmoustache",!0),ei(eo,em,ef,"⎰","\\lmoustache",!0),ei(eo,em,ed,"⟯","\\rgroup",!0),ei(eo,em,ef,"⟮","\\lgroup",!0),ei(eo,em,"bin","∓","\\mp",!0),ei(eo,em,"bin","⊖","\\ominus",!0),ei(eo,em,"bin","⊎","\\uplus",!0),ei(eo,em,"bin","⊓","\\sqcap",!0),ei(eo,em,"bin","∗","\\ast"),ei(eo,em,"bin","⊔","\\sqcup",!0),ei(eo,em,"bin","◯","\\bigcirc",!0),ei(eo,em,"bin","∙","\\bullet",!0),ei(eo,em,"bin","‡","\\ddagger"),ei(eo,em,"bin","≀","\\wr",!0),ei(eo,em,"bin","⨿","\\amalg"),ei(eo,em,"bin","&","\\And"),ei(eo,em,"rel","⟵","\\longleftarrow",!0),ei(eo,em,"rel","⇐","\\Leftarrow",!0),ei(eo,em,"rel","⟸","\\Longleftarrow",!0),ei(eo,em,"rel","⟶","\\longrightarrow",!0),ei(eo,em,"rel","⇒","\\Rightarrow",!0),ei(eo,em,"rel","⟹","\\Longrightarrow",!0),ei(eo,em,"rel","↔","\\leftrightarrow",!0),ei(eo,em,"rel","⟷","\\longleftrightarrow",!0),ei(eo,em,"rel","⇔","\\Leftrightarrow",!0),ei(eo,em,"rel","⟺","\\Longleftrightarrow",!0),ei(eo,em,"rel","↦","\\mapsto",!0),ei(eo,em,"rel","⟼","\\longmapsto",!0),ei(eo,em,"rel","↗","\\nearrow",!0),ei(eo,em,"rel","↩","\\hookleftarrow",!0),ei(eo,em,"rel","↪","\\hookrightarrow",!0),ei(eo,em,"rel","↘","\\searrow",!0),ei(eo,em,"rel","↼","\\leftharpoonup",!0),ei(eo,em,"rel","⇀","\\rightharpoonup",!0),ei(eo,em,"rel","↙","\\swarrow",!0),ei(eo,em,"rel","↽","\\leftharpoondown",!0),ei(eo,em,"rel","⇁","\\rightharpoondown",!0),ei(eo,em,"rel","↖","\\nwarrow",!0),ei(eo,em,"rel","⇌","\\rightleftharpoons",!0),ei(eo,"ams","rel","≮","\\nless",!0),ei(eo,"ams","rel","","\\@nleqslant"),ei(eo,"ams","rel","","\\@nleqq"),ei(eo,"ams","rel","⪇","\\lneq",!0),ei(eo,"ams","rel","≨","\\lneqq",!0),ei(eo,"ams","rel","","\\@lvertneqq"),ei(eo,"ams","rel","⋦","\\lnsim",!0),ei(eo,"ams","rel","⪉","\\lnapprox",!0),ei(eo,"ams","rel","⊀","\\nprec",!0),ei(eo,"ams","rel","⋠","\\npreceq",!0),ei(eo,"ams","rel","⋨","\\precnsim",!0),ei(eo,"ams","rel","⪹","\\precnapprox",!0),ei(eo,"ams","rel","≁","\\nsim",!0),ei(eo,"ams","rel","","\\@nshortmid"),ei(eo,"ams","rel","∤","\\nmid",!0),ei(eo,"ams","rel","⊬","\\nvdash",!0),ei(eo,"ams","rel","⊭","\\nvDash",!0),ei(eo,"ams","rel","⋪","\\ntriangleleft"),ei(eo,"ams","rel","⋬","\\ntrianglelefteq",!0),ei(eo,"ams","rel","⊊","\\subsetneq",!0),ei(eo,"ams","rel","","\\@varsubsetneq"),ei(eo,"ams","rel","⫋","\\subsetneqq",!0),ei(eo,"ams","rel","","\\@varsubsetneqq"),ei(eo,"ams","rel","≯","\\ngtr",!0),ei(eo,"ams","rel","","\\@ngeqslant"),ei(eo,"ams","rel","","\\@ngeqq"),ei(eo,"ams","rel","⪈","\\gneq",!0),ei(eo,"ams","rel","≩","\\gneqq",!0),ei(eo,"ams","rel","","\\@gvertneqq"),ei(eo,"ams","rel","⋧","\\gnsim",!0),ei(eo,"ams","rel","⪊","\\gnapprox",!0),ei(eo,"ams","rel","⊁","\\nsucc",!0),ei(eo,"ams","rel","⋡","\\nsucceq",!0),ei(eo,"ams","rel","⋩","\\succnsim",!0),ei(eo,"ams","rel","⪺","\\succnapprox",!0),ei(eo,"ams","rel","≆","\\ncong",!0),ei(eo,"ams","rel","","\\@nshortparallel"),ei(eo,"ams","rel","∦","\\nparallel",!0),ei(eo,"ams","rel","⊯","\\nVDash",!0),ei(eo,"ams","rel","⋫","\\ntriangleright"),ei(eo,"ams","rel","⋭","\\ntrianglerighteq",!0),ei(eo,"ams","rel","","\\@nsupseteqq"),ei(eo,"ams","rel","⊋","\\supsetneq",!0),ei(eo,"ams","rel","","\\@varsupsetneq"),ei(eo,"ams","rel","⫌","\\supsetneqq",!0),ei(eo,"ams","rel","","\\@varsupsetneqq"),ei(eo,"ams","rel","⊮","\\nVdash",!0),ei(eo,"ams","rel","⪵","\\precneqq",!0),ei(eo,"ams","rel","⪶","\\succneqq",!0),ei(eo,"ams","rel","","\\@nsubseteqq"),ei(eo,"ams","bin","⊴","\\unlhd"),ei(eo,"ams","bin","⊵","\\unrhd"),ei(eo,"ams","rel","↚","\\nleftarrow",!0),ei(eo,"ams","rel","↛","\\nrightarrow",!0),ei(eo,"ams","rel","⇍","\\nLeftarrow",!0),ei(eo,"ams","rel","⇏","\\nRightarrow",!0),ei(eo,"ams","rel","↮","\\nleftrightarrow",!0),ei(eo,"ams","rel","⇎","\\nLeftrightarrow",!0),ei(eo,"ams","rel","△","\\vartriangle"),ei(eo,"ams",ey,"ℏ","\\hslash"),ei(eo,"ams",ey,"▽","\\triangledown"),ei(eo,"ams",ey,"◊","\\lozenge"),ei(eo,"ams",ey,"Ⓢ","\\circledS"),ei(eo,"ams",ey,"\xae","\\circledR"),ei(eh,"ams",ey,"\xae","\\circledR"),ei(eo,"ams",ey,"∡","\\measuredangle",!0),ei(eo,"ams",ey,"∄","\\nexists"),ei(eo,"ams",ey,"℧","\\mho"),ei(eo,"ams",ey,"Ⅎ","\\Finv",!0),ei(eo,"ams",ey,"⅁","\\Game",!0),ei(eo,"ams",ey,"‵","\\backprime"),ei(eo,"ams",ey,"▲","\\blacktriangle"),ei(eo,"ams",ey,"▼","\\blacktriangledown"),ei(eo,"ams",ey,"■","\\blacksquare"),ei(eo,"ams",ey,"⧫","\\blacklozenge"),ei(eo,"ams",ey,"★","\\bigstar"),ei(eo,"ams",ey,"∢","\\sphericalangle",!0),ei(eo,"ams",ey,"∁","\\complement",!0),ei(eo,"ams",ey,"\xf0","\\eth",!0),ei(eh,em,ey,"\xf0","\xf0"),ei(eo,"ams",ey,"╱","\\diagup"),ei(eo,"ams",ey,"╲","\\diagdown"),ei(eo,"ams",ey,"□","\\square"),ei(eo,"ams",ey,"□","\\Box"),ei(eo,"ams",ey,"◊","\\Diamond"),ei(eo,"ams",ey,"\xa5","\\yen",!0),ei(eh,"ams",ey,"\xa5","\\yen",!0),ei(eo,"ams",ey,"✓","\\checkmark",!0),ei(eh,"ams",ey,"✓","\\checkmark"),ei(eo,"ams",ey,"ℶ","\\beth",!0),ei(eo,"ams",ey,"ℸ","\\daleth",!0),ei(eo,"ams",ey,"ℷ","\\gimel",!0),ei(eo,"ams",ey,"ϝ","\\digamma",!0),ei(eo,"ams",ey,"ϰ","\\varkappa"),ei(eo,"ams",ef,"┌","\\@ulcorner",!0),ei(eo,"ams",ed,"┐","\\@urcorner",!0),ei(eo,"ams",ef,"└","\\@llcorner",!0),ei(eo,"ams",ed,"┘","\\@lrcorner",!0),ei(eo,"ams","rel","≦","\\leqq",!0),ei(eo,"ams","rel","⩽","\\leqslant",!0),ei(eo,"ams","rel","⪕","\\eqslantless",!0),ei(eo,"ams","rel","≲","\\lesssim",!0),ei(eo,"ams","rel","⪅","\\lessapprox",!0),ei(eo,"ams","rel","≊","\\approxeq",!0),ei(eo,"ams","bin","⋖","\\lessdot"),ei(eo,"ams","rel","⋘","\\lll",!0),ei(eo,"ams","rel","≶","\\lessgtr",!0),ei(eo,"ams","rel","⋚","\\lesseqgtr",!0),ei(eo,"ams","rel","⪋","\\lesseqqgtr",!0),ei(eo,"ams","rel","≑","\\doteqdot"),ei(eo,"ams","rel","≓","\\risingdotseq",!0),ei(eo,"ams","rel","≒","\\fallingdotseq",!0),ei(eo,"ams","rel","∽","\\backsim",!0),ei(eo,"ams","rel","⋍","\\backsimeq",!0),ei(eo,"ams","rel","⫅","\\subseteqq",!0),ei(eo,"ams","rel","⋐","\\Subset",!0),ei(eo,"ams","rel","⊏","\\sqsubset",!0),ei(eo,"ams","rel","≼","\\preccurlyeq",!0),ei(eo,"ams","rel","⋞","\\curlyeqprec",!0),ei(eo,"ams","rel","≾","\\precsim",!0),ei(eo,"ams","rel","⪷","\\precapprox",!0),ei(eo,"ams","rel","⊲","\\vartriangleleft"),ei(eo,"ams","rel","⊴","\\trianglelefteq"),ei(eo,"ams","rel","⊨","\\vDash",!0),ei(eo,"ams","rel","⊪","\\Vvdash",!0),ei(eo,"ams","rel","⌣","\\smallsmile"),ei(eo,"ams","rel","⌢","\\smallfrown"),ei(eo,"ams","rel","≏","\\bumpeq",!0),ei(eo,"ams","rel","≎","\\Bumpeq",!0),ei(eo,"ams","rel","≧","\\geqq",!0),ei(eo,"ams","rel","⩾","\\geqslant",!0),ei(eo,"ams","rel","⪖","\\eqslantgtr",!0),ei(eo,"ams","rel","≳","\\gtrsim",!0),ei(eo,"ams","rel","⪆","\\gtrapprox",!0),ei(eo,"ams","bin","⋗","\\gtrdot"),ei(eo,"ams","rel","⋙","\\ggg",!0),ei(eo,"ams","rel","≷","\\gtrless",!0),ei(eo,"ams","rel","⋛","\\gtreqless",!0),ei(eo,"ams","rel","⪌","\\gtreqqless",!0),ei(eo,"ams","rel","≖","\\eqcirc",!0),ei(eo,"ams","rel","≗","\\circeq",!0),ei(eo,"ams","rel","≜","\\triangleq",!0),ei(eo,"ams","rel","∼","\\thicksim"),ei(eo,"ams","rel","≈","\\thickapprox"),ei(eo,"ams","rel","⫆","\\supseteqq",!0),ei(eo,"ams","rel","⋑","\\Supset",!0),ei(eo,"ams","rel","⊐","\\sqsupset",!0),ei(eo,"ams","rel","≽","\\succcurlyeq",!0),ei(eo,"ams","rel","⋟","\\curlyeqsucc",!0),ei(eo,"ams","rel","≿","\\succsim",!0),ei(eo,"ams","rel","⪸","\\succapprox",!0),ei(eo,"ams","rel","⊳","\\vartriangleright"),ei(eo,"ams","rel","⊵","\\trianglerighteq"),ei(eo,"ams","rel","⊩","\\Vdash",!0),ei(eo,"ams","rel","∣","\\shortmid"),ei(eo,"ams","rel","∥","\\shortparallel"),ei(eo,"ams","rel","≬","\\between",!0),ei(eo,"ams","rel","⋔","\\pitchfork",!0),ei(eo,"ams","rel","∝","\\varpropto"),ei(eo,"ams","rel","◀","\\blacktriangleleft"),ei(eo,"ams","rel","∴","\\therefore",!0),ei(eo,"ams","rel","∍","\\backepsilon"),ei(eo,"ams","rel","▶","\\blacktriangleright"),ei(eo,"ams","rel","∵","\\because",!0),ei(eo,"ams","rel","⋘","\\llless"),ei(eo,"ams","rel","⋙","\\gggtr"),ei(eo,"ams","bin","⊲","\\lhd"),ei(eo,"ams","bin","⊳","\\rhd"),ei(eo,"ams","rel","≂","\\eqsim",!0),ei(eo,em,"rel","⋈","\\Join"),ei(eo,"ams","rel","≑","\\Doteq",!0),ei(eo,"ams","bin","∔","\\dotplus",!0),ei(eo,"ams","bin","∖","\\smallsetminus"),ei(eo,"ams","bin","⋒","\\Cap",!0),ei(eo,"ams","bin","⋓","\\Cup",!0),ei(eo,"ams","bin","⩞","\\doublebarwedge",!0),ei(eo,"ams","bin","⊟","\\boxminus",!0),ei(eo,"ams","bin","⊞","\\boxplus",!0),ei(eo,"ams","bin","⋇","\\divideontimes",!0),ei(eo,"ams","bin","⋉","\\ltimes",!0),ei(eo,"ams","bin","⋊","\\rtimes",!0),ei(eo,"ams","bin","⋋","\\leftthreetimes",!0),ei(eo,"ams","bin","⋌","\\rightthreetimes",!0),ei(eo,"ams","bin","⋏","\\curlywedge",!0),ei(eo,"ams","bin","⋎","\\curlyvee",!0),ei(eo,"ams","bin","⊝","\\circleddash",!0),ei(eo,"ams","bin","⊛","\\circledast",!0),ei(eo,"ams","bin","⋅","\\centerdot"),ei(eo,"ams","bin","⊺","\\intercal",!0),ei(eo,"ams","bin","⋒","\\doublecap"),ei(eo,"ams","bin","⋓","\\doublecup"),ei(eo,"ams","bin","⊠","\\boxtimes",!0),ei(eo,"ams","rel","⇢","\\dashrightarrow",!0),ei(eo,"ams","rel","⇠","\\dashleftarrow",!0),ei(eo,"ams","rel","⇇","\\leftleftarrows",!0),ei(eo,"ams","rel","⇆","\\leftrightarrows",!0),ei(eo,"ams","rel","⇚","\\Lleftarrow",!0),ei(eo,"ams","rel","↞","\\twoheadleftarrow",!0),ei(eo,"ams","rel","↢","\\leftarrowtail",!0),ei(eo,"ams","rel","↫","\\looparrowleft",!0),ei(eo,"ams","rel","⇋","\\leftrightharpoons",!0),ei(eo,"ams","rel","↶","\\curvearrowleft",!0),ei(eo,"ams","rel","↺","\\circlearrowleft",!0),ei(eo,"ams","rel","↰","\\Lsh",!0),ei(eo,"ams","rel","⇈","\\upuparrows",!0),ei(eo,"ams","rel","↿","\\upharpoonleft",!0),ei(eo,"ams","rel","⇃","\\downharpoonleft",!0),ei(eo,em,"rel","⊶","\\origof",!0),ei(eo,em,"rel","⊷","\\imageof",!0),ei(eo,"ams","rel","⊸","\\multimap",!0),ei(eo,"ams","rel","↭","\\leftrightsquigarrow",!0),ei(eo,"ams","rel","⇉","\\rightrightarrows",!0),ei(eo,"ams","rel","⇄","\\rightleftarrows",!0),ei(eo,"ams","rel","↠","\\twoheadrightarrow",!0),ei(eo,"ams","rel","↣","\\rightarrowtail",!0),ei(eo,"ams","rel","↬","\\looparrowright",!0),ei(eo,"ams","rel","↷","\\curvearrowright",!0),ei(eo,"ams","rel","↻","\\circlearrowright",!0),ei(eo,"ams","rel","↱","\\Rsh",!0),ei(eo,"ams","rel","⇊","\\downdownarrows",!0),ei(eo,"ams","rel","↾","\\upharpoonright",!0),ei(eo,"ams","rel","⇂","\\downharpoonright",!0),ei(eo,"ams","rel","⇝","\\rightsquigarrow",!0),ei(eo,"ams","rel","⇝","\\leadsto"),ei(eo,"ams","rel","⇛","\\Rrightarrow",!0),ei(eo,"ams","rel","↾","\\restriction"),ei(eo,em,ey,"‘","`"),ei(eo,em,ey,"$","\\$"),ei(eh,em,ey,"$","\\$"),ei(eh,em,ey,"$","\\textdollar"),ei(eo,em,ey,"%","\\%"),ei(eh,em,ey,"%","\\%"),ei(eo,em,ey,"_","\\_"),ei(eh,em,ey,"_","\\_"),ei(eh,em,ey,"_","\\textunderscore"),ei(eo,em,ey,"∠","\\angle",!0),ei(eo,em,ey,"∞","\\infty",!0),ei(eo,em,ey,"′","\\prime"),ei(eo,em,ey,"△","\\triangle"),ei(eo,em,ey,"Γ","\\Gamma",!0),ei(eo,em,ey,"Δ","\\Delta",!0),ei(eo,em,ey,"Θ","\\Theta",!0),ei(eo,em,ey,"Λ","\\Lambda",!0),ei(eo,em,ey,"Ξ","\\Xi",!0),ei(eo,em,ey,"Π","\\Pi",!0),ei(eo,em,ey,"Σ","\\Sigma",!0),ei(eo,em,ey,"Υ","\\Upsilon",!0),ei(eo,em,ey,"Φ","\\Phi",!0),ei(eo,em,ey,"Ψ","\\Psi",!0),ei(eo,em,ey,"Ω","\\Omega",!0),ei(eo,em,ey,"A","Α"),ei(eo,em,ey,"B","Β"),ei(eo,em,ey,"E","Ε"),ei(eo,em,ey,"Z","Ζ"),ei(eo,em,ey,"H","Η"),ei(eo,em,ey,"I","Ι"),ei(eo,em,ey,"K","Κ"),ei(eo,em,ey,"M","Μ"),ei(eo,em,ey,"N","Ν"),ei(eo,em,ey,"O","Ο"),ei(eo,em,ey,"P","Ρ"),ei(eo,em,ey,"T","Τ"),ei(eo,em,ey,"X","Χ"),ei(eo,em,ey,"\xac","\\neg",!0),ei(eo,em,ey,"\xac","\\lnot"),ei(eo,em,ey,"⊤","\\top"),ei(eo,em,ey,"⊥","\\bot"),ei(eo,em,ey,"∅","\\emptyset"),ei(eo,"ams",ey,"∅","\\varnothing"),ei(eo,em,ep,"α","\\alpha",!0),ei(eo,em,ep,"β","\\beta",!0),ei(eo,em,ep,"γ","\\gamma",!0),ei(eo,em,ep,"δ","\\delta",!0),ei(eo,em,ep,"ϵ","\\epsilon",!0),ei(eo,em,ep,"ζ","\\zeta",!0),ei(eo,em,ep,"η","\\eta",!0),ei(eo,em,ep,"θ","\\theta",!0),ei(eo,em,ep,"ι","\\iota",!0),ei(eo,em,ep,"κ","\\kappa",!0),ei(eo,em,ep,"λ","\\lambda",!0),ei(eo,em,ep,"μ","\\mu",!0),ei(eo,em,ep,"ν","\\nu",!0),ei(eo,em,ep,"ξ","\\xi",!0),ei(eo,em,ep,"ο","\\omicron",!0),ei(eo,em,ep,"π","\\pi",!0),ei(eo,em,ep,"ρ","\\rho",!0),ei(eo,em,ep,"σ","\\sigma",!0),ei(eo,em,ep,"τ","\\tau",!0),ei(eo,em,ep,"υ","\\upsilon",!0),ei(eo,em,ep,"ϕ","\\phi",!0),ei(eo,em,ep,"χ","\\chi",!0),ei(eo,em,ep,"ψ","\\psi",!0),ei(eo,em,ep,"ω","\\omega",!0),ei(eo,em,ep,"ε","\\varepsilon",!0),ei(eo,em,ep,"ϑ","\\vartheta",!0),ei(eo,em,ep,"ϖ","\\varpi",!0),ei(eo,em,ep,"ϱ","\\varrho",!0),ei(eo,em,ep,"ς","\\varsigma",!0),ei(eo,em,ep,"φ","\\varphi",!0),ei(eo,em,"bin","∗","*",!0),ei(eo,em,"bin","+","+"),ei(eo,em,"bin","−","-",!0),ei(eo,em,"bin","⋅","\\cdot",!0),ei(eo,em,"bin","∘","\\circ",!0),ei(eo,em,"bin","\xf7","\\div",!0),ei(eo,em,"bin","\xb1","\\pm",!0),ei(eo,em,"bin","\xd7","\\times",!0),ei(eo,em,"bin","∩","\\cap",!0),ei(eo,em,"bin","∪","\\cup",!0),ei(eo,em,"bin","∖","\\setminus",!0),ei(eo,em,"bin","∧","\\land"),ei(eo,em,"bin","∨","\\lor"),ei(eo,em,"bin","∧","\\wedge",!0),ei(eo,em,"bin","∨","\\vee",!0),ei(eo,em,ey,"√","\\surd"),ei(eo,em,ef,"⟨","\\langle",!0),ei(eo,em,ef,"∣","\\lvert"),ei(eo,em,ef,"∥","\\lVert"),ei(eo,em,ed,"?","?"),ei(eo,em,ed,"!","!"),ei(eo,em,ed,"⟩","\\rangle",!0),ei(eo,em,ed,"∣","\\rvert"),ei(eo,em,ed,"∥","\\rVert"),ei(eo,em,"rel","=","="),ei(eo,em,"rel",":",":"),ei(eo,em,"rel","≈","\\approx",!0),ei(eo,em,"rel","≅","\\cong",!0),ei(eo,em,"rel","≥","\\ge"),ei(eo,em,"rel","≥","\\geq",!0),ei(eo,em,"rel","←","\\gets"),ei(eo,em,"rel",">","\\gt",!0),ei(eo,em,"rel","∈","\\in",!0),ei(eo,em,"rel","","\\@not"),ei(eo,em,"rel","⊂","\\subset",!0),ei(eo,em,"rel","⊃","\\supset",!0),ei(eo,em,"rel","⊆","\\subseteq",!0),ei(eo,em,"rel","⊇","\\supseteq",!0),ei(eo,"ams","rel","⊈","\\nsubseteq",!0),ei(eo,"ams","rel","⊉","\\nsupseteq",!0),ei(eo,em,"rel","⊨","\\models"),ei(eo,em,"rel","←","\\leftarrow",!0),ei(eo,em,"rel","≤","\\le"),ei(eo,em,"rel","≤","\\leq",!0),ei(eo,em,"rel","<","\\lt",!0),ei(eo,em,"rel","→","\\rightarrow",!0),ei(eo,em,"rel","→","\\to"),ei(eo,"ams","rel","≱","\\ngeq",!0),ei(eo,"ams","rel","≰","\\nleq",!0),ei(eo,em,eb,"\xa0","\\ "),ei(eo,em,eb,"\xa0","\\space"),ei(eo,em,eb,"\xa0","\\nobreakspace"),ei(eh,em,eb,"\xa0","\\ "),ei(eh,em,eb,"\xa0"," "),ei(eh,em,eb,"\xa0","\\space"),ei(eh,em,eb,"\xa0","\\nobreakspace"),ei(eo,em,eb,null,"\\nobreak"),ei(eo,em,eb,null,"\\allowbreak"),ei(eo,em,ex,",",","),ei(eo,em,ex,";",";"),ei(eo,"ams","bin","⊼","\\barwedge",!0),ei(eo,"ams","bin","⊻","\\veebar",!0),ei(eo,em,"bin","⊙","\\odot",!0),ei(eo,em,"bin","⊕","\\oplus",!0),ei(eo,em,"bin","⊗","\\otimes",!0),ei(eo,em,ey,"∂","\\partial",!0),ei(eo,em,"bin","⊘","\\oslash",!0),ei(eo,"ams","bin","⊚","\\circledcirc",!0),ei(eo,"ams","bin","⊡","\\boxdot",!0),ei(eo,em,"bin","△","\\bigtriangleup"),ei(eo,em,"bin","▽","\\bigtriangledown"),ei(eo,em,"bin","†","\\dagger"),ei(eo,em,"bin","⋄","\\diamond"),ei(eo,em,"bin","⋆","\\star"),ei(eo,em,"bin","◃","\\triangleleft"),ei(eo,em,"bin","▹","\\triangleright"),ei(eo,em,ef,"{","\\{"),ei(eh,em,ey,"{","\\{"),ei(eh,em,ey,"{","\\textbraceleft"),ei(eo,em,ed,"}","\\}"),ei(eh,em,ey,"}","\\}"),ei(eh,em,ey,"}","\\textbraceright"),ei(eo,em,ef,"{","\\lbrace"),ei(eo,em,ed,"}","\\rbrace"),ei(eo,em,ef,"[","\\lbrack",!0),ei(eh,em,ey,"[","\\lbrack",!0),ei(eo,em,ed,"]","\\rbrack",!0),ei(eh,em,ey,"]","\\rbrack",!0),ei(eo,em,ef,"(","\\lparen",!0),ei(eo,em,ed,")","\\rparen",!0),ei(eh,em,ey,"<","\\textless",!0),ei(eh,em,ey,">","\\textgreater",!0),ei(eo,em,ef,"⌊","\\lfloor",!0),ei(eo,em,ed,"⌋","\\rfloor",!0),ei(eo,em,ef,"⌈","\\lceil",!0),ei(eo,em,ed,"⌉","\\rceil",!0),ei(eo,em,ey,"\\","\\backslash"),ei(eo,em,ey,"∣","|"),ei(eo,em,ey,"∣","\\vert"),ei(eh,em,ey,"|","\\textbar",!0),ei(eo,em,ey,"∥","\\|"),ei(eo,em,ey,"∥","\\Vert"),ei(eh,em,ey,"∥","\\textbardbl"),ei(eh,em,ey,"~","\\textasciitilde"),ei(eh,em,ey,"\\","\\textbackslash"),ei(eh,em,ey,"^","\\textasciicircum"),ei(eo,em,"rel","↑","\\uparrow",!0),ei(eo,em,"rel","⇑","\\Uparrow",!0),ei(eo,em,"rel","↓","\\downarrow",!0),ei(eo,em,"rel","⇓","\\Downarrow",!0),ei(eo,em,"rel","↕","\\updownarrow",!0),ei(eo,em,"rel","⇕","\\Updownarrow",!0),ei(eo,em,eg,"∐","\\coprod"),ei(eo,em,eg,"⋁","\\bigvee"),ei(eo,em,eg,"⋀","\\bigwedge"),ei(eo,em,eg,"⨄","\\biguplus"),ei(eo,em,eg,"⋂","\\bigcap"),ei(eo,em,eg,"⋃","\\bigcup"),ei(eo,em,eg,"∫","\\int"),ei(eo,em,eg,"∫","\\intop"),ei(eo,em,eg,"∬","\\iint"),ei(eo,em,eg,"∭","\\iiint"),ei(eo,em,eg,"∏","\\prod"),ei(eo,em,eg,"∑","\\sum"),ei(eo,em,eg,"⨂","\\bigotimes"),ei(eo,em,eg,"⨁","\\bigoplus"),ei(eo,em,eg,"⨀","\\bigodot"),ei(eo,em,eg,"∮","\\oint"),ei(eo,em,eg,"∯","\\oiint"),ei(eo,em,eg,"∰","\\oiiint"),ei(eo,em,eg,"⨆","\\bigsqcup"),ei(eo,em,eg,"∫","\\smallint"),ei(eh,em,eu,"…","\\textellipsis"),ei(eo,em,eu,"…","\\mathellipsis"),ei(eh,em,eu,"…","\\ldots",!0),ei(eo,em,eu,"…","\\ldots",!0),ei(eo,em,eu,"⋯","\\@cdots",!0),ei(eo,em,eu,"⋱","\\ddots",!0),ei(eo,em,ey,"⋮","\\varvdots"),ei(eh,em,ey,"⋮","\\varvdots"),ei(eo,em,ec,"ˊ","\\acute"),ei(eo,em,ec,"ˋ","\\grave"),ei(eo,em,ec,"\xa8","\\ddot"),ei(eo,em,ec,"~","\\tilde"),ei(eo,em,ec,"ˉ","\\bar"),ei(eo,em,ec,"˘","\\breve"),ei(eo,em,ec,"ˇ","\\check"),ei(eo,em,ec,"^","\\hat"),ei(eo,em,ec,"⃗","\\vec"),ei(eo,em,ec,"˙","\\dot"),ei(eo,em,ec,"˚","\\mathring"),ei(eo,em,ep,"","\\@imath"),ei(eo,em,ep,"","\\@jmath"),ei(eo,em,ey,"ı","ı"),ei(eo,em,ey,"ȷ","ȷ"),ei(eh,em,ey,"ı","\\i",!0),ei(eh,em,ey,"ȷ","\\j",!0),ei(eh,em,ey,"\xdf","\\ss",!0),ei(eh,em,ey,"\xe6","\\ae",!0),ei(eh,em,ey,"œ","\\oe",!0),ei(eh,em,ey,"\xf8","\\o",!0),ei(eh,em,ey,"\xc6","\\AE",!0),ei(eh,em,ey,"Œ","\\OE",!0),ei(eh,em,ey,"\xd8","\\O",!0),ei(eh,em,ec,"ˊ","\\'"),ei(eh,em,ec,"ˋ","\\`"),ei(eh,em,ec,"ˆ","\\^"),ei(eh,em,ec,"˜","\\~"),ei(eh,em,ec,"ˉ","\\="),ei(eh,em,ec,"˘","\\u"),ei(eh,em,ec,"˙","\\."),ei(eh,em,ec,"\xb8","\\c"),ei(eh,em,ec,"˚","\\r"),ei(eh,em,ec,"ˇ","\\v"),ei(eh,em,ec,"\xa8",'\\"'),ei(eh,em,ec,"˝","\\H"),ei(eh,em,ec,"◯","\\textcircled");let ew={"--":!0,"---":!0,"``":!0,"''":!0};ei(eh,em,ey,"–","--",!0),ei(eh,em,ey,"–","\\textendash"),ei(eh,em,ey,"—","---",!0),ei(eh,em,ey,"—","\\textemdash"),ei(eh,em,ey,"‘","`",!0),ei(eh,em,ey,"‘","\\textquoteleft"),ei(eh,em,ey,"’","'",!0),ei(eh,em,ey,"’","\\textquoteright"),ei(eh,em,ey,"“","``",!0),ei(eh,em,ey,"“","\\textquotedblleft"),ei(eh,em,ey,"”","''",!0),ei(eh,em,ey,"”","\\textquotedblright"),ei(eo,em,ey,"\xb0","\\degree",!0),ei(eh,em,ey,"\xb0","\\degree"),ei(eh,em,ey,"\xb0","\\textdegree",!0),ei(eo,em,ey,"\xa3","\\pounds"),ei(eo,em,ey,"\xa3","\\mathsterling",!0),ei(eh,em,ey,"\xa3","\\pounds"),ei(eh,em,ey,"\xa3","\\textsterling",!0),ei(eo,"ams",ey,"✠","\\maltese"),ei(eh,"ams",ey,"✠","\\maltese");let ev='0123456789/@."';for(let e=0;e<ev.length;e++){let t=ev.charAt(e);ei(eo,em,ey,t,t)}let ek='0123456789!@*()-=+";:?/.,';for(let e=0;e<ek.length;e++){let t=ek.charAt(e);ei(eh,em,ey,t,t)}let eS="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(let e=0;e<eS.length;e++){let t=eS.charAt(e);ei(eo,em,ep,t,t),ei(eh,em,ey,t,t)}ei(eo,"ams",ey,"C","ℂ"),ei(eh,"ams",ey,"C","ℂ"),ei(eo,"ams",ey,"H","ℍ"),ei(eh,"ams",ey,"H","ℍ"),ei(eo,"ams",ey,"N","ℕ"),ei(eh,"ams",ey,"N","ℕ"),ei(eo,"ams",ey,"P","ℙ"),ei(eh,"ams",ey,"P","ℙ"),ei(eo,"ams",ey,"Q","ℚ"),ei(eh,"ams",ey,"Q","ℚ"),ei(eo,"ams",ey,"R","ℝ"),ei(eh,"ams",ey,"R","ℝ"),ei(eo,"ams",ey,"Z","ℤ"),ei(eh,"ams",ey,"Z","ℤ"),ei(eo,em,ep,"h","ℎ"),ei(eh,em,ep,"h","ℎ");let eM="";for(let e=0;e<eS.length;e++){let t=eS.charAt(e);ei(eo,em,ep,t,eM=String.fromCharCode(55349,56320+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56372+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56424+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56580+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56684+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56736+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56788+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56840+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56944+e)),ei(eh,em,ey,t,eM),e<26&&(ei(eo,em,ep,t,eM=String.fromCharCode(55349,56632+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,56476+e)),ei(eh,em,ey,t,eM))}ei(eo,em,ep,"k",eM=String.fromCharCode(55349,56668)),ei(eh,em,ey,"k",eM);for(let e=0;e<10;e++){let t=e.toString();ei(eo,em,ep,t,eM=String.fromCharCode(55349,57294+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,57314+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,57324+e)),ei(eh,em,ey,t,eM),ei(eo,em,ep,t,eM=String.fromCharCode(55349,57334+e)),ei(eh,em,ey,t,eM)}let ez="\xd0\xde\xfe";for(let e=0;e<ez.length;e++){let t=ez.charAt(e);ei(eo,em,ep,t,t),ei(eh,em,ey,t,t)}let eA=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],eT=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],eN=function(e,t){let r=(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320)+65536,l=+("math"!==t);if(119808<=r&&r<120484){let e=Math.floor((r-119808)/26);return[eA[e][2],eA[e][l]]}if(120782<=r&&r<=120831){let e=Math.floor((r-120782)/10);return[eT[e][2],eT[e][l]]}if(120485===r||120486===r)return[eA[0][2],eA[0][l]];if(120486<r&&r<120782)return["",""];throw new n("Unsupported character: "+e)},eC=function(e,t,r){return ea[r][e]&&ea[r][e].replace&&(e=ea[r][e].replace),{value:e,metrics:I(e,t,r)}},ej=function(e,t,r,l,n){let s;let a=eC(e,t,r),i=a.metrics;if(e=a.value,i){let t=i.italic;("text"===r||l&&"mathit"===l.font)&&(t=0),s=new J(e,i.height,i.depth,t,i.skew,i.width,n)}else"undefined"!=typeof console&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '")+r+"'"),s=new J(e,0,0,0,0,0,n);if(l){s.maxFontSize=l.sizeMultiplier,l.style.isTight()&&s.classes.push("mtight");let e=l.getColor();e&&(s.style.color=e)}return s},eq=(e,t)=>{if(U(e.classes)!==U(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(1===e.classes.length){let t=e.classes[0];if("mbin"===t||"mord"===t)return!1}for(let r in e.style)if(e.style.hasOwnProperty(r)&&e.style[r]!==t.style[r])return!1;for(let r in t.style)if(t.style.hasOwnProperty(r)&&e.style[r]!==t.style[r])return!1;return!0},eB=function(e){let t=0,r=0,l=0;for(let n=0;n<e.children.length;n++){let s=e.children[n];s.height>t&&(t=s.height),s.depth>r&&(r=s.depth),s.maxFontSize>l&&(l=s.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=l},eI=function(e,t,r,l){let n=new Y(e,t,r,l);return eB(n),n},eE=(e,t,r,l)=>new Y(e,t,r,l),eO=function(e){let t=new C(e);return eB(t),t},eH=function(e){let t;if("individualShift"===e.positionType){let t=e.children,r=[t[0]],l=-t[0].shift-t[0].elem.depth,n=l;for(let e=1;e<t.length;e++){let l=-t[e].shift-n-t[e].elem.depth,s=l-(t[e-1].elem.height+t[e-1].elem.depth);n+=l,r.push({type:"kern",size:s}),r.push(t[e])}return{children:r,depth:l}}if("top"===e.positionType){let r=e.positionData;for(let t=0;t<e.children.length;t++){let l=e.children[t];r-="kern"===l.type?l.size:l.elem.height+l.elem.depth}t=r}else if("bottom"===e.positionType)t=-e.positionData;else{let r=e.children[0];if("elem"!==r.type)throw Error('First child must have type "elem".');if("shift"===e.positionType)t=-r.elem.depth-e.positionData;else if("firstBaseline"===e.positionType)t=-r.elem.depth;else throw Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:t}},eR=function(e,t,r){let l="";switch(e){case"amsrm":l="AMS";break;case"textrm":l="Main";break;case"textsf":l="SansSerif";break;case"texttt":l="Typewriter";break;default:l=e}return l+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},eL={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},eP={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]};var eD={fontMap:eL,makeSymbol:ej,mathsym:function(e,t,r,l){return(void 0===l&&(l=[]),"boldsymbol"===r.font&&eC(e,"Main-Bold",t).metrics)?ej(e,"Main-Bold",t,r,l.concat(["mathbf"])):"\\"===e||"main"===ea[t][e].font?ej(e,"Main-Regular",t,r,l):ej(e,"AMS-Regular",t,r,l.concat(["amsrm"]))},makeSpan:eI,makeSvgSpan:eE,makeLineSpan:function(e,t,r){let l=eI([e],[],t);return l.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),l.style.borderBottomWidth=_(l.height),l.maxFontSize=1,l},makeAnchor:function(e,t,r,l){let n=new Z(e,t,r,l);return eB(n),n},makeFragment:eO,wrapFragment:function(e,t){return e instanceof C?eI([],[e],t):e},makeVList:function(e,t){let r;let{children:l,depth:n}=eH(e),s=0;for(let e=0;e<l.length;e++){let t=l[e];if("elem"===t.type){let e=t.elem;s=Math.max(s,e.maxFontSize,e.height)}}s+=2;let a=eI(["pstrut"],[]);a.style.height=_(s);let i=[],o=n,h=n,m=n;for(let e=0;e<l.length;e++){let t=l[e];if("kern"===t.type)m+=t.size;else{let e=t.elem,r=eI(t.wrapperClasses||[],[a,e],void 0,t.wrapperStyle||{});r.style.top=_(-s-m-e.depth),t.marginLeft&&(r.style.marginLeft=t.marginLeft),t.marginRight&&(r.style.marginRight=t.marginRight),i.push(r),m+=e.height+e.depth}o=Math.min(o,m),h=Math.max(h,m)}let c=eI(["vlist"],i);if(c.style.height=_(h),o<0){let e=eI([],[]),t=eI(["vlist"],[e]);t.style.height=_(-o);let l=eI(["vlist-s"],[new J("​")]);r=[eI(["vlist-r"],[c,l]),eI(["vlist-r"],[t])]}else r=[eI(["vlist-r"],[c])];let d=eI(["vlist-t"],r);return 2===r.length&&d.classes.push("vlist-t2"),d.height=h,d.depth=-o,d},makeOrd:function(e,t,r){let l=e.mode,n=e.text,s=["mord"],a="math"===l||"text"===l&&t.font,i=a?t.font:t.fontFamily,o="",h="";if(55349===n.charCodeAt(0)&&([o,h]=eN(n,l)),o.length>0)return ej(n,o,l,t,s.concat(h));if(i){let e,o;if("boldsymbol"===i){let t="textord"!==r&&eC(n,"Math-BoldItalic",l).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"};e=t.fontName,o=[t.fontClass]}else a?(e=eL[i].fontName,o=[i]):(e=eR(i,t.fontWeight,t.fontShape),o=[i,t.fontWeight,t.fontShape]);if(eC(n,e,l).metrics)return ej(n,e,l,t,s.concat(o));if(ew.hasOwnProperty(n)&&"Typewriter"===e.slice(0,10)){let r=[];for(let a=0;a<n.length;a++)r.push(ej(n[a],e,l,t,s.concat(o)));return eO(r)}}if("mathord"===r)return ej(n,"Math-Italic",l,t,s.concat(["mathnormal"]));if("textord"===r){let e=ea[l][n]&&ea[l][n].font;if("ams"===e)return ej(n,eR("amsrm",t.fontWeight,t.fontShape),l,t,s.concat("amsrm",t.fontWeight,t.fontShape));if("main"===e||!e)return ej(n,eR("textrm",t.fontWeight,t.fontShape),l,t,s.concat(t.fontWeight,t.fontShape));{let r=eR(e,t.fontWeight,t.fontShape);return ej(n,r,l,t,s.concat(r,t.fontWeight,t.fontShape))}}throw Error("unexpected type: "+r+" in makeOrd")},makeGlue:(e,t)=>{let r=eI(["mspace"],[],t),l=V(e,t);return r.style.marginRight=_(l),r},staticSvg:function(e,t){let[r,l,n]=eP[e],s=eE(["overlay"],[new ee([new et(r)],{width:_(l),height:_(n),style:"width:"+_(l),viewBox:"0 0 "+1e3*l+" "+1e3*n,preserveAspectRatio:"xMinYMin"})],t);return s.height=n,s.style.height=_(n),s.style.width=_(l),s},svgData:eP,tryCombineChars:e=>{for(let t=0;t<e.length-1;t++){let r=e[t],l=e[t+1];r instanceof J&&l instanceof J&&eq(r,l)&&(r.text+=l.text,r.height=Math.max(r.height,l.height),r.depth=Math.max(r.depth,l.depth),r.italic=l.italic,e.splice(t+1,1),t--)}return e}};let eF={number:3,unit:"mu"},eV={number:4,unit:"mu"},e_={number:5,unit:"mu"},eU={mord:{mop:eF,mbin:eV,mrel:e_,minner:eF},mop:{mord:eF,mop:eF,mrel:e_,minner:eF},mbin:{mord:eV,mop:eV,mopen:eV,minner:eV},mrel:{mord:e_,mop:e_,mopen:e_,minner:e_},mopen:{},mclose:{mop:eF,mbin:eV,mrel:e_,minner:eF},mpunct:{mord:eF,mop:eF,mrel:e_,mopen:eF,mclose:eF,mpunct:eF,minner:eF},minner:{mord:eF,mop:eF,mbin:eV,mrel:e_,mopen:eF,mpunct:eF,minner:eF}},e$={mord:{mop:eF},mop:{mord:eF,mop:eF},mbin:{},mrel:{},mopen:{},mclose:{mop:eF},mpunct:{},minner:{mop:eF}},eG={},eW={},eX={};function eY(e){let{type:t,names:r,props:l,handler:n,htmlBuilder:s,mathmlBuilder:a}=e,i={type:t,numArgs:l.numArgs,argTypes:l.argTypes,allowedInArgument:!!l.allowedInArgument,allowedInText:!!l.allowedInText,allowedInMath:void 0===l.allowedInMath||l.allowedInMath,numOptionalArgs:l.numOptionalArgs||0,infix:!!l.infix,primitive:!!l.primitive,handler:n};for(let e=0;e<r.length;++e)eG[r[e]]=i;t&&(s&&(eW[t]=s),a&&(eX[t]=a))}function eZ(e){let{type:t,htmlBuilder:r,mathmlBuilder:l}=e;eY({type:t,names:[],props:{numArgs:0},handler(){throw Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:l})}let eK=function(e){return"ordgroup"===e.type&&1===e.body.length?e.body[0]:e},eQ=function(e){return"ordgroup"===e.type?e.body:[e]},eJ=eD.makeSpan,e0=["leftmost","mbin","mopen","mrel","mop","mpunct"],e1=["rightmost","mrel","mclose","mpunct"],e4={display:v.DISPLAY,text:v.TEXT,script:v.SCRIPT,scriptscript:v.SCRIPTSCRIPT},e5={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},e6=function(e,t,r,l){void 0===l&&(l=[null,null]);let n=[];for(let r=0;r<e.length;r++){let l=te(e[r],t);if(l instanceof C){let e=l.children;n.push(...e)}else n.push(l)}if(eD.tryCombineChars(n),!r)return n;let s=t;if(1===e.length){let r=e[0];"sizing"===r.type?s=t.havingSize(r.size):"styling"===r.type&&(s=t.havingStyle(e4[r.style]))}let a=eJ([l[0]||"leftmost"],[],t),i=eJ([l[1]||"rightmost"],[],t),o="root"===r;return e3(n,(e,t)=>{let r=t.classes[0],l=e.classes[0];"mbin"===r&&m.contains(e1,l)?t.classes[0]="mord":"mbin"===l&&m.contains(e0,r)&&(e.classes[0]="mord")},{node:a},i,o),e3(n,(e,t)=>{let r=e2(t),l=e2(e),n=r&&l?e.hasClass("mtight")?e$[r][l]:eU[r][l]:null;if(n)return eD.makeGlue(n,s)},{node:a},i,o),n},e3=function(e,t,r,l,n){l&&e.push(l);let s=0;for(;s<e.length;s++){let l;let a=e[s],i=e7(a);if(i){e3(i.children,t,r,null,n);continue}let o=!a.hasClass("mspace");if(o){let l=t(a,r.node);l&&(r.insertAfter?r.insertAfter(l):(e.unshift(l),s++))}o?r.node=a:n&&a.hasClass("newline")&&(r.node=eJ(["leftmost"])),l=s,r.insertAfter=t=>{e.splice(l+1,0,t),s++}}l&&e.pop()},e7=function(e){return e instanceof C||e instanceof Z||e instanceof Y&&e.hasClass("enclosing")?e:null},e8=function(e,t){let r=e7(e);if(r){let e=r.children;if(e.length){if("right"===t)return e8(e[e.length-1],"right");if("left"===t)return e8(e[0],"left")}}return e},e2=function(e,t){return e?(t&&(e=e8(e,t)),e5[e.classes[0]]||null):null},e9=function(e,t){let r=["nulldelimiter"].concat(e.baseSizingClasses());return eJ(t.concat(r))},te=function(e,t,r){if(!e)return eJ();if(eW[e.type]){let l=eW[e.type](e,t);if(r&&t.size!==r.size){l=eJ(t.sizingClasses(r),[l],t);let e=t.sizeMultiplier/r.sizeMultiplier;l.height*=e,l.depth*=e}return l}throw new n("Got group of unknown type: '"+e.type+"'")};function tt(e,t){let r=eJ(["base"],e,t),l=eJ(["strut"]);return l.style.height=_(r.height+r.depth),r.depth&&(l.style.verticalAlign=_(-r.depth)),r.children.unshift(l),r}function tr(e,t){let r,l,n=null;1===e.length&&"tag"===e[0].type&&(n=e[0].tag,e=e[0].body);let s=e6(e,t,"root");2===s.length&&s[1].hasClass("tag")&&(r=s.pop());let a=[],i=[];for(let e=0;e<s.length;e++)if(i.push(s[e]),s[e].hasClass("mbin")||s[e].hasClass("mrel")||s[e].hasClass("allowbreak")){let r=!1;for(;e<s.length-1&&s[e+1].hasClass("mspace")&&!s[e+1].hasClass("newline");)e++,i.push(s[e]),s[e].hasClass("nobreak")&&(r=!0);r||(a.push(tt(i,t)),i=[])}else s[e].hasClass("newline")&&(i.pop(),i.length>0&&(a.push(tt(i,t)),i=[]),a.push(s[e]));i.length>0&&a.push(tt(i,t)),n?((l=tt(e6(n,t,!0))).classes=["tag"],a.push(l)):r&&a.push(r);let o=eJ(["katex-html"],a);if(o.setAttribute("aria-hidden","true"),l){let e=l.children[0];e.style.height=_(o.height+o.depth),o.depth&&(e.style.verticalAlign=_(-o.depth))}return o}function tl(e){return new C(e)}class tn{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){let e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=U(this.classes));for(let t=0;t<this.children.length;t++)if(this.children[t]instanceof ts&&this.children[t+1]instanceof ts){let r=this.children[t].toText()+this.children[++t].toText();for(;this.children[t+1]instanceof ts;)r+=this.children[++t].toText();e.appendChild(new ts(r).toNode())}else e.appendChild(this.children[t].toNode());return e}toMarkup(){let e="<"+this.type;for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=m.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+m.escape(U(this.classes))+'"'),e+=">";for(let t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e+("</"+this.type+">")}toText(){return this.children.map(e=>e.toText()).join("")}}class ts{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return m.escape(this.toText())}toText(){return this.text}}class ta{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);{let e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",_(this.width)),e}}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+_(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var ti={MathNode:tn,TextNode:ts,SpaceNode:ta,newDocumentFragment:tl};let to=function(e,t,r){return ea[t][e]&&ea[t][e].replace&&55349!==e.charCodeAt(0)&&!(ew.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.slice(4,6)||r.font&&"tt"===r.font.slice(4,6)))&&(e=ea[t][e].replace),new ti.TextNode(e)},th=function(e){return 1===e.length?e[0]:new ti.MathNode("mrow",e)},tm=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";let r=t.font;if(!r||"mathnormal"===r)return null;let l=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"textord"===e.type?"bold":"bold-italic";if("mathbf"===r)return"bold";if("mathbb"===r)return"double-struck";if("mathsfit"===r)return"sans-serif-italic";else if("mathfrak"===r)return"fraktur";else if("mathscr"===r||"mathcal"===r)return"script";else if("mathsf"===r)return"sans-serif";else if("mathtt"===r)return"monospace";let n=e.text;return m.contains(["\\imath","\\jmath"],n)?null:(ea[l][n]&&ea[l][n].replace&&(n=ea[l][n].replace),I(n,eD.fontMap[r].fontName,l))?eD.fontMap[r].variant:null};function tc(e){if(!e)return!1;if("mi"===e.type&&1===e.children.length){let t=e.children[0];return t instanceof ts&&"."===t.text}if("mo"!==e.type||1!==e.children.length||"true"!==e.getAttribute("separator")||"0em"!==e.getAttribute("lspace")||"0em"!==e.getAttribute("rspace"))return!1;{let t=e.children[0];return t instanceof ts&&","===t.text}}let td=function(e,t,r){let l;if(1===e.length){let l=tp(e[0],t);return r&&l instanceof tn&&"mo"===l.type&&(l.setAttribute("lspace","0em"),l.setAttribute("rspace","0em")),[l]}let n=[];for(let r=0;r<e.length;r++){let s=tp(e[r],t);if(s instanceof tn&&l instanceof tn){if("mtext"===s.type&&"mtext"===l.type&&s.getAttribute("mathvariant")===l.getAttribute("mathvariant")){l.children.push(...s.children);continue}if("mn"===s.type&&"mn"===l.type){l.children.push(...s.children);continue}if(tc(s)&&"mn"===l.type){l.children.push(...s.children);continue}else if("mn"===s.type&&tc(l))s.children=[...l.children,...s.children],n.pop();else if(("msup"===s.type||"msub"===s.type)&&s.children.length>=1&&("mn"===l.type||tc(l))){let e=s.children[0];e instanceof tn&&"mn"===e.type&&(e.children=[...l.children,...e.children],n.pop())}else if("mi"===l.type&&1===l.children.length){let e=l.children[0];if(e instanceof ts&&"̸"===e.text&&("mo"===s.type||"mi"===s.type||"mn"===s.type)){let e=s.children[0];e instanceof ts&&e.text.length>0&&(e.text=e.text.slice(0,1)+"̸"+e.text.slice(1),n.pop())}}}n.push(s),l=s}return n},tu=function(e,t,r){return th(td(e,t,r))},tp=function(e,t){if(!e)return new ti.MathNode("mrow");if(eX[e.type])return eX[e.type](e,t);throw new n("Got group of unknown type: '"+e.type+"'")};function tg(e,t,r,l,n){let s;let a=td(e,r);s=1===a.length&&a[0]instanceof tn&&m.contains(["mrow","mtable"],a[0].type)?a[0]:new ti.MathNode("mrow",a);let i=new ti.MathNode("annotation",[new ti.TextNode(t)]);i.setAttribute("encoding","application/x-tex");let o=new ti.MathNode("semantics",[s,i]),h=new ti.MathNode("math",[o]);return h.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),l&&h.setAttribute("display","block"),eD.makeSpan([n?"katex":"katex-mathml"],[h])}let tf=function(e){return new L({style:e.displayMode?v.DISPLAY:v.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},tx=function(e,t){if(t.displayMode){let r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=eD.makeSpan(r,[e])}return e},tb=function(e,t,r){let l;let n=tf(r);if("mathml"===r.output)return tg(e,t,n,r.displayMode,!0);if("html"===r.output){let t=tr(e,n);l=eD.makeSpan(["katex"],[t])}else{let s=tg(e,t,n,r.displayMode,!1),a=tr(e,n);l=eD.makeSpan(["katex"],[s,a])}return tx(l,r)},ty=function(e,t,r){let l=tr(e,tf(r));return tx(eD.makeSpan(["katex"],[l]),r)},tw={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},tv={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]};var tk={encloseSpan:function(e,t,r,l,n){let s;let a=e.height+e.depth+r+l;if(/fbox|color|angl/.test(t)){if(s=eD.makeSpan(["stretchy",t],[],n),"fbox"===t){let e=n.color&&n.getColor();e&&(s.style.borderColor=e)}}else{let e=[];/^[bx]cancel$/.test(t)&&e.push(new er({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&e.push(new er({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));let r=new ee(e,{width:"100%",height:_(a)});s=eD.makeSvgSpan([],[r],n)}return s.height=a,s.style.height=_(a),s},mathMLnode:function(e){let t=new ti.MathNode("mo",[new ti.TextNode(tw[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},svgSpan:function(e,t){let{span:r,minWidth:l,height:n}=function(){let r=4e5,l=e.label.slice(1);if(m.contains(["widehat","widecheck","widetilde","utilde"],l)){var n;let s,a,i;let o="ordgroup"===(n=e.base).type?n.body.length:1;if(o>5)"widehat"===l||"widecheck"===l?(s=420,r=2364,i=.42,a=l+"4"):(s=312,r=2340,i=.34,a="tilde4");else{let e=[1,1,2,2,3,3][o];"widehat"===l||"widecheck"===l?(r=[0,1062,2364,2364,2364][e],s=[0,239,300,360,420][e],i=[0,.24,.3,.3,.36,.42][e],a=l+e):(r=[0,600,1033,2339,2340][e],s=[0,260,286,306,312][e],i=[0,.26,.286,.3,.306,.34][e],a="tilde"+e)}let h=new ee([new et(a)],{width:"100%",height:_(i),viewBox:"0 0 "+r+" "+s,preserveAspectRatio:"none"});return{span:eD.makeSvgSpan([],[h],t),minWidth:0,height:i}}{let e,n;let s=[],a=tv[l],[i,o,h]=a,m=h/1e3,c=i.length;if(1===c)e=["hide-tail"],n=[a[3]];else if(2===c)e=["halfarrow-left","halfarrow-right"],n=["xMinYMin","xMaxYMin"];else if(3===c)e=["brace-left","brace-center","brace-right"],n=["xMinYMin","xMidYMin","xMaxYMin"];else throw Error("Correct katexImagesData or update code here to support\n                    "+c+" children.");for(let l=0;l<c;l++){let a=new ee([new et(i[l])],{width:"400em",height:_(m),viewBox:"0 0 "+r+" "+h,preserveAspectRatio:n[l]+" slice"}),d=eD.makeSvgSpan([e[l]],[a],t);if(1===c)return{span:d,minWidth:o,height:m};d.style.height=_(m),s.push(d)}return{span:eD.makeSpan(["stretchy"],s,t),minWidth:o,height:m}}}();return r.height=n,r.style.height=_(n),l>0&&(r.style.minWidth=_(l)),r}};function tS(e,t){if(!e||e.type!==t)throw Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return e}function tM(e){let t=tz(e);if(!t)throw Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}function tz(e){return e&&("atom"===e.type||es.hasOwnProperty(e.type))?e:null}let tA=(e,t)=>{let r,l,n,s;e&&"supsub"===e.type?(r=(l=tS(e.base,"accent")).base,e.base=r,n=function(e){if(e instanceof Y)return e;throw Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(te(e,t)),e.base=l):r=(l=tS(e,"accent")).base;let a=te(r,t.havingCrampedStyle()),i=l.isShifty&&m.isCharacterBox(r),o=0;i&&(o=el(te(m.getBaseElem(r),t.havingCrampedStyle())).skew);let h="\\c"===l.label,c=h?a.height+a.depth:Math.min(a.height,t.fontMetrics().xHeight);if(l.isStretchy)s=tk.svgSpan(l,t),s=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"elem",elem:s,wrapperClasses:["svg-align"],wrapperStyle:o>0?{width:"calc(100% - "+_(2*o)+")",marginLeft:_(2*o)}:void 0}]},t);else{let e,r;"\\vec"===l.label?(e=eD.staticSvg("vec",t),r=eD.svgData.vec[1]):((e=el(e=eD.makeOrd({mode:l.mode,text:l.label},t,"textord"))).italic=0,r=e.width,h&&(c+=e.depth)),s=eD.makeSpan(["accent-body"],[e]);let n="\\textcircled"===l.label;n&&(s.classes.push("accent-full"),c=a.height);let i=o;n||(i-=r/2),s.style.left=_(i),"\\textcircled"===l.label&&(s.style.top=".2em"),s=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"kern",size:-c},{type:"elem",elem:s}]},t)}let d=eD.makeSpan(["mord","accent"],[s],t);return n?(n.children[0]=d,n.height=Math.max(d.height,n.height),n.classes[0]="mord",n):d},tT=(e,t)=>{let r=e.isStretchy?tk.mathMLnode(e.label):new ti.MathNode("mo",[to(e.label,e.mode)]),l=new ti.MathNode("mover",[tp(e.base,t),r]);return l.setAttribute("accent","true"),l},tN=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(e=>"\\"+e).join("|"));eY({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(e,t)=>{let r=eK(t[0]),l=!tN.test(e.funcName),n=!l||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:l,isShifty:n,base:r}},htmlBuilder:tA,mathmlBuilder:tT}),eY({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(e,t)=>{let r=t[0],l=e.parser.mode;return"math"===l&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),l="text"),{type:"accent",mode:l,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:tA,mathmlBuilder:tT}),eY({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=t[0];return{type:"accentUnder",mode:r.mode,label:l,base:n}},htmlBuilder:(e,t)=>{let r=te(e.base,t),l=tk.svgSpan(e,t),n=.12*("\\utilde"===e.label),s=eD.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:l,wrapperClasses:["svg-align"]},{type:"kern",size:n},{type:"elem",elem:r}]},t);return eD.makeSpan(["mord","accentunder"],[s],t)},mathmlBuilder:(e,t)=>{let r=tk.mathMLnode(e.label),l=new ti.MathNode("munder",[tp(e.base,t),r]);return l.setAttribute("accentunder","true"),l}});let tC=e=>{let t=new ti.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};eY({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){let{parser:l,funcName:n}=e;return{type:"xArrow",mode:l.mode,label:n,body:t[0],below:r[0]}},htmlBuilder(e,t){let r,l;let n=t.style,s=t.havingStyle(n.sup()),a=eD.wrapFragment(te(e.body,s,t),t),i="\\x"===e.label.slice(0,2)?"x":"cd";a.classes.push(i+"-arrow-pad"),e.below&&(s=t.havingStyle(n.sub()),(r=eD.wrapFragment(te(e.below,s,t),t)).classes.push(i+"-arrow-pad"));let o=tk.svgSpan(e,t),h=-t.fontMetrics().axisHeight+.5*o.height,m=-t.fontMetrics().axisHeight-.5*o.height-.111;if((a.depth>.25||"\\xleftequilibrium"===e.label)&&(m-=a.depth),r){let e=-t.fontMetrics().axisHeight+r.height+.5*o.height+.111;l=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:m},{type:"elem",elem:o,shift:h},{type:"elem",elem:r,shift:e}]},t)}else l=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:m},{type:"elem",elem:o,shift:h}]},t);return l.children[0].children[0].children[1].classes.push("svg-align"),eD.makeSpan(["mrel","x-arrow"],[l],t)},mathmlBuilder(e,t){let r;let l=tk.mathMLnode(e.label);if(l.setAttribute("minsize","x"===e.label.charAt(0)?"1.75em":"3.0em"),e.body){let n=tC(tp(e.body,t));if(e.below){let s=tC(tp(e.below,t));r=new ti.MathNode("munderover",[l,s,n])}else r=new ti.MathNode("mover",[l,n])}else if(e.below){let n=tC(tp(e.below,t));r=new ti.MathNode("munder",[l,n])}else r=tC(),r=new ti.MathNode("mover",[l,r]);return r}});let tj=eD.makeSpan;function tq(e,t){let r=e6(e.body,t,!0);return tj([e.mclass],r,t)}function tB(e,t){let r;let l=td(e.body,t);return"minner"===e.mclass?r=new ti.MathNode("mpadded",l):"mord"===e.mclass?e.isCharacterBox?(r=l[0]).type="mi":r=new ti.MathNode("mi",l):(e.isCharacterBox?(r=l[0]).type="mo":r=new ti.MathNode("mo",l),"mbin"===e.mclass?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):"mpunct"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):"mopen"===e.mclass||"mclose"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0em"):"minner"===e.mclass&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}eY({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(e,t){let{parser:r,funcName:l}=e,n=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+l.slice(5),body:eQ(n),isCharacterBox:m.isCharacterBox(n)}},htmlBuilder:tq,mathmlBuilder:tB});let tI=e=>{let t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"===t.type&&("bin"===t.family||"rel"===t.family)?"m"+t.family:"mord"};eY({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(e,t){let{parser:r}=e;return{type:"mclass",mode:r.mode,mclass:tI(t[0]),body:eQ(t[1]),isCharacterBox:m.isCharacterBox(t[1])}}}),eY({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(e,t){let r,{parser:l,funcName:n}=e,s=t[1],a=t[0];r="\\stackrel"!==n?tI(s):"mrel";let i={type:"op",mode:s.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:"\\stackrel"!==n,body:eQ(s)},o={type:"supsub",mode:a.mode,base:i,sup:"\\underset"===n?null:a,sub:"\\underset"===n?a:null};return{type:"mclass",mode:l.mode,mclass:r,body:[o],isCharacterBox:m.isCharacterBox(o)}},htmlBuilder:tq,mathmlBuilder:tB}),eY({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(e,t){let{parser:r}=e;return{type:"pmb",mode:r.mode,mclass:tI(t[0]),body:eQ(t[0])}},htmlBuilder(e,t){let r=e6(e.body,t,!0),l=eD.makeSpan([e.mclass],r,t);return l.style.textShadow="0.02em 0.01em 0.04px",l},mathmlBuilder(e,t){let r=td(e.body,t),l=new ti.MathNode("mstyle",r);return l.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),l}});let tE={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},tO=()=>({type:"styling",body:[],mode:"math",style:"display"}),tH=e=>"textord"===e.type&&"@"===e.text,tR=(e,t)=>("mathord"===e.type||"atom"===e.type)&&e.text===t;eY({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(e,t){let{parser:r,funcName:l}=e;return{type:"cdlabel",mode:r.mode,side:l.slice(4),label:t[0]}},htmlBuilder(e,t){let r=t.havingStyle(t.style.sup()),l=eD.wrapFragment(te(e.label,r,t),t);return l.classes.push("cd-label-"+e.side),l.style.bottom=_(.8-l.depth),l.height=0,l.depth=0,l},mathmlBuilder(e,t){let r=new ti.MathNode("mrow",[tp(e.label,t)]);return(r=new ti.MathNode("mpadded",[r])).setAttribute("width","0"),"left"===e.side&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new ti.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),eY({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(e,t){let{parser:r}=e;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder(e,t){let r=eD.wrapFragment(te(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:(e,t)=>new ti.MathNode("mrow",[tp(e.fragment,t)])}),eY({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(e,t){let r,{parser:l}=e,s=tS(t[0],"ordgroup").body,a="";for(let e=0;e<s.length;e++)a+=tS(s[e],"textord").text;let i=parseInt(a);if(isNaN(i))throw new n("\\@char has non-numeric argument "+a);if(i<0||i>=1114111)throw new n("\\@char with invalid code point "+a);return i<=65535?r=String.fromCharCode(i):(i-=65536,r=String.fromCharCode((i>>10)+55296,(1023&i)+56320)),{type:"textord",mode:l.mode,text:r}}});let tL=(e,t)=>{let r=e6(e.body,t.withColor(e.color),!1);return eD.makeFragment(r)},tP=(e,t)=>{let r=td(e.body,t.withColor(e.color)),l=new ti.MathNode("mstyle",r);return l.setAttribute("mathcolor",e.color),l};eY({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(e,t){let{parser:r}=e,l=tS(t[0],"color-token").color,n=t[1];return{type:"color",mode:r.mode,color:l,body:eQ(n)}},htmlBuilder:tL,mathmlBuilder:tP}),eY({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(e,t){let{parser:r,breakOnTokenText:l}=e,n=tS(t[0],"color-token").color;r.gullet.macros.set("\\current@color",n);let s=r.parseExpression(!0,l);return{type:"color",mode:r.mode,color:n,body:s}},htmlBuilder:tL,mathmlBuilder:tP}),eY({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(e,t,r){let{parser:l}=e,n="["===l.gullet.future().text?l.parseSizeGroup(!0):null,s=!l.settings.displayMode||!l.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:l.mode,newLine:s,size:n&&tS(n,"size").value}},htmlBuilder(e,t){let r=eD.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=_(V(e.size,t)))),r},mathmlBuilder(e,t){let r=new ti.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",_(V(e.size,t)))),r}});let tD={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},tF=e=>{let t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new n("Expected a control sequence",e);return t},tV=e=>{let t=e.gullet.popToken();return"="===t.text&&" "===(t=e.gullet.popToken()).text&&(t=e.gullet.popToken()),t},t_=(e,t,r,l)=>{let n=e.gullet.macros.get(r.text);null==n&&(r.noexpand=!0,n={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,n,l)};eY({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(e){let{parser:t,funcName:r}=e;t.consumeSpaces();let l=t.fetch();if(tD[l.text])return("\\global"===r||"\\\\globallong"===r)&&(l.text=tD[l.text]),tS(t.parseFunction(),"internal");throw new n("Invalid token after macro prefix",l)}}),eY({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){let t,{parser:r,funcName:l}=e,s=r.gullet.popToken(),a=s.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(a))throw new n("Expected a control sequence",s);let i=0,o=[[]];for(;"{"!==r.gullet.future().text;)if("#"===(s=r.gullet.popToken()).text){if("{"===r.gullet.future().text){t=r.gullet.future(),o[i].push("{");break}if(s=r.gullet.popToken(),!/^[1-9]$/.test(s.text))throw new n('Invalid argument number "'+s.text+'"');if(parseInt(s.text)!==i+1)throw new n('Argument number "'+s.text+'" out of order');i++,o.push([])}else if("EOF"===s.text)throw new n("Expected a macro definition");else o[i].push(s.text);let{tokens:h}=r.gullet.consumeArg();return t&&h.unshift(t),("\\edef"===l||"\\xdef"===l)&&(h=r.gullet.expandTokens(h)).reverse(),r.gullet.macros.set(a,{tokens:h,numArgs:i,delimiters:o},l===tD[l]),{type:"internal",mode:r.mode}}}),eY({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){let{parser:t,funcName:r}=e,l=tF(t.gullet.popToken());t.gullet.consumeSpaces();let n=tV(t);return t_(t,l,n,"\\\\globallet"===r),{type:"internal",mode:t.mode}}}),eY({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){let{parser:t,funcName:r}=e,l=tF(t.gullet.popToken()),n=t.gullet.popToken(),s=t.gullet.popToken();return t_(t,l,s,"\\\\globalfuture"===r),t.gullet.pushToken(s),t.gullet.pushToken(n),{type:"internal",mode:t.mode}}});let tU=function(e,t,r){let l=I(ea.math[e]&&ea.math[e].replace||e,t,r);if(!l)throw Error("Unsupported symbol "+e+" and font size "+t+".");return l},t$=function(e,t,r,l){let n=r.havingBaseStyle(t),s=eD.makeSpan(l.concat(n.sizingClasses(r)),[e],r),a=n.sizeMultiplier/r.sizeMultiplier;return s.height*=a,s.depth*=a,s.maxFontSize=n.sizeMultiplier,s},tG=function(e,t,r){let l=t.havingBaseStyle(r),n=(1-t.sizeMultiplier/l.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=_(n),e.height-=n,e.depth+=n},tW=function(e,t,r,l,n,s){let a=t$(eD.makeSymbol(e,"Main-Regular",n,l),t,l,s);return r&&tG(a,l,t),a},tX=function(e,t,r,l,n,s){let a=eD.makeSymbol(e,"Size"+t+"-Regular",n,l),i=t$(eD.makeSpan(["delimsizing","size"+t],[a],l),v.TEXT,l,s);return r&&tG(i,l,v.TEXT),i},tY=function(e,t,r){return{type:"elem",elem:eD.makeSpan(["delimsizinginner","Size1-Regular"===t?"delim-size1":"delim-size4"],[eD.makeSpan([],[eD.makeSymbol(e,t,r)])])}},tZ=function(e,t,r){let l=j["Size4-Regular"][e.charCodeAt(0)]?j["Size4-Regular"][e.charCodeAt(0)][4]:j["Size1-Regular"][e.charCodeAt(0)][4],n=new ee([new et("inner",A(e,Math.round(1e3*t)))],{width:_(l),height:_(t),style:"width:"+_(l),viewBox:"0 0 "+1e3*l+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),s=eD.makeSvgSpan([],[n],r);return s.height=t,s.style.height=_(t),s.style.width=_(l),{type:"elem",elem:s}},tK={type:"kern",size:-.008},tQ=["|","\\lvert","\\rvert","\\vert"],tJ=["\\|","\\lVert","\\rVert","\\Vert"],t0=function(e,t,r,l,n,s){let a,i,o,h;let c="",d=0;a=o=h=e,i=null;let u="Size1-Regular";"\\uparrow"===e?o=h="⏐":"\\Uparrow"===e?o=h="‖":"\\downarrow"===e?a=o="⏐":"\\Downarrow"===e?a=o="‖":"\\updownarrow"===e?(a="\\uparrow",o="⏐",h="\\downarrow"):"\\Updownarrow"===e?(a="\\Uparrow",o="‖",h="\\Downarrow"):m.contains(tQ,e)?(o="∣",c="vert",d=333):m.contains(tJ,e)?(o="∥",c="doublevert",d=556):"["===e||"\\lbrack"===e?(a="⎡",o="⎢",h="⎣",u="Size4-Regular",c="lbrack",d=667):"]"===e||"\\rbrack"===e?(a="⎤",o="⎥",h="⎦",u="Size4-Regular",c="rbrack",d=667):"\\lfloor"===e||"⌊"===e?(o=a="⎢",h="⎣",u="Size4-Regular",c="lfloor",d=667):"\\lceil"===e||"⌈"===e?(a="⎡",o=h="⎢",u="Size4-Regular",c="lceil",d=667):"\\rfloor"===e||"⌋"===e?(o=a="⎥",h="⎦",u="Size4-Regular",c="rfloor",d=667):"\\rceil"===e||"⌉"===e?(a="⎤",o=h="⎥",u="Size4-Regular",c="rceil",d=667):"("===e||"\\lparen"===e?(a="⎛",o="⎜",h="⎝",u="Size4-Regular",c="lparen",d=875):")"===e||"\\rparen"===e?(a="⎞",o="⎟",h="⎠",u="Size4-Regular",c="rparen",d=875):"\\{"===e||"\\lbrace"===e?(a="⎧",i="⎨",h="⎩",o="⎪",u="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(a="⎫",i="⎬",h="⎭",o="⎪",u="Size4-Regular"):"\\lgroup"===e||"⟮"===e?(a="⎧",h="⎩",o="⎪",u="Size4-Regular"):"\\rgroup"===e||"⟯"===e?(a="⎫",h="⎭",o="⎪",u="Size4-Regular"):"\\lmoustache"===e||"⎰"===e?(a="⎧",h="⎭",o="⎪",u="Size4-Regular"):("\\rmoustache"===e||"⎱"===e)&&(a="⎫",h="⎩",o="⎪",u="Size4-Regular");let p=tU(a,u,n),g=p.height+p.depth,f=tU(o,u,n),x=f.height+f.depth,b=tU(h,u,n),y=b.height+b.depth,w=0,k=1;if(null!==i){let e=tU(i,u,n);w=e.height+e.depth,k=2}let S=g+y+w,M=Math.max(0,Math.ceil((t-S)/(k*x))),z=S+M*k*x,A=l.fontMetrics().axisHeight;r&&(A*=l.sizeMultiplier);let T=z/2-A,C=[];if(c.length>0){let e=Math.round(1e3*z),t=N(c,Math.round(1e3*(z-g-y))),r=new et(c,t),n=(d/1e3).toFixed(3)+"em",s=(e/1e3).toFixed(3)+"em",a=new ee([r],{width:n,height:s,viewBox:"0 0 "+d+" "+e}),i=eD.makeSvgSpan([],[a],l);i.height=e/1e3,i.style.width=n,i.style.height=s,C.push({type:"elem",elem:i})}else{if(C.push(tY(h,u,n)),C.push(tK),null===i)C.push(tZ(o,z-g-y+.016,l));else{let e=(z-g-y-w)/2+.016;C.push(tZ(o,e,l)),C.push(tK),C.push(tY(i,u,n)),C.push(tK),C.push(tZ(o,e,l))}C.push(tK),C.push(tY(a,u,n))}let j=l.havingBaseStyle(v.TEXT),q=eD.makeVList({positionType:"bottom",positionData:T,children:C},j);return t$(eD.makeSpan(["delimsizing","mult"],[q],j),v.TEXT,l,s)},t1=function(e,t,r,l,n){let s=z(e,l,r),a=new ee([new et(e,s)],{width:"400em",height:_(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return eD.makeSvgSpan(["hide-tail"],[a],n)},t4=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],t5=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],t6=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],t3=[0,1.2,1.8,2.4,3],t7=[{type:"small",style:v.SCRIPTSCRIPT},{type:"small",style:v.SCRIPT},{type:"small",style:v.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],t8=[{type:"small",style:v.SCRIPTSCRIPT},{type:"small",style:v.SCRIPT},{type:"small",style:v.TEXT},{type:"stack"}],t2=[{type:"small",style:v.SCRIPTSCRIPT},{type:"small",style:v.SCRIPT},{type:"small",style:v.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],t9=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw Error("Add support for delim type '"+e.type+"' here.")},re=function(e,t,r,l){let n=Math.min(2,3-l.style.size);for(let s=n;s<r.length&&"stack"!==r[s].type;s++){let n=tU(e,t9(r[s]),"math"),a=n.height+n.depth;if("small"===r[s].type&&(a*=l.havingBaseStyle(r[s].style).sizeMultiplier),a>t)return r[s]}return r[r.length-1]},rt=function(e,t,r,l,n,s){let a;"<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),a=m.contains(t6,e)?t7:m.contains(t4,e)?t2:t8;let i=re(e,t,a,l);return"small"===i.type?tW(e,i.style,r,l,n,s):"large"===i.type?tX(e,i.size,r,l,n,s):t0(e,t,r,l,n,s)};var rr={sqrtImage:function(e,t){let r,l;let n=t.havingBaseSizing(),s=re("\\surd",e*n.sizeMultiplier,t2,n),a=n.sizeMultiplier,i=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),o=0,h=0,m=0;return"small"===s.type?(m=1e3+1e3*i+80,e<1?a=1:e<1.4&&(a=.7),o=(1+i+.08)/a,h=(1+i)/a,(r=t1("sqrtMain",o,m,i,t)).style.minWidth="0.853em",l=.833/a):"large"===s.type?(m=1080*t3[s.size],h=(t3[s.size]+i)/a,o=(t3[s.size]+i+.08)/a,(r=t1("sqrtSize"+s.size,o,m,i,t)).style.minWidth="1.02em",l=1/a):(o=e+i+.08,h=e+i,(r=t1("sqrtTall",o,m=Math.floor(1e3*e+i)+80,i,t)).style.minWidth="0.742em",l=1.056),r.height=h,r.style.height=_(o),{span:r,advanceWidth:l,ruleWidth:(t.fontMetrics().sqrtRuleThickness+i)*a}},sizedDelim:function(e,t,r,l,s){if("<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),m.contains(t4,e)||m.contains(t6,e))return tX(e,t,!1,r,l,s);if(m.contains(t5,e))return t0(e,t3[t],!1,r,l,s);throw new n("Illegal delimiter: '"+e+"'")},sizeToMaxHeight:t3,customSizedDelim:rt,leftRightDelim:function(e,t,r,l,n,s){let a=l.fontMetrics().axisHeight*l.sizeMultiplier,i=5/l.fontMetrics().ptPerEm,o=Math.max(t-a,r+a);return rt(e,Math.max(o/500*901,2*o-i),!0,l,n,s)}};let rl={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},rn=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function rs(e,t){let r=tz(e);if(r&&m.contains(rn,r.text))return r;if(r)throw new n("Invalid delimiter '"+r.text+"' after '"+t.funcName+"'",e);throw new n("Invalid delimiter type '"+e.type+"'",e)}function ra(e){if(!e.body)throw Error("Bug: The leftright ParseNode wasn't fully parsed.")}eY({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(e,t)=>{let r=rs(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:rl[e.funcName].size,mclass:rl[e.funcName].mclass,delim:r.text}},htmlBuilder:(e,t)=>"."===e.delim?eD.makeSpan([e.mclass]):rr.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass]),mathmlBuilder:e=>{let t=[];"."!==e.delim&&t.push(to(e.delim,e.mode));let r=new ti.MathNode("mo",t);"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");let l=_(rr.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",l),r.setAttribute("maxsize",l),r}}),eY({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let r=e.parser.gullet.macros.get("\\current@color");if(r&&"string"!=typeof r)throw new n("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:rs(t[0],e).text,color:r}}}),eY({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let r=rs(t[0],e),l=e.parser;++l.leftrightDepth;let n=l.parseExpression(!1);--l.leftrightDepth,l.expect("\\right",!1);let s=tS(l.parseFunction(),"leftright-right");return{type:"leftright",mode:l.mode,body:n,left:r.text,right:s.delim,rightColor:s.color}},htmlBuilder:(e,t)=>{let r,l;ra(e);let n=e6(e.body,t,!0,["mopen","mclose"]),s=0,a=0,i=!1;for(let e=0;e<n.length;e++)n[e].isMiddle?i=!0:(s=Math.max(n[e].height,s),a=Math.max(n[e].depth,a));if(s*=t.sizeMultiplier,a*=t.sizeMultiplier,r="."===e.left?e9(t,["mopen"]):rr.leftRightDelim(e.left,s,a,t,e.mode,["mopen"]),n.unshift(r),i)for(let t=1;t<n.length;t++){let r=n[t].isMiddle;r&&(n[t]=rr.leftRightDelim(r.delim,s,a,r.options,e.mode,[]))}if("."===e.right)l=e9(t,["mclose"]);else{let r=e.rightColor?t.withColor(e.rightColor):t;l=rr.leftRightDelim(e.right,s,a,r,e.mode,["mclose"])}return n.push(l),eD.makeSpan(["minner"],n,t)},mathmlBuilder:(e,t)=>{ra(e);let r=td(e.body,t);if("."!==e.left){let t=new ti.MathNode("mo",[to(e.left,e.mode)]);t.setAttribute("fence","true"),r.unshift(t)}if("."!==e.right){let t=new ti.MathNode("mo",[to(e.right,e.mode)]);t.setAttribute("fence","true"),e.rightColor&&t.setAttribute("mathcolor",e.rightColor),r.push(t)}return th(r)}}),eY({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let r=rs(t[0],e);if(!e.parser.leftrightDepth)throw new n("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:(e,t)=>{let r;if("."===e.delim)r=e9(t,[]);else{r=rr.sizedDelim(e.delim,1,t,e.mode,[]);let l={delim:e.delim,options:t};r.isMiddle=l}return r},mathmlBuilder:(e,t)=>{let r="\\vert"===e.delim||"|"===e.delim?to("|","text"):to(e.delim,e.mode),l=new ti.MathNode("mo",[r]);return l.setAttribute("fence","true"),l.setAttribute("lspace","0.05em"),l.setAttribute("rspace","0.05em"),l}});let ri=(e,t)=>{let r,l;let n=eD.wrapFragment(te(e.body,t),t),s=e.label.slice(1),a=t.sizeMultiplier,i=0,o=m.isCharacterBox(e.body);if("sout"===s)(r=eD.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/a,i=-.5*t.fontMetrics().xHeight;else if("phase"===s){let e=V({number:.6,unit:"pt"},t),l=V({number:.35,unit:"ex"},t);a/=t.havingBaseSizing().sizeMultiplier;let s=n.height+n.depth+e+l;n.style.paddingLeft=_(s/2+e);let o=Math.floor(1e3*s*a),h=new ee([new et("phase","M400000 "+o+" H0 L"+o/2+" 0 l65 45 L145 "+(o-80)+" H400000z")],{width:"400em",height:_(o/1e3),viewBox:"0 0 400000 "+o,preserveAspectRatio:"xMinYMin slice"});(r=eD.makeSvgSpan(["hide-tail"],[h],t)).style.height=_(s),i=n.depth+e+l}else{/cancel/.test(s)?o||n.classes.push("cancel-pad"):"angl"===s?n.classes.push("anglpad"):n.classes.push("boxpad");let l=0,a=0,h=0;/box/.test(s)?(h=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),a=l=t.fontMetrics().fboxsep+("colorbox"===s?0:h)):"angl"===s?(l=4*(h=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness)),a=Math.max(0,.25-n.depth)):a=l=.2*!!o,r=tk.encloseSpan(n,s,l,a,t),/fbox|boxed|fcolorbox/.test(s)?(r.style.borderStyle="solid",r.style.borderWidth=_(h)):"angl"===s&&.049!==h&&(r.style.borderTopWidth=_(h),r.style.borderRightWidth=_(h)),i=n.depth+a,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}if(e.backgroundColor)l=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:i},{type:"elem",elem:n,shift:0}]},t);else{let e=/cancel|phase/.test(s)?["svg-align"]:[];l=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:n,shift:0},{type:"elem",elem:r,shift:i,wrapperClasses:e}]},t)}return(/cancel/.test(s)&&(l.height=n.height,l.depth=n.depth),/cancel/.test(s)&&!o)?eD.makeSpan(["mord","cancel-lap"],[l],t):eD.makeSpan(["mord"],[l],t)},ro=(e,t)=>{let r=0,l=new ti.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[tp(e.body,t)]);switch(e.label){case"\\cancel":l.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":l.setAttribute("notation","downdiagonalstrike");break;case"\\phase":l.setAttribute("notation","phasorangle");break;case"\\sout":l.setAttribute("notation","horizontalstrike");break;case"\\fbox":l.setAttribute("notation","box");break;case"\\angl":l.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,l.setAttribute("width","+"+2*r+"pt"),l.setAttribute("height","+"+2*r+"pt"),l.setAttribute("lspace",r+"pt"),l.setAttribute("voffset",r+"pt"),"\\fcolorbox"===e.label){let r=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);l.setAttribute("style","border: "+r+"em solid "+String(e.borderColor))}break;case"\\xcancel":l.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&l.setAttribute("mathbackground",e.backgroundColor),l};eY({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(e,t,r){let{parser:l,funcName:n}=e,s=tS(t[0],"color-token").color,a=t[1];return{type:"enclose",mode:l.mode,label:n,backgroundColor:s,body:a}},htmlBuilder:ri,mathmlBuilder:ro}),eY({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(e,t,r){let{parser:l,funcName:n}=e,s=tS(t[0],"color-token").color,a=tS(t[1],"color-token").color,i=t[2];return{type:"enclose",mode:l.mode,label:n,backgroundColor:a,borderColor:s,body:i}},htmlBuilder:ri,mathmlBuilder:ro}),eY({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(e,t){let{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),eY({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(e,t){let{parser:r,funcName:l}=e,n=t[0];return{type:"enclose",mode:r.mode,label:l,body:n}},htmlBuilder:ri,mathmlBuilder:ro}),eY({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(e,t){let{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});let rh={};function rm(e){let{type:t,names:r,props:l,handler:n,htmlBuilder:s,mathmlBuilder:a}=e,i={type:t,numArgs:l.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:n};for(let e=0;e<r.length;++e)rh[r[e]]=i;s&&(eW[t]=s),a&&(eX[t]=a)}let rc={};class rd{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?e&&e.loc&&t.loc&&e.loc.lexer===t.loc.lexer?new rd(e.loc.lexer,e.loc.start,t.loc.end):null:e&&e.loc}}class ru{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new ru(t,rd.range(this,e))}}function rp(e){let t=[];e.consumeSpaces();let r=e.fetch().text;for("\\relax"===r&&(e.consume(),e.consumeSpaces(),r=e.fetch().text);"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.fetch().text;return t}let rg=e=>{if(!e.parser.settings.displayMode)throw new n("{"+e.envName+"} can be used only in display mode.")};function rf(e){if(-1===e.indexOf("ed"))return -1===e.indexOf("*")}function rx(e,t,r){let{hskipBeforeAndAfter:l,addJot:s,cols:a,arraystretch:i,colSeparationType:o,autoTag:h,singleRow:m,emptySingleRow:c,maxNumCols:d,leqno:u}=t;if(e.gullet.beginGroup(),m||e.gullet.macros.set("\\cr","\\\\\\relax"),!i){let t=e.gullet.expandMacroAsText("\\arraystretch");if(null==t)i=1;else if(!(i=parseFloat(t))||i<0)throw new n("Invalid \\arraystretch: "+t)}e.gullet.beginGroup();let p=[],g=[p],f=[],x=[],b=null!=h?[]:void 0;function y(){h&&e.gullet.macros.set("\\@eqnsw","1",!0)}function w(){b&&(e.gullet.macros.get("\\df@tag")?(b.push(e.subparse([new ru("\\df@tag")])),e.gullet.macros.set("\\df@tag",void 0,!0)):b.push(!!h&&"1"===e.gullet.macros.get("\\@eqnsw")))}for(y(),x.push(rp(e));;){let t=e.parseExpression(!1,m?"\\end":"\\\\");e.gullet.endGroup(),e.gullet.beginGroup(),t={type:"ordgroup",mode:e.mode,body:t},r&&(t={type:"styling",mode:e.mode,style:r,body:[t]}),p.push(t);let l=e.fetch().text;if("&"===l){if(d&&p.length===d){if(m||o)throw new n("Too many tab characters: &",e.nextToken);e.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}e.consume()}else if("\\end"===l){w(),1===p.length&&"styling"===t.type&&0===t.body[0].body.length&&(g.length>1||!c)&&g.pop(),x.length<g.length+1&&x.push([]);break}else if("\\\\"===l){let t;e.consume()," "!==e.gullet.future().text&&(t=e.parseSizeGroup(!0)),f.push(t?t.value:null),w(),x.push(rp(e)),p=[],g.push(p),y()}else throw new n("Expected & or \\\\ or \\cr or \\end",e.nextToken)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:s,arraystretch:i,body:g,cols:a,rowGaps:f,hskipBeforeAndAfter:l,hLinesBeforeRow:x,colSeparationType:o,tags:b,leqno:u}}function rb(e){return"d"===e.slice(0,1)?"display":"text"}let ry=function(e,t){let r,l,s,a;let i=e.body.length,o=e.hLinesBeforeRow,h=0,c=Array(i),d=[],u=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),p=1/t.fontMetrics().ptPerEm,g=5*p;e.colSeparationType&&"small"===e.colSeparationType&&(g=.2778*(t.havingStyle(v.SCRIPT).sizeMultiplier/t.sizeMultiplier));let f="CD"===e.colSeparationType?V({number:3,unit:"ex"},t):12*p,x=3*p,b=e.arraystretch*f,y=.7*b,w=.3*b,k=0;function S(e){for(let t=0;t<e.length;++t)t>0&&(k+=.25),d.push({pos:k,isDashed:e[t]})}for(S(o[0]),r=0;r<e.body.length;++r){let n=e.body[r],s=y,a=w;h<n.length&&(h=n.length);let i=Array(n.length);for(l=0;l<n.length;++l){let e=te(n[l],t);a<e.depth&&(a=e.depth),s<e.height&&(s=e.height),i[l]=e}let m=e.rowGaps[r],d=0;m&&(d=V(m,t))>0&&(a<(d+=w)&&(a=d),d=0),e.addJot&&(a+=x),i.height=s,i.depth=a,i.pos=k+=s,k+=a+d,c[r]=i,S(o[r+1])}let M=k/2+t.fontMetrics().axisHeight,z=e.cols||[],A=[],T=[];if(e.tags&&e.tags.some(e=>e))for(r=0;r<i;++r){let l;let n=c[r],s=n.pos-M,a=e.tags[r];(l=!0===a?eD.makeSpan(["eqn-num"],[],t):!1===a?eD.makeSpan([],[],t):eD.makeSpan([],e6(a,t,!0),t)).depth=n.depth,l.height=n.height,T.push({type:"elem",elem:l,shift:s})}for(l=0,a=0;l<h||a<z.length;++l,++a){let o,d=z[a]||{},p=!0;for(;"separator"===d.type;){if(p||((s=eD.makeSpan(["arraycolsep"],[])).style.width=_(t.fontMetrics().doubleRuleSep),A.push(s)),"|"===d.separator||":"===d.separator){let e="|"===d.separator?"solid":"dashed",r=eD.makeSpan(["vertical-separator"],[],t);r.style.height=_(k),r.style.borderRightWidth=_(u),r.style.borderRightStyle=e,r.style.margin="0 "+_(-u/2);let l=k-M;l&&(r.style.verticalAlign=_(-l)),A.push(r)}else throw new n("Invalid separator type: "+d.separator);d=z[++a]||{},p=!1}if(l>=h)continue;(l>0||e.hskipBeforeAndAfter)&&0!==(o=m.deflt(d.pregap,g))&&((s=eD.makeSpan(["arraycolsep"],[])).style.width=_(o),A.push(s));let f=[];for(r=0;r<i;++r){let e=c[r],t=e[l];if(!t)continue;let n=e.pos-M;t.depth=e.depth,t.height=e.height,f.push({type:"elem",elem:t,shift:n})}f=eD.makeVList({positionType:"individualShift",children:f},t),f=eD.makeSpan(["col-align-"+(d.align||"c")],[f]),A.push(f),(l<h-1||e.hskipBeforeAndAfter)&&0!==(o=m.deflt(d.postgap,g))&&((s=eD.makeSpan(["arraycolsep"],[])).style.width=_(o),A.push(s))}if(c=eD.makeSpan(["mtable"],A),d.length>0){let e=eD.makeLineSpan("hline",t,u),r=eD.makeLineSpan("hdashline",t,u),l=[{type:"elem",elem:c,shift:0}];for(;d.length>0;){let t=d.pop(),n=t.pos-M;t.isDashed?l.push({type:"elem",elem:r,shift:n}):l.push({type:"elem",elem:e,shift:n})}c=eD.makeVList({positionType:"individualShift",children:l},t)}if(0===T.length)return eD.makeSpan(["mord"],[c],t);{let e=eD.makeVList({positionType:"individualShift",children:T},t);return e=eD.makeSpan(["tag"],[e],t),eD.makeFragment([c,e])}},rw={c:"center ",l:"left ",r:"right "},rv=function(e,t){let r=[],l=new ti.MathNode("mtd",[],["mtr-glue"]),n=new ti.MathNode("mtd",[],["mml-eqn-num"]);for(let s=0;s<e.body.length;s++){let a=e.body[s],i=[];for(let e=0;e<a.length;e++)i.push(new ti.MathNode("mtd",[tp(a[e],t)]));e.tags&&e.tags[s]&&(i.unshift(l),i.push(l),e.leqno?i.unshift(n):i.push(n)),r.push(new ti.MathNode("mtr",i))}let s=new ti.MathNode("mtable",r),a=.5===e.arraystretch?.1:.16+e.arraystretch-1+.09*!!e.addJot;s.setAttribute("rowspacing",_(a));let i="",o="";if(e.cols&&e.cols.length>0){let t=e.cols,r="",l=!1,n=0,a=t.length;"separator"===t[0].type&&(i+="top ",n=1),"separator"===t[t.length-1].type&&(i+="bottom ",a-=1);for(let e=n;e<a;e++)"align"===t[e].type?(o+=rw[t[e].align],l&&(r+="none "),l=!0):"separator"===t[e].type&&l&&(r+="|"===t[e].separator?"solid ":"dashed ",l=!1);s.setAttribute("columnalign",o.trim()),/[sd]/.test(r)&&s.setAttribute("columnlines",r.trim())}if("align"===e.colSeparationType){let t=e.cols||[],r="";for(let e=1;e<t.length;e++)r+=e%2?"0em ":"1em ";s.setAttribute("columnspacing",r.trim())}else"alignat"===e.colSeparationType||"gather"===e.colSeparationType?s.setAttribute("columnspacing","0em"):"small"===e.colSeparationType?s.setAttribute("columnspacing","0.2778em"):"CD"===e.colSeparationType?s.setAttribute("columnspacing","0.5em"):s.setAttribute("columnspacing","1em");let h="",m=e.hLinesBeforeRow;i+=(m[0].length>0?"left ":"")+(m[m.length-1].length>0?"right ":"");for(let e=1;e<m.length-1;e++)h+=0===m[e].length?"none ":m[e][0]?"dashed ":"solid ";return/[sd]/.test(h)&&s.setAttribute("rowlines",h.trim()),""!==i&&(s=new ti.MathNode("menclose",[s])).setAttribute("notation",i.trim()),e.arraystretch&&e.arraystretch<1&&(s=new ti.MathNode("mstyle",[s])).setAttribute("scriptlevel","1"),s},rk=function(e,t){let r;-1===e.envName.indexOf("ed")&&rg(e);let l=[],s=e.envName.indexOf("at")>-1?"alignat":"align",a="split"===e.envName,i=rx(e.parser,{cols:l,addJot:!0,autoTag:a?void 0:rf(e.envName),emptySingleRow:!0,colSeparationType:s,maxNumCols:a?2:void 0,leqno:e.parser.settings.leqno},"display"),o=0,h={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&"ordgroup"===t[0].type){let e="";for(let r=0;r<t[0].body.length;r++)e+=tS(t[0].body[r],"textord").text;o=2*(r=Number(e))}let m=!o;i.body.forEach(function(e){for(let t=1;t<e.length;t+=2){let r=tS(e[t],"styling");tS(r.body[0],"ordgroup").body.unshift(h)}if(m)o<e.length&&(o=e.length);else{let t=e.length/2;if(r<t)throw new n("Too many math in a row: expected "+r+", but got "+t,e[0])}});for(let e=0;e<o;++e){let t="r",r=0;e%2==1?t="l":e>0&&m&&(r=1),l[e]={type:"align",align:t,pregap:r,postgap:0}}return i.colSeparationType=m?"align":"alignat",i};rm({type:"array",names:["array","darray"],props:{numArgs:1},handler(e,t){let r=(tz(t[0])?[t[0]]:tS(t[0],"ordgroup").body).map(function(e){let t=tM(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new n("Unknown column alignment: "+t,e)}),l={cols:r,hskipBeforeAndAfter:!0,maxNumCols:r.length};return rx(e.parser,l,rb(e.envName))},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(e){let t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],r="c",l={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if("*"===e.envName.charAt(e.envName.length-1)){let t=e.parser;if(t.consumeSpaces(),"["===t.fetch().text){if(t.consume(),t.consumeSpaces(),r=t.fetch().text,-1==="lcr".indexOf(r))throw new n("Expected l or c or r",t.nextToken);t.consume(),t.consumeSpaces(),t.expect("]"),t.consume(),l.cols=[{type:"align",align:r}]}}let s=rx(e.parser,l,rb(e.envName)),a=Math.max(0,...s.body.map(e=>e.length));return s.cols=Array(a).fill({type:"align",align:r}),t?{type:"leftright",mode:e.mode,body:[s],left:t[0],right:t[1],rightColor:void 0}:s},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(e){let t=rx(e.parser,{arraystretch:.5},"script");return t.colSeparationType="small",t},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["subarray"],props:{numArgs:1},handler(e,t){let r=(tz(t[0])?[t[0]]:tS(t[0],"ordgroup").body).map(function(e){let t=tM(e).text;if(-1!=="lc".indexOf(t))return{type:"align",align:t};throw new n("Unknown column alignment: "+t,e)});if(r.length>1)throw new n("{subarray} can contain only one column");let l={cols:r,hskipBeforeAndAfter:!1,arraystretch:.5};if((l=rx(e.parser,l,"script")).body.length>0&&l.body[0].length>1)throw new n("{subarray} can contain only one column");return l},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(e){let t=rx(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},rb(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:rk,htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(e){m.contains(["gather","gather*"],e.envName)&&rg(e);let t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:rf(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return rx(e.parser,t,"display")},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:rk,htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(e){rg(e);let t={autoTag:rf(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return rx(e.parser,t,"display")},htmlBuilder:ry,mathmlBuilder:rv}),rm({type:"array",names:["CD"],props:{numArgs:0},handler:e=>(rg(e),function(e){let t=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){t.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();let r=e.fetch().text;if("&"===r||"\\\\"===r)e.consume();else if("\\end"===r){0===t[t.length-1].length&&t.pop();break}else throw new n("Expected \\\\ or \\cr or \\end",e.nextToken)}let r=[],l=[r];for(let s=0;s<t.length;s++){let a=t[s],i=tO();for(let t=0;t<a.length;t++)if(tH(a[t])){r.push(i);let l=tM(a[t+=1]).text,s=[,,];if(s[0]={type:"ordgroup",mode:"math",body:[]},s[1]={type:"ordgroup",mode:"math",body:[]},"=|.".indexOf(l)>-1);else if("<>AV".indexOf(l)>-1)for(let e=0;e<2;e++){let r=!0;for(let i=t+1;i<a.length;i++){if(tR(a[i],l)){r=!1,t=i;break}if(tH(a[i]))throw new n("Missing a "+l+" character to complete a CD arrow.",a[i]);s[e].body.push(a[i])}if(r)throw new n("Missing a "+l+" character to complete a CD arrow.",a[t])}else throw new n('Expected one of "<>AV=|." after @',a[t]);let o={type:"styling",body:[function(e,t,r){let l=tE[e];switch(l){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(l,[t[0]],[t[1]]);case"\\uparrow":case"\\downarrow":{let e=r.callFunction("\\\\cdleft",[t[0]],[]),n=r.callFunction("\\Big",[{type:"atom",text:l,mode:"math",family:"rel"}],[]),s=r.callFunction("\\\\cdright",[t[1]],[]);return r.callFunction("\\\\cdparent",[{type:"ordgroup",mode:"math",body:[e,n,s]}],[])}case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}(l,s,e)],mode:"math",style:"display"};r.push(o),i=tO()}else i.body.push(a[t]);s%2==0?r.push(i):r.shift(),r=[],l.push(r)}e.gullet.endGroup(),e.gullet.endGroup();let s=Array(l[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:l,arraystretch:1,addJot:!0,rowGaps:[null],cols:s,colSeparationType:"CD",hLinesBeforeRow:Array(l.length+1).fill([])}}(e.parser)),htmlBuilder:ry,mathmlBuilder:rv}),rc["\\nonumber"]="\\gdef\\@eqnsw{0}",rc["\\notag"]="\\nonumber",eY({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(e,t){throw new n(e.funcName+" valid only within array environment")}}),eY({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(e,t){let{parser:r,funcName:l}=e,s=t[0];if("ordgroup"!==s.type)throw new n("Invalid environment name",s);let a="";for(let e=0;e<s.body.length;++e)a+=tS(s.body[e],"textord").text;if("\\begin"===l){if(!rh.hasOwnProperty(a))throw new n("No such environment: "+a,s);let e=rh[a],{args:t,optArgs:l}=r.parseArguments("\\begin{"+a+"}",e),i={mode:r.mode,envName:a,parser:r},o=e.handler(i,t,l);r.expect("\\end",!1);let h=r.nextToken,m=tS(r.parseFunction(),"environment");if(m.name!==a)throw new n("Mismatch: \\begin{"+a+"} matched by \\end{"+m.name+"}",h);return o}return{type:"environment",mode:r.mode,name:a,nameGroup:s}}});let rS=(e,t)=>{let r=e.font,l=t.withFont(r);return te(e.body,l)},rM=(e,t)=>{let r=e.font,l=t.withFont(r);return tp(e.body,l)},rz={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};eY({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=eK(t[0]),s=l;return s in rz&&(s=rz[s]),{type:"font",mode:r.mode,font:s.slice(1),body:n}},htmlBuilder:rS,mathmlBuilder:rM}),eY({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(e,t)=>{let{parser:r}=e,l=t[0],n=m.isCharacterBox(l);return{type:"mclass",mode:r.mode,mclass:tI(l),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:l}],isCharacterBox:n}}}),eY({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{let{parser:r,funcName:l,breakOnTokenText:n}=e,{mode:s}=r,a=r.parseExpression(!0,n);return{type:"font",mode:s,font:"math"+l.slice(1),body:{type:"ordgroup",mode:r.mode,body:a}}},htmlBuilder:rS,mathmlBuilder:rM});let rA=(e,t)=>{let r=t;return"display"===e?r=r.id>=v.SCRIPT.id?r.text():v.DISPLAY:"text"===e&&r.size===v.DISPLAY.size?r=v.TEXT:"script"===e?r=v.SCRIPT:"scriptscript"===e&&(r=v.SCRIPTSCRIPT),r},rT=(e,t)=>{let r,l,n,s,a,i,o,h,m,c,d;let u=rA(e.size,t.style),p=u.fracNum(),g=u.fracDen();r=t.havingStyle(p);let f=te(e.numer,r,t);if(e.continued){let e=8.5/t.fontMetrics().ptPerEm,r=3.5/t.fontMetrics().ptPerEm;f.height=f.height<e?e:f.height,f.depth=f.depth<r?r:f.depth}r=t.havingStyle(g);let x=te(e.denom,r,t);if(e.hasBarLine?(e.barSize?(n=V(e.barSize,t),l=eD.makeLineSpan("frac-line",t,n)):l=eD.makeLineSpan("frac-line",t),n=l.height,s=l.height):(l=null,n=0,s=t.fontMetrics().defaultRuleThickness),u.size===v.DISPLAY.size||"display"===e.size?(a=t.fontMetrics().num1,i=n>0?3*s:7*s,o=t.fontMetrics().denom1):(n>0?(a=t.fontMetrics().num2,i=s):(a=t.fontMetrics().num3,i=3*s),o=t.fontMetrics().denom2),l){let e=t.fontMetrics().axisHeight;a-f.depth-(e+.5*n)<i&&(a+=i-(a-f.depth-(e+.5*n))),e-.5*n-(x.height-o)<i&&(o+=i-(e-.5*n-(x.height-o)));let r=-(e-.5*n);h=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:x,shift:o},{type:"elem",elem:l,shift:r},{type:"elem",elem:f,shift:-a}]},t)}else{let e=a-f.depth-(x.height-o);e<i&&(a+=.5*(i-e),o+=.5*(i-e)),h=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:x,shift:o},{type:"elem",elem:f,shift:-a}]},t)}return r=t.havingStyle(u),h.height*=r.sizeMultiplier/t.sizeMultiplier,h.depth*=r.sizeMultiplier/t.sizeMultiplier,m=u.size===v.DISPLAY.size?t.fontMetrics().delim1:u.size===v.SCRIPTSCRIPT.size?t.havingStyle(v.SCRIPT).fontMetrics().delim2:t.fontMetrics().delim2,c=null==e.leftDelim?e9(t,["mopen"]):rr.customSizedDelim(e.leftDelim,m,!0,t.havingStyle(u),e.mode,["mopen"]),d=e.continued?eD.makeSpan([]):null==e.rightDelim?e9(t,["mclose"]):rr.customSizedDelim(e.rightDelim,m,!0,t.havingStyle(u),e.mode,["mclose"]),eD.makeSpan(["mord"].concat(r.sizingClasses(t)),[c,eD.makeSpan(["mfrac"],[h]),d],t)},rN=(e,t)=>{let r=new ti.MathNode("mfrac",[tp(e.numer,t),tp(e.denom,t)]);if(e.hasBarLine){if(e.barSize){let l=V(e.barSize,t);r.setAttribute("linethickness",_(l))}}else r.setAttribute("linethickness","0px");let l=rA(e.size,t.style);if(l.size!==t.style.size){r=new ti.MathNode("mstyle",[r]);let e=l.size===v.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",e),r.setAttribute("scriptlevel","0")}if(null!=e.leftDelim||null!=e.rightDelim){let t=[];if(null!=e.leftDelim){let r=new ti.MathNode("mo",[new ti.TextNode(e.leftDelim.replace("\\",""))]);r.setAttribute("fence","true"),t.push(r)}if(t.push(r),null!=e.rightDelim){let r=new ti.MathNode("mo",[new ti.TextNode(e.rightDelim.replace("\\",""))]);r.setAttribute("fence","true"),t.push(r)}return th(t)}return r};eY({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(e,t)=>{let r,{parser:l,funcName:n}=e,s=t[0],a=t[1],i=null,o=null,h="auto";switch(n){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,i="(",o=")";break;case"\\\\bracefrac":r=!1,i="\\{",o="\\}";break;case"\\\\brackfrac":r=!1,i="[",o="]";break;default:throw Error("Unrecognized genfrac command")}switch(n){case"\\dfrac":case"\\dbinom":h="display";break;case"\\tfrac":case"\\tbinom":h="text"}return{type:"genfrac",mode:l.mode,continued:!1,numer:s,denom:a,hasBarLine:r,leftDelim:i,rightDelim:o,size:h,barSize:null}},htmlBuilder:rT,mathmlBuilder:rN}),eY({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=t[0],s=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:n,denom:s,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),eY({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(e){let t,{parser:r,funcName:l,token:n}=e;switch(l){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:n}}});let rC=["display","text","script","scriptscript"],rj=function(e){let t=null;return e.length>0&&(t="."===(t=e)?null:t),t};eY({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(e,t){let r,{parser:l}=e,n=t[4],s=t[5],a=eK(t[0]),i="atom"===a.type&&"open"===a.family?rj(a.text):null,o=eK(t[1]),h="atom"===o.type&&"close"===o.family?rj(o.text):null,m=tS(t[2],"size"),c=null;r=!!m.isBlank||(c=m.value).number>0;let d="auto",u=t[3];return"ordgroup"===u.type?u.body.length>0&&(d=rC[Number(tS(u.body[0],"textord").text)]):d=rC[Number((u=tS(u,"textord")).text)],{type:"genfrac",mode:l.mode,numer:n,denom:s,continued:!1,hasBarLine:r,barSize:c,leftDelim:i,rightDelim:h,size:d}},htmlBuilder:rT,mathmlBuilder:rN}),eY({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(e,t){let{parser:r,funcName:l,token:n}=e;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:tS(t[0],"size").value,token:n}}}),eY({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=t[0],s=h(tS(t[1],"infix").size),a=t[2],i=s.number>0;return{type:"genfrac",mode:r.mode,numer:n,denom:a,continued:!1,hasBarLine:i,barSize:s,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:rT,mathmlBuilder:rN});let rq=(e,t)=>{let r,l,n;let s=t.style;"supsub"===e.type?(r=e.sup?te(e.sup,t.havingStyle(s.sup()),t):te(e.sub,t.havingStyle(s.sub()),t),l=tS(e.base,"horizBrace")):l=tS(e,"horizBrace");let a=te(l.base,t.havingBaseStyle(v.DISPLAY)),i=tk.svgSpan(l,t);if(l.isOver?(n=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"kern",size:.1},{type:"elem",elem:i}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(n=eD.makeVList({positionType:"bottom",positionData:a.depth+.1+i.height,children:[{type:"elem",elem:i},{type:"kern",size:.1},{type:"elem",elem:a}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){let e=eD.makeSpan(["mord",l.isOver?"mover":"munder"],[n],t);n=l.isOver?eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:e},{type:"kern",size:.2},{type:"elem",elem:r}]},t):eD.makeVList({positionType:"bottom",positionData:e.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:e}]},t)}return eD.makeSpan(["mord",l.isOver?"mover":"munder"],[n],t)};eY({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(e,t){let{parser:r,funcName:l}=e;return{type:"horizBrace",mode:r.mode,label:l,isOver:/^\\over/.test(l),base:t[0]}},htmlBuilder:rq,mathmlBuilder:(e,t)=>{let r=tk.mathMLnode(e.label);return new ti.MathNode(e.isOver?"mover":"munder",[tp(e.base,t),r])}}),eY({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,l=t[1],n=tS(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:n})?{type:"href",mode:r.mode,href:n,body:eQ(l)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(e,t)=>{let r=e6(e.body,t,!1);return eD.makeAnchor(e.href,[],r,t)},mathmlBuilder:(e,t)=>{let r=tu(e.body,t);return r instanceof tn||(r=new tn("mrow",[r])),r.setAttribute("href",e.href),r}}),eY({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,l=tS(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:l}))return r.formatUnsupportedCmd("\\url");let n=[];for(let e=0;e<l.length;e++){let t=l[e];"~"===t&&(t="\\textasciitilde"),n.push({type:"textord",mode:"text",text:t})}let s={type:"text",mode:r.mode,font:"\\texttt",body:n};return{type:"href",mode:r.mode,href:l,body:eQ(s)}}}),eY({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(e,t){let{parser:r}=e;return{type:"hbox",mode:r.mode,body:eQ(t[0])}},htmlBuilder(e,t){let r=e6(e.body,t,!1);return eD.makeFragment(r)},mathmlBuilder:(e,t)=>new ti.MathNode("mrow",td(e.body,t))}),eY({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(e,t)=>{let r,{parser:l,funcName:s,token:a}=e,i=tS(t[0],"raw").string,o=t[1];l.settings.strict&&l.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");let h={};switch(s){case"\\htmlClass":h.class=i,r={command:"\\htmlClass",class:i};break;case"\\htmlId":h.id=i,r={command:"\\htmlId",id:i};break;case"\\htmlStyle":h.style=i,r={command:"\\htmlStyle",style:i};break;case"\\htmlData":{let e=i.split(",");for(let t=0;t<e.length;t++){let r=e[t].split("=");if(2!==r.length)throw new n("Error parsing key-value for \\htmlData");h["data-"+r[0].trim()]=r[1].trim()}r={command:"\\htmlData",attributes:h};break}default:throw Error("Unrecognized html command")}return l.settings.isTrusted(r)?{type:"html",mode:l.mode,attributes:h,body:eQ(o)}:l.formatUnsupportedCmd(s)},htmlBuilder:(e,t)=>{let r=e6(e.body,t,!1),l=["enclosing"];e.attributes.class&&l.push(...e.attributes.class.trim().split(/\s+/));let n=eD.makeSpan(l,r,t);for(let t in e.attributes)"class"!==t&&e.attributes.hasOwnProperty(t)&&n.setAttribute(t,e.attributes[t]);return n},mathmlBuilder:(e,t)=>tu(e.body,t)}),eY({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e;return{type:"htmlmathml",mode:r.mode,html:eQ(t[0]),mathml:eQ(t[1])}},htmlBuilder:(e,t)=>{let r=e6(e.html,t,!1);return eD.makeFragment(r)},mathmlBuilder:(e,t)=>tu(e.mathml,t)});let rB=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};{let t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new n("Invalid size: '"+e+"' in \\includegraphics");let r={number:+(t[1]+t[2]),unit:t[3]};if(!F(r))throw new n("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r}};eY({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(e,t,r)=>{let{parser:l}=e,s={number:0,unit:"em"},a={number:.9,unit:"em"},i={number:0,unit:"em"},o="";if(r[0]){let e=tS(r[0],"raw").string.split(",");for(let t=0;t<e.length;t++){let r=e[t].split("=");if(2===r.length){let e=r[1].trim();switch(r[0].trim()){case"alt":o=e;break;case"width":s=rB(e);break;case"height":a=rB(e);break;case"totalheight":i=rB(e);break;default:throw new n("Invalid key: '"+r[0]+"' in \\includegraphics.")}}}}let h=tS(t[0],"url").url;return(""===o&&(o=(o=(o=h).replace(/^.*[\\/]/,"")).substring(0,o.lastIndexOf("."))),l.settings.isTrusted({command:"\\includegraphics",url:h}))?{type:"includegraphics",mode:l.mode,alt:o,width:s,height:a,totalheight:i,src:h}:l.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(e,t)=>{let r=V(e.height,t),l=0;e.totalheight.number>0&&(l=V(e.totalheight,t)-r);let n=0;e.width.number>0&&(n=V(e.width,t));let s={height:_(r+l)};n>0&&(s.width=_(n)),l>0&&(s.verticalAlign=_(-l));let a=new K(e.src,e.alt,s);return a.height=r,a.depth=l,a},mathmlBuilder:(e,t)=>{let r=new ti.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);let l=V(e.height,t),n=0;if(e.totalheight.number>0&&(n=V(e.totalheight,t)-l,r.setAttribute("valign",_(-n))),r.setAttribute("height",_(l+n)),e.width.number>0){let l=V(e.width,t);r.setAttribute("width",_(l))}return r.setAttribute("src",e.src),r}}),eY({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(e,t){let{parser:r,funcName:l}=e,n=tS(t[0],"size");if(r.settings.strict){let e="m"===l[1],t="mu"===n.value.unit;e?(t||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+l+" supports only mu units, not "+n.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+l+" works only in math mode")):t&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+l+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:n.value}},htmlBuilder:(e,t)=>eD.makeGlue(e.dimension,t),mathmlBuilder(e,t){let r=V(e.dimension,t);return new ti.SpaceNode(r)}}),eY({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=t[0];return{type:"lap",mode:r.mode,alignment:l.slice(5),body:n}},htmlBuilder:(e,t)=>{let r;"clap"===e.alignment?(r=eD.makeSpan([],[te(e.body,t)]),r=eD.makeSpan(["inner"],[r],t)):r=eD.makeSpan(["inner"],[te(e.body,t)]);let l=eD.makeSpan(["fix"],[]),n=eD.makeSpan([e.alignment],[r,l],t),s=eD.makeSpan(["strut"]);return s.style.height=_(n.height+n.depth),n.depth&&(s.style.verticalAlign=_(-n.depth)),n.children.unshift(s),n=eD.makeSpan(["thinbox"],[n],t),eD.makeSpan(["mord","vbox"],[n],t)},mathmlBuilder:(e,t)=>{let r=new ti.MathNode("mpadded",[tp(e.body,t)]);if("rlap"!==e.alignment){let t="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",t+"width")}return r.setAttribute("width","0px"),r}}),eY({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){let{funcName:r,parser:l}=e,n=l.mode;l.switchMode("math");let s="\\("===r?"\\)":"$",a=l.parseExpression(!1,s);return l.expect(s),l.switchMode(n),{type:"styling",mode:l.mode,style:"text",body:a}}}),eY({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){throw new n("Mismatched "+e.funcName)}});let rI=(e,t)=>{switch(t.style.size){case v.DISPLAY.size:return e.display;case v.TEXT.size:return e.text;case v.SCRIPT.size:return e.script;case v.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};eY({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(e,t)=>{let{parser:r}=e;return{type:"mathchoice",mode:r.mode,display:eQ(t[0]),text:eQ(t[1]),script:eQ(t[2]),scriptscript:eQ(t[3])}},htmlBuilder:(e,t)=>{let r=e6(rI(e,t),t,!1);return eD.makeFragment(r)},mathmlBuilder:(e,t)=>tu(rI(e,t),t)});let rE=(e,t,r,l,n,s,a)=>{let i,o,h;e=eD.makeSpan([],[e]);let c=r&&m.isCharacterBox(r);if(t){let e=te(t,l.havingStyle(n.sup()),l);o={elem:e,kern:Math.max(l.fontMetrics().bigOpSpacing1,l.fontMetrics().bigOpSpacing3-e.depth)}}if(r){let e=te(r,l.havingStyle(n.sub()),l);i={elem:e,kern:Math.max(l.fontMetrics().bigOpSpacing2,l.fontMetrics().bigOpSpacing4-e.height)}}if(o&&i){let t=l.fontMetrics().bigOpSpacing5+i.elem.height+i.elem.depth+i.kern+e.depth+a;h=eD.makeVList({positionType:"bottom",positionData:t,children:[{type:"kern",size:l.fontMetrics().bigOpSpacing5},{type:"elem",elem:i.elem,marginLeft:_(-s)},{type:"kern",size:i.kern},{type:"elem",elem:e},{type:"kern",size:o.kern},{type:"elem",elem:o.elem,marginLeft:_(s)},{type:"kern",size:l.fontMetrics().bigOpSpacing5}]},l)}else if(i){let t=e.height-a;h=eD.makeVList({positionType:"top",positionData:t,children:[{type:"kern",size:l.fontMetrics().bigOpSpacing5},{type:"elem",elem:i.elem,marginLeft:_(-s)},{type:"kern",size:i.kern},{type:"elem",elem:e}]},l)}else{if(!o)return e;let t=e.depth+a;h=eD.makeVList({positionType:"bottom",positionData:t,children:[{type:"elem",elem:e},{type:"kern",size:o.kern},{type:"elem",elem:o.elem,marginLeft:_(s)},{type:"kern",size:l.fontMetrics().bigOpSpacing5}]},l)}let d=[h];if(i&&0!==s&&!c){let e=eD.makeSpan(["mspace"],[],l);e.style.marginRight=_(s),d.unshift(e)}return eD.makeSpan(["mop","op-limits"],d,l)},rO=["\\smallint"],rH=(e,t)=>{let r,l,n,s;let a=!1;"supsub"===e.type?(r=e.sup,l=e.sub,n=tS(e.base,"op"),a=!0):n=tS(e,"op");let i=t.style,o=!1;if(i.size===v.DISPLAY.size&&n.symbol&&!m.contains(rO,n.name)&&(o=!0),n.symbol){let e=o?"Size2-Regular":"Size1-Regular",r="";if(("\\oiint"===n.name||"\\oiiint"===n.name)&&(r=n.name.slice(1),n.name="oiint"===r?"\\iint":"\\iiint"),s=eD.makeSymbol(n.name,e,"math",t,["mop","op-symbol",o?"large-op":"small-op"]),r.length>0){let e=s.italic,l=eD.staticSvg(r+"Size"+(o?"2":"1"),t);s=eD.makeVList({positionType:"individualShift",children:[{type:"elem",elem:s,shift:0},{type:"elem",elem:l,shift:.08*!!o}]},t),n.name="\\"+r,s.classes.unshift("mop"),s.italic=e}}else if(n.body){let e=e6(n.body,t,!0);1===e.length&&e[0]instanceof J?(s=e[0]).classes[0]="mop":s=eD.makeSpan(["mop"],e,t)}else{let e=[];for(let r=1;r<n.name.length;r++)e.push(eD.mathsym(n.name[r],n.mode,t));s=eD.makeSpan(["mop"],e,t)}let h=0,c=0;return((s instanceof J||"\\oiint"===n.name||"\\oiiint"===n.name)&&!n.suppressBaseShift&&(h=(s.height-s.depth)/2-t.fontMetrics().axisHeight,c=s.italic),a)?rE(s,r,l,t,i,c,h):(h&&(s.style.position="relative",s.style.top=_(h)),s)},rR=(e,t)=>{let r;if(e.symbol)r=new tn("mo",[to(e.name,e.mode)]),m.contains(rO,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new tn("mo",td(e.body,t));else{r=new tn("mi",[new ts(e.name.slice(1))]);let t=new tn("mo",[to("⁡","text")]);r=e.parentIsSupSub?new tn("mrow",[r,t]):tl([r,t])}return r},rL={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};eY({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=l;return 1===n.length&&(n=rL[n]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:rH,mathmlBuilder:rR}),eY({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let{parser:r}=e,l=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:eQ(l)}},htmlBuilder:rH,mathmlBuilder:rR});let rP={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};eY({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(e){let{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:rH,mathmlBuilder:rR}),eY({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(e){let{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:rH,mathmlBuilder:rR}),eY({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(e){let{parser:t,funcName:r}=e,l=r;return 1===l.length&&(l=rP[l]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:l}},htmlBuilder:rH,mathmlBuilder:rR});let rD=(e,t)=>{let r,l,n,s;let a=!1;if("supsub"===e.type?(r=e.sup,l=e.sub,n=tS(e.base,"operatorname"),a=!0):n=tS(e,"operatorname"),n.body.length>0){let e=e6(n.body.map(e=>{let t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e}),t.withFont("mathrm"),!0);for(let t=0;t<e.length;t++){let r=e[t];r instanceof J&&(r.text=r.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}s=eD.makeSpan(["mop"],e,t)}else s=eD.makeSpan(["mop"],[],t);return a?rE(s,r,l,t,t.style,0,0):s};function rF(e,t,r){let l=e6(e,t,!1),n=t.sizeMultiplier/r.sizeMultiplier;for(let e=0;e<l.length;e++){let s=l[e].classes.indexOf("sizing");s<0?Array.prototype.push.apply(l[e].classes,t.sizingClasses(r)):l[e].classes[s+1]==="reset-size"+t.size&&(l[e].classes[s+1]="reset-size"+r.size),l[e].height*=n,l[e].depth*=n}return eD.makeFragment(l)}eY({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(e,t)=>{let{parser:r,funcName:l}=e,n=t[0];return{type:"operatorname",mode:r.mode,body:eQ(n),alwaysHandleSupSub:"\\operatornamewithlimits"===l,limits:!1,parentIsSupSub:!1}},htmlBuilder:rD,mathmlBuilder:(e,t)=>{let r=td(e.body,t.withFont("mathrm")),l=!0;for(let e=0;e<r.length;e++){let t=r[e];if(t instanceof ti.SpaceNode);else if(t instanceof ti.MathNode)switch(t.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{let e=t.children[0];1===t.children.length&&e instanceof ti.TextNode?e.text=e.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):l=!1;break}default:l=!1}else l=!1}if(l){let e=r.map(e=>e.toText()).join("");r=[new ti.TextNode(e)]}let n=new ti.MathNode("mi",r);n.setAttribute("mathvariant","normal");let s=new ti.MathNode("mo",[to("⁡","text")]);return e.parentIsSupSub?new ti.MathNode("mrow",[n,s]):ti.newDocumentFragment([n,s])}}),rc["\\operatorname"]="\\@ifstar\\operatornamewithlimits\\operatorname@",eZ({type:"ordgroup",htmlBuilder:(e,t)=>e.semisimple?eD.makeFragment(e6(e.body,t,!1)):eD.makeSpan(["mord"],e6(e.body,t,!0),t),mathmlBuilder:(e,t)=>tu(e.body,t,!0)}),eY({type:"overline",names:["\\overline"],props:{numArgs:1},handler(e,t){let{parser:r}=e,l=t[0];return{type:"overline",mode:r.mode,body:l}},htmlBuilder(e,t){let r=te(e.body,t.havingCrampedStyle()),l=eD.makeLineSpan("overline-line",t),n=t.fontMetrics().defaultRuleThickness,s=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*n},{type:"elem",elem:l},{type:"kern",size:n}]},t);return eD.makeSpan(["mord","overline"],[s],t)},mathmlBuilder(e,t){let r=new ti.MathNode("mo",[new ti.TextNode("‾")]);r.setAttribute("stretchy","true");let l=new ti.MathNode("mover",[tp(e.body,t),r]);return l.setAttribute("accent","true"),l}}),eY({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,l=t[0];return{type:"phantom",mode:r.mode,body:eQ(l)}},htmlBuilder:(e,t)=>{let r=e6(e.body,t.withPhantom(),!1);return eD.makeFragment(r)},mathmlBuilder:(e,t)=>{let r=td(e.body,t);return new ti.MathNode("mphantom",r)}}),eY({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,l=t[0];return{type:"hphantom",mode:r.mode,body:l}},htmlBuilder:(e,t)=>{let r=eD.makeSpan([],[te(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(let e=0;e<r.children.length;e++)r.children[e].height=0,r.children[e].depth=0;return r=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),eD.makeSpan(["mord"],[r],t)},mathmlBuilder:(e,t)=>{let r=td(eQ(e.body),t),l=new ti.MathNode("mphantom",r),n=new ti.MathNode("mpadded",[l]);return n.setAttribute("height","0px"),n.setAttribute("depth","0px"),n}}),eY({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,l=t[0];return{type:"vphantom",mode:r.mode,body:l}},htmlBuilder:(e,t)=>{let r=eD.makeSpan(["inner"],[te(e.body,t.withPhantom())]),l=eD.makeSpan(["fix"],[]);return eD.makeSpan(["mord","rlap"],[r,l],t)},mathmlBuilder:(e,t)=>{let r=td(eQ(e.body),t),l=new ti.MathNode("mphantom",r),n=new ti.MathNode("mpadded",[l]);return n.setAttribute("width","0px"),n}}),eY({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(e,t){let{parser:r}=e,l=tS(t[0],"size").value,n=t[1];return{type:"raisebox",mode:r.mode,dy:l,body:n}},htmlBuilder(e,t){let r=te(e.body,t),l=V(e.dy,t);return eD.makeVList({positionType:"shift",positionData:-l,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){let r=new ti.MathNode("mpadded",[tp(e.body,t)]),l=e.dy.number+e.dy.unit;return r.setAttribute("voffset",l),r}}),eY({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(e){let{parser:t}=e;return{type:"internal",mode:t.mode}}}),eY({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(e,t,r){let{parser:l}=e,n=r[0],s=tS(t[0],"size"),a=tS(t[1],"size");return{type:"rule",mode:l.mode,shift:n&&tS(n,"size").value,width:s.value,height:a.value}},htmlBuilder(e,t){let r=eD.makeSpan(["mord","rule"],[],t),l=V(e.width,t),n=V(e.height,t),s=e.shift?V(e.shift,t):0;return r.style.borderRightWidth=_(l),r.style.borderTopWidth=_(n),r.style.bottom=_(s),r.width=l,r.height=n+s,r.depth=-s,r.maxFontSize=1.125*n*t.sizeMultiplier,r},mathmlBuilder(e,t){let r=V(e.width,t),l=V(e.height,t),n=e.shift?V(e.shift,t):0,s=t.color&&t.getColor()||"black",a=new ti.MathNode("mspace");a.setAttribute("mathbackground",s),a.setAttribute("width",_(r)),a.setAttribute("height",_(l));let i=new ti.MathNode("mpadded",[a]);return n>=0?i.setAttribute("height",_(n)):(i.setAttribute("height",_(n)),i.setAttribute("depth",_(-n))),i.setAttribute("voffset",_(n)),i}});let rV=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];eY({type:"sizing",names:rV,props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{let{breakOnTokenText:r,funcName:l,parser:n}=e,s=n.parseExpression(!1,r);return{type:"sizing",mode:n.mode,size:rV.indexOf(l)+1,body:s}},htmlBuilder:(e,t)=>{let r=t.havingSize(e.size);return rF(e.body,r,t)},mathmlBuilder:(e,t)=>{let r=t.havingSize(e.size),l=td(e.body,r),n=new ti.MathNode("mstyle",l);return n.setAttribute("mathsize",_(r.sizeMultiplier)),n}}),eY({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(e,t,r)=>{let{parser:l}=e,n=!1,s=!1,a=r[0]&&tS(r[0],"ordgroup");if(a){let e="";for(let t=0;t<a.body.length;++t)if("t"===(e=a.body[t].text))n=!0;else if("b"===e)s=!0;else{n=!1,s=!1;break}}else n=!0,s=!0;let i=t[0];return{type:"smash",mode:l.mode,body:i,smashHeight:n,smashDepth:s}},htmlBuilder:(e,t)=>{let r=eD.makeSpan([],[te(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(let e=0;e<r.children.length;e++)r.children[e].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(let e=0;e<r.children.length;e++)r.children[e].depth=0;let l=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return eD.makeSpan(["mord"],[l],t)},mathmlBuilder:(e,t)=>{let r=new ti.MathNode("mpadded",[tp(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),eY({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){let{parser:l}=e,n=r[0],s=t[0];return{type:"sqrt",mode:l.mode,body:s,index:n}},htmlBuilder(e,t){let r=te(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=eD.wrapFragment(r,t);let l=t.fontMetrics().defaultRuleThickness,n=l;t.style.id<v.TEXT.id&&(n=t.fontMetrics().xHeight);let s=l+n/4,a=r.height+r.depth+s+l,{span:i,ruleWidth:o,advanceWidth:h}=rr.sqrtImage(a,t),m=i.height-o;m>r.height+r.depth+s&&(s=(s+m-r.height-r.depth)/2);let c=i.height-r.height-s-o;r.style.paddingLeft=_(h);let d=eD.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+c)},{type:"elem",elem:i},{type:"kern",size:o}]},t);if(!e.index)return eD.makeSpan(["mord","sqrt"],[d],t);{let r=t.havingStyle(v.SCRIPTSCRIPT),l=te(e.index,r,t),n=.6*(d.height-d.depth),s=eD.makeVList({positionType:"shift",positionData:-n,children:[{type:"elem",elem:l}]},t),a=eD.makeSpan(["root"],[s]);return eD.makeSpan(["mord","sqrt"],[a,d],t)}},mathmlBuilder(e,t){let{body:r,index:l}=e;return l?new ti.MathNode("mroot",[tp(r,t),tp(l,t)]):new ti.MathNode("msqrt",[tp(r,t)])}});let r_={display:v.DISPLAY,text:v.TEXT,script:v.SCRIPT,scriptscript:v.SCRIPTSCRIPT};eY({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e,t){let{breakOnTokenText:r,funcName:l,parser:n}=e,s=n.parseExpression(!0,r),a=l.slice(1,l.length-5);return{type:"styling",mode:n.mode,style:a,body:s}},htmlBuilder(e,t){let r=r_[e.style],l=t.havingStyle(r).withFont("");return rF(e.body,l,t)},mathmlBuilder(e,t){let r=r_[e.style],l=t.havingStyle(r),n=td(e.body,l),s=new ti.MathNode("mstyle",n),a={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return s.setAttribute("scriptlevel",a[0]),s.setAttribute("displaystyle",a[1]),s}});let rU=function(e,t){let r=e.base;if(!r)return null;if("op"===r.type)return r.limits&&(t.style.size===v.DISPLAY.size||r.alwaysHandleSupSub)?rH:null;if("operatorname"===r.type)return r.alwaysHandleSupSub&&(t.style.size===v.DISPLAY.size||r.limits)?rD:null;if("accent"===r.type)return m.isCharacterBox(r.base)?tA:null;if("horizBrace"===r.type)return!e.sub===r.isOver?rq:null;else return null};eZ({type:"supsub",htmlBuilder(e,t){let r,l,n,s;let a=rU(e,t);if(a)return a(e,t);let{base:i,sup:o,sub:h}=e,c=te(i,t),d=t.fontMetrics(),u=0,p=0,g=i&&m.isCharacterBox(i);if(o){let e=t.havingStyle(t.style.sup());r=te(o,e,t),g||(u=c.height-e.fontMetrics().supDrop*e.sizeMultiplier/t.sizeMultiplier)}if(h){let e=t.havingStyle(t.style.sub());l=te(h,e,t),g||(p=c.depth+e.fontMetrics().subDrop*e.sizeMultiplier/t.sizeMultiplier)}n=t.style===v.DISPLAY?d.sup1:t.style.cramped?d.sup3:d.sup2;let f=t.sizeMultiplier,x=_(.5/d.ptPerEm/f),b=null;if(l){let t=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(c instanceof J||t)&&(b=_(-c.italic))}if(r&&l){u=Math.max(u,n,r.depth+.25*d.xHeight),p=Math.max(p,d.sub2);let e=4*d.defaultRuleThickness;if(u-r.depth-(l.height-p)<e){p=e-(u-r.depth)+l.height;let t=.8*d.xHeight-(u-r.depth);t>0&&(u+=t,p-=t)}let a=[{type:"elem",elem:l,shift:p,marginRight:x,marginLeft:b},{type:"elem",elem:r,shift:-u,marginRight:x}];s=eD.makeVList({positionType:"individualShift",children:a},t)}else if(l){p=Math.max(p,d.sub1,l.height-.8*d.xHeight);let e=[{type:"elem",elem:l,marginLeft:b,marginRight:x}];s=eD.makeVList({positionType:"shift",positionData:p,children:e},t)}else if(r)u=Math.max(u,n,r.depth+.25*d.xHeight),s=eD.makeVList({positionType:"shift",positionData:-u,children:[{type:"elem",elem:r,marginRight:x}]},t);else throw Error("supsub must have either sup or sub.");let y=e2(c,"right")||"mord";return eD.makeSpan([y],[c,eD.makeSpan(["msupsub"],[s])],t)},mathmlBuilder(e,t){let r,l,n=!1;e.base&&"horizBrace"===e.base.type&&!!e.sup===e.base.isOver&&(n=!0,r=e.base.isOver),e.base&&("op"===e.base.type||"operatorname"===e.base.type)&&(e.base.parentIsSupSub=!0);let s=[tp(e.base,t)];if(e.sub&&s.push(tp(e.sub,t)),e.sup&&s.push(tp(e.sup,t)),n)l=r?"mover":"munder";else if(e.sub){if(e.sup){let r=e.base;l=r&&"op"===r.type&&r.limits&&t.style===v.DISPLAY?"munderover":r&&"operatorname"===r.type&&r.alwaysHandleSupSub&&(t.style===v.DISPLAY||r.limits)?"munderover":"msubsup"}else{let r=e.base;l=r&&"op"===r.type&&r.limits&&(t.style===v.DISPLAY||r.alwaysHandleSupSub)?"munder":r&&"operatorname"===r.type&&r.alwaysHandleSupSub&&(r.limits||t.style===v.DISPLAY)?"munder":"msub"}}else{let r=e.base;l=r&&"op"===r.type&&r.limits&&(t.style===v.DISPLAY||r.alwaysHandleSupSub)?"mover":r&&"operatorname"===r.type&&r.alwaysHandleSupSub&&(r.limits||t.style===v.DISPLAY)?"mover":"msup"}return new ti.MathNode(l,s)}}),eZ({type:"atom",htmlBuilder:(e,t)=>eD.mathsym(e.text,e.mode,t,["m"+e.family]),mathmlBuilder(e,t){let r=new ti.MathNode("mo",[to(e.text,e.mode)]);if("bin"===e.family){let l=tm(e,t);"bold-italic"===l&&r.setAttribute("mathvariant",l)}else"punct"===e.family?r.setAttribute("separator","true"):("open"===e.family||"close"===e.family)&&r.setAttribute("stretchy","false");return r}});let r$={mi:"italic",mn:"normal",mtext:"normal"};eZ({type:"mathord",htmlBuilder:(e,t)=>eD.makeOrd(e,t,"mathord"),mathmlBuilder(e,t){let r=new ti.MathNode("mi",[to(e.text,e.mode,t)]),l=tm(e,t)||"italic";return l!==r$[r.type]&&r.setAttribute("mathvariant",l),r}}),eZ({type:"textord",htmlBuilder:(e,t)=>eD.makeOrd(e,t,"textord"),mathmlBuilder(e,t){let r;let l=to(e.text,e.mode,t),n=tm(e,t)||"normal";return n!==r$[(r="text"===e.mode?new ti.MathNode("mtext",[l]):/[0-9]/.test(e.text)?new ti.MathNode("mn",[l]):"\\prime"===e.text?new ti.MathNode("mo",[l]):new ti.MathNode("mi",[l])).type]&&r.setAttribute("mathvariant",n),r}});let rG={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},rW={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};eZ({type:"spacing",htmlBuilder(e,t){if(rW.hasOwnProperty(e.text)){let r=rW[e.text].className||"";if("text"!==e.mode)return eD.makeSpan(["mspace",r],[eD.mathsym(e.text,e.mode,t)],t);{let l=eD.makeOrd(e,t,"textord");return l.classes.push(r),l}}if(rG.hasOwnProperty(e.text))return eD.makeSpan(["mspace",rG[e.text]],[],t);throw new n('Unknown type of space "'+e.text+'"')},mathmlBuilder(e,t){let r;if(rW.hasOwnProperty(e.text))r=new ti.MathNode("mtext",[new ti.TextNode("\xa0")]);else if(rG.hasOwnProperty(e.text))return new ti.MathNode("mspace");else throw new n('Unknown type of space "'+e.text+'"');return r}});let rX=()=>{let e=new ti.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};eZ({type:"tag",mathmlBuilder(e,t){let r=new ti.MathNode("mtable",[new ti.MathNode("mtr",[rX(),new ti.MathNode("mtd",[tu(e.body,t)]),rX(),new ti.MathNode("mtd",[tu(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});let rY={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},rZ={"\\textbf":"textbf","\\textmd":"textmd"},rK={"\\textit":"textit","\\textup":"textup"},rQ=(e,t)=>{let r=e.font;return r?rY[r]?t.withTextFontFamily(rY[r]):rZ[r]?t.withTextFontWeight(rZ[r]):"\\emph"===r?"textit"===t.fontShape?t.withTextFontShape("textup"):t.withTextFontShape("textit"):t.withTextFontShape(rK[r]):t};eY({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(e,t){let{parser:r,funcName:l}=e,n=t[0];return{type:"text",mode:r.mode,body:eQ(n),font:l}},htmlBuilder(e,t){let r=rQ(e,t),l=e6(e.body,r,!0);return eD.makeSpan(["mord","text"],l,r)},mathmlBuilder(e,t){let r=rQ(e,t);return tu(e.body,r)}}),eY({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(e,t){let{parser:r}=e;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder(e,t){let r=te(e.body,t),l=eD.makeLineSpan("underline-line",t),n=t.fontMetrics().defaultRuleThickness,s=eD.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:n},{type:"elem",elem:l},{type:"kern",size:3*n},{type:"elem",elem:r}]},t);return eD.makeSpan(["mord","underline"],[s],t)},mathmlBuilder(e,t){let r=new ti.MathNode("mo",[new ti.TextNode("‾")]);r.setAttribute("stretchy","true");let l=new ti.MathNode("munder",[tp(e.body,t),r]);return l.setAttribute("accentunder","true"),l}}),eY({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(e,t){let{parser:r}=e;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder(e,t){let r=te(e.body,t),l=t.fontMetrics().axisHeight,n=.5*(r.height-l-(r.depth+l));return eD.makeVList({positionType:"shift",positionData:n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:(e,t)=>new ti.MathNode("mpadded",[tp(e.body,t)],["vcenter"])}),eY({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(e,t,r){throw new n("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(e,t){let r=rJ(e),l=[],n=t.havingStyle(t.style.text());for(let t=0;t<r.length;t++){let s=r[t];"~"===s&&(s="\\textasciitilde"),l.push(eD.makeSymbol(s,"Typewriter-Regular",e.mode,n,["mord","texttt"]))}return eD.makeSpan(["mord","text"].concat(n.sizingClasses(t)),eD.tryCombineChars(l),n)},mathmlBuilder(e,t){let r=new ti.TextNode(rJ(e)),l=new ti.MathNode("mtext",[r]);return l.setAttribute("mathvariant","monospace"),l}});let rJ=e=>e.body.replace(/ /g,e.star?"␣":"\xa0"),r0="[ \r\n	]",r1="[̀-ͯ]",r4=RegExp(r1+"+$"),r5="("+r0+"+)|\\\\(\n|[ \r	]+\n?)[ \r	]*|([!-\\[\\]-‧‪-퟿豈-￿]"+r1+"*|[\uD800-\uDBFF][\uDC00-\uDFFF]"+r1+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|(\\\\[a-zA-Z@]+)"+r0+"*|\\\\[^\uD800-\uDFFF])";class r6{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=RegExp(r5,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){let e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new ru("EOF",new rd(this,t,t));let r=this.tokenRegex.exec(e);if(null===r||r.index!==t)throw new n("Unexpected character: '"+e[t]+"'",new ru(e[t],new rd(this,t,t+1)));let l=r[6]||r[3]||(r[2]?"\\ ":" ");if(14===this.catcodes[l]){let t=e.indexOf("\n",this.tokenRegex.lastIndex);return -1===t?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=t+1,this.lex()}return new ru(l,new rd(this,t,this.tokenRegex.lastIndex))}}class r3{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(0===this.undefStack.length)throw new n("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");let e=this.undefStack.pop();for(let t in e)e.hasOwnProperty(t)&&(null==e[t]?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(void 0===r&&(r=!1),r){for(let t=0;t<this.undefStack.length;t++)delete this.undefStack[t][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{let t=this.undefStack[this.undefStack.length-1];t&&!t.hasOwnProperty(e)&&(t[e]=this.current[e])}null==t?delete this.current[e]:this.current[e]=t}}rc["\\noexpand"]=function(e){let t=e.popToken();return e.isExpandable(t.text)&&(t.noexpand=!0,t.treatAsRelax=!0),{tokens:[t],numArgs:0}},rc["\\expandafter"]=function(e){let t=e.popToken();return e.expandOnce(!0),{tokens:[t],numArgs:0}},rc["\\@firstoftwo"]=function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}},rc["\\@secondoftwo"]=function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}},rc["\\@ifnextchar"]=function(e){let t=e.consumeArgs(3);e.consumeSpaces();let r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}},rc["\\@ifstar"]="\\@ifnextchar *{\\@firstoftwo{#1}}",rc["\\TextOrMath"]=function(e){let t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}};let r7={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};rc["\\char"]=function(e){let t,r=e.popToken(),l="";if("'"===r.text)t=8,r=e.popToken();else if('"'===r.text)t=16,r=e.popToken();else if("`"===r.text){if("\\"===(r=e.popToken()).text[0])l=r.text.charCodeAt(1);else if("EOF"===r.text)throw new n("\\char` missing argument");else l=r.text.charCodeAt(0)}else t=10;if(t){let s;if(null==(l=r7[r.text])||l>=t)throw new n("Invalid base-"+t+" digit "+r.text);for(;null!=(s=r7[e.future().text])&&s<t;)l*=t,l+=s,e.popToken()}return"\\@char{"+l+"}"};let r8=(e,t,r,l)=>{let s=e.consumeArg().tokens;if(1!==s.length)throw new n("\\newcommand's first argument must be a macro name");let a=s[0].text,i=e.isDefined(a);if(i&&!t)throw new n("\\newcommand{"+a+"} attempting to redefine "+a+"; use \\renewcommand");if(!i&&!r)throw new n("\\renewcommand{"+a+"} when command "+a+" does not yet exist; use \\newcommand");let o=0;if(1===(s=e.consumeArg().tokens).length&&"["===s[0].text){let t="",r=e.expandNextToken();for(;"]"!==r.text&&"EOF"!==r.text;)t+=r.text,r=e.expandNextToken();if(!t.match(/^\s*[0-9]+\s*$/))throw new n("Invalid number of arguments: "+t);o=parseInt(t),s=e.consumeArg().tokens}return i&&l||e.macros.set(a,{tokens:s,numArgs:o}),""};rc["\\newcommand"]=e=>r8(e,!1,!0,!1),rc["\\renewcommand"]=e=>r8(e,!0,!1,!1),rc["\\providecommand"]=e=>r8(e,!0,!0,!0),rc["\\message"]=e=>(console.log(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),rc["\\errmessage"]=e=>(console.error(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),rc["\\show"]=e=>{let t=e.popToken(),r=t.text;return console.log(t,e.macros.get(r),eG[r],ea.math[r],ea.text[r]),""},rc["\\bgroup"]="{",rc["\\egroup"]="}",rc["~"]="\\nobreakspace",rc["\\lq"]="`",rc["\\rq"]="'",rc["\\aa"]="\\r a",rc["\\AA"]="\\r A",rc["\\textcopyright"]="\\html@mathml{\\textcircled{c}}{\\char`\xa9}",rc["\\copyright"]="\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}",rc["\\textregistered"]="\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xae}",rc["ℬ"]="\\mathscr{B}",rc["ℰ"]="\\mathscr{E}",rc["ℱ"]="\\mathscr{F}",rc["ℋ"]="\\mathscr{H}",rc["ℐ"]="\\mathscr{I}",rc["ℒ"]="\\mathscr{L}",rc["ℳ"]="\\mathscr{M}",rc["ℛ"]="\\mathscr{R}",rc["ℭ"]="\\mathfrak{C}",rc["ℌ"]="\\mathfrak{H}",rc["ℨ"]="\\mathfrak{Z}",rc["\\Bbbk"]="\\Bbb{k}",rc["\xb7"]="\\cdotp",rc["\\llap"]="\\mathllap{\\textrm{#1}}",rc["\\rlap"]="\\mathrlap{\\textrm{#1}}",rc["\\clap"]="\\mathclap{\\textrm{#1}}",rc["\\mathstrut"]="\\vphantom{(}",rc["\\underbar"]="\\underline{\\text{#1}}",rc["\\not"]='\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}',rc["\\neq"]="\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}",rc["\\ne"]="\\neq",rc["≠"]="\\neq",rc["\\notin"]="\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}",rc["∉"]="\\notin",rc["≘"]="\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}",rc["≙"]="\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}",rc["≚"]="\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}",rc["≛"]="\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}",rc["≝"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}",rc["≞"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}",rc["≟"]="\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}",rc["⟂"]="\\perp",rc["‼"]="\\mathclose{!\\mkern-0.8mu!}",rc["∌"]="\\notni",rc["⌜"]="\\ulcorner",rc["⌝"]="\\urcorner",rc["⌞"]="\\llcorner",rc["⌟"]="\\lrcorner",rc["\xa9"]="\\copyright",rc["\xae"]="\\textregistered",rc["️"]="\\textregistered",rc["\\ulcorner"]='\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}',rc["\\urcorner"]='\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}',rc["\\llcorner"]='\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}',rc["\\lrcorner"]='\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}',rc["\\vdots"]="{\\varvdots\\rule{0pt}{15pt}}",rc["⋮"]="\\vdots",rc["\\varGamma"]="\\mathit{\\Gamma}",rc["\\varDelta"]="\\mathit{\\Delta}",rc["\\varTheta"]="\\mathit{\\Theta}",rc["\\varLambda"]="\\mathit{\\Lambda}",rc["\\varXi"]="\\mathit{\\Xi}",rc["\\varPi"]="\\mathit{\\Pi}",rc["\\varSigma"]="\\mathit{\\Sigma}",rc["\\varUpsilon"]="\\mathit{\\Upsilon}",rc["\\varPhi"]="\\mathit{\\Phi}",rc["\\varPsi"]="\\mathit{\\Psi}",rc["\\varOmega"]="\\mathit{\\Omega}",rc["\\substack"]="\\begin{subarray}{c}#1\\end{subarray}",rc["\\colon"]="\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax",rc["\\boxed"]="\\fbox{$\\displaystyle{#1}$}",rc["\\iff"]="\\DOTSB\\;\\Longleftrightarrow\\;",rc["\\implies"]="\\DOTSB\\;\\Longrightarrow\\;",rc["\\impliedby"]="\\DOTSB\\;\\Longleftarrow\\;",rc["\\dddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}",rc["\\ddddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}";let r2={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};rc["\\dots"]=function(e){let t="\\dotso",r=e.expandAfterFuture().text;return r in r2?t=r2[r]:"\\not"===r.slice(0,4)?t="\\dotsb":r in ea.math&&m.contains(["bin","rel"],ea.math[r].group)&&(t="\\dotsb"),t};let r9={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};rc["\\dotso"]=function(e){return e.future().text in r9?"\\ldots\\,":"\\ldots"},rc["\\dotsc"]=function(e){let t=e.future().text;return t in r9&&","!==t?"\\ldots\\,":"\\ldots"},rc["\\cdots"]=function(e){return e.future().text in r9?"\\@cdots\\,":"\\@cdots"},rc["\\dotsb"]="\\cdots",rc["\\dotsm"]="\\cdots",rc["\\dotsi"]="\\!\\cdots",rc["\\dotsx"]="\\ldots\\,",rc["\\DOTSI"]="\\relax",rc["\\DOTSB"]="\\relax",rc["\\DOTSX"]="\\relax",rc["\\tmspace"]="\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax",rc["\\,"]="\\tmspace+{3mu}{.1667em}",rc["\\thinspace"]="\\,",rc["\\>"]="\\mskip{4mu}",rc["\\:"]="\\tmspace+{4mu}{.2222em}",rc["\\medspace"]="\\:",rc["\\;"]="\\tmspace+{5mu}{.2777em}",rc["\\thickspace"]="\\;",rc["\\!"]="\\tmspace-{3mu}{.1667em}",rc["\\negthinspace"]="\\!",rc["\\negmedspace"]="\\tmspace-{4mu}{.2222em}",rc["\\negthickspace"]="\\tmspace-{5mu}{.277em}",rc["\\enspace"]="\\kern.5em ",rc["\\enskip"]="\\hskip.5em\\relax",rc["\\quad"]="\\hskip1em\\relax",rc["\\qquad"]="\\hskip2em\\relax",rc["\\tag"]="\\@ifstar\\tag@literal\\tag@paren",rc["\\tag@paren"]="\\tag@literal{({#1})}",rc["\\tag@literal"]=e=>{if(e.macros.get("\\df@tag"))throw new n("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"},rc["\\bmod"]="\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}",rc["\\pod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)",rc["\\pmod"]="\\pod{{\\rm mod}\\mkern6mu#1}",rc["\\mod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1",rc["\\newline"]="\\\\\\relax",rc["\\TeX"]="\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}";let le=_(j["Main-Regular"][84][1]-.7*j["Main-Regular"][65][1]);rc["\\LaTeX"]="\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+le+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}",rc["\\KaTeX"]="\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+le+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}",rc["\\hspace"]="\\@ifstar\\@hspacer\\@hspace",rc["\\@hspace"]="\\hskip #1\\relax",rc["\\@hspacer"]="\\rule{0pt}{0pt}\\hskip #1\\relax",rc["\\ordinarycolon"]=":",rc["\\vcentcolon"]="\\mathrel{\\mathop\\ordinarycolon}",rc["\\dblcolon"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}',rc["\\coloneqq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}',rc["\\Coloneqq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}',rc["\\coloneq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}',rc["\\Coloneq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}',rc["\\eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}',rc["\\Eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}',rc["\\eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}',rc["\\Eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}',rc["\\colonapprox"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}',rc["\\Colonapprox"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}',rc["\\colonsim"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}',rc["\\Colonsim"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}',rc["∷"]="\\dblcolon",rc["∹"]="\\eqcolon",rc["≔"]="\\coloneqq",rc["≕"]="\\eqqcolon",rc["⩴"]="\\Coloneqq",rc["\\ratio"]="\\vcentcolon",rc["\\coloncolon"]="\\dblcolon",rc["\\colonequals"]="\\coloneqq",rc["\\coloncolonequals"]="\\Coloneqq",rc["\\equalscolon"]="\\eqqcolon",rc["\\equalscoloncolon"]="\\Eqqcolon",rc["\\colonminus"]="\\coloneq",rc["\\coloncolonminus"]="\\Coloneq",rc["\\minuscolon"]="\\eqcolon",rc["\\minuscoloncolon"]="\\Eqcolon",rc["\\coloncolonapprox"]="\\Colonapprox",rc["\\coloncolonsim"]="\\Colonsim",rc["\\simcolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}",rc["\\simcoloncolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}",rc["\\approxcolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}",rc["\\approxcoloncolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}",rc["\\notni"]="\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}",rc["\\limsup"]="\\DOTSB\\operatorname*{lim\\,sup}",rc["\\liminf"]="\\DOTSB\\operatorname*{lim\\,inf}",rc["\\injlim"]="\\DOTSB\\operatorname*{inj\\,lim}",rc["\\projlim"]="\\DOTSB\\operatorname*{proj\\,lim}",rc["\\varlimsup"]="\\DOTSB\\operatorname*{\\overline{lim}}",rc["\\varliminf"]="\\DOTSB\\operatorname*{\\underline{lim}}",rc["\\varinjlim"]="\\DOTSB\\operatorname*{\\underrightarrow{lim}}",rc["\\varprojlim"]="\\DOTSB\\operatorname*{\\underleftarrow{lim}}",rc["\\gvertneqq"]="\\html@mathml{\\@gvertneqq}{≩}",rc["\\lvertneqq"]="\\html@mathml{\\@lvertneqq}{≨}",rc["\\ngeqq"]="\\html@mathml{\\@ngeqq}{≱}",rc["\\ngeqslant"]="\\html@mathml{\\@ngeqslant}{≱}",rc["\\nleqq"]="\\html@mathml{\\@nleqq}{≰}",rc["\\nleqslant"]="\\html@mathml{\\@nleqslant}{≰}",rc["\\nshortmid"]="\\html@mathml{\\@nshortmid}{∤}",rc["\\nshortparallel"]="\\html@mathml{\\@nshortparallel}{∦}",rc["\\nsubseteqq"]="\\html@mathml{\\@nsubseteqq}{⊈}",rc["\\nsupseteqq"]="\\html@mathml{\\@nsupseteqq}{⊉}",rc["\\varsubsetneq"]="\\html@mathml{\\@varsubsetneq}{⊊}",rc["\\varsubsetneqq"]="\\html@mathml{\\@varsubsetneqq}{⫋}",rc["\\varsupsetneq"]="\\html@mathml{\\@varsupsetneq}{⊋}",rc["\\varsupsetneqq"]="\\html@mathml{\\@varsupsetneqq}{⫌}",rc["\\imath"]="\\html@mathml{\\@imath}{ı}",rc["\\jmath"]="\\html@mathml{\\@jmath}{ȷ}",rc["\\llbracket"]="\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}",rc["\\rrbracket"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}",rc["⟦"]="\\llbracket",rc["⟧"]="\\rrbracket",rc["\\lBrace"]="\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}",rc["\\rBrace"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}",rc["⦃"]="\\lBrace",rc["⦄"]="\\rBrace",rc["\\minuso"]="\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}",rc["⦵"]="\\minuso",rc["\\darr"]="\\downarrow",rc["\\dArr"]="\\Downarrow",rc["\\Darr"]="\\Downarrow",rc["\\lang"]="\\langle",rc["\\rang"]="\\rangle",rc["\\uarr"]="\\uparrow",rc["\\uArr"]="\\Uparrow",rc["\\Uarr"]="\\Uparrow",rc["\\N"]="\\mathbb{N}",rc["\\R"]="\\mathbb{R}",rc["\\Z"]="\\mathbb{Z}",rc["\\alef"]="\\aleph",rc["\\alefsym"]="\\aleph",rc["\\Alpha"]="\\mathrm{A}",rc["\\Beta"]="\\mathrm{B}",rc["\\bull"]="\\bullet",rc["\\Chi"]="\\mathrm{X}",rc["\\clubs"]="\\clubsuit",rc["\\cnums"]="\\mathbb{C}",rc["\\Complex"]="\\mathbb{C}",rc["\\Dagger"]="\\ddagger",rc["\\diamonds"]="\\diamondsuit",rc["\\empty"]="\\emptyset",rc["\\Epsilon"]="\\mathrm{E}",rc["\\Eta"]="\\mathrm{H}",rc["\\exist"]="\\exists",rc["\\harr"]="\\leftrightarrow",rc["\\hArr"]="\\Leftrightarrow",rc["\\Harr"]="\\Leftrightarrow",rc["\\hearts"]="\\heartsuit",rc["\\image"]="\\Im",rc["\\infin"]="\\infty",rc["\\Iota"]="\\mathrm{I}",rc["\\isin"]="\\in",rc["\\Kappa"]="\\mathrm{K}",rc["\\larr"]="\\leftarrow",rc["\\lArr"]="\\Leftarrow",rc["\\Larr"]="\\Leftarrow",rc["\\lrarr"]="\\leftrightarrow",rc["\\lrArr"]="\\Leftrightarrow",rc["\\Lrarr"]="\\Leftrightarrow",rc["\\Mu"]="\\mathrm{M}",rc["\\natnums"]="\\mathbb{N}",rc["\\Nu"]="\\mathrm{N}",rc["\\Omicron"]="\\mathrm{O}",rc["\\plusmn"]="\\pm",rc["\\rarr"]="\\rightarrow",rc["\\rArr"]="\\Rightarrow",rc["\\Rarr"]="\\Rightarrow",rc["\\real"]="\\Re",rc["\\reals"]="\\mathbb{R}",rc["\\Reals"]="\\mathbb{R}",rc["\\Rho"]="\\mathrm{P}",rc["\\sdot"]="\\cdot",rc["\\sect"]="\\S",rc["\\spades"]="\\spadesuit",rc["\\sub"]="\\subset",rc["\\sube"]="\\subseteq",rc["\\supe"]="\\supseteq",rc["\\Tau"]="\\mathrm{T}",rc["\\thetasym"]="\\vartheta",rc["\\weierp"]="\\wp",rc["\\Zeta"]="\\mathrm{Z}",rc["\\argmin"]="\\DOTSB\\operatorname*{arg\\,min}",rc["\\argmax"]="\\DOTSB\\operatorname*{arg\\,max}",rc["\\plim"]="\\DOTSB\\mathop{\\operatorname{plim}}\\limits",rc["\\bra"]="\\mathinner{\\langle{#1}|}",rc["\\ket"]="\\mathinner{|{#1}\\rangle}",rc["\\braket"]="\\mathinner{\\langle{#1}\\rangle}",rc["\\Bra"]="\\left\\langle#1\\right|",rc["\\Ket"]="\\left|#1\\right\\rangle";let lt=e=>t=>{let r=t.consumeArg().tokens,l=t.consumeArg().tokens,n=t.consumeArg().tokens,s=t.consumeArg().tokens,a=t.macros.get("|"),i=t.macros.get("\\|");t.macros.beginGroup();let o=t=>r=>{e&&(r.macros.set("|",a),n.length&&r.macros.set("\\|",i));let s=t;return!t&&n.length&&"|"===r.future().text&&(r.popToken(),s=!0),{tokens:s?n:l,numArgs:0}};t.macros.set("|",o(!1)),n.length&&t.macros.set("\\|",o(!0));let h=t.consumeArg().tokens,m=t.expandTokens([...s,...h,...r]);return t.macros.endGroup(),{tokens:m.reverse(),numArgs:0}};e=lt(!1),rc["\\bra@ket"]=e,t=lt(!0),rc["\\bra@set"]=t,rc["\\Braket"]="\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}",rc["\\Set"]="\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}",rc["\\set"]="\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}",rc["\\angln"]="{\\angl n}",rc["\\blue"]="\\textcolor{##6495ed}{#1}",rc["\\orange"]="\\textcolor{##ffa500}{#1}",rc["\\pink"]="\\textcolor{##ff00af}{#1}",rc["\\red"]="\\textcolor{##df0030}{#1}",rc["\\green"]="\\textcolor{##28ae7b}{#1}",rc["\\gray"]="\\textcolor{gray}{#1}",rc["\\purple"]="\\textcolor{##9d38bd}{#1}",rc["\\blueA"]="\\textcolor{##ccfaff}{#1}",rc["\\blueB"]="\\textcolor{##80f6ff}{#1}",rc["\\blueC"]="\\textcolor{##63d9ea}{#1}",rc["\\blueD"]="\\textcolor{##11accd}{#1}",rc["\\blueE"]="\\textcolor{##0c7f99}{#1}",rc["\\tealA"]="\\textcolor{##94fff5}{#1}",rc["\\tealB"]="\\textcolor{##26edd5}{#1}",rc["\\tealC"]="\\textcolor{##01d1c1}{#1}",rc["\\tealD"]="\\textcolor{##01a995}{#1}",rc["\\tealE"]="\\textcolor{##208170}{#1}",rc["\\greenA"]="\\textcolor{##b6ffb0}{#1}",rc["\\greenB"]="\\textcolor{##8af281}{#1}",rc["\\greenC"]="\\textcolor{##74cf70}{#1}",rc["\\greenD"]="\\textcolor{##1fab54}{#1}",rc["\\greenE"]="\\textcolor{##0d923f}{#1}",rc["\\goldA"]="\\textcolor{##ffd0a9}{#1}",rc["\\goldB"]="\\textcolor{##ffbb71}{#1}",rc["\\goldC"]="\\textcolor{##ff9c39}{#1}",rc["\\goldD"]="\\textcolor{##e07d10}{#1}",rc["\\goldE"]="\\textcolor{##a75a05}{#1}",rc["\\redA"]="\\textcolor{##fca9a9}{#1}",rc["\\redB"]="\\textcolor{##ff8482}{#1}",rc["\\redC"]="\\textcolor{##f9685d}{#1}",rc["\\redD"]="\\textcolor{##e84d39}{#1}",rc["\\redE"]="\\textcolor{##bc2612}{#1}",rc["\\maroonA"]="\\textcolor{##ffbde0}{#1}",rc["\\maroonB"]="\\textcolor{##ff92c6}{#1}",rc["\\maroonC"]="\\textcolor{##ed5fa6}{#1}",rc["\\maroonD"]="\\textcolor{##ca337c}{#1}",rc["\\maroonE"]="\\textcolor{##9e034e}{#1}",rc["\\purpleA"]="\\textcolor{##ddd7ff}{#1}",rc["\\purpleB"]="\\textcolor{##c6b9fc}{#1}",rc["\\purpleC"]="\\textcolor{##aa87ff}{#1}",rc["\\purpleD"]="\\textcolor{##7854ab}{#1}",rc["\\purpleE"]="\\textcolor{##543b78}{#1}",rc["\\mintA"]="\\textcolor{##f5f9e8}{#1}",rc["\\mintB"]="\\textcolor{##edf2df}{#1}",rc["\\mintC"]="\\textcolor{##e0e5cc}{#1}",rc["\\grayA"]="\\textcolor{##f6f7f7}{#1}",rc["\\grayB"]="\\textcolor{##f0f1f2}{#1}",rc["\\grayC"]="\\textcolor{##e3e5e6}{#1}",rc["\\grayD"]="\\textcolor{##d6d8da}{#1}",rc["\\grayE"]="\\textcolor{##babec2}{#1}",rc["\\grayF"]="\\textcolor{##888d93}{#1}",rc["\\grayG"]="\\textcolor{##626569}{#1}",rc["\\grayH"]="\\textcolor{##3b3e40}{#1}",rc["\\grayI"]="\\textcolor{##21242c}{#1}",rc["\\kaBlue"]="\\textcolor{##314453}{#1}",rc["\\kaGreen"]="\\textcolor{##71B307}{#1}";let lr={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class ll{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new r3(rc,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new r6(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){let t,r,l;if(e){if(this.consumeSpaces(),"["!==this.future().text)return null;t=this.popToken(),{tokens:l,end:r}=this.consumeArg(["]"])}else({tokens:l,start:t,end:r}=this.consumeArg());return this.pushToken(new ru("EOF",r.loc)),this.pushTokens(l),t.range(r,"")}consumeSpaces(){for(;;)if(" "===this.future().text)this.stack.pop();else break}consumeArg(e){let t;let r=[],l=e&&e.length>0;l||this.consumeSpaces();let s=this.future(),a=0,i=0;do{if(t=this.popToken(),r.push(t),"{"===t.text)++a;else if("}"===t.text){if(-1==--a)throw new n("Extra }",t)}else if("EOF"===t.text)throw new n("Unexpected end of input in a macro argument, expected '"+(e&&l?e[i]:"}")+"'",t);if(e&&l){if((0===a||1===a&&"{"===e[i])&&t.text===e[i]){if(++i===e.length){r.splice(-i,i);break}}else i=0}}while(0!==a||l);return"{"===s.text&&"}"===r[r.length-1].text&&(r.pop(),r.shift()),r.reverse(),{tokens:r,start:s,end:t}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new n("The length of delimiters doesn't match the number of args!");let r=t[0];for(let e=0;e<r.length;e++){let t=this.popToken();if(r[e]!==t.text)throw new n("Use of the macro doesn't match its definition",t)}}let r=[];for(let l=0;l<e;l++)r.push(this.consumeArg(t&&t[l+1]).tokens);return r}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new n("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){let t=this.popToken(),r=t.text,l=t.noexpand?null:this._getExpansion(r);if(null==l||e&&l.unexpandable){if(e&&null==l&&"\\"===r[0]&&!this.isDefined(r))throw new n("Undefined control sequence: "+r);return this.pushToken(t),!1}this.countExpansion(1);let s=l.tokens,a=this.consumeArgs(l.numArgs,l.delimiters);if(l.numArgs){s=s.slice();for(let e=s.length-1;e>=0;--e){let t=s[e];if("#"===t.text){if(0===e)throw new n("Incomplete placeholder at end of macro body",t);if("#"===(t=s[--e]).text)s.splice(e+1,1);else if(/^[1-9]$/.test(t.text))s.splice(e,2,...a[+t.text-1]);else throw new n("Not a valid argument number",t)}}}return this.pushTokens(s),s.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(!1===this.expandOnce()){let e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw Error()}expandMacro(e){return this.macros.has(e)?this.expandTokens([new ru(e)]):void 0}expandTokens(e){let t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(!1===this.expandOnce(!0)){let e=this.stack.pop();e.treatAsRelax&&(e.noexpand=!1,e.treatAsRelax=!1),t.push(e)}return this.countExpansion(t.length),t}expandMacroAsText(e){let t=this.expandMacro(e);return t?t.map(e=>e.text).join(""):t}_getExpansion(e){let t=this.macros.get(e);if(null==t)return t;if(1===e.length){let t=this.lexer.catcodes[e];if(null!=t&&13!==t)return}let r="function"==typeof t?t(this):t;if("string"==typeof r){let e=0;if(-1!==r.indexOf("#")){let t=r.replace(/##/g,"");for(;-1!==t.indexOf("#"+(e+1));)++e}let t=new r6(r,this.settings),l=[],n=t.lex();for(;"EOF"!==n.text;)l.push(n),n=t.lex();return l.reverse(),{tokens:l,numArgs:e}}return r}isDefined(e){return this.macros.has(e)||eG.hasOwnProperty(e)||ea.math.hasOwnProperty(e)||ea.text.hasOwnProperty(e)||lr.hasOwnProperty(e)}isExpandable(e){let t=this.macros.get(e);return null!=t?"string"==typeof t||"function"==typeof t||!t.unexpandable:eG.hasOwnProperty(e)&&!eG[e].primitive}}let ln=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,ls=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9",ₐ:"a",ₑ:"e",ₕ:"h",ᵢ:"i",ⱼ:"j",ₖ:"k",ₗ:"l",ₘ:"m",ₙ:"n",ₒ:"o",ₚ:"p",ᵣ:"r",ₛ:"s",ₜ:"t",ᵤ:"u",ᵥ:"v",ₓ:"x",ᵦ:"β",ᵧ:"γ",ᵨ:"ρ",ᵩ:"ϕ",ᵪ:"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","\xb9":"1","\xb2":"2","\xb3":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9",ᴬ:"A",ᴮ:"B",ᴰ:"D",ᴱ:"E",ᴳ:"G",ᴴ:"H",ᴵ:"I",ᴶ:"J",ᴷ:"K",ᴸ:"L",ᴹ:"M",ᴺ:"N",ᴼ:"O",ᴾ:"P",ᴿ:"R",ᵀ:"T",ᵁ:"U",ⱽ:"V",ᵂ:"W",ᵃ:"a",ᵇ:"b",ᶜ:"c",ᵈ:"d",ᵉ:"e",ᶠ:"f",ᵍ:"g",ʰ:"h",ⁱ:"i",ʲ:"j",ᵏ:"k",ˡ:"l",ᵐ:"m",ⁿ:"n",ᵒ:"o",ᵖ:"p",ʳ:"r",ˢ:"s",ᵗ:"t",ᵘ:"u",ᵛ:"v",ʷ:"w",ˣ:"x",ʸ:"y",ᶻ:"z",ᵝ:"β",ᵞ:"γ",ᵟ:"δ",ᵠ:"ϕ",ᵡ:"χ",ᶿ:"θ"}),la={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},li={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class lo{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new ll(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(void 0===t&&(t=!0),this.fetch().text!==e)throw new n("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return null==this.nextToken&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{let e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){let t=this.nextToken;this.consume(),this.gullet.pushToken(new ru("}")),this.gullet.pushTokens(e);let r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){let r=[];for(;;){"math"===this.mode&&this.consumeSpaces();let l=this.fetch();if(-1!==lo.endOfExpression.indexOf(l.text)||t&&l.text===t||e&&eG[l.text]&&eG[l.text].infix)break;let n=this.parseAtom(t);if(n){if("internal"===n.type)continue}else break;r.push(n)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){let t,r=-1;for(let l=0;l<e.length;l++)if("infix"===e[l].type){if(-1!==r)throw new n("only one infix operator per group",e[l].token);r=l,t=e[l].replaceWith}if(-1===r||!t)return e;{let l,n,s;let a=e.slice(0,r),i=e.slice(r+1);return l=1===a.length&&"ordgroup"===a[0].type?a[0]:{type:"ordgroup",mode:this.mode,body:a},n=1===i.length&&"ordgroup"===i[0].type?i[0]:{type:"ordgroup",mode:this.mode,body:i},["\\\\abovefrac"===t?this.callFunction(t,[l,e[r],n],[]):this.callFunction(t,[l,n],[])]}}handleSupSubscript(e){let t;let r=this.fetch(),l=r.text;this.consume(),this.consumeSpaces();do{var s;t=this.parseGroup(e)}while((null==(s=t)?void 0:s.type)==="internal");if(!t)throw new n("Expected group after '"+l+"'",r);return t}formatUnsupportedCmd(e){let t=[];for(let r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});let r={type:"text",mode:this.mode,body:t};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[r]}}parseAtom(e){let t,r;let l=this.parseGroup("atom",e);if((null==l?void 0:l.type)==="internal"||"text"===this.mode)return l;for(;;){this.consumeSpaces();let e=this.fetch();if("\\limits"===e.text||"\\nolimits"===e.text){if(l&&"op"===l.type)l.limits="\\limits"===e.text,l.alwaysHandleSupSub=!0;else if(l&&"operatorname"===l.type)l.alwaysHandleSupSub&&(l.limits="\\limits"===e.text);else throw new n("Limit controls must follow a math operator",e);this.consume()}else if("^"===e.text){if(t)throw new n("Double superscript",e);t=this.handleSupSubscript("superscript")}else if("_"===e.text){if(r)throw new n("Double subscript",e);r=this.handleSupSubscript("subscript")}else if("'"===e.text){if(t)throw new n("Double superscript",e);let r={type:"textord",mode:this.mode,text:"\\prime"},l=[r];for(this.consume();"'"===this.fetch().text;)l.push(r),this.consume();"^"===this.fetch().text&&l.push(this.handleSupSubscript("superscript")),t={type:"ordgroup",mode:this.mode,body:l}}else if(ls[e.text]){let l=ln.test(e.text),n=[];for(n.push(new ru(ls[e.text])),this.consume();;){let e=this.fetch().text;if(!ls[e]||ln.test(e)!==l)break;n.unshift(new ru(ls[e])),this.consume()}let s=this.subparse(n);l?r={type:"ordgroup",mode:"math",body:s}:t={type:"ordgroup",mode:"math",body:s}}else break}return t||r?{type:"supsub",mode:this.mode,base:l,sup:t,sub:r}:l}parseFunction(e,t){let r=this.fetch(),l=r.text,s=eG[l];if(!s)return null;if(this.consume(),t&&"atom"!==t&&!s.allowedInArgument)throw new n("Got function '"+l+"' with no arguments"+(t?" as "+t:""),r);if("text"!==this.mode||s.allowedInText){if("math"===this.mode&&!1===s.allowedInMath)throw new n("Can't use function '"+l+"' in math mode",r)}else throw new n("Can't use function '"+l+"' in text mode",r);let{args:a,optArgs:i}=this.parseArguments(l,s);return this.callFunction(l,a,i,r,e)}callFunction(e,t,r,l,s){let a=eG[e];if(a&&a.handler)return a.handler({funcName:e,parser:this,token:l,breakOnTokenText:s},t,r);throw new n("No function handler for "+e)}parseArguments(e,t){let r=t.numArgs+t.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};let l=[],s=[];for(let a=0;a<r;a++){let r=t.argTypes&&t.argTypes[a],i=a<t.numOptionalArgs;(t.primitive&&null==r||"sqrt"===t.type&&1===a&&null==s[0])&&(r="primitive");let o=this.parseGroupOfType("argument to '"+e+"'",r,i);if(i)s.push(o);else if(null!=o)l.push(o);else throw new n("Null argument, please report this as a bug")}return{args:l,optArgs:s}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":{let e=this.parseArgumentGroup(r,"text");return null!=e?{type:"styling",mode:e.mode,body:[e],style:"text"}:null}case"raw":{let e=this.parseStringGroup("raw",r);return null!=e?{type:"raw",mode:"text",string:e.text}:null}case"primitive":{if(r)throw new n("A primitive argument cannot be optional");let t=this.parseGroup(e);if(null==t)throw new n("Expected group as "+e,this.fetch());return t}case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new n("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;" "===this.fetch().text;)this.consume()}parseStringGroup(e,t){let r;let l=this.gullet.scanArgument(t);if(null==l)return null;let n="";for(;"EOF"!==(r=this.fetch()).text;)n+=r.text,this.consume();return this.consume(),l.text=n,l}parseRegexGroup(e,t){let r;let l=this.fetch(),s=l,a="";for(;"EOF"!==(r=this.fetch()).text&&e.test(a+r.text);)a+=(s=r).text,this.consume();if(""===a)throw new n("Invalid "+t+": '"+l.text+"'",l);return l.range(s,a)}parseColorGroup(e){let t=this.parseStringGroup("color",e);if(null==t)return null;let r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new n("Invalid color: '"+t.text+"'",t);let l=r[0];return/^[0-9a-f]{6}$/i.test(l)&&(l="#"+l),{type:"color-token",mode:this.mode,color:l}}parseSizeGroup(e){let t;let r=!1;if(this.gullet.consumeSpaces(),!(t=e||"{"===this.gullet.future().text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",r=!0);let l=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!l)throw new n("Invalid size: '"+t.text+"'",t);let s={number:+(l[1]+l[2]),unit:l[3]};if(!F(s))throw new n("Invalid unit: '"+s.unit+"'",t);return{type:"size",mode:this.mode,value:s,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);let t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),null==t)return null;let r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){let r=this.gullet.scanArgument(e);if(null==r)return null;let l=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();let n=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();let s={type:"ordgroup",mode:this.mode,loc:r.loc,body:n};return t&&this.switchMode(l),s}parseGroup(e,t){let r;let l=this.fetch(),s=l.text;if("{"===s||"\\begingroup"===s){this.consume();let e="{"===s?"}":"\\endgroup";this.gullet.beginGroup();let t=this.parseExpression(!1,e),n=this.fetch();this.expect(e),this.gullet.endGroup(),r={type:"ordgroup",mode:this.mode,loc:rd.range(l,n),body:t,semisimple:"\\begingroup"===s||void 0}}else if(null==(r=this.parseFunction(t,e)||this.parseSymbol())&&"\\"===s[0]&&!lr.hasOwnProperty(s)){if(this.settings.throwOnError)throw new n("Undefined control sequence: "+s,l);r=this.formatUnsupportedCmd(s),this.consume()}return r}formLigatures(e){let t=e.length-1;for(let r=0;r<t;++r){let l=e[r],n=l.text;"-"===n&&"-"===e[r+1].text&&(r+1<t&&"-"===e[r+2].text?(e.splice(r,3,{type:"textord",mode:"text",loc:rd.range(l,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:rd.range(l,e[r+1]),text:"--"}),t-=1)),("'"===n||"`"===n)&&e[r+1].text===n&&(e.splice(r,2,{type:"textord",mode:"text",loc:rd.range(l,e[r+1]),text:n+n}),t-=1)}}parseSymbol(){let e;let t=this.fetch(),r=t.text;if(/^\\verb[^a-zA-Z]/.test(r)){this.consume();let e=r.slice(5),t="*"===e.charAt(0);if(t&&(e=e.slice(1)),e.length<2||e.charAt(0)!==e.slice(-1))throw new n("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:e=e.slice(1,-1),star:t}}li.hasOwnProperty(r[0])&&!ea[this.mode][r[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+r[0]+'" used in math mode',t),r=li[r[0]]+r.slice(1));let l=r4.exec(r);if(l&&("i"===(r=r.substring(0,l.index))?r="ı":"j"===r&&(r="ȷ")),ea[this.mode][r]){let l;this.settings.strict&&"math"===this.mode&&ez.indexOf(r)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+r[0]+'" used in math mode',t);let n=ea[this.mode][r].group,s=rd.range(t);e=en.hasOwnProperty(n)?{type:"atom",mode:this.mode,family:n,loc:s,text:r}:{type:n,mode:this.mode,loc:s,text:r}}else{if(!(r.charCodeAt(0)>=128))return null;this.settings.strict&&(M(r.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+r[0]+'" used in math mode',t):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+r[0]+'" ('+r.charCodeAt(0)+")",t)),e={type:"textord",mode:"text",loc:rd.range(t),text:r}}if(this.consume(),l)for(let r=0;r<l[0].length;r++){let s=l[0][r];if(!la[s])throw new n("Unknown accent ' "+s+"'",t);let a=la[s][this.mode]||la[s].text;if(!a)throw new n("Accent "+s+" unsupported in "+this.mode+" mode",t);e={type:"accent",mode:this.mode,loc:rd.range(t),label:a,isStretchy:!1,isShifty:!0,base:e}}return e}}lo.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var lh=function(e,t){if(!("string"==typeof e||e instanceof String))throw TypeError("KaTeX can only parse string typed expression");let r=new lo(e,t);delete r.gullet.macros.current["\\df@tag"];let l=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new n("\\tag works only in display equations");l=[{type:"tag",mode:"text",body:l,tag:r.subparse([new ru("\\df@tag")])}]}return l};let lm=function(e,t,r){t.textContent="";let l=ld(e,r).toNode();t.appendChild(l)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),lm=function(){throw new n("KaTeX doesn't work in quirks mode.")});let lc=function(e,t,r){if(r.throwOnError||!(e instanceof n))throw e;let l=eD.makeSpan(["katex-error"],[new J(t)]);return l.setAttribute("title",e.toString()),l.setAttribute("style","color:"+r.errorColor),l},ld=function(e,t){let r=new d(t);try{let t=lh(e,r);return tb(t,e,r)}catch(t){return lc(t,e,r)}};var lu={version:"0.16.22",render:lm,renderToString:function(e,t){return ld(e,t).toMarkup()},ParseError:n,SETTINGS_SCHEMA:c,__parse:function(e,t){return lh(e,new d(t))},__renderToDomTree:ld,__renderToHTMLTree:function(e,t){let r=new d(t);try{let t=lh(e,r);return ty(t,e,r)}catch(t){return lc(t,e,r)}},__setFontMetrics:function(e,t){j[e]=t},__defineSymbol:ei,__defineFunction:eY,__defineMacro:function(e,t){rc[e]=t},__domTree:{Span:Y,Anchor:Z,SymbolNode:J,SvgNode:ee,PathNode:et,LineNode:er}};return l.default}()},9005:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=(0,r(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14583:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var l=r(60687);r(43210);var n=r(29523),s=r(47033),a=r(14952);function i({currentPage:e,totalPages:t,onPageChange:r,pageSize:i,totalItems:o,onPageSizeChange:h,pageSizeOptions:m=[5,10,20,50]}){let c=Math.min(o,(e-1)*i+1),d=Math.min(o,e*i);return(0,l.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,l.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:o>0?(0,l.jsxs)("p",{children:["Showing ",c," to ",d," of ",o," items"]}):(0,l.jsx)("p",{children:"No items"})}),(0,l.jsxs)("div",{className:"flex items-center space-x-6",children:[h&&(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,l.jsx)("select",{className:"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm",value:i,onChange:e=>h(Number(e.target.value)),children:m.map(e=>(0,l.jsx)("option",{value:e,children:e},e))})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>r(e-1),disabled:1===e,className:"h-8 w-8 p-0",children:[(0,l.jsx)(s.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Previous page"})]}),(()=>{let r=[];if(t<=5)for(let e=1;e<=t;e++)r.push(e);else{r.push(1),e>3&&r.push("ellipsis");let l=Math.max(2,e-1),n=Math.min(t-1,e+1);for(let e=l;e<=n;e++)r.push(e);e<t-2&&r.push("ellipsis"),t>1&&r.push(t)}return r})().map((t,s)=>"ellipsis"===t?(0,l.jsx)("span",{className:"px-2",children:"..."},`ellipsis-${s}`):(0,l.jsx)(n.$,{variant:e===t?"default":"outline",size:"sm",onClick:()=>r(t),className:"h-8 w-8 p-0",children:t},`page-${t}`)),(0,l.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>r(e+1),disabled:e===t||0===t,className:"h-8 w-8 p-0",children:[(0,l.jsx)(a.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"sr-only",children:"Next page"})]})]})]})]})}},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});let l=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17507:(e,t,r)=>{Promise.resolve().then(r.bind(r,78646))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28340:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var l=r(60687),n=r(43210),s=r.n(n),a=r(63239),i=r(29523),o=r(96474),h=r(15079),m=r(12048),c=r(99270),d=r(9005),u=r(11860),p=r(44493),g=r(96834),f=r(63143),x=r(88233),b=r(82080),y=r(3589),w=r(78272),v=r(62688);let k=(0,v.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var S=r(29045),M=r(4780);function z({src:e,alt:t="Image",className:r,maxWidth:s=300,maxHeight:a=200,onClick:i}){let[o,h]=(0,n.useState)(!1),[m,c]=(0,n.useState)(!0),d=(0,S.B_)(e);return!d||o?(0,l.jsx)("div",{className:(0,M.cn)("flex items-center justify-center bg-gray-100 border border-gray-200 rounded-md text-gray-500 text-sm",r),style:{maxWidth:s,maxHeight:Math.min(a,100)},children:o?"Failed to load image":"Invalid image"}):(0,l.jsxs)("div",{className:(0,M.cn)("relative inline-block",r),children:[m&&(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 border border-gray-200 rounded-md",style:{maxWidth:s,maxHeight:a},children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"})}),(0,l.jsx)("img",{src:d,alt:t,className:(0,M.cn)("rounded-md border border-gray-200 object-contain",i&&"cursor-pointer hover:opacity-80 transition-opacity",m&&"opacity-0"),style:{maxWidth:s,maxHeight:a,display:m?"none":"block"},onLoad:()=>c(!1),onError:()=>{h(!0),c(!1)},onClick:i})]})}var A=r(75105);function T({text:e,className:t}){if(!e)return null;let r=function(e){let t;let r=[],n=0,s=0,a=e.replace(/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g,e=>{try{let t=e.trim(),r=(t=t.replace(/<br\s*\/?>/gi," ")).split("|").map(e=>e.trim()).filter(e=>e);if(r.length<4)return e;let l=[],n=[];for(let e=0;e<r.length;e++){let t=r[e];if(t.match(/^:?-+:?$/)){n.length>0&&(l.push("| "+n.join(" | ")+" |"),n=[]);let e=Array(Math.max(2,n.length||2)).fill(":--");l.push("| "+e.join(" | ")+" |");continue}t.match(/^\d+/)&&n.length>=2?(n.length>0&&l.push("| "+n.join(" | ")+" |"),n=[t]):n.push(t)}if(n.length>0&&l.push("| "+n.join(" | ")+" |"),l.length>=3)return"\n\n"+l.join("\n")+"\n\n";return e}catch(t){return console.warn("Error fixing malformed table:",t),e}}),i=/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g,o=/<table[\s\S]*?<\/table>/gi,h=[];for(;null!==(t=i.exec(a));)h.push({start:t.index,end:t.index+t[0].length,content:t[0],type:"markdown"});for(;null!==(t=o.exec(e));)h.push({start:t.index,end:t.index+t[0].length,content:t[0],type:"html"});for(let t of(h.sort((e,t)=>e.start-t.start),h)){if(n<t.start){let l=N(e.slice(n,t.start),s);r.push(...l.elements),s=l.nextKey}if("markdown"===t.type){let e=function(e,t){let r=function(e){try{let t=e.trim().split("\n").filter(e=>e.trim());if(t.length<2)return null;let r=t[0].split("|").map(e=>e.trim()).filter(e=>e),l=t[1].split("|").map(e=>{let t=e.trim();return t.startsWith(":")&&t.endsWith(":")?"center":t.endsWith(":")?"right":"left"}).filter((e,t)=>t<r.length),n=[];for(let e=2;e<t.length;e++){let l=t[e].split("|").map(e=>e.trim()).filter(e=>e);if(l.length>0){for(;l.length<r.length;)l.push("");n.push(l.slice(0,r.length))}}return{headers:r,rows:n,alignments:l}}catch(e){return console.warn("Error parsing markdown table:",e),null}}(e);if(!r)return null;let{headers:n,rows:s,alignments:a}=r;return(0,l.jsx)("div",{className:"my-4 overflow-x-auto",children:(0,l.jsxs)("table",{className:"min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg",children:[(0,l.jsx)("thead",{className:"bg-gray-50",children:(0,l.jsx)("tr",{children:n.map((e,t)=>(0,l.jsx)("th",{className:(0,M.cn)("border border-gray-300 px-4 py-2 font-semibold text-gray-900","center"===a[t]&&"text-center","right"===a[t]&&"text-right","left"===a[t]&&"text-left"),children:e},t))})}),(0,l.jsx)("tbody",{children:s.map((e,t)=>(0,l.jsx)("tr",{className:t%2==0?"bg-white":"bg-gray-50",children:e.map((e,t)=>(0,l.jsx)("td",{className:(0,M.cn)("border border-gray-300 px-4 py-2 text-gray-700","center"===a[t]&&"text-center","right"===a[t]&&"text-right","left"===a[t]&&"text-left"),children:(0,l.jsx)(T,{text:e})},t))},t))})]})},t)}(t.content,s++);e&&r.push(e)}else{var m,c;let e=(m=t.content,c=s++,(0,l.jsx)("div",{className:"my-4 overflow-x-auto",dangerouslySetInnerHTML:{__html:m.replace(/<table/g,'<table class="min-w-full border-collapse border border-gray-300 bg-white shadow-sm rounded-lg"').replace(/<th/g,'<th class="border border-gray-300 px-4 py-2 font-semibold text-gray-900 bg-gray-50"').replace(/<td/g,'<td class="border border-gray-300 px-4 py-2 text-gray-700"')}},c));e&&r.push(e)}n=t.end}if(n<e.length){let t=N(e.slice(n),s);r.push(...t.elements)}return r}(e);return(0,l.jsx)("div",{className:(0,M.cn)("enhanced-text-renderer",t),children:r})}function N(e,t){let r=[],n=t;for(let t of e.split(/(\$\$[\s\S]*?\$\$|\$[^$]*?\$)/))if(t.startsWith("$$")&&t.endsWith("$$")){let e=t.slice(2,-2).trim();try{r.push((0,l.jsx)("div",{className:"my-2 katex-isolated",children:(0,l.jsx)(A.BlockMath,{math:e,errorColor:"#dc2626",renderError:e=>(0,l.jsxs)("div",{className:"p-2 bg-red-50 border border-red-200 rounded text-red-700",children:["Error rendering math: ",e.message]})})},n++))}catch(t){console.warn("Error rendering block math:",t),r.push((0,l.jsxs)("div",{className:"my-2 p-2 bg-red-50 border border-red-200 rounded text-red-700",children:["Error rendering math: ",e]},n++))}}else if(t.startsWith("$")&&t.endsWith("$")&&t.length>2){let e=t.slice(1,-1).trim();try{r.push((0,l.jsx)(A.InlineMath,{math:e,errorColor:"#dc2626",renderError:e=>(0,l.jsxs)("span",{className:"px-1 bg-red-50 border border-red-200 rounded text-red-700",children:["Error: ",e.message]})},n++))}catch(t){console.warn("Error rendering inline math:",t),r.push((0,l.jsxs)("span",{className:"px-1 bg-red-50 border border-red-200 rounded text-red-700",children:["Error: ",e]},n++))}}else if(t.trim()){let e=t.split("\n").map((e,t,r)=>(0,l.jsxs)(s().Fragment,{children:[e,t<r.length-1&&(0,l.jsx)("br",{})]},`${n}-line-${t}`));r.push((0,l.jsx)("span",{children:e},n++))}return{elements:r,nextKey:n}}function C({text:e,className:t,imageClassName:r,maxImageWidth:n=300,maxImageHeight:s=200,questionImages:a}){let{cleanText:i,images:o}=(0,S.Xw)(e,a);return(0,l.jsxs)("div",{className:(0,M.cn)("space-y-3",t),children:[i&&(0,l.jsx)("div",{className:"text-base",children:(0,l.jsx)(T,{text:i})}),o.length>0&&(0,l.jsx)("div",{className:"space-y-2",children:o.map(e=>(0,l.jsx)("div",{className:"flex flex-col space-y-1",children:(0,l.jsx)(z,{src:e.src,alt:e.alt,className:r,maxWidth:n,maxHeight:s})},e.id))})]})}r(55583);let j=(0,v.A)("beaker",[["path",{d:"M4.5 3h15",key:"c7n0jr"}],["path",{d:"M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3",key:"m1uhx7"}],["path",{d:"M6 14h12",key:"4cwo0f"}]]);function q({text:e,images:t={},className:r,imageClassName:s,maxImageWidth:a=400,maxImageHeight:o=300,showImageToggle:h=!0}){let[m,c]=(0,n.useState)(!0),[d,u]=(0,n.useState)(new Set),{cleanText:p,imageRefs:g}=(e=>{let r;if(!e)return{cleanText:"",imageRefs:[]};let l=e,n=[],s=/\[CHEMICAL_IMAGE_(\d+)_([^\]]+)\]/g;for(;null!==(r=s.exec(e));){let[e,s,a]=r,i=`chemical_img_${s}_${a}`;t[i]?(n.push(i),l=l.replace(e,`[IMAGE_PLACEHOLDER_${i}]`)):l=l.replace(e,"")}return{cleanText:l=l.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,r,l)=>{let s=Object.keys(t).find(e=>e.includes(l)||l.includes(e.replace("chemical_img_","")));return s?(n.push(s),`[IMAGE_PLACEHOLDER_${s}]`):`[Missing Image: ${l}]`}),imageRefs:[...new Set(n)]}})(e),f=g.length>0,x=e=>{u(t=>{let r=new Set(t);return r.has(e)?r.delete(e):r.add(e),r})};return(0,l.jsxs)("div",{className:(0,M.cn)("space-y-3",r),children:[f&&h&&(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,l.jsx)(j,{className:"h-4 w-4"}),g.length," Chemical Structure",1!==g.length?"s":""]}),(0,l.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>c(!m),className:"h-7 px-3 text-xs",children:m?"Hide Images":"Show Images"})]}),(0,l.jsx)("div",{className:"text-base leading-relaxed",children:p?p.split(/(\[IMAGE_PLACEHOLDER_[^\]]+\])/).map((e,r)=>{let n=e.match(/\[IMAGE_PLACEHOLDER_([^\]]+)\]/);if(n){let e=n[1],h=t[e];if(h&&m){let t=d.has(e);return(0,l.jsxs)("div",{className:"my-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-blue-700",children:[(0,l.jsx)(j,{className:"h-4 w-4"}),"Chemical Structure"]}),(0,l.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>x(e),className:"h-6 px-2 text-blue-600 hover:text-blue-700",children:t?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Collapse"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(w.A,{className:"h-3 w-3 mr-1"}),"Expand"]})})]}),(0,l.jsx)(z,{src:h,alt:`Chemical structure ${e}`,className:(0,M.cn)("border border-blue-300 rounded-md transition-all duration-200",s,t?"cursor-zoom-out":"cursor-zoom-in"),maxWidth:t?1.5*a:a,maxHeight:t?1.5*o:o,onClick:()=>x(e)})]},r)}return(0,l.jsxs)("div",{className:"my-2 p-2 bg-gray-100 border border-gray-200 rounded text-sm text-gray-600",children:["[Chemical Structure - ",e,"]"]},r)}return e?(0,l.jsx)("span",{children:(0,l.jsx)(T,{text:e})},r):null}):null}),m&&g.length>0&&(0,l.jsx)("div",{className:"space-y-3",children:g.map(e=>{if(p.includes(`[IMAGE_PLACEHOLDER_${e}]`))return null;let r=t[e];if(!r)return null;let n=d.has(e);return(0,l.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-green-700",children:[(0,l.jsx)(j,{className:"h-4 w-4"}),"Chemical Structure - ",e.replace("chemical_img_","").replace(/_/g," ")]}),(0,l.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>x(e),className:"h-6 px-2 text-green-600 hover:text-green-700",children:n?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Collapse"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(w.A,{className:"h-3 w-3 mr-1"}),"Expand"]})})]}),(0,l.jsx)(z,{src:r,alt:`Chemical structure ${e}`,className:(0,M.cn)("border border-green-300 rounded-md transition-all duration-200",s,n?"cursor-zoom-out":"cursor-zoom-in"),maxWidth:n?1.5*a:a,maxHeight:n?1.5*o:o,onClick:()=>x(e)})]},e)})})]})}function B({option:e,images:t={},isCorrect:r=!1,className:n}){let s=e.imageUrl&&"string"==typeof e.imageUrl&&t[e.imageUrl]||e.imageUrl;return(0,l.jsxs)("div",{className:(0,M.cn)("flex items-start p-3 rounded-md border transition-colors",r?"border-green-500 bg-green-50":"border-gray-200 hover:border-green-300",n),children:[(0,l.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:(0,l.jsx)("span",{className:"text-sm font-medium",children:e.label})}),(0,l.jsxs)("div",{className:"flex-1",children:[e.text&&!e.isImageOption&&(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(q,{text:e.text,images:t,maxImageWidth:200,maxImageHeight:150,showImageToggle:!1})}),s&&(0,l.jsx)("div",{className:e.isImageOption?"":"mt-2",children:(0,l.jsxs)("div",{className:"p-2 bg-green-50 border border-green-200 rounded-md",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 mb-2 text-xs text-green-700",children:[(0,l.jsx)(j,{className:"h-3 w-3"}),"Chemical Structure Option"]}),(0,l.jsx)(z,{src:s,alt:`Option ${e.label} - Chemical Structure`,maxWidth:200,maxHeight:150,className:"border border-green-300 rounded"})]})}),e.isImageOption&&!s&&(0,l.jsx)("div",{className:"text-gray-500 italic text-sm",children:"Chemical structure option (image not available)"})]})]})}var I=r(6862),E=r(94487),O=r(31981),H=r(20140),R=r(16189);function L({questions:e,onDifficultyChange:t,onReviewStatusChange:r,onQuestionDeleted:s}){let a=(0,R.useRouter)(),[o,m]=(0,n.useState)(!1),[c,d]=(0,n.useState)(null),[u,v]=(0,n.useState)(!1),[S,M]=(0,n.useState)(new Set),[A,T]=(0,n.useState)(new Set),N=e=>{M(t=>{let r=new Set(t);return r.has(e)?r.delete(e):r.add(e),r})},j=e=>{T(t=>{let r=new Set(t);return r.has(e)?r.delete(e):r.add(e),r})},L=e=>{d(e),m(!0)},P=e=>{a.push(`/admin/edit-question/${e}`)},D=async()=>{if(c)try{v(!0);let e=await (0,E.ul)(c);(0,O.cY)(e)&&s&&s(c)}catch(e){console.error("Unexpected error deleting question:",e),(0,H.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{v(!1),m(!1),d(null)}};return(0,l.jsxs)("div",{className:"space-y-4",children:[e.map(e=>(0,l.jsx)(p.Zp,{className:"overflow-hidden",children:(0,l.jsx)(p.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-wrap items-center justify-between gap-2",children:[(0,l.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,l.jsx)(g.E,{variant:"outline",className:"font-normal",children:e.subject}),(0,l.jsx)(g.E,{variant:"outline",className:"font-normal",children:e.chapter}),e.reviewStatus&&(0,l.jsx)(g.E,{className:"approved"===e.reviewStatus?"bg-green-100 text-green-800 hover:bg-green-100":"rejected"===e.reviewStatus?"bg-red-100 text-red-800 hover:bg-red-100":"bg-yellow-100 text-yellow-800 hover:bg-yellow-100",children:e.reviewStatus.charAt(0).toUpperCase()+e.reviewStatus.slice(1)})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)(h.l6,{defaultValue:e.difficulty.toLowerCase(),onValueChange:r=>t(e.id,r),children:[(0,l.jsx)(h.bq,{className:"w-[110px]",children:(0,l.jsx)(h.yv,{placeholder:"Difficulty"})}),(0,l.jsxs)(h.gC,{children:[(0,l.jsx)(h.eb,{value:"easy",children:"Easy"}),(0,l.jsx)(h.eb,{value:"medium",children:"Medium"}),(0,l.jsx)(h.eb,{value:"hard",children:"Hard"})]})]}),(0,l.jsxs)(h.l6,{defaultValue:e.reviewStatus.toLowerCase(),onValueChange:t=>r(e.id,t),children:[(0,l.jsx)(h.bq,{className:"w-[110px]",children:(0,l.jsx)(h.yv,{placeholder:"Review Status"})}),(0,l.jsxs)(h.gC,{children:[(0,l.jsx)(h.eb,{value:"pending",children:"Pending"}),(0,l.jsx)(h.eb,{value:"approved",children:"Approved"}),(0,l.jsx)(h.eb,{value:"rejected",children:"Rejected"})]})]}),(0,l.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>P(e.id),title:"Edit question",children:(0,l.jsx)(f.A,{className:"h-4 w-4"})}),(0,l.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>L(e.id),title:"Delete question",className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,l.jsx)(x.A,{className:"h-4 w-4"})})]})]}),(0,l.jsx)("div",{className:"font-medium",children:e.isChemical&&(e.chemicalImages||e.imageData)?(0,l.jsx)(q,{text:e.text,images:e.imageData||e.chemicalImages,maxImageWidth:400,maxImageHeight:300}):(0,l.jsx)(C,{text:e.text,maxImageWidth:400,maxImageHeight:300,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})}),(0,l.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:e.options.map((t,r)=>e.isChemical&&(e.chemicalImages||e.imageData)?(0,l.jsx)(B,{option:t,images:e.imageData||e.chemicalImages,isCorrect:t.text===e.correctAnswer},r):(0,l.jsxs)("div",{className:`flex items-start p-3 rounded-md border ${t.text===e.correctAnswer?"border-green-500 bg-green-50":"border-gray-200"}`,children:[(0,l.jsx)("div",{className:"flex-shrink-0 w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3",children:(0,l.jsx)("span",{className:"text-sm",children:t.label})}),(0,l.jsxs)("div",{className:"flex-1",children:[t.text&&!t.isImageOption&&(0,l.jsx)("div",{className:"mb-2",children:(0,l.jsx)(C,{text:t.text,maxImageWidth:200,maxImageHeight:150,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})}),t.imageUrl&&(0,l.jsx)("div",{className:t.isImageOption?"":"mt-2",children:(0,l.jsx)(z,{src:t.imageUrl,alt:`Option ${t.label}`,maxWidth:200,maxHeight:150,className:"border-0"})}),t.isImageOption&&!t.imageUrl&&(0,l.jsx)("div",{className:"text-gray-500 italic",children:"Image option"})]})]},r))}),e.solution&&(0,l.jsxs)("div",{className:"border-t pt-4",children:[(0,l.jsxs)(i.$,{variant:"ghost",onClick:()=>N(e.id),className:"flex items-center gap-2 p-0 h-auto font-medium text-blue-600 hover:text-blue-700",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),"Solution",S.has(e.id)?(0,l.jsx)(y.A,{className:"h-4 w-4"}):(0,l.jsx)(w.A,{className:"h-4 w-4"})]}),S.has(e.id)&&(0,l.jsxs)("div",{className:"mt-3 space-y-3 bg-blue-50 p-4 rounded-lg",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-1",children:"Methodology:"}),(0,l.jsx)("p",{className:"text-sm text-gray-700",children:e.solution.methodology})]}),e.solution.key_concepts&&e.solution.key_concepts.length>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-1",children:"Key Concepts:"}),(0,l.jsx)("div",{className:"flex flex-wrap gap-1",children:e.solution.key_concepts.map((e,t)=>(0,l.jsx)(g.E,{variant:"secondary",className:"text-xs",children:e},t))})]}),e.solution.steps&&e.solution.steps.length>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-2",children:"Solution Steps:"}),(0,l.jsx)("div",{className:"space-y-2",children:e.solution.steps.map((t,r)=>(0,l.jsx)("div",{className:"text-sm text-gray-700",children:(0,l.jsx)(C,{text:t,maxImageWidth:300,maxImageHeight:200,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})},r))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:"font-medium text-sm text-blue-800 mb-1",children:"Final Explanation:"}),(0,l.jsx)("div",{className:"text-sm text-gray-700",children:(0,l.jsx)(C,{text:e.solution.final_explanation,maxImageWidth:300,maxImageHeight:200,questionImages:e.imageUrls&&Array.isArray(e.imageUrls)?{"image-1":e.imageUrls[0]}:e.imageData||e.chemicalImages})})]})]})]}),e.hints&&e.hints.length>0&&(0,l.jsxs)("div",{className:"border-t pt-4",children:[(0,l.jsxs)(i.$,{variant:"ghost",onClick:()=>j(e.id),className:"flex items-center gap-2 p-0 h-auto font-medium text-amber-600 hover:text-amber-700",children:[(0,l.jsx)(k,{className:"h-4 w-4"}),"Hints (",e.hints.length,")",A.has(e.id)?(0,l.jsx)(y.A,{className:"h-4 w-4"}):(0,l.jsx)(w.A,{className:"h-4 w-4"})]}),A.has(e.id)&&(0,l.jsx)("div",{className:"mt-3 space-y-2 bg-amber-50 p-4 rounded-lg",children:e.hints.map((e,t)=>(0,l.jsx)("div",{className:"text-sm text-gray-700",children:(0,l.jsx)(C,{text:e,maxImageWidth:300,maxImageHeight:200})},t))})]})]})})},e.id)),(0,l.jsx)(I.Lt,{open:o,onOpenChange:m,children:(0,l.jsxs)(I.EO,{children:[(0,l.jsxs)(I.wd,{children:[(0,l.jsx)(I.r7,{children:"Are you sure you want to delete this question?"}),(0,l.jsx)(I.$v,{children:"This action cannot be undone. This will permanently delete the question and all associated data."})]}),(0,l.jsxs)(I.ck,{children:[(0,l.jsx)(I.Zr,{children:"Cancel"}),(0,l.jsx)(I.Rx,{onClick:D,className:"bg-red-600 hover:bg-red-700",disabled:u,children:u?"Deleting...":"Delete"})]})]})})]})}var P=r(14583),D=r(85726);function F(){return(0,l.jsx)(p.Zp,{children:(0,l.jsx)(p.Wu,{className:"p-6",children:(0,l.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(D.E,{className:"h-6 w-20"}),(0,l.jsx)(D.E,{className:"h-6 w-20"})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(D.E,{className:"h-8 w-24"}),(0,l.jsx)(D.E,{className:"h-8 w-8"}),(0,l.jsx)(D.E,{className:"h-8 w-8"})]})]}),(0,l.jsx)(D.E,{className:"h-6 w-full"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[(0,l.jsx)(D.E,{className:"h-16 w-full"}),(0,l.jsx)(D.E,{className:"h-16 w-full"}),(0,l.jsx)(D.E,{className:"h-16 w-full"}),(0,l.jsx)(D.E,{className:"h-16 w-full"})]})]})})})}function V(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)([]),[a,o]=(0,n.useState)("all_subjects"),[p,g]=(0,n.useState)("all_chapters"),[f,x]=(0,n.useState)(""),[b,y]=(0,n.useState)(void 0),[w,v]=(0,n.useState)([]),[k,M]=(0,n.useState)({currentPage:1,totalPages:1,totalItems:0,itemsPerPage:10}),[z,A]=(0,n.useState)(10),[T,N]=(0,n.useState)(!0),[C,j]=(0,n.useState)(0),q=async(e,t)=>{try{let r=await fetch(`http://localhost:3000/api/questions/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("backendToken")}`},body:JSON.stringify({difficulty:t})});if(!r.ok)throw Error(`Failed to update question: ${r.status}`);v(r=>r.map(r=>r._id===e?{...r,difficulty:t}:r)),(0,H.o)({title:"Success",description:"Question difficulty updated successfully"})}catch(e){console.error("Error updating question difficulty:",e),(0,H.o)({title:"Error",description:"Failed to update question difficulty",variant:"destructive"})}},B=async(e,t)=>{try{await (0,E.v5)(e,t),v(r=>r.map(r=>r._id===e?{...r,reviewStatus:t}:r)),(0,H.o)({title:"Success",description:`Question ${t} successfully`})}catch(e){console.error("Error updating question review status:",e),(0,H.o)({title:"Error",description:"Failed to update question review status",variant:"destructive"})}},I=(e,t)=>{if(!e||!t||0===t.length)return e||"";let r=e;return!(r=(r=r.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,r)=>t.length>0?`<img src="${t[0]}" alt="${r||"Question Image"}" style="max-width: 300px; height: auto; display: block; margin: 10px auto;" />`:e)).replace(/<img[^>]*src=["']([^"']*)["'][^>]*>/gi,e=>t.length>0?`<img src="${t[0]}" alt="Question Image" style="max-width: 300px; height: auto; display: block; margin: 10px auto;" />`:e)).includes("<img")&&t.length>0&&/image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(r)&&(r+=`
<img src="${t[0]}" alt="Question Image" style="max-width: 300px; height: auto; display: block; margin: 10px auto;" />`),r},O=w.map(e=>{try{let t=[],r=Array.isArray(e.options)?e.options.filter(e=>null!=e):[];return r.length>0?t="string"==typeof r[0]?1===r.length&&r[0].includes(",")?r[0].split(",").map((e,t)=>{let r=e.trim();return(0,S.XI)(r)?{label:String.fromCharCode(97+t),text:"",imageUrl:(0,S.UF)(r),isImageOption:!0}:{label:String.fromCharCode(97+t),text:r}}):r.map((e,t)=>{let r=e.trim();return(0,S.XI)(r)?{label:String.fromCharCode(97+t),text:"",imageUrl:(0,S.UF)(r),isImageOption:!0}:{label:String.fromCharCode(97+t),text:r}}):r.map((e,t)=>({label:String.fromCharCode(97+t),text:"string"==typeof e?e:e&&e.text||"",imageUrl:"object"==typeof e&&e?e.imageUrl:void 0})):(console.warn(`Question ${e._id} has no valid options:`,e.options),t=[{label:"a",text:"No options available"},{label:"b",text:"No options available"},{label:"c",text:"No options available"},{label:"d",text:"No options available"}]),{id:e._id,subject:e.subjectId.name,chapter:e.chapterId?.name||e.topicId?.name||"No Chapter",text:I(e.content,e.imageUrls||[]),options:t,difficulty:e.difficulty.charAt(0).toUpperCase()+e.difficulty.slice(1),correctAnswer:e.answer,reviewStatus:e.reviewStatus,solution:e.solution,hints:e.hints}}catch(t){return console.error(`Error formatting question ${e._id}:`,t,e),{id:e._id||"unknown",subject:e.subjectId?.name||"Unknown Subject",chapter:e.chapterId?.name||e.topicId?.name||"No Chapter",text:I(e.content||"Error loading question content",e.imageUrls||[]),options:[{label:"a",text:"Error loading options"},{label:"b",text:"Error loading options"},{label:"c",text:"Error loading options"},{label:"d",text:"Error loading options"}],difficulty:e.difficulty||"Unknown",correctAnswer:e.answer||"a",reviewStatus:e.reviewStatus||"pending",solution:e.solution,hints:e.hints}}});return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Subject"}),(0,l.jsxs)(h.l6,{value:a,onValueChange:o,children:[(0,l.jsx)(h.bq,{children:(0,l.jsx)(h.yv,{placeholder:"Select Subject"})}),(0,l.jsxs)(h.gC,{children:[(0,l.jsx)(h.eb,{value:"all_subjects",children:"All Subjects"}),e.map(e=>(0,l.jsx)(h.eb,{value:e._id,children:e.name},e._id))]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Chapter"}),(0,l.jsxs)(h.l6,{value:p,onValueChange:g,disabled:"all_subjects"===a||0===r.length,children:[(0,l.jsx)(h.bq,{children:(0,l.jsx)(h.yv,{placeholder:"all_subjects"!==a?"Select Chapter":"Select Subject First"})}),(0,l.jsxs)(h.gC,{children:[(0,l.jsx)(h.eb,{value:"all_chapters",children:"All Chapters"}),r.map(e=>(0,l.jsx)(h.eb,{value:e._id,children:e.name},e._id))]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Search"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(c.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(m.p,{type:"text",placeholder:"Search questions...",className:"pl-8",value:f,onChange:e=>x(e.target.value)})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("label",{className:"text-sm font-medium",children:"Filter by images:"}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)(i.$,{variant:!0===b?"default":"outline",size:"sm",onClick:()=>y(!0),className:"flex items-center gap-1",children:[(0,l.jsx)(d.A,{className:"h-4 w-4"}),"With Images"]}),(0,l.jsxs)(i.$,{variant:!1===b?"default":"outline",size:"sm",onClick:()=>y(!1),className:"flex items-center gap-1",children:[(0,l.jsx)(u.A,{className:"h-4 w-4"}),"Without Images"]}),(0,l.jsx)(i.$,{variant:void 0===b?"default":"outline",size:"sm",onClick:()=>y(void 0),children:"All Questions"})]})]})]}),T?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(F,{}),(0,l.jsx)(F,{}),(0,l.jsx)(F,{})]}):O.length>0?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(L,{questions:O,onDifficultyChange:q,onReviewStatusChange:B,onQuestionDeleted:e=>{v(t=>t.filter(t=>t._id!==e)),M(e=>({...e,totalItems:e.totalItems-1,totalPages:Math.ceil((e.totalItems-1)/z)}))}}),k.totalItems>0&&(0,l.jsx)(P.d,{currentPage:k.currentPage,totalPages:k.totalPages,pageSize:z,totalItems:k.totalItems,onPageChange:e=>{M(t=>({...t,currentPage:e}))},onPageSizeChange:e=>{A(e),M(t=>({...t,currentPage:1,itemsPerPage:e}))},pageSizeOptions:[5,10,20,50]})]}):(0,l.jsx)("div",{className:"text-center py-8",children:(0,l.jsx)("p",{className:"text-muted-foreground",children:"No questions found. Try adjusting your filters."})})]})}var _=r(85814),U=r.n(_);let $=()=>(0,l.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,l.jsxs)("div",{className:"container mx-auto px-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Question Bank"}),(0,l.jsx)(a.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Question Bank"}],className:"text-sm mt-1"})]}),(0,l.jsx)(U(),{href:"/admin/add-question",children:(0,l.jsxs)(i.$,{className:"bg-[#05603A] hover:bg-[#04502F] text-white",children:["Add Questions",(0,l.jsx)(o.A,{className:"w-4 h-4 ml-2"})]})})]}),(0,l.jsx)("div",{className:"container mx-auto py-10",children:(0,l.jsx)(V,{})})]})})},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29045:(e,t,r)=>{"use strict";function l(e){return!!e&&"string"==typeof e&&(!!/^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i.test(e)||e.length>100&&/^[A-Za-z0-9+/]*={0,2}$/.test(e))}function n(e){if(!e)return"";let t=/^(data:image\/[^;]+;base64,)(data:image\/[^;]+;base64,)/;t.test(e)&&(e=e.replace(t,"$2"));let r=e.match(/^(data:image\/[^;]+;base64,)+/);if(r){let t=r[0].length,l=e.substring(t),n=r[0].match(/data:image\/[^;]+;base64,$/);if(n)return n[0]+l}return e.startsWith("data:image/")?e:`data:image/jpeg;base64,${e}`}function s(e,t){if(!e)return{cleanText:e,images:[]};let r=[],s=e,a=[...e.matchAll(/<img\s+[^>]*src=["']([^"']+)["'][^>]*(?:alt=["']([^"']*)["'])?[^>]*\/?>/gi)];a.length>0&&a.forEach((e,t)=>{let a=e[0],i=e[1],o=e[2]||`Image ${r.length+1}`;if((i=i.trim()).includes("base64,")||l(i)){let e=`extracted-image-html-${t}`,l=n(i);r.push({id:e,src:l,alt:o}),s=s.replace(a,"")}});let i=[...s.matchAll(/!\[([^\]]*)\]\(([^)]+)\)/g)];i.length>0&&i.forEach((e,a)=>{let i=e[0],o=e[1]||`Image ${r.length+1}`,h=e[2];if((h=h.trim()).includes("base64,")||l(h)){let e=`extracted-image-markdown-${a}`,t=n(h);r.push({id:e,src:t,alt:o}),s=s.replace(i,"")}else if(t){let e=function(e,t){if(t[e])return e;for(let r of[e,e.replace(".jpeg","").replace(".jpg","").replace(".png",""),`img-${e}`,`image-${e}`,e.replace("img-","").replace("image-","")])for(let e of Object.keys(t))if(e.includes(r)||r.includes(e))return e;let r=e.match(/\d+/g);if(r){for(let e of r)for(let r of Object.keys(t))if(r.includes(e))return r}return null}(h,t);if(e&&t[e]){let l=`extracted-image-ref-${a}`,h=n(t[e]);r.push({id:l,src:h,alt:o}),s=s.replace(i,"")}else s=s.replace(i,`[Missing Image: ${h}]`)}else s=s.replace(i,`[Image: ${h}]`)});let o=s.match(/data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g);o&&o.forEach((e,t)=>{if(l(e)){let l=`extracted-image-dataurl-${t}`,a=n(e);r.push({id:l,src:a,alt:`Extracted image ${r.length+1}`}),s=s.replace(e,"")}});let h=s.match(/\b[A-Za-z0-9+/]{200,}={0,2}\b/g);return h&&h.forEach((e,t)=>{if(l(e)){let l=`extracted-image-raw-${t}`,a=n(e);r.push({id:l,src:a,alt:`Extracted image ${r.length+1}`}),s=s.replace(e,"")}}),{cleanText:s=s.replace(/\s+/g," ").trim(),images:r}}function a(e){if(!e||!l(e))return null;try{return n(e)}catch(e){return console.warn("Invalid base64 image source:",e),null}}r.d(t,{B_:()=>a,UF:()=>n,XI:()=>l,Xw:()=>s})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},55511:e=>{"use strict";e.exports=require("crypto")},55583:()=>{},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75105:function(e,t,r){var l,n,s;s=function(e,t,r,l){"use strict";function n(e){return e&&e.__esModule?e:{default:e}}function s(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(s=function(e){return e?r:t})(e)}Object.defineProperty(e,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{BlockMath:()=>m,InlineMath:()=>c}),t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=s(t);if(r&&r.has(e))return r.get(e);var l={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=n?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(l,a,i):l[a]=e[a]}return l.default=e,r&&r.set(e,l),l}(t),r=n(r),l=n(l);let a=(e,{displayMode:n})=>{let s=({children:r,errorColor:s,math:a,renderError:i})=>{let o=null!=a?a:r,{html:h,error:m}=(0,t.useMemo)(()=>{try{return{html:l.default.renderToString(o,{displayMode:n,errorColor:s,throwOnError:!!i}),error:void 0}}catch(e){if(e instanceof l.default.ParseError||e instanceof TypeError)return{error:e};throw e}},[o,s,i]);return m?i?i(m):t.default.createElement(e,{html:`${m.message}`}):t.default.createElement(e,{html:h})};return s.propTypes={children:r.default.string,errorColor:r.default.string,math:r.default.string,renderError:r.default.func},s},i={html:r.default.string.isRequired},o=({html:e})=>t.default.createElement("div",{"data-testid":"react-katex",dangerouslySetInnerHTML:{__html:e}});o.propTypes=i;let h=({html:e})=>t.default.createElement("span",{"data-testid":"react-katex",dangerouslySetInnerHTML:{__html:e}});h.propTypes=i;let m=a(o,{displayMode:!0}),c=a(h,{displayMode:!1})},"object"==typeof e.exports?s(t,r(43210),r(87955),r(6601)):(l=[t,r(43210),r(87955),r(6601)],void 0===(n=s.apply(t,l))||(e.exports=n))},78646:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});let l=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\question-bank\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83163:(e,t,r)=>{Promise.resolve().then(r.bind(r,28340))},83997:e=>{"use strict";e.exports=require("tty")},84031:(e,t,r)=>{"use strict";var l=r(34452);function n(){}function s(){}s.resetWarningCache=n,e.exports=function(){function e(e,t,r,n,s,a){if(a!==l){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:n};return r.PropTypes=r,r}},87955:(e,t,r)=>{e.exports=r(84031)()},94735:e=>{"use strict";e.exports=require("events")},97579:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>d,tree:()=>h});var l=r(65239),n=r(48088),s=r(88170),a=r.n(s),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let h={children:["",{children:["admin",{children:["question-bank",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,78646)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},d=new l.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/question-bank/page",pathname:"/admin/question-bank",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),l=t.X(0,[4447,4619,3287,9592,2581,6822,4707,6658,5091,9371],()=>r(97579));module.exports=l})();