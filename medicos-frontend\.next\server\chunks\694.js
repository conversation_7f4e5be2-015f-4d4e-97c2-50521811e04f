"use strict";exports.id=694,exports.ids=[694],exports.modules={16023:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},41862:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},65822:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},74797:(e,t,r)=>{r.d(t,{C1:()=>ep,q7:()=>ef,bL:()=>ed});var n=r(43210),o=r.t(n,2);function i(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function a(...e){return n.useCallback(u(...e),e)}var c=r(60687);function s(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let i=n.createContext(o),l=r.length;r=[...r,o];let u=t=>{let{scope:r,children:o,...u}=t,a=r?.[e]?.[l]||i,s=n.useMemo(()=>u,Object.values(u));return(0,c.jsx)(a.Provider,{value:s,children:o})};return u.displayName=t+"Provider",[u,function(r,u){let a=u?.[e]?.[l]||i,c=n.useContext(a);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}function d(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var i;let e,l;let a=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(c.ref=t?u(t,a):a),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,l=n.Children.toArray(o),u=l.find(p);if(u){let e=u.props.children,o=l.map(t=>t!==u?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,c.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,c.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}r(51215);var f=Symbol("radix.slottable");function p(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===f}var m=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=d(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,c.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),v=new WeakMap;function y(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,n=h(t),o=n>=0?n:r+n;return o<0||o>=r?-1:o}(e,t);return -1===r?void 0:e[r]}function h(e){return e!=e||0===e?0:Math.trunc(e)}var w=globalThis?.document?n.useLayoutEffect:()=>{},b=o[" useId ".trim().toString()]||(()=>void 0),g=0,R=o[" useInsertionEffect ".trim().toString()]||w;function x({prop:e,defaultProp:t,onChange:r=()=>{},caller:o}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[r,o]=n.useState(e),i=n.useRef(r),l=n.useRef(t);return R(()=>{l.current=t},[t]),n.useEffect(()=>{i.current!==r&&(l.current?.(r),i.current=r)},[r,i]),[r,o,l]}({defaultProp:t,onChange:r}),a=void 0!==e,c=a?e:i;{let t=n.useRef(void 0!==e);n.useEffect(()=>{let e=t.current;if(e!==a){let t=a?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=a},[a,o])}return[c,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u.current?.(r)}else l(t)},[a,e,l,u])]}Symbol("RADIX:SYNC_STATE");var A=n.createContext(void 0);function N(e){let t=n.useContext(A);return e||t||"ltr"}var E="rovingFocusGroup.onEntryFocus",k={bubbles:!1,cancelable:!0},C="RovingFocusGroup",[S,M,I]=function(e){let t=e+"CollectionProvider",[r,o]=s(t),[i,l]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,o=n.useRef(null),l=n.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:l,collectionRef:o,children:r})};u.displayName=t;let f=e+"CollectionSlot",p=d(f),m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=a(t,l(f,r).collectionRef);return(0,c.jsx)(p,{ref:o,children:n})});m.displayName=f;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=d(v),w=n.forwardRef((e,t)=>{let{scope:r,children:o,...i}=e,u=n.useRef(null),s=a(t,u),d=l(v,r);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...i}),()=>void d.itemMap.delete(u))),(0,c.jsx)(h,{[y]:"",ref:s,children:o})});return w.displayName=v,[{Provider:u,Slot:m,ItemSlot:w},function(t){let r=l(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},o]}(C),[j,T]=s(C,[I]),[O,D]=j(C),P=n.forwardRef((e,t)=>(0,c.jsx)(S.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(S.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,c.jsx)(F,{...e,ref:t})})}));P.displayName=C;var F=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:l=!1,dir:u,currentTabStopId:s,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:f,onEntryFocus:p,preventScrollOnEntryFocus:v=!1,...y}=e,h=n.useRef(null),w=a(t,h),b=N(u),[g,R]=x({prop:s,defaultProp:d??null,onChange:f,caller:C}),[A,S]=n.useState(!1),I=function(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}(p),j=M(r),T=n.useRef(!1),[D,P]=n.useState(0);return n.useEffect(()=>{let e=h.current;if(e)return e.addEventListener(E,I),()=>e.removeEventListener(E,I)},[I]),(0,c.jsx)(O,{scope:r,orientation:o,dir:b,loop:l,currentTabStopId:g,onItemFocus:n.useCallback(e=>R(e),[R]),onItemShiftTab:n.useCallback(()=>S(!0),[]),onFocusableItemAdd:n.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>P(e=>e-1),[]),children:(0,c.jsx)(m.div,{tabIndex:A||0===D?-1:0,"data-orientation":o,...y,ref:w,style:{outline:"none",...e.style},onMouseDown:i(e.onMouseDown,()=>{T.current=!0}),onFocus:i(e.onFocus,e=>{let t=!T.current;if(e.target===e.currentTarget&&t&&!A){let t=new CustomEvent(E,k);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);$([e.find(e=>e.active),e.find(e=>e.id===g),...e].filter(Boolean).map(e=>e.ref.current),v)}}T.current=!1}),onBlur:i(e.onBlur,()=>S(!1))})})}),L="RovingFocusGroupItem",_=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:l=!1,tabStopId:u,children:a,...s}=e,d=function(e){let[t,r]=n.useState(b());return w(()=>{r(e=>e??String(g++))},[void 0]),e||(t?`radix-${t}`:"")}(),f=u||d,p=D(L,r),v=p.currentTabStopId===f,y=M(r),{onFocusableItemAdd:h,onFocusableItemRemove:R,currentTabStopId:x}=p;return n.useEffect(()=>{if(o)return h(),()=>R()},[o,h,R]),(0,c.jsx)(S.ItemSlot,{scope:r,id:f,focusable:o,active:l,children:(0,c.jsx)(m.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...s,ref:t,onMouseDown:i(e.onMouseDown,e=>{o?p.onItemFocus(f):e.preventDefault()}),onFocus:i(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:i(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return U[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>$(r))}}),children:"function"==typeof a?a({isCurrentTabStop:v,hasTabStop:null!=x}):a})})});_.displayName=L;var U={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function $(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var W=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,i]=n.useState(),l=n.useRef(null),u=n.useRef(e),a=n.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=z(l.current);a.current="mounted"===c?e:"none"},[c]),w(()=>{let t=l.current,r=u.current;if(r!==e){let n=a.current,o=z(t);e?s("MOUNT"):"none"===o||t?.display==="none"?s("UNMOUNT"):r&&n!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),w(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=z(l.current).includes(r.animationName);if(r.target===o&&n&&(s("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(a.current=z(l.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}s("ANIMATION_END")},[o,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,i(e)},[])}}(t),i="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),l=a(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?n.cloneElement(i,{ref:l}):null};function z(e){return e?.animationName||"none"}W.displayName="Presence";var G="Radio",[K,B]=s(G),[V,q]=K(G),H=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:o,checked:l=!1,required:u,disabled:s,value:d="on",onCheck:f,form:p,...v}=e,[y,h]=n.useState(null),w=a(t,e=>h(e)),b=n.useRef(!1),g=!y||p||!!y.closest("form");return(0,c.jsxs)(V,{scope:r,checked:l,disabled:s,children:[(0,c.jsx)(m.button,{type:"button",role:"radio","aria-checked":l,"data-state":J(l),"data-disabled":s?"":void 0,disabled:s,value:d,...v,ref:w,onClick:i(e.onClick,e=>{l||f?.(),g&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),g&&(0,c.jsx)(Z,{control:y,bubbles:!b.current,name:o,value:d,checked:l,required:u,disabled:s,form:p,style:{transform:"translateX(-100%)"}})]})});H.displayName=G;var X="RadioIndicator",Y=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,i=q(X,r);return(0,c.jsx)(W,{present:n||i.checked,children:(0,c.jsx)(m.span,{"data-state":J(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});Y.displayName=X;var Z=n.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:o=!0,...i},l)=>{let u=n.useRef(null),s=a(u,l),d=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),f=function(e){let[t,r]=n.useState(void 0);return w(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(t);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(d!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[d,r,o]),(0,c.jsx)(m.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:s,style:{...i.style,...f,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function J(e){return e?"checked":"unchecked"}Z.displayName="RadioBubbleInput";var Q=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],ee="RadioGroup",[et,er]=s(ee,[T,B]),en=T(),eo=B(),[ei,el]=et(ee),eu=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:i,required:l=!1,disabled:u=!1,orientation:a,dir:s,loop:d=!0,onValueChange:f,...p}=e,v=en(r),y=N(s),[h,w]=x({prop:i,defaultProp:o??"",onChange:f,caller:ee});return(0,c.jsx)(ei,{scope:r,name:n,required:l,disabled:u,value:h,onValueChange:w,children:(0,c.jsx)(P,{asChild:!0,...v,orientation:a,dir:y,loop:d,children:(0,c.jsx)(m.div,{role:"radiogroup","aria-required":l,"aria-orientation":a,"data-disabled":u?"":void 0,dir:y,...p,ref:t})})})});eu.displayName=ee;var ea="RadioGroupItem",ec=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:o,...l}=e,u=el(ea,r),s=u.disabled||o,d=en(r),f=eo(r),p=n.useRef(null),m=a(t,p),v=u.value===l.value,y=n.useRef(!1);return n.useEffect(()=>{let e=e=>{Q.includes(e.key)&&(y.current=!0)},t=()=>y.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,c.jsx)(_,{asChild:!0,...d,focusable:!s,active:v,children:(0,c.jsx)(H,{disabled:s,required:u.required,checked:v,...f,...l,name:u.name,ref:m,onCheck:()=>u.onValueChange(l.value),onKeyDown:i(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:i(l.onFocus,()=>{y.current&&p.current?.click()})})})});ec.displayName=ea;var es=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=eo(r);return(0,c.jsx)(Y,{...o,...n,ref:t})});es.displayName="RadioGroupIndicator";var ed=eu,ef=ec,ep=es},93661:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},96882:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};