(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={1322:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:o,blurHeight:s,blurDataURL:n,objectFit:a}=e,i=o?40*o:t,l=s?40*s:r,d=i&&l?"viewBox='0 0 "+i+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+n+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,t,r)=>{let{createProxy:o}=r(39844);e.exports=o("C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4774:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eV});var o=r(37413),s=r(45572),n=r(4536),a=r.n(n),i=r(70099),l=r.n(i);function d(){return(0,o.jsxs)("div",{className:"flex flex-col space-y-6",children:[(0,o.jsx)("h1",{className:"text-3xl md:text-5xl font-extrabold leading-tight md:leading-none",style:{fontFamily:"Nunito, sans-serif"},children:'NEET & JEE Papers – Practice Smarter, Rank Higher!"'}),(0,o.jsx)("p",{className:"text-gray-600",children:"Download free NEET and JEE question papers, mock tests, and practice sets to boost your preparation. Study smart, practice more, and get exam-ready today."}),(0,o.jsx)("div",{children:(0,o.jsx)(a(),{href:"/login",className:"inline-block px-6 py-3 text-white font-medium rounded-md shadow-sm",style:{backgroundColor:"#84BF9F"},children:"Login / Create Account"})}),(0,o.jsxs)("div",{className:"flex items-center mt-4",children:[(0,o.jsxs)("div",{className:"flex -space-x-2 mr-3",children:[(0,o.jsx)(l(),{src:"/assets/landing-page/pro-user-1.png",alt:"User",width:32,height:32,className:"rounded-full border-2 border-white"}),(0,o.jsx)(l(),{src:"/assets/landing-page/pro-user-2.png",alt:"User",width:32,height:32,className:"rounded-full border-2 border-white"}),(0,o.jsx)(l(),{src:"/assets/landing-page/pro-user-3.png",alt:"User",width:32,height:32,className:"rounded-full border-2 border-white"})]}),(0,o.jsx)("span",{className:"font-medium",children:"3,500+ Pro Users"})]})]})}var c=r(26373);let m=(0,c.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function u(){return(0,o.jsx)("div",{className:"hidden lg:block bg-white shadow-md p-6 border border-gray-100",children:(0,o.jsx)(l(),{src:"/assets/landing-page/chart.png",alt:"User",width:600,height:500,className:" border-white"})})}function p(){return(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center mb-16",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h2",{className:"text-3xl md:text-5xl font-bold mb-6",style:{fontFamily:"Nunito, sans-serif"},children:"About Medicos"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6",children:"Medicos Edu Consultant Pvt. Ltd. is an educational consultancy specializing in NEET and JEE exam preparation. Their approach emphasizes personalized mentorship and strategic practice to help students achieve high rankings."}),(0,o.jsxs)(a(),{href:"#",className:"inline-flex items-center text-gray-700 hover:text-gray-900",children:["Readmore ",(0,o.jsx)(m,{className:"ml-2 h-4 w-4"})]})]}),(0,o.jsx)(u,{})]})}let h=(0,c.A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]]),g=(0,c.A)("atom",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["path",{d:"M20.2 20.2c2.04-2.03.02-7.36-4.5-11.9-4.54-4.52-9.87-6.54-11.9-4.5-2.04 2.03-.02 7.36 4.5 11.9 4.54 4.52 9.87 6.54 11.9 4.5Z",key:"1l2ple"}],["path",{d:"M15.7 15.7c4.52-4.54 6.54-9.87 4.5-11.9-2.03-2.04-7.36-.02-11.9 4.5-4.52 4.54-6.54 9.87-4.5 11.9 2.03 2.04 7.36.02 11.9-4.5Z",key:"1wam0m"}]]),f=(0,c.A)("flask-round",[["path",{d:"M10 2v6.292a7 7 0 1 0 4 0V2",key:"1s42pc"}],["path",{d:"M5 15h14",key:"m0yey3"}],["path",{d:"M8.5 2h7",key:"csnxdl"}]]),x=(0,c.A)("microscope",[["path",{d:"M6 18h8",key:"1borvv"}],["path",{d:"M3 22h18",key:"8prr45"}],["path",{d:"M14 22a7 7 0 1 0 0-14h-1",key:"1jwaiy"}],["path",{d:"M9 14h2",key:"197e7h"}],["path",{d:"M9 12a2 2 0 0 1-2-2V6h6v4a2 2 0 0 1-2 2Z",key:"1bmzmy"}],["path",{d:"M12 6V3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3",key:"1drr47"}]]);function b({title:e,icon:t,color:r}){let{border:s,bg:n,text:a}={purple:{border:"border-l-purple-200",bg:"bg-purple-100",text:"text-purple-500"},amber:{border:"border-l-amber-200",bg:"bg-amber-100",text:"text-amber-500"},red:{border:"border-l-red-200",bg:"bg-red-100",text:"text-red-500"},teal:{border:"border-l-teal-200",bg:"bg-teal-100",text:"text-teal-500"}}[r];return(0,o.jsxs)("div",{className:`bg-white rounded-lg shadow-md p-6 border-l-4 ${s} hover:shadow-lg transition-shadow`,children:[(0,o.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,o.jsx)("div",{className:`${n} p-3 rounded-lg`,children:(0,o.jsx)(t,{className:`h-6 w-6 ${a}`})})}),(0,o.jsx)("h3",{className:"text-3xl md:text-5xl font-semibold mb-2",style:{fontFamily:"Nunito, sans-serif"},children:e})]})}function v(){return(0,o.jsxs)("div",{className:"text-center mb-16",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-5xl font-bold text-center mb-12",style:{fontFamily:"Nunito, sans-serif"},children:"Lessons revolve around 4 areas"}),(0,o.jsx)("p",{className:"text-gray-600 mb-10 max-w-3xl mx-auto",children:"Comprehensive Lessons in Math, Physics, Chemistry & Biology"}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[{title:"Math",icon:h,color:"purple"},{title:"Physics",icon:g,color:"amber"},{title:"Chemistry",icon:f,color:"red"},{title:"Biology",icon:x,color:"teal"}].map((e,t)=>(0,o.jsx)(b,{title:e.title,icon:e.icon,color:e.color},t))})]})}function y(){return(0,o.jsx)("div",{className:"max-w-lg mx-auto overflow-x-auto",children:(0,o.jsx)("div",{className:"flex min-w-max justify-center space-x-6 border-b",children:[{name:"NEET",isActive:!0},{name:"JEE",isActive:!1},{name:"K-12",isActive:!1},{name:"CET",isActive:!1}].map(e=>(0,o.jsx)("button",{className:`px-6 py-3 font-semibold ${e.isActive?"text-green-600 border-b-2 border-green-600":"text-gray-500 hover:text-gray-700"}`,children:e.name},e.name))})})}let w=(0,c.A)("phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),j=(0,c.A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);r(61120);var N=r(45332);function k(){for(var e,t,r=0,o="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,o,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t){if(Array.isArray(t)){var n=t.length;for(r=0;r<n;r++)t[r]&&(o=e(t[r]))&&(s&&(s+=" "),s+=o)}else for(o in t)t[o]&&(s&&(s+=" "),s+=o)}return s}(e))&&(o&&(o+=" "),o+=t);return o}let C=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,z=e=>{let t=S(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),E(r,t)||_(e)},getConflictingClassGroupIds:(e,t)=>{let s=r[e]||[];return t&&o[e]?[...s,...o[e]]:s}}},E=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),s=o?E(e.slice(1),o):void 0;if(s)return s;if(0===t.validators.length)return;let n=e.join("-");return t.validators.find(({validator:e})=>e(n))?.classGroupId},P=/^\[(.+)\]$/,_=e=>{if(P.test(e)){let t=P.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},S=e=>{let{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(let e in r)M(r[e],o,e,t);return o},M=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:F(t,e)).classGroupId=r;return}if("function"==typeof e){if(A(e)){M(e(o),t,r,o);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,s])=>{M(s,F(t,e),r,o)})})},F=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},A=e=>e.isThemeGetter,O=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,s=(s,n)=>{r.set(s,n),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(s(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):s(e,t)}}},R=e=>{let{prefix:t,experimentalParseClassName:r}=e,o=e=>{let t;let r=[],o=0,s=0,n=0;for(let a=0;a<e.length;a++){let i=e[a];if(0===o&&0===s){if(":"===i){r.push(e.slice(n,a)),n=a+1;continue}if("/"===i){t=a;continue}}"["===i?o++:"]"===i?o--:"("===i?s++:")"===i&&s--}let a=0===r.length?e:e.substring(n),i=D(a);return{modifiers:r,hasImportantModifier:i!==a,baseClassName:i,maybePostfixModifierPosition:t&&t>n?t-n:void 0}};if(t){let e=t+":",r=o;o=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=o;o=t=>r({className:t,parseClassName:e})}return o},D=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,T=e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r}},I=e=>({cache:O(e.cacheSize),parseClassName:R(e),sortModifiers:T(e),...z(e)}),q=/\s+/,L=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:s,sortModifiers:n}=t,a=[],i=e.trim().split(q),l="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:u,maybePostfixModifierPosition:p}=r(t);if(d){l=t+(l.length>0?" "+l:l);continue}let h=!!p,g=o(h?u.substring(0,p):u);if(!g){if(!h||!(g=o(u))){l=t+(l.length>0?" "+l:l);continue}h=!1}let f=n(c).join(":"),x=m?f+"!":f,b=x+g;if(a.includes(b))continue;a.push(b);let v=s(g,h);for(let e=0;e<v.length;++e){let t=v[e];a.push(x+t)}l=t+(l.length>0?" "+l:l)}return l};function G(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=U(e))&&(o&&(o+=" "),o+=t);return o}let U=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=U(e[o]))&&(r&&(r+=" "),r+=t);return r},$=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},W=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,V=/^\((?:(\w[\w-]*):)?(.+)\)$/i,B=/^\d+\/\d+$/,J=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,H=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,X=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Y=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Z=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,K=e=>B.test(e),Q=e=>!!e&&!Number.isNaN(Number(e)),ee=e=>!!e&&Number.isInteger(Number(e)),et=e=>e.endsWith("%")&&Q(e.slice(0,-1)),er=e=>J.test(e),eo=()=>!0,es=e=>H.test(e)&&!X.test(e),en=()=>!1,ea=e=>Y.test(e),ei=e=>Z.test(e),el=e=>!ec(e)&&!ef(e),ed=e=>eN(e,e_,en),ec=e=>W.test(e),em=e=>eN(e,eS,es),eu=e=>eN(e,eM,Q),ep=e=>eN(e,eC,en),eh=e=>eN(e,eE,ei),eg=e=>eN(e,en,ea),ef=e=>V.test(e),ex=e=>ek(e,eS),eb=e=>ek(e,eF),ev=e=>ek(e,eC),ey=e=>ek(e,e_),ew=e=>ek(e,eE),ej=e=>ek(e,eA,!0),eN=(e,t,r)=>{let o=W.exec(e);return!!o&&(o[1]?t(o[1]):r(o[2]))},ek=(e,t,r=!1)=>{let o=V.exec(e);return!!o&&(o[1]?t(o[1]):r)},eC=e=>"position"===e,ez=new Set(["image","url"]),eE=e=>ez.has(e),eP=new Set(["length","size","percentage"]),e_=e=>eP.has(e),eS=e=>"length"===e,eM=e=>"number"===e,eF=e=>"family-name"===e,eA=e=>"shadow"===e;Symbol.toStringTag;let eO=function(e,...t){let r,o,s;let n=function(i){return o=(r=I(t.reduce((e,t)=>t(e),e()))).cache.get,s=r.cache.set,n=a,a(i)};function a(e){let t=o(e);if(t)return t;let n=L(e,r);return s(e,n),n}return function(){return n(G.apply(null,arguments))}}(()=>{let e=$("color"),t=$("font"),r=$("text"),o=$("font-weight"),s=$("tracking"),n=$("leading"),a=$("breakpoint"),i=$("container"),l=$("spacing"),d=$("radius"),c=$("shadow"),m=$("inset-shadow"),u=$("drop-shadow"),p=$("blur"),h=$("perspective"),g=$("aspect"),f=$("ease"),x=$("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],v=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],y=()=>["auto","hidden","clip","visible","scroll"],w=()=>["auto","contain","none"],j=()=>[ef,ec,l],N=()=>[K,"full","auto",...j()],k=()=>[ee,"none","subgrid",ef,ec],C=()=>["auto",{span:["full",ee,ef,ec]},ef,ec],z=()=>[ee,"auto",ef,ec],E=()=>["auto","min","max","fr",ef,ec],P=()=>["start","end","center","between","around","evenly","stretch","baseline"],_=()=>["start","end","center","stretch"],S=()=>["auto",...j()],M=()=>[K,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...j()],F=()=>[e,ef,ec],A=()=>[et,em],O=()=>["","none","full",d,ef,ec],R=()=>["",Q,ex,em],D=()=>["solid","dashed","dotted","double"],T=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],I=()=>["","none",p,ef,ec],q=()=>["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",ef,ec],L=()=>["none",Q,ef,ec],G=()=>["none",Q,ef,ec],U=()=>[Q,ef,ec],W=()=>[K,"full",...j()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[er],breakpoint:[er],color:[eo],container:[er],"drop-shadow":[er],ease:["in","out","in-out"],font:[el],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[er],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[er],shadow:[er],spacing:["px",Q],text:[er],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",K,ec,ef,g]}],container:["container"],columns:[{columns:[Q,ec,ef,i]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...v(),ec,ef]}],overflow:[{overflow:y()}],"overflow-x":[{"overflow-x":y()}],"overflow-y":[{"overflow-y":y()}],overscroll:[{overscroll:w()}],"overscroll-x":[{"overscroll-x":w()}],"overscroll-y":[{"overscroll-y":w()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:N()}],"inset-x":[{"inset-x":N()}],"inset-y":[{"inset-y":N()}],start:[{start:N()}],end:[{end:N()}],top:[{top:N()}],right:[{right:N()}],bottom:[{bottom:N()}],left:[{left:N()}],visibility:["visible","invisible","collapse"],z:[{z:[ee,"auto",ef,ec]}],basis:[{basis:[K,"full","auto",i,...j()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Q,K,"auto","initial","none",ec]}],grow:[{grow:["",Q,ef,ec]}],shrink:[{shrink:["",Q,ef,ec]}],order:[{order:[ee,"first","last","none",ef,ec]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:j()}],"gap-x":[{"gap-x":j()}],"gap-y":[{"gap-y":j()}],"justify-content":[{justify:[...P(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...P()]}],"align-items":[{items:[..._(),"baseline"]}],"align-self":[{self:["auto",..._(),"baseline"]}],"place-content":[{"place-content":P()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:j()}],px:[{px:j()}],py:[{py:j()}],ps:[{ps:j()}],pe:[{pe:j()}],pt:[{pt:j()}],pr:[{pr:j()}],pb:[{pb:j()}],pl:[{pl:j()}],m:[{m:S()}],mx:[{mx:S()}],my:[{my:S()}],ms:[{ms:S()}],me:[{me:S()}],mt:[{mt:S()}],mr:[{mr:S()}],mb:[{mb:S()}],ml:[{ml:S()}],"space-x":[{"space-x":j()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":j()}],"space-y-reverse":["space-y-reverse"],size:[{size:M()}],w:[{w:[i,"screen",...M()]}],"min-w":[{"min-w":[i,"screen","none",...M()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[a]},...M()]}],h:[{h:["screen",...M()]}],"min-h":[{"min-h":["screen","none",...M()]}],"max-h":[{"max-h":["screen",...M()]}],"font-size":[{text:["base",r,ex,em]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,ef,eu]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",et,ec]}],"font-family":[{font:[eb,ec,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,ef,ec]}],"line-clamp":[{"line-clamp":[Q,"none",ef,eu]}],leading:[{leading:[n,...j()]}],"list-image":[{"list-image":["none",ef,ec]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ef,ec]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:F()}],"text-color":[{text:F()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...D(),"wavy"]}],"text-decoration-thickness":[{decoration:[Q,"from-font","auto",ef,em]}],"text-decoration-color":[{decoration:F()}],"underline-offset":[{"underline-offset":[Q,"auto",ef,ec]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:j()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ef,ec]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ef,ec]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...v(),ev,ep]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:["auto","cover","contain",ey,ed]}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},ee,ef,ec],radial:["",ef,ec],conic:[ee,ef,ec]},ew,eh]}],"bg-color":[{bg:F()}],"gradient-from-pos":[{from:A()}],"gradient-via-pos":[{via:A()}],"gradient-to-pos":[{to:A()}],"gradient-from":[{from:F()}],"gradient-via":[{via:F()}],"gradient-to":[{to:F()}],rounded:[{rounded:O()}],"rounded-s":[{"rounded-s":O()}],"rounded-e":[{"rounded-e":O()}],"rounded-t":[{"rounded-t":O()}],"rounded-r":[{"rounded-r":O()}],"rounded-b":[{"rounded-b":O()}],"rounded-l":[{"rounded-l":O()}],"rounded-ss":[{"rounded-ss":O()}],"rounded-se":[{"rounded-se":O()}],"rounded-ee":[{"rounded-ee":O()}],"rounded-es":[{"rounded-es":O()}],"rounded-tl":[{"rounded-tl":O()}],"rounded-tr":[{"rounded-tr":O()}],"rounded-br":[{"rounded-br":O()}],"rounded-bl":[{"rounded-bl":O()}],"border-w":[{border:R()}],"border-w-x":[{"border-x":R()}],"border-w-y":[{"border-y":R()}],"border-w-s":[{"border-s":R()}],"border-w-e":[{"border-e":R()}],"border-w-t":[{"border-t":R()}],"border-w-r":[{"border-r":R()}],"border-w-b":[{"border-b":R()}],"border-w-l":[{"border-l":R()}],"divide-x":[{"divide-x":R()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":R()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...D(),"hidden","none"]}],"divide-style":[{divide:[...D(),"hidden","none"]}],"border-color":[{border:F()}],"border-color-x":[{"border-x":F()}],"border-color-y":[{"border-y":F()}],"border-color-s":[{"border-s":F()}],"border-color-e":[{"border-e":F()}],"border-color-t":[{"border-t":F()}],"border-color-r":[{"border-r":F()}],"border-color-b":[{"border-b":F()}],"border-color-l":[{"border-l":F()}],"divide-color":[{divide:F()}],"outline-style":[{outline:[...D(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Q,ef,ec]}],"outline-w":[{outline:["",Q,ex,em]}],"outline-color":[{outline:[e]}],shadow:[{shadow:["","none",c,ej,eg]}],"shadow-color":[{shadow:F()}],"inset-shadow":[{"inset-shadow":["none",ef,ec,m]}],"inset-shadow-color":[{"inset-shadow":F()}],"ring-w":[{ring:R()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:F()}],"ring-offset-w":[{"ring-offset":[Q,em]}],"ring-offset-color":[{"ring-offset":F()}],"inset-ring-w":[{"inset-ring":R()}],"inset-ring-color":[{"inset-ring":F()}],opacity:[{opacity:[Q,ef,ec]}],"mix-blend":[{"mix-blend":[...T(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":T()}],filter:[{filter:["","none",ef,ec]}],blur:[{blur:I()}],brightness:[{brightness:[Q,ef,ec]}],contrast:[{contrast:[Q,ef,ec]}],"drop-shadow":[{"drop-shadow":["","none",u,ef,ec]}],grayscale:[{grayscale:["",Q,ef,ec]}],"hue-rotate":[{"hue-rotate":[Q,ef,ec]}],invert:[{invert:["",Q,ef,ec]}],saturate:[{saturate:[Q,ef,ec]}],sepia:[{sepia:["",Q,ef,ec]}],"backdrop-filter":[{"backdrop-filter":["","none",ef,ec]}],"backdrop-blur":[{"backdrop-blur":I()}],"backdrop-brightness":[{"backdrop-brightness":[Q,ef,ec]}],"backdrop-contrast":[{"backdrop-contrast":[Q,ef,ec]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Q,ef,ec]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Q,ef,ec]}],"backdrop-invert":[{"backdrop-invert":["",Q,ef,ec]}],"backdrop-opacity":[{"backdrop-opacity":[Q,ef,ec]}],"backdrop-saturate":[{"backdrop-saturate":[Q,ef,ec]}],"backdrop-sepia":[{"backdrop-sepia":["",Q,ef,ec]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":j()}],"border-spacing-x":[{"border-spacing-x":j()}],"border-spacing-y":[{"border-spacing-y":j()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ef,ec]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Q,"initial",ef,ec]}],ease:[{ease:["linear","initial",f,ef,ec]}],delay:[{delay:[Q,ef,ec]}],animate:[{animate:["none",x,ef,ec]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,ef,ec]}],"perspective-origin":[{"perspective-origin":q()}],rotate:[{rotate:L()}],"rotate-x":[{"rotate-x":L()}],"rotate-y":[{"rotate-y":L()}],"rotate-z":[{"rotate-z":L()}],scale:[{scale:G()}],"scale-x":[{"scale-x":G()}],"scale-y":[{"scale-y":G()}],"scale-z":[{"scale-z":G()}],"scale-3d":["scale-3d"],skew:[{skew:U()}],"skew-x":[{"skew-x":U()}],"skew-y":[{"skew-y":U()}],transform:[{transform:[ef,ec,"","none","gpu","cpu"]}],"transform-origin":[{origin:q()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:W()}],"translate-x":[{"translate-x":W()}],"translate-y":[{"translate-y":W()}],"translate-z":[{"translate-z":W()}],"translate-none":["translate-none"],accent:[{accent:F()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:F()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ef,ec]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":j()}],"scroll-mx":[{"scroll-mx":j()}],"scroll-my":[{"scroll-my":j()}],"scroll-ms":[{"scroll-ms":j()}],"scroll-me":[{"scroll-me":j()}],"scroll-mt":[{"scroll-mt":j()}],"scroll-mr":[{"scroll-mr":j()}],"scroll-mb":[{"scroll-mb":j()}],"scroll-ml":[{"scroll-ml":j()}],"scroll-p":[{"scroll-p":j()}],"scroll-px":[{"scroll-px":j()}],"scroll-py":[{"scroll-py":j()}],"scroll-ps":[{"scroll-ps":j()}],"scroll-pe":[{"scroll-pe":j()}],"scroll-pt":[{"scroll-pt":j()}],"scroll-pr":[{"scroll-pr":j()}],"scroll-pb":[{"scroll-pb":j()}],"scroll-pl":[{"scroll-pl":j()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ef,ec]}],fill:[{fill:["none",...F()]}],"stroke-w":[{stroke:[Q,ex,em,eu]}],stroke:[{stroke:["none",...F()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["before","after","placeholder","file","marker","selection","first-line","first-letter","backdrop","*","**"]}}),eR=((e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return k(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:n}=t,a=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],o=null==n?void 0:n[e];if(null===t)return null;let a=C(t)||C(o);return s[e][a]}),i=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return k(e,a,null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:o,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...n,...i}[t]):({...n,...i})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function eD({className:e,variant:t,size:r,asChild:s=!1,...n}){let a=s?N.Slot:"button";return(0,o.jsx)(a,{"data-slot":"button",className:function(...e){return eO(k(e))}(eR({variant:t,size:r,className:e})),...n})}function eT(){return(0,o.jsx)("section",{className:"w-full py-12 md:py-24 lg:py-32 overflow-hidden bg-gray-50",children:(0,o.jsx)("div",{className:"container px-4 md:px-6 mx-auto",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 items-center",children:[(0,o.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,o.jsxs)("h2",{className:"text-3xl md:text-5xl font-bold mb-4",style:{fontFamily:"Nunito, sans-serif"},children:["Providing best ",(0,o.jsx)("span",{className:"text-green-600",children:"growth"})," solutions"]}),(0,o.jsx)("p",{className:"text-gray-500 md:text-lg max-w-md",children:"You can read this text, but it't matter. It's concept, not important for your life or life your friends."}),(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)(eD,{className:"rounded-full bg-white text-black hover:bg-gray-100 border border-gray-200 px-6",children:"Get FREE Consultation"})})]}),(0,o.jsx)("div",{className:"relative h-[400px] md:h-[500px]",children:(0,o.jsxs)("div",{className:"relative h-full w-full",children:[(0,o.jsx)("div",{className:"absolute right-0 top-0 w-[90%] h-[90%] bg-gray-100 rounded-full"}),(0,o.jsx)("div",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 h-full w-full z-10",children:(0,o.jsx)(l(),{src:"/assets/landing-page/right-guy.png",alt:"Person pointing to growth solutions",width:700,height:700,className:"object-contain h-full w-full"})}),(0,o.jsx)("div",{className:"absolute top-[20%] right-[30%] bg-green-600 text-white p-3 rounded-full shadow-lg hidden md:block",children:(0,o.jsx)(w,{className:"w-6 h-6"})}),(0,o.jsx)("div",{className:"absolute top-[50%] left-[10%] bg-white p-3 rounded-lg shadow-lg hidden md:block",children:(0,o.jsx)(j,{className:"w-6 h-6 text-green-600"})}),(0,o.jsx)("div",{className:"absolute bottom-[30%] right-[10%] bg-white py-2 px-4 rounded-lg shadow-lg hidden md:block",children:(0,o.jsxs)("p",{className:"text-sm font-medium",children:[(0,o.jsx)("span",{className:"text-green-600",children:"50k"})," Monthly Users"]})})]})})]})})})}var eI=r(46900),eq=r.n(eI),eL=r(84346),eG=r.n(eL);function eU(){return(0,o.jsx)("div",{className:`${eq().variable} ${eG().variable} min-h-screen`,style:{backgroundColor:"#087E3F"},children:(0,o.jsx)("div",{className:"container mx-auto px-6 py-12",children:(0,o.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center min-h-[80vh]",children:[(0,o.jsxs)("div",{className:"text-white space-y-6",children:[(0,o.jsx)("h1",{className:"text-3xl md:text-5xl font-nunito text-white leading-none",style:{fontFamily:"Nunito, sans-serif"},children:"Transforming education for generation"}),(0,o.jsx)("p",{className:"font-manrope text-white/90",style:{fontFamily:"var(--font-manrope)",fontWeight:500,fontSize:"16px",lineHeight:"100%",letterSpacing:"0%"},children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Elementum felis, sed ullamcorper tempus faucibus in imperdiet. Semper justo mauris sed fusce erat aenean tristique."})]}),(0,o.jsxs)("div",{className:"relative h-full flex flex-col items-center justify-center lg:justify-end",children:[(0,o.jsx)("div",{className:"relative w-full max-w-[591px] lg:w-[591px] h-auto hidden lg:block -my-4",children:(0,o.jsx)(l(),{src:"/assets/landing-page/trans-merged.png",alt:"Dashboard Interface",width:591,height:597,className:"object-contain",style:{marginTop:"-10px",marginBottom:"-10px"}})}),(0,o.jsx)("div",{className:"relative w-full max-w-[591px] lg:w-[591px] h-auto block lg:hidden -my-4",children:(0,o.jsx)(l(),{src:"/assets/landing-page/trans-1.png",alt:"Dashboard Interface",width:591,height:597,className:"object-contain"})})]})]})})})}var e$=r(62404);function eW(){return(0,o.jsx)("footer",{className:"py-12 px-4",style:{backgroundColor:"#087E3F14"},children:(0,o.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,o.jsx)("div",{className:"flex justify-center mb-8",children:(0,o.jsx)(a(),{href:"/",className:"flex items-center",children:(0,o.jsx)(l(),{src:"/assets/logo/medicos-logo.svg",alt:"Medicos Logo",width:120,height:40,className:"h-auto"})})}),(0,o.jsx)("nav",{className:"flex flex-wrap justify-center items-center gap-6 md:gap-8 mb-8",children:[{name:"Privacy policy",href:"/privacy"},{name:"Terms & conditions",href:"/terms"},{name:"Accessibility guidelines",href:"/accessibility"},{name:"Contact us",href:"/contact"},{name:"Sitemap",href:"/sitemap"}].map((e,t)=>(0,o.jsx)(a(),{href:e.href,className:"text-gray-600 hover:text-gray-800 transition-colors duration-200 text-sm md:text-base",children:e.name},e.name))}),(0,o.jsx)("div",{className:"text-center",children:(0,o.jsx)("p",{className:"text-gray-500 text-sm",children:"Copyright \xa9 2025, Medicos. All rights reserved."})})]})})}function eV(){return(0,o.jsxs)("main",{className:"min-h-screen",children:[(0,o.jsx)(s.default,{}),(0,o.jsx)("div",{className:"container mx-auto px-4 py-8 md:py-4",children:(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 items-center",children:[(0,o.jsx)(d,{}),(0,o.jsx)("div",{className:"hidden md:block relative",children:(0,o.jsx)(l(),{src:"/assets/landing-page/hero.png",alt:"NEET & JEE Dashboard",width:700,height:600,className:"w-full h-auto"})})]})}),(0,o.jsxs)("div",{className:"container mx-auto px-4 py-12 text-center",children:[(0,o.jsx)("p",{className:"text-gray-600 mb-8",children:"Trusted by 50,000+ businesses for innovative design and growth."}),(0,o.jsxs)("div",{className:"flex flex-wrap justify-center items-center gap-8 md:gap-12",children:[(0,o.jsx)(l(),{src:"/assets/landing-page/google-logo.png",alt:"Google",width:120,height:40,className:"h-8 w-auto opacity-50"}),(0,o.jsx)(l(),{src:"/assets/landing-page/fb-logo.png",alt:"Facebook",width:120,height:40,className:"h-8 w-auto opacity-50"}),(0,o.jsx)(l(),{src:"/assets/landing-page/yt-logo.png",alt:"YouTube",width:120,height:40,className:"h-8 w-auto opacity-50"}),(0,o.jsx)(l(),{src:"/assets/landing-page/pin-logo.png",alt:"Pinterest",width:120,height:40,className:"h-8 w-auto opacity-50"}),(0,o.jsx)(l(),{src:"/assets/landing-page/twitch-logo.png",alt:"Twitch",width:120,height:40,className:"h-8 w-auto opacity-50"})]})]}),(0,o.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,o.jsx)(p,{}),(0,o.jsx)(v,{}),(0,o.jsx)(y,{})]}),(0,o.jsxs)("div",{className:"container mx-auto",children:[(0,o.jsx)(eT,{}),(0,o.jsx)(eU,{}),(0,o.jsx)(e$.default,{}),(0,o.jsx)(eW,{})]})]})}},9131:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return i}}),r(21122);let o=r(1322),s=r(27894);function n(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function i(e,t){var r,i;let l,d,c,{src:m,sizes:u,unoptimized:p=!1,priority:h=!1,loading:g,className:f,quality:x,width:b,height:v,fill:y=!1,style:w,overrideSrc:j,onLoad:N,onLoadingComplete:k,placeholder:C="empty",blurDataURL:z,fetchPriority:E,decoding:P="async",layout:_,objectFit:S,objectPosition:M,lazyBoundary:F,lazyRoot:A,...O}=e,{imgConf:R,showAltText:D,blurComplete:T,defaultLoader:I}=t,q=R||s.imageConfigDefault;if("allSizes"in q)l=q;else{let e=[...q.deviceSizes,...q.imageSizes].sort((e,t)=>e-t),t=q.deviceSizes.sort((e,t)=>e-t),o=null==(r=q.qualities)?void 0:r.sort((e,t)=>e-t);l={...q,allSizes:e,deviceSizes:t,qualities:o}}if(void 0===I)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let L=O.loader||I;delete O.loader,delete O.srcSet;let G="__next_img_default"in L;if(G){if("custom"===l.loader)throw Object.defineProperty(Error('Image with src "'+m+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=L;L=t=>{let{config:r,...o}=t;return e(o)}}if(_){"fill"===_&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[_];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[_];t&&!u&&(u=t)}let U="",$=a(b),W=a(v);if((i=m)&&"object"==typeof i&&(n(i)||void 0!==i.src)){let e=n(m)?m.default:m;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,z=z||e.blurDataURL,U=e.src,!y){if($||W){if($&&!W){let t=$/e.width;W=Math.round(e.height*t)}else if(!$&&W){let t=W/e.height;$=Math.round(e.width*t)}}else $=e.width,W=e.height}}let V=!h&&("lazy"===g||void 0===g);(!(m="string"==typeof m?m:U)||m.startsWith("data:")||m.startsWith("blob:"))&&(p=!0,V=!1),l.unoptimized&&(p=!0),G&&!l.dangerouslyAllowSVG&&m.split("?",1)[0].endsWith(".svg")&&(p=!0);let B=a(x),J=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:S,objectPosition:M}:{},D?{}:{color:"transparent"},w),H=T||"empty"===C?null:"blur"===C?'url("data:image/svg+xml;charset=utf-8,'+(0,o.getImageBlurSvg)({widthInt:$,heightInt:W,blurWidth:d,blurHeight:c,blurDataURL:z||"",objectFit:J.objectFit})+'")':'url("'+C+'")',X=H?{backgroundSize:J.objectFit||"cover",backgroundPosition:J.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},Y=function(e){let{config:t,src:r,unoptimized:o,width:s,quality:n,sizes:a,loader:i}=e;if(o)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:o,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let o;o=e.exec(r);o)t.push(parseInt(o[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=o[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:o,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,a),c=l.length-1;return{sizes:a||"w"!==d?a:"100vw",srcSet:l.map((e,o)=>i({config:t,src:r,quality:n,width:e})+" "+("w"===d?e:o+1)+d).join(", "),src:i({config:t,src:r,quality:n,width:l[c]})}}({config:l,src:m,unoptimized:p,width:$,quality:B,sizes:u,loader:L});return{props:{...O,loading:V?"lazy":g,fetchPriority:E,width:$,height:W,decoding:P,className:f,style:{...J,...X},sizes:Y.sizes,srcSet:Y.srcSet,src:j||Y.src},meta:{unoptimized:p,priority:h,placeholder:C,fill:y}}}},10225:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var o=r(60687),s=r(43210),n=r(30474),a=r(85814),i=r.n(a);let l=(0,r(62688).A)("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);function d(){let[e,t]=(0,s.useState)(!1);return(0,o.jsx)("header",{className:"",children:(0,o.jsxs)("div",{className:"container mx-auto px-4",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between h-22 md:h-26",children:[(0,o.jsx)(i(),{href:"/",className:"flex items-center",children:(0,o.jsx)(n.default,{src:"/assets/logo/medicos-logo.svg",alt:"Medicos Logo",width:224,height:40,className:"h-auto"})}),(0,o.jsx)("button",{className:"md:hidden focus:outline-none",onClick:()=>{t(!e)},"aria-label":"Toggle menu",children:(0,o.jsx)(l,{className:"h-6 w-6"})}),(0,o.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[(0,o.jsx)(i(),{href:"/",className:"text-base font-normal text-gray-900 hover:text-gray-600",style:{fontFamily:"Proxima Nova, sans-serif",letterSpacing:"-0.15px"},children:"Home"}),(0,o.jsx)(i(),{href:"/about",className:"text-base font-normal text-gray-900 hover:text-gray-600",style:{fontFamily:"Proxima Nova, sans-serif",letterSpacing:"-0.15px"},children:"About"}),(0,o.jsx)(i(),{href:"/services",className:"text-base font-normal text-gray-900 hover:text-gray-600",style:{fontFamily:"Proxima Nova, sans-serif",letterSpacing:"-0.15px"},children:"Services"}),(0,o.jsx)(i(),{href:"/pricing",className:"text-base font-normal text-gray-900 hover:text-gray-600",style:{fontFamily:"Proxima Nova, sans-serif",letterSpacing:"-0.15px"},children:"Pricing"}),(0,o.jsx)(i(),{href:"/contact",className:"text-base font-normal text-gray-900 hover:text-gray-600",style:{fontFamily:"Proxima Nova, sans-serif",letterSpacing:"-0.15px"},children:"Contact"}),(0,o.jsx)(i(),{href:"/try-free",className:"text-base font-normal text-gray-900 border border-gray-300 rounded-full px-6 py-2 hover:bg-gray-50",style:{fontFamily:"Proxima Nova, sans-serif",letterSpacing:"-0.15px"},children:"Try for Free"})]})]}),e&&(0,o.jsx)("div",{className:"md:hidden py-4 border-t border-gray-100",children:(0,o.jsxs)("nav",{className:"flex flex-col space-y-4 pb-4",children:[(0,o.jsx)(i(),{href:"/",className:"text-base font-normal text-gray-900 hover:text-gray-600",onClick:()=>t(!1),children:"Home"}),(0,o.jsx)(i(),{href:"/about",className:"text-base font-normal text-gray-900 hover:text-gray-600",onClick:()=>t(!1),children:"About"}),(0,o.jsx)(i(),{href:"/services",className:"text-base font-normal text-gray-900 hover:text-gray-600",onClick:()=>t(!1),children:"Services"}),(0,o.jsx)(i(),{href:"/pricing",className:"text-base font-normal text-gray-900 hover:text-gray-600",onClick:()=>t(!1),children:"Pricing"}),(0,o.jsx)(i(),{href:"/contact",className:"text-base font-normal text-gray-900 hover:text-gray-600",onClick:()=>t(!1),children:"Contact"}),(0,o.jsx)(i(),{href:"/try-free",className:"text-base font-normal text-gray-900 border border-gray-300 rounded-full px-6 py-2 hover:bg-gray-50 inline-block w-fit",onClick:()=>t(!1),children:"Try for Free"})]})})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14299:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var o=r(65239),s=r(48088),n=r(88170),a=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4774)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return o}});let r=["default","imgix","cloudinary","akamai","custom"],o={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32091:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:o,width:s,quality:n}=e,a=n||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(o)+"&w="+s+"&q="+a+(o.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}}),r.__next_img_default=!0;let o=r},33873:e=>{"use strict";e.exports=require("path")},45332:(e,t,r)=>{"use strict";r.d(t,{Slot:()=>s});var o=r(12907);(0,o.registerClientReference)(function(){throw Error("Attempted to call Root() from the server but Root is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\@radix-ui\\react-slot\\dist\\index.mjs","Root");let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call Slot() from the server but Slot is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\@radix-ui\\react-slot\\dist\\index.mjs","Slot");(0,o.registerClientReference)(function(){throw Error("Attempted to call Slottable() from the server but Slottable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\@radix-ui\\react-slot\\dist\\index.mjs","Slottable"),(0,o.registerClientReference)(function(){throw Error("Attempted to call createSlot() from the server but createSlot is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\@radix-ui\\react-slot\\dist\\index.mjs","createSlot"),(0,o.registerClientReference)(function(){throw Error("Attempted to call createSlottable() from the server but createSlottable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\@radix-ui\\react-slot\\dist\\index.mjs","createSlottable")},45572:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\landing-page\\\\navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\navbar.tsx","default")},46900:e=>{e.exports={style:{fontFamily:"'Nunito', 'Nunito Fallback'",fontWeight:800,fontStyle:"normal"},className:"__className_375539",variable:"__variable_375539"}},47033:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},49603:(e,t,r)=>{let{createProxy:o}=r(39844);e.exports=o("C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\node_modules\\next\\dist\\client\\image-component.js")},62404:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\components\\\\landing-page\\\\TestimonialsContact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TestimonialsContact.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70099:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return i}});let o=r(72639),s=r(9131),n=r(49603),a=o._(r(32091));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=n.Image},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79433:(e,t,r)=>{Promise.resolve().then(r.bind(r,11329)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.t.bind(r,46533,23)),Promise.resolve().then(r.bind(r,10225)),Promise.resolve().then(r.bind(r,79866))},79551:e=>{"use strict";e.exports=require("url")},79866:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var o=r(60687),s=r(43210),n=r(47033),a=r(14952);let i=[{id:1,name:"Amelia Joseph",role:"Chief Manager",image:"/placeholder.svg?height=60&width=60",text:"My vision came alive effortlessly. Their blend of casual and professional approach made the process a breeze. Creativity flowed, and the results were beyond my expectations."},{id:2,name:"Jacob Joshua",role:"Chief Manager",image:"/placeholder.svg?height=60&width=60",text:"I found the digital expertise I needed. Their creative-professional balance exceeded expectations. Friendly interactions, exceptional outcomes. For digital enchantment, it's got to be Embrace!"},{id:3,name:"Sarah Wilson",role:"Creative Director",image:"/placeholder.svg?height=60&width=60",text:"Embrace really nailed our brand's authentic style. They're the perfect blend of creativity and strategy. Thrilled with the results and ongoing partnership."}],l=[{id:1,image:"/assets/landing-page/q-1.png",position:"top-4 left-8"},{id:2,image:"/assets/landing-page/q-2.png",position:"top-8 right-12"},{id:3,image:"/assets/landing-page/q-3.png",position:"bottom-12 left-4"},{id:4,image:"/assets/landing-page/q-4.png",position:"bottom-8 right-8"},{id:5,image:"/assets/landing-page/q-5.png",position:"top-1/2 left-12"},{id:6,image:"/assets/landing-page/q-6.png",position:"top-1/3 right-4"}];function d(){let[e,t]=(0,s.useState)(0),[r,d]=(0,s.useState)(""),[c,m]=(0,s.useState)(!1),u=()=>{t(e=>(e+1)%i.length)},p=()=>{t(e=>(e-1+i.length)%i.length)};return(0,o.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,o.jsxs)("section",{className:"py-16 px-4 max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"flex items-center justify-between mb-12",children:[(0,o.jsx)("h2",{className:"text-3xl md:text-5xl font-bold mb-8 text-center",style:{fontFamily:"Nunito, sans-serif"},children:"What Our Client Said About Us"}),(0,o.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,o.jsx)("button",{onClick:p,className:"p-3 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors","aria-label":"Previous testimonial",children:(0,o.jsx)(n.A,{className:"w-5 h-5 text-gray-600"})}),(0,o.jsx)("button",{onClick:u,className:"p-3 rounded-full transition-colors",style:{backgroundColor:"#087E3F"},"aria-label":"Next testimonial",children:(0,o.jsx)(a.A,{className:"w-5 h-5 text-white"})})]})]}),(0,o.jsx)("div",{className:"hidden md:grid md:grid-cols-3 gap-6",children:i.map((t,r)=>(0,o.jsxs)("div",{className:`p-6 rounded-2xl transition-all duration-300 ${r===e?"text-white shadow-xl":"bg-white border border-gray-200 text-gray-900"}`,style:{backgroundColor:r===e?"#087E3F":"white"},children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,o.jsx)("img",{src:t.image||"/placeholder.svg",alt:t.name,className:"w-12 h-12 rounded-full object-cover"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-semibold text-lg",children:t.name}),(0,o.jsx)("p",{className:`text-sm ${r===e?"text-green-100":"text-gray-600"}`,children:t.role})]})]}),(0,o.jsx)("p",{className:`text-sm leading-relaxed ${r===e?"text-green-50":"text-gray-700"}`,children:t.text})]},t.id))}),(0,o.jsxs)("div",{className:"md:hidden",children:[(0,o.jsxs)("div",{className:"p-6 rounded-2xl text-white shadow-xl mb-6",style:{backgroundColor:"#087E3F"},children:[(0,o.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,o.jsx)("img",{src:i[e].image||"/placeholder.svg",alt:i[e].name,className:"w-12 h-12 rounded-full object-cover"}),(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"font-semibold text-lg",children:i[e].name}),(0,o.jsx)("p",{className:"text-sm text-green-100",children:i[e].role})]})]}),(0,o.jsx)("p",{className:"text-sm leading-relaxed text-green-50",children:i[e].text})]}),(0,o.jsx)("div",{className:"flex justify-center space-x-2 mb-8",children:i.map((r,s)=>(0,o.jsx)("button",{onClick:()=>t(s),className:`w-2 h-2 rounded-full transition-colors ${s===e?"bg-green-600":"bg-gray-300"}`,"aria-label":`Go to testimonial ${s+1}`},s))}),(0,o.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,o.jsx)("button",{onClick:p,className:"p-3 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors","aria-label":"Previous testimonial",children:(0,o.jsx)(n.A,{className:"w-5 h-5 text-gray-600"})}),(0,o.jsx)("button",{onClick:u,className:"p-3 rounded-full transition-colors",style:{backgroundColor:"#087E3F"},"aria-label":"Next testimonial",children:(0,o.jsx)(a.A,{className:"w-5 h-5 text-white"})})]})]})]}),(0,o.jsxs)("section",{className:"py-8 md:py-16 px-4 relative overflow-hidden md:max-w-6xl md:mx-auto md:rounded-2xl md:my-12",style:{backgroundColor:"#F3F3F3"},children:[l.map(e=>(0,o.jsx)("div",{className:`absolute hidden lg:block ${e.position} animate-pulse`,children:(0,o.jsx)("img",{src:e.image||"/placeholder.svg",alt:"User avatar",className:"w-10 h-10 rounded-full object-cover shadow-lg"})},e.id)),(0,o.jsxs)("div",{className:"max-w-2xl mx-auto text-center relative z-10 ",children:[(0,o.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-gray-900 mb-4",children:"Do you still have any questions?"}),(0,o.jsx)("p",{className:"text-gray-600 mb-8 text-sm md:text-base",children:"Don't hesitate to leave us your phone number. We will contact you to discuss any questions you may have"}),(0,o.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r.trim()&&(m(!0),console.log("Phone number submitted:",r),setTimeout(()=>{m(!1),d("")},2e3))},className:"flex flex-col sm:flex-row gap-4 max-w-md mx-auto",children:[(0,o.jsx)("input",{type:"tel",value:r,onChange:e=>d(e.target.value),placeholder:"Enter your phone number",className:"flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent",required:!0}),(0,o.jsx)("button",{type:"submit",disabled:c,className:"px-6 py-3 rounded-lg text-white font-medium transition-all duration-200 hover:opacity-90 disabled:opacity-50",style:{backgroundColor:"#087E3F80"},children:c?"Subscribed!":"Subscribe"})]}),c&&(0,o.jsx)("p",{className:"mt-4 text-green-600 font-medium",children:"Thank you! We'll contact you soon."})]})]})]})}},84346:e=>{e.exports={style:{fontFamily:"'Manrope', 'Manrope Fallback'",fontWeight:500,fontStyle:"normal"},className:"__className_54ee17",variable:"__variable_54ee17"}},89161:(e,t,r)=>{Promise.resolve().then(r.bind(r,45332)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.t.bind(r,49603,23)),Promise.resolve().then(r.bind(r,45572)),Promise.resolve().then(r.bind(r,62404))}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,4619,3287,5361,4707],()=>r(14299));module.exports=o})();