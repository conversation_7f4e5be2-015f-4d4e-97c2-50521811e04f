"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9078],{12379:(e,t,a)=>{a.d(t,{A:()=>d});var r=a(95155),n=a(12115),o=a(6874),s=a.n(o),i=a(59434),l=a(13052),c=a(5623);let d=e=>{let{items:t,maxItems:a=4,className:o}=e,d=n.useMemo(()=>t.length<=a?t:[t[0],{label:"..."},...t.slice(-2)],[t,a]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",o),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let a=t===d.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(l.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}):a?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(s(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},17759:(e,t,a)=>{a.d(t,{C5:()=>v,MJ:()=>p,Rr:()=>x,eI:()=>h,lR:()=>f,lV:()=>c,zB:()=>u});var r=a(95155),n=a(12115),o=a(66634),s=a(62177),i=a(59434),l=a(85057);let c=s.Op,d=n.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(s.xI,{...t})})},m=()=>{let e=n.useContext(d),t=n.useContext(g),{getFieldState:a}=(0,s.xW)(),r=(0,s.lN)({name:e.name}),o=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=t;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...o}},g=n.createContext({});function h(e){let{className:t,...a}=e,o=n.useId();return(0,r.jsx)(g.Provider,{value:{id:o},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",t),...a})})}function f(e){let{className:t,...a}=e,{error:n,formItemId:o}=m();return(0,r.jsx)(l.J,{"data-slot":"form-label","data-error":!!n,className:(0,i.cn)("data-[error=true]:text-destructive",t),htmlFor:o,...a})}function p(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:s,formMessageId:i}=m();return(0,r.jsx)(o.Slot,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(s," ").concat(i):"".concat(s),"aria-invalid":!!a,...t})}function x(e){let{className:t,...a}=e,{formDescriptionId:n}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:n,className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}function v(e){var t;let{className:a,...n}=e,{error:o,formMessageId:s}=m(),l=o?String(null!==(t=null==o?void 0:o.message)&&void 0!==t?t:""):n.children;return l?(0,r.jsx)("p",{"data-slot":"form-message",id:s,className:(0,i.cn)("text-destructive text-sm",a),...n,children:l}):null}},17972:(e,t,a)=>{a.d(t,{G7:()=>d,Gj:()=>c,L$:()=>g,h_:()=>h,oI:()=>u,uB:()=>l,xL:()=>m});var r=a(95155);a(12115);var n=a(77740),o=a(47924),s=a(59434),i=a(54165);function l(e){let{className:t,...a}=e;return(0,r.jsx)(n.uB,{"data-slot":"command",className:(0,s.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...a})}function c(e){let{title:t="Command Palette",description:a="Search for a command to run...",children:n,...o}=e;return(0,r.jsxs)(i.lG,{...o,children:[(0,r.jsxs)(i.c7,{className:"sr-only",children:[(0,r.jsx)(i.L3,{children:t}),(0,r.jsx)(i.rr,{children:a})]}),(0,r.jsx)(i.Cf,{className:"overflow-hidden p-0",children:(0,r.jsx)(l,{className:"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:n})})]})}function d(e){let{className:t,...a}=e;return(0,r.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,r.jsx)(o.A,{className:"size-4 shrink-0 opacity-50"}),(0,r.jsx)(n.uB.Input,{"data-slot":"command-input",className:(0,s.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",t),...a})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(n.uB.List,{"data-slot":"command-list",className:(0,s.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",t),...a})}function m(e){let{...t}=e;return(0,r.jsx)(n.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(n.uB.Group,{"data-slot":"command-group",className:(0,s.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(n.uB.Item,{"data-slot":"command-item",className:(0,s.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}},30285:(e,t,a)=>{a.d(t,{$:()=>l,r:()=>i});var r=a(95155);a(12115);var n=a(66634),o=a(74466),s=a(59434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:o,asChild:l=!1,...c}=e,d=l?n.Slot:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,s.cn)(i({variant:a,size:o,className:t})),...c})}},46104:(e,t,a)=>{a.d(t,{l:()=>g});var r=a(95155),n=a(12115),o=a(27213),s=a(99890),i=a(54416),l=a(1243),c=a(30285),d=a(15312),u=a(59434);let m=n.forwardRef((e,t)=>{let{className:a,value:n,...o}=e;return(0,r.jsx)(d.bL,{ref:t,className:(0,u.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",a),...o,children:(0,r.jsx)(d.C1,{className:"h-full w-full flex-1 bg-red-500 transition-all",style:{transform:"translateX(-".concat(100-(n||0),"%)")}})})});function g(e){let{value:t,onChange:a,maxSize:d=0x3200000,acceptedTypes:g=["image/jpeg","image/jpg","image/png","image/svg+xml"]}=e,[h,f]=(0,n.useState)(!1),[p,x]=(0,n.useState)(0),[v,b]=(0,n.useState)(null),y=(0,n.useRef)(null),j=()=>{x(0);let e=setInterval(()=>{x(t=>t>=100?(clearInterval(e),100):t+5)},100)},w=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?f(!0):"dragleave"===e.type&&f(!1)},N=e=>e.size>d?(b("File size exceeds ".concat(d/1048576,"MB limit.")),!1):g.includes(e.type)?(b(null),!0):(b("File type not supported."),!1),k=()=>{a(null),x(0),y.current&&(y.current.value="")},E=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",S=t?Array.from(t):[],_=S.length>0;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("div",{className:(0,u.cn)("border-2 border-dashed rounded-lg p-6 transition-colors",h?"border-primary bg-primary/5":"border-muted-foreground/25",_?"bg-muted/50":""),onDragEnter:w,onDragLeave:w,onDragOver:w,onDrop:e=>{e.preventDefault(),e.stopPropagation(),f(!1),e.dataTransfer.files&&e.dataTransfer.files.length>0&&N(e.dataTransfer.files[0])&&(a(e.dataTransfer.files),j())},children:(0,r.jsx)("div",{className:"flex flex-col items-center justify-center gap-2 text-center",children:_?(0,r.jsxs)("div",{className:"w-full",children:[S.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded-md mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"bg-muted p-1 rounded",children:(0,r.jsx)(s.A,{className:"h-5 w-5 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-primary",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:E(e.size)})]})]}),(0,r.jsxs)(c.$,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:k,children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Remove file"})]})]},t)),(0,r.jsxs)("div",{className:"w-full mt-2",children:[(0,r.jsx)(m,{value:p,className:"h-2"}),(0,r.jsxs)("p",{className:"text-xs text-right mt-1 text-muted-foreground",children:[p,"%"]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"rounded-full bg-primary/10 p-3 text-primary",children:(0,r.jsx)(o.A,{className:"h-10 w-10"})}),(0,r.jsxs)("p",{className:"text-sm font-medium",children:["Drop your files here or"," ",(0,r.jsx)(c.$,{type:"button",variant:"link",className:"p-0 h-auto text-primary",onClick:()=>{var e;return null===(e=y.current)||void 0===e?void 0:e.click()},children:"browse"})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Maximum size: ",d/1048576,"MB"]})]})})}),v&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-destructive text-sm",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("p",{children:v})]}),(0,r.jsx)("input",{ref:y,type:"file",className:"hidden",onChange:e=>{e.preventDefault(),e.target.files&&e.target.files.length>0&&N(e.target.files[0])&&(a(e.target.files),j())},accept:g.join(",")})]})}m.displayName=d.bL.displayName},54165:(e,t,a)=>{a.d(t,{Cf:()=>d,L3:()=>m,c7:()=>u,lG:()=>i,rr:()=>g});var r=a(95155);a(12115);var n=a(4033),o=a(54416),s=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dialog",...t})}function l(e){let{...t}=e;return(0,r.jsx)(n.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function d(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[a,(0,r.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,s.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,s.cn)("text-lg leading-none font-semibold",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}},55097:(e,t,a)=>{a.d(t,{$y:()=>o,cY:()=>s,hS:()=>n});var r=a(56671);function n(e){var t,a,n,o;let s,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",l=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=i;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)&&(c=e.data.message),(null==e?void 0:e.status)?s=e.status:(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)&&(s=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===s?c="Server error. Please try again later.":503===s&&(c="Service temporarily unavailable. Please try again later.")),l&&r.oR.error(c),{success:!1,error:c,statusCode:s}}function o(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&r.oR.success(a),{success:!0,data:e}}function s(e){return!0===e.success}},59434:(e,t,a)=>{a.d(t,{b:()=>s,cn:()=>o});var r=a(52596),n=a(17307);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function s(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},62523:(e,t,a)=>{a.d(t,{p:()=>o});var r=a(95155);a(12115);var n=a(59434);function o(e){let{className:t,type:a,...o}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},74125:(e,t,a)=>{a.d(t,{$T:()=>u,JY:()=>o,M0:()=>n,N0:()=>l,mS:()=>c,mi:()=>d,pZ:()=>s,qk:()=>i});var r=a(55097);async function n(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to create college. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"College created successfully!")}catch(e){return console.error("Error creating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to create college. Please try again.","Failed to create college. Please try again.")}}async function o(){let e=localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let t=await fetch("".concat("http://localhost:3000/api","/colleges"),{method:"GET",headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(t.status),"Failed to load colleges. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching colleges:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load colleges. Please try again.","Failed to load colleges. Please try again.")}}async function s(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to delete college. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"College deleted successfully!")}catch(e){return console.error("Error deleting college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to delete college. Please try again.","Failed to delete college. Please try again.")}}async function i(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to load college. Please try again.")}let n=await a.json();return(0,r.$y)(n)}catch(e){return console.error("Error fetching college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load college. Please try again.","Failed to load college. Please try again.")}}async function l(e,t){let a=localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let n=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to update college. Please try again.")}let o=await n.json();return(0,r.$y)(o,!0,"College updated successfully!")}catch(e){return console.error("Error updating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to update college. Please try again.","Failed to update college. Please try again.")}}async function c(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let a=await fetch("".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/summary"),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(a.status))}return await a.json()}catch(e){throw console.error("Error fetching college:",e),e}}async function d(e,t,a){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let n="".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/question-papers?startDate=").concat(encodeURIComponent(t),"&endDate=").concat(encodeURIComponent(a)),o=await fetch(n,{method:"GET",headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}});if(!o.ok){let e=await o.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(o.status))}return await o.json()}catch(e){throw console.error("Error fetching question paper stats:",e),e}}async function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=localStorage.getItem("backendToken");if(!a)throw Error("Authentication required");try{let r="".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/top-teachers?limit=").concat(t),n=await fetch(r,{method:"GET",headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}});if(!n.ok){let e=await n.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(n.status))}return await n.json()}catch(e){throw console.error("Error fetching top teachers:",e),e}}},85057:(e,t,a)=>{a.d(t,{J:()=>s});var r=a(95155);a(12115);var n=a(40968),o=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},88262:(e,t,a)=>{a.d(t,{d:()=>c,o:()=>d});var r=a(12115);new EventTarget;let n=[],o=[];function s(){o.forEach(e=>e([...n]))}function i(e){let t=e.id||Math.random().toString(36).substring(2,9),a={...e,id:t};return n=[...n,a],s(),setTimeout(()=>{l(t)},5e3),t}function l(e){n=n.filter(t=>t.id!==e),s()}function c(){let[e,t]=r.useState(n);return r.useEffect(()=>(o.push(t),t([...n]),()=>{o=o.filter(e=>e!==t)}),[]),{toast:e=>i(e),dismiss:e=>{e?l(e):n.forEach(e=>e.id&&l(e.id))},toasts:e}}let d=e=>i(e)},88539:(e,t,a)=>{a.d(t,{T:()=>o});var r=a(95155);a(12115);var n=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,n.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},95984:(e,t,a)=>{a.d(t,{L:()=>f});var r=a(95155),n=a(12115),o=a(66474),s=a(30285),i=a(17972),l=a(62523),c=a(67140),d=a(59434);function u(e){let{...t}=e;return(0,r.jsx)(c.bL,{"data-slot":"popover",...t})}function m(e){let{...t}=e;return(0,r.jsx)(c.l9,{"data-slot":"popover-trigger",...t})}function g(e){let{className:t,align:a="center",sideOffset:n=4,...o}=e;return(0,r.jsx)(c.ZL,{children:(0,r.jsx)(c.UC,{"data-slot":"popover-content",align:a,sideOffset:n,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...o})})}let h=[{id:"us",name:"United States",code:"+1",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{id:"ca",name:"Canada",code:"+1",flag:"\uD83C\uDDE8\uD83C\uDDE6"},{id:"gb",name:"United Kingdom",code:"+44",flag:"\uD83C\uDDEC\uD83C\uDDE7"},{id:"au",name:"Australia",code:"+61",flag:"\uD83C\uDDE6\uD83C\uDDFA"}];function f(e){let{value:t,onChange:a,className:c}=e,[f,p]=(0,n.useState)(!1),[x,v]=(0,n.useState)(h[0]),b=e=>{let t=e.replace(/\D/g,"");if("us"===x.id||"ca"===x.id){if(t.length<=3);else if(t.length<=6)return"(".concat(t.slice(0,3),") ").concat(t.slice(3));else return"(".concat(t.slice(0,3),") ").concat(t.slice(3,6),"-").concat(t.slice(6,10))}return t},y=e=>{v(e),p(!1),a("")};return(0,r.jsxs)("div",{className:(0,d.cn)("flex",c),children:[(0,r.jsxs)(u,{open:f,onOpenChange:p,children:[(0,r.jsx)(m,{asChild:!0,children:(0,r.jsxs)(s.$,{variant:"outline",role:"combobox","aria-expanded":f,className:"w-[80px] justify-between px-2 border-r-0 rounded-r-none",children:[(0,r.jsx)("span",{className:"mr-1",children:x.flag}),(0,r.jsx)(o.A,{className:"h-4 w-4 opacity-50"})]})}),(0,r.jsx)(g,{className:"w-[200px] p-0",children:(0,r.jsxs)(i.uB,{children:[(0,r.jsx)(i.G7,{placeholder:"Search country..."}),(0,r.jsxs)(i.oI,{children:[(0,r.jsx)(i.xL,{children:"No country found."}),(0,r.jsx)(i.L$,{className:"max-h-[200px] overflow-y-auto",children:h.map(e=>(0,r.jsxs)(i.h_,{value:e.name,onSelect:()=>y(e),children:[(0,r.jsx)("span",{className:"mr-2",children:e.flag}),(0,r.jsx)("span",{children:e.name}),(0,r.jsx)("span",{className:"ml-auto text-muted-foreground",children:e.code})]},e.id))})]})]})})]}),(0,r.jsx)(l.p,{type:"tel",value:t,onChange:e=>{a(b(e.target.value))},className:"rounded-l-none",placeholder:"us"===x.id?"(*************":"Phone number"})]})}}}]);