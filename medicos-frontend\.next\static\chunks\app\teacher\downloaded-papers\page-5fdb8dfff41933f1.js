(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5687],{5040:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},12379:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var a=s(95155),n=s(12115),r=s(6874),o=s.n(r),i=s(59434),l=s(13052),c=s(5623);let d=e=>{let{items:t,maxItems:s=4,className:r}=e,d=n.useMemo(()=>t.length<=s?t:[t[0],{label:"..."},...t.slice(-2)],[t,s]);return(0,a.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",r),children:(0,a.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let s=t===d.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)(l.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}):s?(0,a.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,a.jsx)(o(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},25531:(e,t,s)=>{"use strict";s.d(t,{ZG:()=>r,cH:()=>o,ig:()=>i});let a="http://localhost:3000/api";function n(){let e=localStorage.getItem("backendToken"),t=localStorage.getItem("firebaseToken"),s=localStorage.getItem("token"),a={"Content-Type":"application/json"};if(e)a.Authorization="Bearer ".concat(e);else if(t)a.Authorization="Bearer ".concat(t);else if(s)a.Authorization="Bearer ".concat(s);else throw Error("Authentication required - Please log in again. No valid authentication token found.");return a}async function r(e){try{let t=n(),s=await fetch("".concat(a,"/question-papers"),{method:"POST",headers:t,body:JSON.stringify(e)});if(!s.ok){let e="Error: ".concat(s.status," - ").concat(s.statusText);try{let t=await s.text();if(t)try{let s=JSON.parse(t);e=s&&s.message?s.message:s&&s.error?s.error:t}catch(s){e=t}}catch(e){}if(!e||e==="Error: ".concat(s.status," - ").concat(s.statusText))switch(s.status){case 401:e="Authentication required - Please log in again.";break;case 403:e="Access denied - You don't have permission to perform this action.";break;case 404:e="Resource not found - The requested item could not be found.";break;case 429:e="Too many requests - Please wait a moment before trying again.";break;case 500:e="Server error - Please try again later.";break;case 503:e="Service unavailable - The server is temporarily down.";break;default:s.status>=400&&s.status<500?e="Invalid request - Please check your input and try again.":s.status>=500&&(e="Server error - Please try again later.")}return{success:!1,error:e}}let r=await s.json();if(console.log("Raw API response from createQuestionPaper:",r),r.questionPaper)return{success:!0,data:r.questionPaper};return{success:!0,data:r}}catch(e){return{success:!1,error:"Network error - Please check your connection and try again."}}}async function o(e){try{let t;if(console.log("getQuestionPaperForPDF called with:",e),!e||"undefined"===e||"null"===e)throw Error("Invalid question paper ID: ".concat(e));let s=n(),r=await fetch("".concat(a,"/question-papers/").concat(e),{method:"GET",headers:s});if(!r.ok){let e=await r.text();throw console.error("API Error Response:",e),Error("Failed to fetch question paper: ".concat(r.status," ").concat(r.statusText))}let o=await r.json();if(console.log("Question paper data fetched:",o),console.log("College ID in question paper:",o.collegeId),o.college?(t={name:o.college.name,logoUrl:o.college.logoUrl,address:o.college.address},console.log("College information found in response:",t)):o.collegeId&&"object"==typeof o.collegeId&&o.collegeId.name?(t={name:o.collegeId.name,logoUrl:o.collegeId.logoUrl,address:o.collegeId.address},console.log("College information found in populated collegeId:",t)):console.log("No college information available - PDF will be generated without college branding"),o.questionPaper)return{questionPaper:o.questionPaper,college:o.college||t};return{questionPaper:o,college:t}}catch(e){throw console.error("Error fetching question paper for PDF:",e),e}}async function i(){try{let e=n(),t=await fetch("".concat(a,"/question-papers"),{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(t.status," - ").concat(t.statusText))}return await t.json()}catch(e){throw console.error("Error fetching question papers:",e),e}}},28905:(e,t,s)=>{"use strict";s.d(t,{C:()=>o});var a=s(12115),n=s(6101),r=s(52712),o=e=>{let{present:t,children:s}=e,o=function(e){var t,s;let[n,o]=a.useState(),l=a.useRef({}),c=a.useRef(e),d=a.useRef("none"),[u,m]=(t=e?"mounted":"unmounted",s={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>{let a=s[e][t];return null!=a?a:e},t));return a.useEffect(()=>{let e=i(l.current);d.current="mounted"===u?e:"none"},[u]),(0,r.N)(()=>{let t=l.current,s=c.current;if(s!==e){let a=d.current,n=i(t);e?m("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?m("UNMOUNT"):s&&a!==n?m("ANIMATION_OUT"):m("UNMOUNT"),c.current=e}},[e,m]),(0,r.N)(()=>{if(n){var e;let t;let s=null!==(e=n.ownerDocument.defaultView)&&void 0!==e?e:window,a=e=>{let a=i(l.current).includes(e.animationName);if(e.target===n&&a&&(m("ANIMATION_END"),!c.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=s.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},r=e=>{e.target===n&&(d.current=i(l.current))};return n.addEventListener("animationstart",r),n.addEventListener("animationcancel",a),n.addEventListener("animationend",a),()=>{s.clearTimeout(t),n.removeEventListener("animationstart",r),n.removeEventListener("animationcancel",a),n.removeEventListener("animationend",a)}}m("ANIMATION_END")},[n,m]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(t),l="function"==typeof s?s({present:o.isPresent}):a.Children.only(s),c=(0,n.s)(o.ref,function(e){var t,s;let a=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,n=a&&"isReactWarning"in a&&a.isReactWarning;return n?e.ref:(n=(a=null===(s=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===s?void 0:s.get)&&"isReactWarning"in a&&a.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof s||o.isPresent?a.cloneElement(l,{ref:c}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},29797:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var a=s(95155);s(12115);var n=s(30285),r=s(42355),o=s(13052);function i(e){let{currentPage:t,totalPages:s,onPageChange:i,pageSize:l,totalItems:c,onPageSizeChange:d,pageSizeOptions:u=[5,10,20,50]}=e,m=Math.min(c,(t-1)*l+1),h=Math.min(c,t*l);return(0,a.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,a.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:c>0?(0,a.jsxs)("p",{children:["Showing ",m," to ",h," of ",c," items"]}):(0,a.jsx)("p",{children:"No items"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[d&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,a.jsx)("select",{className:"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm",value:l,onChange:e=>d(Number(e.target.value)),children:u.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>i(t-1),disabled:1===t,className:"h-8 w-8 p-0",children:[(0,a.jsx)(r.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Previous page"})]}),(()=>{let e=[];if(s<=5)for(let t=1;t<=s;t++)e.push(t);else{e.push(1),t>3&&e.push("ellipsis");let a=Math.max(2,t-1),n=Math.min(s-1,t+1);for(let t=a;t<=n;t++)e.push(t);t<s-2&&e.push("ellipsis"),s>1&&e.push(s)}return e})().map((e,s)=>"ellipsis"===e?(0,a.jsx)("span",{className:"px-2",children:"..."},"ellipsis-".concat(s)):(0,a.jsx)(n.$,{variant:t===e?"default":"outline",size:"sm",onClick:()=>i(e),className:"h-8 w-8 p-0",children:e},"page-".concat(e))),(0,a.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>i(t+1),disabled:t===s||0===s,className:"h-8 w-8 p-0",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Next page"})]})]})]})]})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,r:()=>i});var a=s(95155);s(12115);var n=s(66634),r=s(74466),o=s(59434);let i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:r,asChild:l=!1,...c}=e,d=l?n.Slot:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:s,size:r,className:t})),...c})}},44838:(e,t,s)=>{"use strict";s.d(t,{SQ:()=>l,_2:()=>c,lp:()=>d,mB:()=>u,rI:()=>o,ty:()=>i});var a=s(95155);s(12115);var n=s(76215),r=s(59434);function o(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,a.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function l(e){let{className:t,sideOffset:s=4,...o}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...o})})}function c(e){let{className:t,inset:s,variant:o="default",...i}=e;return(0,a.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":o,className:(0,r.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function d(e){let{className:t,inset:s,...o}=e;return(0,a.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":s,className:(0,r.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...o})}function u(e){let{className:t,...s}=e;return(0,a.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,r.cn)("bg-border -mx-1 my-1 h-px",t),...s})}},46872:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v});var a=s(95155),n=s(12115),r=s(91769),o=s(12379),i=s(30285),l=s(57434),c=s(19946);let d=(0,c.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),u=(0,c.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var m=s(69074),h=s(91788);let p=(0,c.A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);var x=s(5040),g=s(44838),f=s(25531),y=s(29797);let v=()=>{let[e,t]=(0,n.useState)(1),[s,c]=(0,n.useState)(10),[v,b]=(0,n.useState)(new Set),{data:w=[],isLoading:j,error:N,refetch:k}=(0,r.I)({queryKey:["question-papers"],queryFn:f.ig,staleTime:3e5}),A=Math.ceil(w.length/s),q=(e-1)*s,P=w.slice(q,q+s),S=e=>{let t=[];return e.isMultiSubject&&e.sections?e.sections.forEach(e=>{e.questions&&Array.isArray(e.questions)&&e.questions.forEach(s=>{let a=s.question||s;t.push({question:a.content||a.question||"",options:a.options||[],answer:a.answer||"",subject:e.subjectName||e.name||"General",imageUrls:a.imageUrls||[],solution:a.solution||null,hints:a.hints||[]})})}):e.questions&&Array.isArray(e.questions)&&e.questions.forEach(s=>{var a;t.push({question:s.content||s.question||"",options:s.options||[],answer:s.answer||"",subject:(null===(a=e.subjectId)||void 0===a?void 0:a.name)||"General",imageUrls:s.imageUrls||[],solution:s.solution||null,hints:s.hints||[]})}),t},M=async(e,t,s)=>{let a=S(e),n={title:e.title||"Question Paper",description:e.description||"",duration:e.duration||60,totalMarks:e.totalMarks||100,questions:a,includeAnswers:!1,filename:t,collegeName:(null==s?void 0:s.name)||"",collegeLogoUrl:(null==s?void 0:s.logoUrl)||""},r=await fetch("/api/generate-paper-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!r.ok)throw Error("Failed to generate questions PDF");return r.blob()},_=async(e,t,s)=>{let a=S(e),n={title:e.title||"Question Paper",description:e.description||"",duration:e.duration||60,totalMarks:e.totalMarks||100,questions:a,filename:t,collegeName:(null==s?void 0:s.name)||"",collegeLogoUrl:(null==s?void 0:s.logoUrl)||""};console.log("Calling /api/generate-solutions-pdf with payload:",n);let r=await fetch("/api/generate-solutions-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n)});if(!r.ok)throw Error("Failed to generate solutions PDF");return r.blob()},U=async(e,t)=>{let s=S(e),a={title:e.title||"Question Paper",questions:s,filename:t},n=await fetch("/api/generate-answers-excel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error("Failed to generate answers Excel");return n.blob()},E=async(e,t)=>{try{let s;b(t=>new Set(t).add(e._id));let a=await (0,f.cH)(e._id),n=a.questionPaper,r=a.college,o=n.questions&&n.questions.length>0,i=n.sections&&n.sections.some(e=>e.questions&&e.questions.length>0);if(!o&&!i)throw Error("This question paper does not contain any questions. It may have been created incorrectly.");let l=new Date().toISOString().slice(0,19).replace(/[:-]/g,""),c=Math.random().toString(36).substring(2,8),d=e.title.replace(/[^a-zA-Z0-9\s]/g,"").replace(/\s+/g,"_");switch(t){case"questions":s="".concat(d,"_Questions_").concat(l,"_").concat(c,".pdf");let u=await M(n,s,r),m=window.URL.createObjectURL(u),h=document.createElement("a");h.href=m,h.download=s,document.body.appendChild(h),h.click(),document.body.removeChild(h),window.URL.revokeObjectURL(m);break;case"answers":s="".concat(d,"_Answers_").concat(l,"_").concat(c,".xlsx");let p=await U(n,s),x=window.URL.createObjectURL(p),g=document.createElement("a");g.href=x,g.download=s,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(x);break;case"solutions":s="".concat(d,"_Solutions_").concat(l,"_").concat(c,".pdf"),console.log("Generating solutions PDF with filename:",s);let y=await _(n,s,r),v=window.URL.createObjectURL(y),w=document.createElement("a");w.href=v,w.download=s,w.setAttribute("download",s),document.body.appendChild(w),w.click(),document.body.removeChild(w),window.URL.revokeObjectURL(v),console.log("Solutions PDF download initiated with filename:",s);break;default:throw Error("Invalid download type")}console.log("Download completed successfully")}catch(e){console.error("Download failed:",e),alert(e instanceof Error?e.message:"Failed to download the question paper. Please try again.")}finally{b(t=>{let s=new Set(t);return s.delete(e._id),s})}},C=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Downloaded Papers"}),(0,a.jsx)(o.A,{items:[{label:"Home",href:"/teacher"},{label:"...",href:"#"},{label:"Downloaded Papers"}],className:"text-sm mt-1"})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Question Papers"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[w.length," total papers available for download"]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>k(),disabled:j,className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),j?"Refreshing...":"Refresh"]})})]})}),(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:j?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Loading question papers..."})]})}):N?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-500 mb-4",children:"Failed to load question papers"}),(0,a.jsx)(i.$,{onClick:()=>k(),variant:"outline",children:"Try Again"})]})}):0===w.length?(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(l.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500 mb-2",children:"No question papers found"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Generate your first question paper to see it here"})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b border-gray-200 bg-gray-50",children:[(0,a.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Paper Title"}),(0,a.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Subject"}),(0,a.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Details"}),(0,a.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Created"}),(0,a.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Status"}),(0,a.jsx)("th",{className:"py-3 px-6 text-center font-medium text-gray-700",children:"Actions"})]})}),(0,a.jsx)("tbody",{children:P.map(e=>(0,a.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(l.A,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900 line-clamp-2",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["ID: ",e._id.slice(-8)]})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:e.subjectId?(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.subjectId.name}):(0,a.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:["Multi-Subject",e.subjectCount?" (".concat(e.subjectCount,")"):""]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[(0,a.jsx)(d,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[e.totalMarks," marks"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[(0,a.jsx)(u,{className:"w-3 h-3"}),(0,a.jsxs)("span",{children:[e.duration," min"]})]})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[(0,a.jsx)(m.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:C(e.createdAt)})]})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat("active"===e.status.toLowerCase()?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"),children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,a.jsx)("td",{className:"py-4 px-6",children:(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)(g.rI,{children:[(0,a.jsx)(g.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"outline",size:"sm",disabled:v.has(e._id),className:"flex items-center gap-2",children:[(0,a.jsx)(h.A,{className:"w-4 h-4"}),v.has(e._id)?"Downloading...":"Download"]})}),(0,a.jsxs)(g.SQ,{align:"end",className:"w-48",children:[(0,a.jsxs)(g._2,{onClick:()=>E(e,"questions"),disabled:v.has(e._id),className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)(l.A,{className:"w-4 h-4"}),"Download Questions"]}),(0,a.jsxs)(g._2,{onClick:()=>E(e,"answers"),disabled:v.has(e._id),className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)(p,{className:"w-4 h-4"}),"Download Answers"]}),(0,a.jsxs)(g._2,{onClick:()=>E(e,"solutions"),disabled:v.has(e._id),className:"flex items-center gap-2 cursor-pointer",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),"Download Solutions"]})]})]})})})]},e._id))})]})}),A>1&&(0,a.jsx)("div",{className:"px-6 py-4 border-t border-gray-200",children:(0,a.jsx)(y.d,{currentPage:e,totalPages:A,onPageChange:t,pageSize:s,totalItems:w.length,onPageSizeChange:c})})]})})]})]})})}},57434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,t,s)=>{"use strict";s.d(t,{b:()=>o,cn:()=>r});var a=s(52596),n=s(17307);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}function o(e){return new Promise((t,s)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>t(a.result),a.onerror=e=>s(e)})}},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},79776:(e,t,s)=>{Promise.resolve().then(s.bind(s,46872))},91788:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,1141,2571,6457,1165,6967,7546,8441,1684,7358],()=>t(79776)),_N_E=e.O()}]);