(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9237],{2564:(e,t,r)=>{"use strict";r.d(t,{b:()=>i,s:()=>o});var n=r(12115),l=r(63540),a=r(95155),o=n.forwardRef((e,t)=>(0,a.jsx)(l.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));o.displayName="VisuallyHidden";var i=o},2603:function(e,t,r){var n,l,a;a=function(e,t,r,n){"use strict";function l(e){return e&&e.__esModule?e:{default:e}}function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}Object.defineProperty(e,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{BlockMath:()=>c,InlineMath:()=>d}),t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=l?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(t),r=l(r),n=l(n);let o=(e,{displayMode:l})=>{let a=({children:r,errorColor:a,math:o,renderError:i})=>{let s=null!=o?o:r,{html:u,error:c}=(0,t.useMemo)(()=>{try{return{html:n.default.renderToString(s,{displayMode:l,errorColor:a,throwOnError:!!i}),error:void 0}}catch(e){if(e instanceof n.default.ParseError||e instanceof TypeError)return{error:e};throw e}},[s,a,i]);return c?i?i(c):t.default.createElement(e,{html:`${c.message}`}):t.default.createElement(e,{html:u})};return a.propTypes={children:r.default.string,errorColor:r.default.string,math:r.default.string,renderError:r.default.func},a},i={html:r.default.string.isRequired},s=({html:e})=>t.default.createElement("div",{"data-testid":"react-katex",dangerouslySetInnerHTML:{__html:e}});s.propTypes=i;let u=({html:e})=>t.default.createElement("span",{"data-testid":"react-katex",dangerouslySetInnerHTML:{__html:e}});u.propTypes=i;let c=o(s,{displayMode:!0}),d=o(u,{displayMode:!1})},"object"==typeof e.exports?a(t,r(12115),r(38637),r(97707)):(n=[t,r(12115),r(38637),r(97707)],void 0===(l=a.apply(t,n))||(e.exports=l))},5040:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13717:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},27213:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},31491:()=>{},35563:(e,t,r)=>{"use strict";r.d(t,{rc:()=>P,ZD:()=>T,UC:()=>R,VY:()=>V,hJ:()=>C,ZL:()=>D,bL:()=>E,hE:()=>S});var n=r(12115),l=r(95155);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return n.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}(...e),e)}var i=r(4033),s=r(66634),u="AlertDialog",[c,d]=function(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return a.scopeName=e,[function(t,a){let o=n.createContext(a),i=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,u=r?.[e]?.[i]||o,c=n.useMemo(()=>s,Object.values(s));return(0,l.jsx)(u.Provider,{value:c,children:a})};return s.displayName=t+"Provider",[s,function(r,l){let s=l?.[e]?.[i]||o,u=n.useContext(s);if(u)return u;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(a,...t)]}(u,[i.Hs]),p=(0,i.Hs)(),f=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,l.jsx)(i.bL,{...n,...r,modal:!0})};f.displayName=u,n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=p(r);return(0,l.jsx)(i.l9,{...a,...n,ref:t})}).displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,l.jsx)(i.ZL,{...n,...r})};y.displayName="AlertDialogPortal";var h=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=p(r);return(0,l.jsx)(i.hJ,{...a,...n,ref:t})});h.displayName="AlertDialogOverlay";var m="AlertDialogContent",[v,g]=c(m),b=(0,s.Dc)("AlertDialogContent"),x=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:a,...s}=e,u=p(r),c=n.useRef(null),d=o(t,c),f=n.useRef(null);return(0,l.jsx)(i.G$,{contentName:m,titleName:k,docsSlug:"alert-dialog",children:(0,l.jsx)(v,{scope:r,cancelRef:f,children:(0,l.jsxs)(i.UC,{role:"alertdialog",...u,...s,ref:d,onOpenAutoFocus:function(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}(s.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=f.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,l.jsx)(b,{children:a}),(0,l.jsx)(N,{contentRef:c})]})})})});x.displayName=m;var k="AlertDialogTitle",A=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=p(r);return(0,l.jsx)(i.hE,{...a,...n,ref:t})});A.displayName=k;var j="AlertDialogDescription",w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=p(r);return(0,l.jsx)(i.VY,{...a,...n,ref:t})});w.displayName=j;var _=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,a=p(r);return(0,l.jsx)(i.bm,{...a,...n,ref:t})});_.displayName="AlertDialogAction";var O="AlertDialogCancel",M=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:a}=g(O,r),s=p(r),u=o(t,a);return(0,l.jsx)(i.bm,{...s,...n,ref:u})});M.displayName=O;var N=e=>{let{contentRef:t}=e,r="`".concat(m,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(m,"` by passing a `").concat(j,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(m,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},E=f,D=y,C=h,R=x,P=_,T=M,S=A,V=w},38637:(e,t,r)=>{e.exports=r(79399)()},42355:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},47924:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66634:(e,t,r)=>{"use strict";r.d(t,{Slot:()=>i,TL:()=>o,Dc:()=>u});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var a=r(95155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{var r,a,o;let i,s;let{children:u,...c}=e,d=function(...e){return n.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}(...e),e)}(n.isValidElement(u)?(s=(i=null===(a=Object.getOwnPropertyDescriptor((r=u).props,"ref"))||void 0===a?void 0:a.get)&&"isReactWarning"in i&&i.isReactWarning)?r.ref:(s=(i=null===(o=Object.getOwnPropertyDescriptor(r,"ref"))||void 0===o?void 0:o.get)&&"isReactWarning"in i&&i.isReactWarning)?r.props.ref:r.props.ref||r.ref:void 0,t);if(n.isValidElement(u)){let e=function(e,t){let r={...t};for(let n in t){let l=e[n],a=t[n];/^on[A-Z]/.test(n)?l&&a?r[n]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let n=a(...t);return l(...t),n}:l&&(r[n]=l):"style"===n?r[n]={...l,...a}:"className"===n&&(r[n]=[l,a].filter(Boolean).join(" "))}return{...e,...r}}(c,u.props);return u.type!==n.Fragment&&(e.ref=d),n.cloneElement(u,e)}return n.Children.count(u)>1?n.Children.only(null):null});return t.displayName="".concat(e,".SlotClone"),t}(e),r=n.forwardRef((e,r)=>{let{children:l,...o}=e,i=n.Children.toArray(l),s=i.find(c);if(s){let e=s.props.children,l=i.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,a.jsx)(t,{...o,ref:r,children:l})});return r.displayName="".concat(e,".Slot"),r}var i=o("Slot"),s=Symbol("radix.slottable");function u(e){let t=e=>{let{children:t}=e;return(0,a.jsx)(a.Fragment,{children:t})};return t.displayName="".concat(e,".Slottable"),t.__radixId=s,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},70463:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},72948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let a=l(t)||l(n);return o[e][a]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,s,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...u}[t]):({...i,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},75478:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("beaker",[["path",{d:"M4.5 3h15",key:"c7n0jr"}],["path",{d:"M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3",key:"m1uhx7"}],["path",{d:"M6 14h12",key:"4cwo0f"}]])},79399:(e,t,r)=>{"use strict";var n=r(72948);function l(){}function a(){}a.resetWarningCache=l,e.exports=function(){function e(e,t,r,l,a,o){if(o!==n){var i=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:l};return r.PropTypes=r,r}},84616:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}}]);