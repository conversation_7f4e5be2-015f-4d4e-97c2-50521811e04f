(()=>{var e={};e.id=7759,e.ids=[7759],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6869:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),c=s(30893),o={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["add-subjectandtopic",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7293)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/add-subjectandtopic/page",pathname:"/admin/add-subjectandtopic",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7293:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-subjectandtopic\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>l});var a=s(60687);s(43210);var r=s(17403),i=s(78272),n=s(13964),c=s(3589),o=s(4780);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function l({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...n}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(x,{})]})})}function m({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(c.A,{className:"size-4"})})}function x({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20943:(e,t,s)=>{Promise.resolve().then(s.bind(s,99111))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39654:(e,t,s)=>{Promise.resolve().then(s.bind(s,77481))},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42505:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(37413),r=s(92555);function i(){return(0,a.jsx)(r.W,{message:"Loading admin dashboard..."})}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>n});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61193:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(60687),r=s(66327),i=s(99557),n=s(45285),c=s(53355);function o({children:e}){return(0,a.jsx)(n.A,{allowedRoles:[i.g.SUPER_ADMIN],children:(0,a.jsx)(c.default,{children:(0,a.jsx)(r.N,{role:i.g.SUPER_ADMIN,children:e})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var a=s(60687),r=s(43210),i=s.n(r),n=s(85814),c=s.n(n),o=s(4780),d=s(14952),l=s(93661);let u=({items:e,maxItems:t=4,className:s})=>{let r=i().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,a.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,o.cn)("flex items-center text-sm",s),children:(0,a.jsx)("ol",{className:"flex items-center space-x-1",children:r.map((e,t)=>{let s=t===r.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)(d.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}):s?(0,a.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,a.jsx)(c(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},63564:(e,t,s)=>{"use strict";s.d(t,{Kd:()=>r,OY:()=>i,Tw:()=>n,do:()=>c});var a=s(62185);let r=async(e,t)=>{try{let s=new URLSearchParams;e&&s.append("chapterId",e),t&&s.append("subjectId",t);let r=s.toString(),i=r?`/topics?${r}`:"/topics";return await (0,a.apiCall)(i)}catch(e){throw console.error("Error fetching topics:",e),Error(e.message||"Failed to fetch topics")}},i=async e=>{try{return await (0,a.apiCall)("/topics",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating topic:",e),Error(e.message||"Failed to create topic")}},n=async(e,t)=>{try{return await (0,a.apiCall)(`/topics/${e}`,{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error(`Error updating topic ${e}:`,t),Error(t.message||"Failed to update topic")}},c=async e=>{try{await (0,a.apiCall)(`/topics/${e}`,{method:"DELETE"})}catch(t){throw console.error(`Error deleting topic ${e}:`,t),Error(t.message||"Failed to delete topic")}}},74075:e=>{"use strict";e.exports=require("zlib")},77481:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(60687),r=s(43210),i=s(63239),n=s(29523),c=s(12048),o=s(80013),d=s(15079),l=s(44493),u=s(96834),p=s(41862),m=s(96474),h=s(62688);let x=(0,h.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var j=s(11860);let g=(0,h.A)("pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);var b=s(88233),f=s(52581),v=s(82013),y=s(63564);function N({title:e="Subject & Topic Manager",description:t="Manage subjects and topics in a two-level hierarchy",onDataChange:s,initialSubjects:i=[],initialTopics:h=[]}){let[N,w]=(0,r.useState)(i),[C,E]=(0,r.useState)(h),[k,A]=(0,r.useState)(""),[S,F]=(0,r.useState)(""),[P,z]=(0,r.useState)(""),[_,R]=(0,r.useState)(""),[T,q]=(0,r.useState)(""),[D,M]=(0,r.useState)(null),[$,L]=(0,r.useState)(null),[U,O]=(0,r.useState)(""),[I,J]=(0,r.useState)(""),[Z,B]=(0,r.useState)(""),[G,W]=(0,r.useState)(""),[H,K]=(0,r.useState)(!1),[V,Q]=(0,r.useState)(!1),Y=async()=>{if(!S.trim()){(0,f.oR)({title:"Error",description:"Subject name cannot be empty",variant:"destructive"});return}try{Q(!0);let e={name:S.trim(),description:P.trim()||void 0},t=await (0,v.Fb)(e),s={id:t._id,name:t.name,description:t.description};w(e=>[...e,s]),F(""),z(""),(0,f.oR)({title:"Success",description:"Subject added successfully"})}catch(e){console.error("Error adding subject:",e),(0,f.oR)({title:"Error",description:e.message||"Failed to add subject",variant:"destructive"})}finally{Q(!1)}},X=async e=>{if(!U.trim()){(0,f.oR)({title:"Error",description:"Subject name cannot be empty",variant:"destructive"});return}try{Q(!0);let t={name:U.trim(),description:I.trim()||void 0};await (0,v.iQ)(e,t),w(t=>t.map(t=>t.id===e?{...t,name:U.trim(),description:I.trim()||void 0}:t)),M(null),O(""),J(""),(0,f.oR)({title:"Success",description:"Subject updated successfully"})}catch(e){console.error("Error updating subject:",e),(0,f.oR)({title:"Error",description:e.message||"Failed to update subject",variant:"destructive"})}finally{Q(!1)}},ee=async e=>{try{Q(!0),await (0,v.gg)(e),w(t=>t.filter(t=>t.id!==e)),setChapters(t=>t.filter(t=>t.subjectId!==e)),k===e&&A(""),(0,f.oR)({title:"Success",description:"Subject deleted successfully"})}catch(e){console.error("Error deleting subject:",e),(0,f.oR)({title:"Error",description:e.message||"Failed to delete subject",variant:"destructive"})}finally{Q(!1)}},et=e=>{M(e.id),O(e.name),J(e.description||"")},es=()=>{M(null),O(""),J("")},ea=e=>C.filter(t=>t.subjectId===e),er=N.find(e=>e.id===k),ei=ea(k),en=async()=>{if(_.trim()&&k)try{Q(!0);let e={name:_.trim(),subjectId:k,description:T.trim()||void 0},t=await (0,y.OY)(e),s={id:t._id,name:t.name,subjectId:k,description:t.description};E(e=>[...e,s]),R(""),q(""),(0,f.oR)({title:"Success",description:"Topic added successfully"})}catch(e){console.error("Error adding topic:",e),(0,f.oR)({title:"Error",description:e.message||"Failed to add topic",variant:"destructive"})}finally{Q(!1)}},ec=async e=>{if(Z.trim())try{Q(!0);let t={name:Z.trim(),description:G.trim()||void 0},s=await (0,y.Tw)(e,t);E(t=>t.map(t=>t.id===e?{...t,name:s.name,description:s.description}:t)),L(null),B(""),W(""),(0,f.oR)({title:"Success",description:"Topic updated successfully"})}catch(e){console.error("Error updating topic:",e),(0,f.oR)({title:"Error",description:e.message||"Failed to update topic",variant:"destructive"})}finally{Q(!1)}},eo=async e=>{try{Q(!0),await (0,y.do)(e),E(t=>t.filter(t=>t.id!==e)),(0,f.oR)({title:"Success",description:"Topic deleted successfully"})}catch(e){console.error("Error deleting topic:",e),(0,f.oR)({title:"Error",description:e.message||"Failed to delete topic",variant:"destructive"})}finally{Q(!1)}},ed=e=>{L(e.id),B(e.name),W(e.description||"")},el=()=>{L(null),B(""),W("")};return H?(0,a.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading subjects, chapters, and topics..."})]}):(0,a.jsxs)("div",{className:"w-full max-w-6xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:e}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:t})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Subjects"]}),(0,a.jsx)(l.BT,{children:"Add and manage subjects"})]}),(0,a.jsxs)(l.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"new-subject-name",children:"Subject Name"}),(0,a.jsx)(c.p,{id:"new-subject-name",placeholder:"Enter subject name",value:S,onChange:e=>F(e.target.value),onKeyPress:e=>"Enter"===e.key&&Y()})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"new-subject-description",children:"Description (Optional)"}),(0,a.jsx)(c.p,{id:"new-subject-description",placeholder:"Enter subject description",value:P,onChange:e=>z(e.target.value),onKeyPress:e=>"Enter"===e.key&&Y()})]}),(0,a.jsx)(n.$,{onClick:Y,className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:V,children:V?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Add Subject"]})})]}),(0,a.jsxs)("div",{className:"space-y-2 mt-6",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["All Subjects (",N.length,")"]}),0===N.length?(0,a.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:"No subjects added yet"}):(0,a.jsx)("div",{className:"space-y-2 max-h-[300px] overflow-y-auto pr-2",children:N.map(e=>(0,a.jsx)("div",{className:`p-3 border rounded-md cursor-pointer transition-colors ${k===e.id?"border-primary bg-primary/5":"border-border hover:bg-muted/50"}`,onClick:()=>A(e.id),children:D===e.id?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.p,{value:U,onChange:e=>O(e.target.value),placeholder:"Subject name",className:"mb-2"}),(0,a.jsx)(c.p,{value:I,onChange:e=>J(e.target.value),placeholder:"Description (optional)",className:"mb-2"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{size:"sm",onClick:()=>X(e.id),className:"flex-1",disabled:V,children:V?(0,a.jsx)(p.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(x,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{size:"sm",variant:"outline",onClick:es,className:"flex-1",disabled:V,children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(n.$,{size:"icon",variant:"ghost",onClick:t=>{t.stopPropagation(),et(e)},disabled:V,children:(0,a.jsx)(g,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{size:"icon",variant:"ghost",onClick:t=>{t.stopPropagation(),ee(e.id)},disabled:V,children:(0,a.jsx)(b.A,{className:"h-4 w-4 text-destructive"})})]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description}),(0,a.jsxs)(u.E,{variant:"outline",className:"mt-2",children:[ea(e.id).length," topics"]})]})},e.id))})]})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Topics"]}),(0,a.jsx)(l.BT,{children:"Manage topics under subjects"})]}),(0,a.jsxs)(l.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"topic-subject-select",children:"Select Subject"}),(0,a.jsxs)(d.l6,{value:k,onValueChange:A,disabled:0===N.length,children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:0===N.length?"No subjects available":"Select a subject"})}),(0,a.jsx)(d.gC,{children:N.map(e=>(0,a.jsx)(d.eb,{value:e.id,children:e.name},e.id))})]})]}),k&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"new-topic-name",children:"Topic Name"}),(0,a.jsx)(c.p,{id:"new-topic-name",placeholder:"Enter topic name",value:_,onChange:e=>R(e.target.value),onKeyPress:e=>"Enter"===e.key&&en(),disabled:!k})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"new-topic-description",children:"Description (Optional)"}),(0,a.jsx)(c.p,{id:"new-topic-description",placeholder:"Enter topic description",value:T,onChange:e=>q(e.target.value),onKeyPress:e=>"Enter"===e.key&&en(),disabled:!k})]}),(0,a.jsx)(n.$,{onClick:en,className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:!k||V,children:V?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Add Topic"]})})]}),(0,a.jsxs)("div",{className:"space-y-2 mt-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium",children:er?`Topics for ${er.name} (${ei.length})`:"Select a subject to view topics"}),ei.length>0?ei.map(e=>(0,a.jsx)("div",{className:"p-3 border rounded-lg",children:$===e.id?(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.p,{value:Z,onChange:e=>B(e.target.value),placeholder:"Topic name",disabled:V}),(0,a.jsx)(c.p,{value:G,onChange:e=>W(e.target.value),placeholder:"Topic description (optional)",disabled:V}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(n.$,{size:"sm",onClick:()=>ec(e.id),className:"flex-1",disabled:V,children:V?(0,a.jsx)(p.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(x,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{size:"sm",variant:"outline",onClick:el,className:"flex-1",disabled:V,children:(0,a.jsx)(j.A,{className:"h-4 w-4"})})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex gap-1",children:[(0,a.jsx)(n.$,{size:"icon",variant:"ghost",onClick:()=>ed(e),disabled:V,children:(0,a.jsx)(g,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{size:"icon",variant:"ghost",onClick:()=>eo(e.id),disabled:V,children:(0,a.jsx)(b.A,{className:"h-4 w-4 text-destructive"})})]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description})]})},e.id)):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:k?"No topics yet":"Select a subject to manage topics"})]})]})]})]}),N.length>0&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Summary"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:N.map(e=>{let t=ea(e.id);return(0,a.jsxs)("div",{className:"p-3 border rounded-lg",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:e.name}),(0,a.jsx)("div",{className:"space-y-2",children:t.length>0?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mb-2",children:[t.length," topics"]}),(0,a.jsx)("div",{className:"space-y-1",children:t.map(e=>(0,a.jsx)(u.E,{variant:"secondary",className:"mr-1 mb-1 text-xs",children:e.name},e.id))})]}):(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No topics yet"})})]},e.id)})})})]})]})}let w=()=>(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Add Subjects & Topics"}),(0,a.jsx)(i.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Add Subjects & Topics"}],className:"text-sm mt-1"})]}),(0,a.jsx)("div",{className:"container mx-auto py-10",children:(0,a.jsx)(N,{title:"Add Subjects & Topics",description:"Organize your educational content in a two-level hierarchy",onDataChange:(e,t)=>{console.log("Data updated:",{subjects:e,topics:t})}})})]})})},78148:(e,t,s)=>{"use strict";s.d(t,{b:()=>c});var a=s(43210),r=s(3416),i=s(60687),n=a.forwardRef((e,t)=>(0,i.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var c=n},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80775:(e,t,s)=>{Promise.resolve().then(s.bind(s,61193))},81630:e=>{"use strict";e.exports=require("http")},82013:(e,t,s)=>{"use strict";s.d(t,{CG:()=>i,Fb:()=>o,Nj:()=>r,getSubjectsWithTopics:()=>c,gg:()=>l,iQ:()=>d,py:()=>n});var a=s(62185);let r=async()=>{try{return await (0,a.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},i=async()=>{try{return(await (0,a.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},n=async()=>{try{return await (0,a.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},c=async()=>{try{return await (0,a.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},o=async e=>{try{return await (0,a.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},d=async(e,t)=>{try{return await (0,a.apiCall)(`/subjects/${e}`,{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error(`Error updating subject ${e}:`,t),Error(t.message||"Failed to update subject")}},l=async e=>{try{await (0,a.apiCall)(`/subjects/${e}`,{method:"DELETE"})}catch(t){throw console.error(`Error deleting subject ${e}:`,t),Error(t.message||"Failed to delete subject")}}},83997:e=>{"use strict";e.exports=require("tty")},88233:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93661:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx","default")},99486:(e,t,s)=>{Promise.resolve().then(s.bind(s,7293))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,4619,3287,9592,2581,6822,4707,6658],()=>s(6869));module.exports=a})();