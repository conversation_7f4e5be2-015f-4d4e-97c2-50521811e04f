"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8377],{2564:(e,t,n)=>{n.d(t,{b:()=>a,s:()=>l});var r=n(12115),o=n(63540),i=n(95155),l=r.forwardRef((e,t)=>(0,i.jsx)(o.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));l.displayName="VisuallyHidden";var a=l},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),i=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),s=r.useRef({}),u=r.useRef(e),c=r.useRef("none"),[d,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=a(s.current);c.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=s.current,n=u.current;if(n!==e){let r=c.current,o=a(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),u.current=e}},[e,p]),(0,i.N)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=a(s.current).includes(e.animationName);if(e.target===o&&r&&(p("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=a(s.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}p("ANIMATION_END")},[o,p]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),l(e)},[])}}(t),s="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),u=(0,o.s)(l.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof n||l.isPresent?r.cloneElement(s,{ref:u}):null};function a(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},54416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},78082:(e,t,n)=>{n.d(t,{i3:()=>K,UC:()=>z,ZL:()=>Y,Kq:()=>q,bL:()=>G,l9:()=>X});var r=n(12115),o=n(85185),i=n(6101),l=n(46081),a=n(19178),s=n(61285),u=n(22197),c=n(34378),d=n(28905),p=n(63540),f=n(95155);r.forwardRef((e,t)=>{let{children:n,...o}=e,i=r.Children.toArray(n),l=i.find(h);if(l){let e=l.props.children,n=i.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,f.jsx)(m,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,f.jsx)(m,{...o,ref:t,children:n})}).displayName="Slot";var m=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),l=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(l.ref=t?(0,i.t)(t,e):e),r.cloneElement(n,l)}return r.Children.count(n)>1?r.Children.only(null):null});m.displayName="SlotClone";var v=({children:e})=>(0,f.jsx)(f.Fragment,{children:e});function h(e){return r.isValidElement(e)&&e.type===v}var y=n(5845),g=n(2564),[x,w]=(0,l.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),C="TooltipProvider",T="tooltip.open",[E,N]=x(C),R=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,[a,s]=r.useState(!0),u=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,f.jsx)(E,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),s(!1)},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>s(!0),o)},[o]),isPointerInTransitRef:u,onPointerInTransitChange:r.useCallback(e=>{u.current=e},[]),disableHoverableContent:i,children:l})};R.displayName=C;var O="Tooltip",[k,j]=x(O),M=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:c}=e,d=N(O,e.__scopeTooltip),p=b(t),[m,v]=r.useState(null),h=(0,s.B)(),g=r.useRef(0),x=null!=a?a:d.disableHoverableContent,w=null!=c?c:d.delayDuration,C=r.useRef(!1),[E=!1,R]=(0,y.i)({prop:o,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(T))):d.onClose(),null==l||l(e)}}),j=r.useMemo(()=>E?C.current?"delayed-open":"instant-open":"closed",[E]),M=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,C.current=!1,R(!0)},[R]),L=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,R(!1)},[R]),P=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{C.current=!0,R(!0),g.current=0},w)},[w,R]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,f.jsx)(u.bL,{...p,children:(0,f.jsx)(k,{scope:t,contentId:h,open:E,stateAttribute:j,trigger:m,onTriggerChange:v,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayed?P():M()},[d.isOpenDelayed,P,M]),onTriggerLeave:r.useCallback(()=>{x?L():(window.clearTimeout(g.current),g.current=0)},[L,x]),onOpen:M,onClose:L,disableHoverableContent:x,children:n})})};M.displayName=O;var L="TooltipTrigger",P=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,a=j(L,n),s=N(L,n),c=b(n),d=r.useRef(null),m=(0,i.s)(t,d,a.onTriggerChange),v=r.useRef(!1),h=r.useRef(!1),y=r.useCallback(()=>v.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",y),[y]),(0,f.jsx)(u.Mz,{asChild:!0,...c,children:(0,f.jsx)(p.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:m,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{v.current=!0,document.addEventListener("pointerup",y,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{v.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});P.displayName=L;var _="TooltipPortal",[A,I]=x(_,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,i=j(_,t);return(0,f.jsx)(A,{scope:t,forceMount:n,children:(0,f.jsx)(d.C,{present:n||i.open,children:(0,f.jsx)(c.Z,{asChild:!0,container:o,children:r})})})};D.displayName=_;var U="TooltipContent",S=r.forwardRef((e,t)=>{let n=I(U,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=j(U,e.__scopeTooltip);return(0,f.jsx)(d.C,{present:r||l.open,children:l.disableHoverableContent?(0,f.jsx)(V,{side:o,...i,ref:t}):(0,f.jsx)(W,{side:o,...i,ref:t})})}),W=r.forwardRef((e,t)=>{let n=j(U,e.__scopeTooltip),o=N(U,e.__scopeTooltip),l=r.useRef(null),a=(0,i.s)(t,l),[s,u]=r.useState(null),{trigger:c,onClose:d}=n,p=l.current,{onPointerInTransitChange:m}=o,v=r.useCallback(()=>{u(null),m(!1)},[m]),h=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),m(!0)},[m]);return r.useEffect(()=>()=>v(),[v]),r.useEffect(()=>{if(c&&p){let e=e=>h(e,p),t=e=>h(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,h,v]),r.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,s=t[i].x,u=t[i].y;a>r!=u>r&&n<(s-l)*(r-a)/(u-a)+l&&(o=!o)}return o}(n,s);r?v():o&&(v(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,v]),(0,f.jsx)(V,{...e,ref:a})}),[F,B]=x(O,{isInside:!1}),V=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=j(U,n),p=b(n),{onClose:m}=d;return r.useEffect(()=>(document.addEventListener(T,m),()=>document.removeEventListener(T,m)),[m]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&m()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,m]),(0,f.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,f.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,f.jsx)(v,{children:o}),(0,f.jsx)(F,{scope:n,isInside:!0,children:(0,f.jsx)(g.b,{id:d.contentId,role:"tooltip",children:i||o})})]})})});S.displayName=U;var H="TooltipArrow",Z=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=b(n);return B(H,n).isInside?null:(0,f.jsx)(u.i3,{...o,...r,ref:t})});Z.displayName=H;var q=R,G=M,X=P,Y=D,z=S,K=Z}}]);