"use strict";exports.id=9371,exports.ids=[9371],exports.modules={6862:(e,t,a)=>{a.d(t,{Lt:()=>O,Rx:()=>L,Zr:()=>S,EO:()=>E,$v:()=>I,ck:()=>H,wd:()=>P,r7:()=>F});var r=a(60687),n=a(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return n.useCallback(function(...e){return t=>{let a=!1,r=e.map(e=>{let r=o(e,t);return a||"function"!=typeof r||(a=!0),r});if(a)return()=>{for(let t=0;t<r.length;t++){let a=r[t];"function"==typeof a?a():o(e[t],null)}}}}(...e),e)}var s=a(6491),i=a(11329),c="AlertDialog",[d,u]=function(e,t=[]){let a=[],o=()=>{let t=a.map(e=>n.createContext(e));return function(a){let r=a?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...a,[e]:r}}),[a,r])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),s=a.length;a=[...a,o];let i=t=>{let{scope:a,children:o,...i}=t,c=a?.[e]?.[s]||l,d=n.useMemo(()=>i,Object.values(i));return(0,r.jsx)(c.Provider,{value:d,children:o})};return i.displayName=t+"Provider",[i,function(a,r){let i=r?.[e]?.[s]||l,c=n.useContext(i);if(c)return c;if(void 0!==o)return o;throw Error(`\`${a}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let a=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=a.reduce((t,{useScope:a,scopeName:r})=>{let n=a(e)[`__scope${r}`];return{...t,...n}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return a.scopeName=t.scopeName,a}(o,...t)]}(c,[s.Hs]),f=(0,s.Hs)(),p=e=>{let{__scopeAlertDialog:t,...a}=e,n=f(t);return(0,r.jsx)(s.bL,{...n,...a,modal:!0})};p.displayName=c,n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,o=f(a);return(0,r.jsx)(s.l9,{...o,...n,ref:t})}).displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:t,...a}=e,n=f(t);return(0,r.jsx)(s.ZL,{...n,...a})};m.displayName="AlertDialogPortal";var x=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,o=f(a);return(0,r.jsx)(s.hJ,{...o,...n,ref:t})});x.displayName="AlertDialogOverlay";var g="AlertDialogContent",[y,h]=d(g),v=(0,i.Dc)("AlertDialogContent"),j=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:o,...i}=e,c=f(a),d=n.useRef(null),u=l(t,d),p=n.useRef(null);return(0,r.jsx)(s.G$,{contentName:g,titleName:N,docsSlug:"alert-dialog",children:(0,r.jsx)(y,{scope:a,cancelRef:p,children:(0,r.jsxs)(s.UC,{role:"alertdialog",...c,...i,ref:u,onOpenAutoFocus:function(e,t,{checkForDefaultPrevented:a=!0}={}){return function(r){if(e?.(r),!1===a||!r.defaultPrevented)return t?.(r)}}(i.onOpenAutoFocus,e=>{e.preventDefault(),p.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,r.jsx)(v,{children:o}),(0,r.jsx)(M,{contentRef:d})]})})})});j.displayName=g;var N="AlertDialogTitle",b=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,o=f(a);return(0,r.jsx)(s.hE,{...o,...n,ref:t})});b.displayName=N;var A="AlertDialogDescription",w=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,o=f(a);return(0,r.jsx)(s.VY,{...o,...n,ref:t})});w.displayName=A;var k=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,o=f(a);return(0,r.jsx)(s.bm,{...o,...n,ref:t})});k.displayName="AlertDialogAction";var D="AlertDialogCancel",$=n.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...n}=e,{cancelRef:o}=h(D,a),i=f(a),c=l(t,o);return(0,r.jsx)(s.bm,{...i,...n,ref:c})});$.displayName=D;var M=({contentRef:e})=>{let t=`\`${g}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${g}\` by passing a \`${A}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${g}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return n.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},R=a(4780),C=a(29523);function O({...e}){return(0,r.jsx)(p,{"data-slot":"alert-dialog",...e})}function z({...e}){return(0,r.jsx)(m,{"data-slot":"alert-dialog-portal",...e})}function _({className:e,...t}){return(0,r.jsx)(x,{"data-slot":"alert-dialog-overlay",className:(0,R.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function E({className:e,...t}){return(0,r.jsxs)(z,{children:[(0,r.jsx)(_,{}),(0,r.jsx)(j,{"data-slot":"alert-dialog-content",className:(0,R.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function P({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,R.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function H({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,R.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function F({className:e,...t}){return(0,r.jsx)(b,{"data-slot":"alert-dialog-title",className:(0,R.cn)("text-lg font-semibold",e),...t})}function I({className:e,...t}){return(0,r.jsx)(w,{"data-slot":"alert-dialog-description",className:(0,R.cn)("text-muted-foreground text-sm",e),...t})}function L({className:e,...t}){return(0,r.jsx)(k,{className:(0,R.cn)((0,C.r)(),e),...t})}function S({className:e,...t}){return(0,r.jsx)($,{className:(0,R.cn)((0,C.r)({variant:"outline"}),e),...t})}},47033:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},63143:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},88233:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93661:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},96474:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};