(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1913],{97707:function(e){var t;"undefined"!=typeof self&&self,e.exports=function(){"use strict";var e,t,r={};r.d=function(e,t){for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var n={};r.d(n,{default:function(){return nu}});class l{constructor(e,t){let r,n;this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;let i="KaTeX parse error: "+e,a=t&&t.loc;if(a&&a.start<=a.end){let e;let t=a.lexer.input;r=a.start,n=a.end,r===t.length?i+=" at end of input: ":i+=" at position "+(r+1)+": ";let l=t.slice(r,n).replace(/[^]/g,"$&̲");i+=(r>15?"…"+t.slice(r-15,r):t.slice(0,r))+l+(n+15<t.length?t.slice(n,n+15)+"…":t.slice(n))}let s=Error(i);return s.name="ParseError",s.__proto__=l.prototype,s.position=r,null!=r&&null!=n&&(s.length=n-r),s.rawMessage=e,s}}l.prototype.__proto__=Error.prototype;let i=/([A-Z])/g,a={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},s=/[&><"']/g,o=function(e){return"ordgroup"===e.type||"color"===e.type?1===e.body.length?o(e.body[0]):e:"font"===e.type?o(e.body):e},h=function(e){if(!e)throw Error("Expected non-null, but got "+String(e));return e};var m={contains:function(e,t){return -1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(s,e=>a[e])},hyphenate:function(e){return e.replace(i,"-$1").toLowerCase()},getBaseElem:o,isCharacterBox:function(e){let t=o(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type},protocolFromUrl:function(e){let t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?":"===t[2]&&/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?t[1].toLowerCase():null:"_relative"}};let c={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:e=>"#"+e},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(e,t)=>(t.push(e),t)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:e=>Math.max(0,e),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:e=>Math.max(0,e),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:e=>Math.max(0,e),cli:"-e, --max-expand <n>",cliProcessor:e=>"Infinity"===e?1/0:parseInt(e)},globalGroup:{type:"boolean",cli:!1}};class p{constructor(e){for(let t in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},c)if(c.hasOwnProperty(t)){let r=c[t];this[t]=void 0!==e[t]?r.processor?r.processor(e[t]):e[t]:function(e){if(e.default)return e.default;let t=e.type,r=Array.isArray(t)?t[0]:t;if("string"!=typeof r)return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}(r)}}reportNonstrict(e,t,r){let n=this.strict;if("function"==typeof n&&(n=n(e,t,r)),n&&"ignore"!==n){if(!0===n||"error"===n)throw new l("LaTeX-incompatible input and strict mode is set to 'error': "+(t+" [")+e+"]",r);"warn"===n?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" [")+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t+" [")+e+"]")}}useStrictBehavior(e,t,r){let n=this.strict;if("function"==typeof n)try{n=n(e,t,r)}catch(e){n="error"}return!!n&&"ignore"!==n&&(!0===n||"error"===n||("warn"===n?("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+(t+" [")+e+"]"),!1):("undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t+" [")+e+"]"),!1)))}isTrusted(e){if(e.url&&!e.protocol){let t=m.protocolFromUrl(e.url);if(null==t)return!1;e.protocol=t}return!!("function"==typeof this.trust?this.trust(e):this.trust)}}class u{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return d[g[this.id]]}sub(){return d[f[this.id]]}fracNum(){return d[b[this.id]]}fracDen(){return d[y[this.id]]}cramp(){return d[x[this.id]]}text(){return d[w[this.id]]}isTight(){return this.size>=2}}let d=[new u(0,0,!1),new u(1,0,!0),new u(2,1,!1),new u(3,1,!0),new u(4,2,!1),new u(5,2,!0),new u(6,3,!1),new u(7,3,!0)],g=[4,5,4,5,6,7,6,7],f=[5,5,5,5,7,7,7,7],b=[2,3,4,5,6,7,6,7],y=[3,3,5,5,7,7,7,7],x=[1,1,3,3,5,5,7,7],w=[0,1,2,3,2,3,2,3];var v={DISPLAY:d[0],TEXT:d[2],SCRIPT:d[4],SCRIPTSCRIPT:d[6]};let k=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}],S=[];function M(e){for(let t=0;t<S.length;t+=2)if(e>=S[t]&&e<=S[t+1])return!0;return!1}k.forEach(e=>e.blocks.forEach(e=>S.push(...e)));let z=function(e,t,r){var n,l,i,a,s,o;t*=1e3;let h="";switch(e){case"sqrtMain":h="M95,"+(622+(n=t)+80)+"\nc-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14\nc0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54\nc44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10\ns173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429\nc69,-144,104.5,-217.7,106.5,-221\nl"+n/2.075+" -"+n+"\nc5.3,-9.3,12,-14,20,-14\nH400000v"+(40+n)+"H845.2724\ns-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7\nc-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z\nM"+(834+n)+" 80h400000v"+(40+n)+"h-400000z";break;case"sqrtSize1":h="M263,"+(601+(l=t)+80)+"c0.7,0,18,39.7,52,119\nc34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120\nc340,-704.7,510.7,-1060.3,512,-1067\nl"+l/2.084+" -"+l+"\nc4.7,-7.3,11,-11,19,-11\nH40000v"+(40+l)+"H1012.3\ns-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232\nc-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1\ns-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26\nc-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z\nM"+(1001+l)+" 80h400000v"+(40+l)+"h-400000z";break;case"sqrtSize2":h="M983 "+(10+(i=t)+80)+"\nl"+i/3.13+" -"+i+"\nc4,-6.7,10,-10,18,-10 H400000v"+(40+i)+"\nH1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7\ns-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744\nc-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30\nc26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722\nc56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5\nc53.7,-170.3,84.5,-266.8,92.5,-289.5z\nM"+(1001+i)+" 80h400000v"+(40+i)+"h-400000z";break;case"sqrtSize3":h="M424,"+(2398+(a=t)+80)+"\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl"+a/4.223+" -"+a+"c4,-6.7,10,-10,18,-10 H400000\nv"+(40+a)+"H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M"+(1001+a)+" 80\nh400000v"+(40+a)+"h-400000z";break;case"sqrtSize4":h="M473,"+(2713+(s=t)+80)+"\nc339.3,-1799.3,509.3,-2700,510,-2702 l"+s/5.298+" -"+s+"\nc3.3,-7.3,9.3,-11,18,-11 H400000v"+(40+s)+"H1017.7\ns-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200\nc0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26\ns76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,\n606zM"+(1001+s)+" 80h400000v"+(40+s)+"H1017.7z";break;case"sqrtTall":h="M702 "+((o=t)+80)+"H400000"+(40+o)+"\nH742v"+(r-54-80-o)+"l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1\nh-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170\nc-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667\n219 661 l218 661zM702 80H400000v"+(40+o)+"H742z"}return h},A=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t+" H367z M367 0 H410 V")+t+" H367z";case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t+" H478z M478 0 H521 V")+t+" H478z";default:return""}},T={doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"},B=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v1759 h347 v-84\nH403z M403 1759 V0 H319 V1759 v"+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v1759 H0 v84 H347z\nM347 1759 V0 H263 V1759 v"+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z\nM367 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M410 15 H367 v585 v"+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+" v1715 h263 v84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+" v1799 H0 v-84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v602 h84z\nM403 1759 V0 H319 V1759 v"+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v602 h84z\nM347 1759 V0 h-84 V1759 v"+t+" v602 h84z";case"lparen":return"M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1\nc-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,\n-36,557 l0,"+(t+84)+"c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,\n949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9\nc0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,\n-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189\nl0,-"+(t+92)+"c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,\n-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z";case"rparen":return"M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,\n63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5\nc11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,"+(t+9)+"\nc-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664\nc-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11\nc0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17\nc242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558\nl0,-"+(t+144)+"c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,\n-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z";default:throw Error("Unknown stretchy delimiter.")}};class C{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return m.contains(this.classes,e)}toNode(){let e=document.createDocumentFragment();for(let t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){let e="";for(let t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){return this.children.map(e=>e.toText()).join("")}}var q={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-99e-5,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-99e-5,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-99e-5,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-99e-5,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}};let N={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},I={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function H(e,t,r){if(!q[t])throw Error("Font metrics not found for font: "+t+".");let n=e.charCodeAt(0),l=q[t][n];if(!l&&e[0]in I&&(n=I[e[0]].charCodeAt(0),l=q[t][n]),!l&&"text"===r&&M(n)&&(l=q[t][77]),l)return{depth:l[0],height:l[1],italic:l[2],skew:l[3],width:l[4]}}let R={},O=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],E=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],L=function(e,t){return t.size<2?e:O[e-1][t.size-1]};class D{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||D.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=E[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){let t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(let r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new D(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:L(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:E[e-1]})}havingBaseStyle(e){e=e||this.style.text();let t=L(D.BASESIZE,e);return this.size===t&&this.textSize===D.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){let e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==D.BASESIZE?["sizing","reset-size"+this.size,"size"+D.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){let t;if(!R[t=e>=5?0:e>=3?1:2]){let e=R[t]={cssEmPerMu:N.quad[t]/18};for(let r in N)N.hasOwnProperty(r)&&(e[r]=N[r][t])}return R[t]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}}D.BASESIZE=6;let V={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},P={ex:!0,em:!0,mu:!0},F=function(e){return"string"!=typeof e&&(e=e.unit),e in V||e in P||"ex"===e},G=function(e,t){let r;if(e.unit in V)r=V[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)r=t.fontMetrics().cssEmPerMu;else{let n;if(n=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)r=n.fontMetrics().xHeight;else if("em"===e.unit)r=n.fontMetrics().quad;else throw new l("Invalid unit: '"+e.unit+"'");n!==t&&(r*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},U=function(e){return+e.toFixed(4)+"em"},Y=function(e){return e.filter(e=>e).join(" ")},X=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");let e=t.getColor();e&&(this.style.color=e)}},_=function(e){let t=document.createElement(e);for(let e in t.className=Y(this.classes),this.style)this.style.hasOwnProperty(e)&&(t.style[e]=this.style[e]);for(let e in this.attributes)this.attributes.hasOwnProperty(e)&&t.setAttribute(e,this.attributes[e]);for(let e=0;e<this.children.length;e++)t.appendChild(this.children[e].toNode());return t},W=/[\s"'>/=\x00-\x1f]/,j=function(e){let t="<"+e;this.classes.length&&(t+=' class="'+m.escape(Y(this.classes))+'"');let r="";for(let e in this.style)this.style.hasOwnProperty(e)&&(r+=m.hyphenate(e)+":"+this.style[e]+";");for(let e in r&&(t+=' style="'+m.escape(r)+'"'),this.attributes)if(this.attributes.hasOwnProperty(e)){if(W.test(e))throw new l("Invalid attribute name '"+e+"'");t+=" "+e+'="'+m.escape(this.attributes[e])+'"'}t+=">";for(let e=0;e<this.children.length;e++)t+=this.children[e].toMarkup();return t+("</"+e+">")};class ${constructor(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,X.call(this,e,r,n),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return m.contains(this.classes,e)}toNode(){return _.call(this,"span")}toMarkup(){return j.call(this,"span")}}class Z{constructor(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,X.call(this,t,n),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return m.contains(this.classes,e)}toNode(){return _.call(this,"a")}toMarkup(){return j.call(this,"a")}}class K{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return m.contains(this.classes,e)}toNode(){let e=document.createElement("img");for(let t in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){let e='<img src="'+m.escape(this.src)+'" alt="'+m.escape(this.alt)+'"',t="";for(let e in this.style)this.style.hasOwnProperty(e)&&(t+=m.hyphenate(e)+":"+this.style[e]+";");return t&&(e+=' style="'+m.escape(t)+'"'),e+="'/>"}}let J={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};class Q{constructor(e,t,r,n,l,i,a,s){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=n||0,this.skew=l||0,this.width=i||0,this.classes=a||[],this.style=s||{},this.maxFontSize=0;let o=function(e){for(let t=0;t<k.length;t++){let r=k[t];for(let t=0;t<r.blocks.length;t++){let n=r.blocks[t];if(e>=n[0]&&e<=n[1])return r.name}}return null}(this.text.charCodeAt(0));o&&this.classes.push(o+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=J[this.text])}hasClass(e){return m.contains(this.classes,e)}toNode(){let e=document.createTextNode(this.text),t=null;for(let e in this.italic>0&&((t=document.createElement("span")).style.marginRight=U(this.italic)),this.classes.length>0&&((t=t||document.createElement("span")).className=Y(this.classes)),this.style)this.style.hasOwnProperty(e)&&((t=t||document.createElement("span")).style[e]=this.style[e]);return t?(t.appendChild(e),t):e}toMarkup(){let e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="',t+=m.escape(Y(this.classes)),t+='"');let r="";for(let e in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(e)&&(r+=m.hyphenate(e)+":"+this.style[e]+";");r&&(e=!0,t+=' style="'+m.escape(r)+'"');let n=m.escape(this.text);return e?(t+=">",t+=n,t+="</span>"):n}}class ee{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){let e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(let t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){let e='<svg xmlns="http://www.w3.org/2000/svg"';for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+m.escape(this.attributes[t])+'"');e+=">";for(let t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e+"</svg>"}}class et{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){let e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",T[this.pathName]),e}toMarkup(){return this.alternate?'<path d="'+m.escape(this.alternate)+'"/>':'<path d="'+m.escape(T[this.pathName])+'"/>'}}class er{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){let e=document.createElementNS("http://www.w3.org/2000/svg","line");for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e}toMarkup(){let e="<line";for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+m.escape(this.attributes[t])+'"');return e+"/>"}}function en(e){if(e instanceof Q)return e;throw Error("Expected symbolNode but got "+String(e)+".")}let el={bin:1,close:1,inner:1,open:1,punct:1,rel:1},ei={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},ea={math:{},text:{}};function es(e,t,r,n,l,i){ea[e][l]={font:t,group:r,replace:n},i&&n&&(ea[e][n]=ea[e][l])}let eo="math",eh="text",em="main",ec="accent-token",ep="close",eu="inner",ed="mathord",eg="op-token",ef="open",eb="punct",ey="spacing",ex="textord";es(eo,em,"rel","≡","\\equiv",!0),es(eo,em,"rel","≺","\\prec",!0),es(eo,em,"rel","≻","\\succ",!0),es(eo,em,"rel","∼","\\sim",!0),es(eo,em,"rel","⊥","\\perp"),es(eo,em,"rel","⪯","\\preceq",!0),es(eo,em,"rel","⪰","\\succeq",!0),es(eo,em,"rel","≃","\\simeq",!0),es(eo,em,"rel","∣","\\mid",!0),es(eo,em,"rel","≪","\\ll",!0),es(eo,em,"rel","≫","\\gg",!0),es(eo,em,"rel","≍","\\asymp",!0),es(eo,em,"rel","∥","\\parallel"),es(eo,em,"rel","⋈","\\bowtie",!0),es(eo,em,"rel","⌣","\\smile",!0),es(eo,em,"rel","⊑","\\sqsubseteq",!0),es(eo,em,"rel","⊒","\\sqsupseteq",!0),es(eo,em,"rel","≐","\\doteq",!0),es(eo,em,"rel","⌢","\\frown",!0),es(eo,em,"rel","∋","\\ni",!0),es(eo,em,"rel","∝","\\propto",!0),es(eo,em,"rel","⊢","\\vdash",!0),es(eo,em,"rel","⊣","\\dashv",!0),es(eo,em,"rel","∋","\\owns"),es(eo,em,eb,".","\\ldotp"),es(eo,em,eb,"⋅","\\cdotp"),es(eo,em,ex,"#","\\#"),es(eh,em,ex,"#","\\#"),es(eo,em,ex,"&","\\&"),es(eh,em,ex,"&","\\&"),es(eo,em,ex,"ℵ","\\aleph",!0),es(eo,em,ex,"∀","\\forall",!0),es(eo,em,ex,"ℏ","\\hbar",!0),es(eo,em,ex,"∃","\\exists",!0),es(eo,em,ex,"∇","\\nabla",!0),es(eo,em,ex,"♭","\\flat",!0),es(eo,em,ex,"ℓ","\\ell",!0),es(eo,em,ex,"♮","\\natural",!0),es(eo,em,ex,"♣","\\clubsuit",!0),es(eo,em,ex,"℘","\\wp",!0),es(eo,em,ex,"♯","\\sharp",!0),es(eo,em,ex,"♢","\\diamondsuit",!0),es(eo,em,ex,"ℜ","\\Re",!0),es(eo,em,ex,"♡","\\heartsuit",!0),es(eo,em,ex,"ℑ","\\Im",!0),es(eo,em,ex,"♠","\\spadesuit",!0),es(eo,em,ex,"\xa7","\\S",!0),es(eh,em,ex,"\xa7","\\S"),es(eo,em,ex,"\xb6","\\P",!0),es(eh,em,ex,"\xb6","\\P"),es(eo,em,ex,"†","\\dag"),es(eh,em,ex,"†","\\dag"),es(eh,em,ex,"†","\\textdagger"),es(eo,em,ex,"‡","\\ddag"),es(eh,em,ex,"‡","\\ddag"),es(eh,em,ex,"‡","\\textdaggerdbl"),es(eo,em,ep,"⎱","\\rmoustache",!0),es(eo,em,ef,"⎰","\\lmoustache",!0),es(eo,em,ep,"⟯","\\rgroup",!0),es(eo,em,ef,"⟮","\\lgroup",!0),es(eo,em,"bin","∓","\\mp",!0),es(eo,em,"bin","⊖","\\ominus",!0),es(eo,em,"bin","⊎","\\uplus",!0),es(eo,em,"bin","⊓","\\sqcap",!0),es(eo,em,"bin","∗","\\ast"),es(eo,em,"bin","⊔","\\sqcup",!0),es(eo,em,"bin","◯","\\bigcirc",!0),es(eo,em,"bin","∙","\\bullet",!0),es(eo,em,"bin","‡","\\ddagger"),es(eo,em,"bin","≀","\\wr",!0),es(eo,em,"bin","⨿","\\amalg"),es(eo,em,"bin","&","\\And"),es(eo,em,"rel","⟵","\\longleftarrow",!0),es(eo,em,"rel","⇐","\\Leftarrow",!0),es(eo,em,"rel","⟸","\\Longleftarrow",!0),es(eo,em,"rel","⟶","\\longrightarrow",!0),es(eo,em,"rel","⇒","\\Rightarrow",!0),es(eo,em,"rel","⟹","\\Longrightarrow",!0),es(eo,em,"rel","↔","\\leftrightarrow",!0),es(eo,em,"rel","⟷","\\longleftrightarrow",!0),es(eo,em,"rel","⇔","\\Leftrightarrow",!0),es(eo,em,"rel","⟺","\\Longleftrightarrow",!0),es(eo,em,"rel","↦","\\mapsto",!0),es(eo,em,"rel","⟼","\\longmapsto",!0),es(eo,em,"rel","↗","\\nearrow",!0),es(eo,em,"rel","↩","\\hookleftarrow",!0),es(eo,em,"rel","↪","\\hookrightarrow",!0),es(eo,em,"rel","↘","\\searrow",!0),es(eo,em,"rel","↼","\\leftharpoonup",!0),es(eo,em,"rel","⇀","\\rightharpoonup",!0),es(eo,em,"rel","↙","\\swarrow",!0),es(eo,em,"rel","↽","\\leftharpoondown",!0),es(eo,em,"rel","⇁","\\rightharpoondown",!0),es(eo,em,"rel","↖","\\nwarrow",!0),es(eo,em,"rel","⇌","\\rightleftharpoons",!0),es(eo,"ams","rel","≮","\\nless",!0),es(eo,"ams","rel","","\\@nleqslant"),es(eo,"ams","rel","","\\@nleqq"),es(eo,"ams","rel","⪇","\\lneq",!0),es(eo,"ams","rel","≨","\\lneqq",!0),es(eo,"ams","rel","","\\@lvertneqq"),es(eo,"ams","rel","⋦","\\lnsim",!0),es(eo,"ams","rel","⪉","\\lnapprox",!0),es(eo,"ams","rel","⊀","\\nprec",!0),es(eo,"ams","rel","⋠","\\npreceq",!0),es(eo,"ams","rel","⋨","\\precnsim",!0),es(eo,"ams","rel","⪹","\\precnapprox",!0),es(eo,"ams","rel","≁","\\nsim",!0),es(eo,"ams","rel","","\\@nshortmid"),es(eo,"ams","rel","∤","\\nmid",!0),es(eo,"ams","rel","⊬","\\nvdash",!0),es(eo,"ams","rel","⊭","\\nvDash",!0),es(eo,"ams","rel","⋪","\\ntriangleleft"),es(eo,"ams","rel","⋬","\\ntrianglelefteq",!0),es(eo,"ams","rel","⊊","\\subsetneq",!0),es(eo,"ams","rel","","\\@varsubsetneq"),es(eo,"ams","rel","⫋","\\subsetneqq",!0),es(eo,"ams","rel","","\\@varsubsetneqq"),es(eo,"ams","rel","≯","\\ngtr",!0),es(eo,"ams","rel","","\\@ngeqslant"),es(eo,"ams","rel","","\\@ngeqq"),es(eo,"ams","rel","⪈","\\gneq",!0),es(eo,"ams","rel","≩","\\gneqq",!0),es(eo,"ams","rel","","\\@gvertneqq"),es(eo,"ams","rel","⋧","\\gnsim",!0),es(eo,"ams","rel","⪊","\\gnapprox",!0),es(eo,"ams","rel","⊁","\\nsucc",!0),es(eo,"ams","rel","⋡","\\nsucceq",!0),es(eo,"ams","rel","⋩","\\succnsim",!0),es(eo,"ams","rel","⪺","\\succnapprox",!0),es(eo,"ams","rel","≆","\\ncong",!0),es(eo,"ams","rel","","\\@nshortparallel"),es(eo,"ams","rel","∦","\\nparallel",!0),es(eo,"ams","rel","⊯","\\nVDash",!0),es(eo,"ams","rel","⋫","\\ntriangleright"),es(eo,"ams","rel","⋭","\\ntrianglerighteq",!0),es(eo,"ams","rel","","\\@nsupseteqq"),es(eo,"ams","rel","⊋","\\supsetneq",!0),es(eo,"ams","rel","","\\@varsupsetneq"),es(eo,"ams","rel","⫌","\\supsetneqq",!0),es(eo,"ams","rel","","\\@varsupsetneqq"),es(eo,"ams","rel","⊮","\\nVdash",!0),es(eo,"ams","rel","⪵","\\precneqq",!0),es(eo,"ams","rel","⪶","\\succneqq",!0),es(eo,"ams","rel","","\\@nsubseteqq"),es(eo,"ams","bin","⊴","\\unlhd"),es(eo,"ams","bin","⊵","\\unrhd"),es(eo,"ams","rel","↚","\\nleftarrow",!0),es(eo,"ams","rel","↛","\\nrightarrow",!0),es(eo,"ams","rel","⇍","\\nLeftarrow",!0),es(eo,"ams","rel","⇏","\\nRightarrow",!0),es(eo,"ams","rel","↮","\\nleftrightarrow",!0),es(eo,"ams","rel","⇎","\\nLeftrightarrow",!0),es(eo,"ams","rel","△","\\vartriangle"),es(eo,"ams",ex,"ℏ","\\hslash"),es(eo,"ams",ex,"▽","\\triangledown"),es(eo,"ams",ex,"◊","\\lozenge"),es(eo,"ams",ex,"Ⓢ","\\circledS"),es(eo,"ams",ex,"\xae","\\circledR"),es(eh,"ams",ex,"\xae","\\circledR"),es(eo,"ams",ex,"∡","\\measuredangle",!0),es(eo,"ams",ex,"∄","\\nexists"),es(eo,"ams",ex,"℧","\\mho"),es(eo,"ams",ex,"Ⅎ","\\Finv",!0),es(eo,"ams",ex,"⅁","\\Game",!0),es(eo,"ams",ex,"‵","\\backprime"),es(eo,"ams",ex,"▲","\\blacktriangle"),es(eo,"ams",ex,"▼","\\blacktriangledown"),es(eo,"ams",ex,"■","\\blacksquare"),es(eo,"ams",ex,"⧫","\\blacklozenge"),es(eo,"ams",ex,"★","\\bigstar"),es(eo,"ams",ex,"∢","\\sphericalangle",!0),es(eo,"ams",ex,"∁","\\complement",!0),es(eo,"ams",ex,"\xf0","\\eth",!0),es(eh,em,ex,"\xf0","\xf0"),es(eo,"ams",ex,"╱","\\diagup"),es(eo,"ams",ex,"╲","\\diagdown"),es(eo,"ams",ex,"□","\\square"),es(eo,"ams",ex,"□","\\Box"),es(eo,"ams",ex,"◊","\\Diamond"),es(eo,"ams",ex,"\xa5","\\yen",!0),es(eh,"ams",ex,"\xa5","\\yen",!0),es(eo,"ams",ex,"✓","\\checkmark",!0),es(eh,"ams",ex,"✓","\\checkmark"),es(eo,"ams",ex,"ℶ","\\beth",!0),es(eo,"ams",ex,"ℸ","\\daleth",!0),es(eo,"ams",ex,"ℷ","\\gimel",!0),es(eo,"ams",ex,"ϝ","\\digamma",!0),es(eo,"ams",ex,"ϰ","\\varkappa"),es(eo,"ams",ef,"┌","\\@ulcorner",!0),es(eo,"ams",ep,"┐","\\@urcorner",!0),es(eo,"ams",ef,"└","\\@llcorner",!0),es(eo,"ams",ep,"┘","\\@lrcorner",!0),es(eo,"ams","rel","≦","\\leqq",!0),es(eo,"ams","rel","⩽","\\leqslant",!0),es(eo,"ams","rel","⪕","\\eqslantless",!0),es(eo,"ams","rel","≲","\\lesssim",!0),es(eo,"ams","rel","⪅","\\lessapprox",!0),es(eo,"ams","rel","≊","\\approxeq",!0),es(eo,"ams","bin","⋖","\\lessdot"),es(eo,"ams","rel","⋘","\\lll",!0),es(eo,"ams","rel","≶","\\lessgtr",!0),es(eo,"ams","rel","⋚","\\lesseqgtr",!0),es(eo,"ams","rel","⪋","\\lesseqqgtr",!0),es(eo,"ams","rel","≑","\\doteqdot"),es(eo,"ams","rel","≓","\\risingdotseq",!0),es(eo,"ams","rel","≒","\\fallingdotseq",!0),es(eo,"ams","rel","∽","\\backsim",!0),es(eo,"ams","rel","⋍","\\backsimeq",!0),es(eo,"ams","rel","⫅","\\subseteqq",!0),es(eo,"ams","rel","⋐","\\Subset",!0),es(eo,"ams","rel","⊏","\\sqsubset",!0),es(eo,"ams","rel","≼","\\preccurlyeq",!0),es(eo,"ams","rel","⋞","\\curlyeqprec",!0),es(eo,"ams","rel","≾","\\precsim",!0),es(eo,"ams","rel","⪷","\\precapprox",!0),es(eo,"ams","rel","⊲","\\vartriangleleft"),es(eo,"ams","rel","⊴","\\trianglelefteq"),es(eo,"ams","rel","⊨","\\vDash",!0),es(eo,"ams","rel","⊪","\\Vvdash",!0),es(eo,"ams","rel","⌣","\\smallsmile"),es(eo,"ams","rel","⌢","\\smallfrown"),es(eo,"ams","rel","≏","\\bumpeq",!0),es(eo,"ams","rel","≎","\\Bumpeq",!0),es(eo,"ams","rel","≧","\\geqq",!0),es(eo,"ams","rel","⩾","\\geqslant",!0),es(eo,"ams","rel","⪖","\\eqslantgtr",!0),es(eo,"ams","rel","≳","\\gtrsim",!0),es(eo,"ams","rel","⪆","\\gtrapprox",!0),es(eo,"ams","bin","⋗","\\gtrdot"),es(eo,"ams","rel","⋙","\\ggg",!0),es(eo,"ams","rel","≷","\\gtrless",!0),es(eo,"ams","rel","⋛","\\gtreqless",!0),es(eo,"ams","rel","⪌","\\gtreqqless",!0),es(eo,"ams","rel","≖","\\eqcirc",!0),es(eo,"ams","rel","≗","\\circeq",!0),es(eo,"ams","rel","≜","\\triangleq",!0),es(eo,"ams","rel","∼","\\thicksim"),es(eo,"ams","rel","≈","\\thickapprox"),es(eo,"ams","rel","⫆","\\supseteqq",!0),es(eo,"ams","rel","⋑","\\Supset",!0),es(eo,"ams","rel","⊐","\\sqsupset",!0),es(eo,"ams","rel","≽","\\succcurlyeq",!0),es(eo,"ams","rel","⋟","\\curlyeqsucc",!0),es(eo,"ams","rel","≿","\\succsim",!0),es(eo,"ams","rel","⪸","\\succapprox",!0),es(eo,"ams","rel","⊳","\\vartriangleright"),es(eo,"ams","rel","⊵","\\trianglerighteq"),es(eo,"ams","rel","⊩","\\Vdash",!0),es(eo,"ams","rel","∣","\\shortmid"),es(eo,"ams","rel","∥","\\shortparallel"),es(eo,"ams","rel","≬","\\between",!0),es(eo,"ams","rel","⋔","\\pitchfork",!0),es(eo,"ams","rel","∝","\\varpropto"),es(eo,"ams","rel","◀","\\blacktriangleleft"),es(eo,"ams","rel","∴","\\therefore",!0),es(eo,"ams","rel","∍","\\backepsilon"),es(eo,"ams","rel","▶","\\blacktriangleright"),es(eo,"ams","rel","∵","\\because",!0),es(eo,"ams","rel","⋘","\\llless"),es(eo,"ams","rel","⋙","\\gggtr"),es(eo,"ams","bin","⊲","\\lhd"),es(eo,"ams","bin","⊳","\\rhd"),es(eo,"ams","rel","≂","\\eqsim",!0),es(eo,em,"rel","⋈","\\Join"),es(eo,"ams","rel","≑","\\Doteq",!0),es(eo,"ams","bin","∔","\\dotplus",!0),es(eo,"ams","bin","∖","\\smallsetminus"),es(eo,"ams","bin","⋒","\\Cap",!0),es(eo,"ams","bin","⋓","\\Cup",!0),es(eo,"ams","bin","⩞","\\doublebarwedge",!0),es(eo,"ams","bin","⊟","\\boxminus",!0),es(eo,"ams","bin","⊞","\\boxplus",!0),es(eo,"ams","bin","⋇","\\divideontimes",!0),es(eo,"ams","bin","⋉","\\ltimes",!0),es(eo,"ams","bin","⋊","\\rtimes",!0),es(eo,"ams","bin","⋋","\\leftthreetimes",!0),es(eo,"ams","bin","⋌","\\rightthreetimes",!0),es(eo,"ams","bin","⋏","\\curlywedge",!0),es(eo,"ams","bin","⋎","\\curlyvee",!0),es(eo,"ams","bin","⊝","\\circleddash",!0),es(eo,"ams","bin","⊛","\\circledast",!0),es(eo,"ams","bin","⋅","\\centerdot"),es(eo,"ams","bin","⊺","\\intercal",!0),es(eo,"ams","bin","⋒","\\doublecap"),es(eo,"ams","bin","⋓","\\doublecup"),es(eo,"ams","bin","⊠","\\boxtimes",!0),es(eo,"ams","rel","⇢","\\dashrightarrow",!0),es(eo,"ams","rel","⇠","\\dashleftarrow",!0),es(eo,"ams","rel","⇇","\\leftleftarrows",!0),es(eo,"ams","rel","⇆","\\leftrightarrows",!0),es(eo,"ams","rel","⇚","\\Lleftarrow",!0),es(eo,"ams","rel","↞","\\twoheadleftarrow",!0),es(eo,"ams","rel","↢","\\leftarrowtail",!0),es(eo,"ams","rel","↫","\\looparrowleft",!0),es(eo,"ams","rel","⇋","\\leftrightharpoons",!0),es(eo,"ams","rel","↶","\\curvearrowleft",!0),es(eo,"ams","rel","↺","\\circlearrowleft",!0),es(eo,"ams","rel","↰","\\Lsh",!0),es(eo,"ams","rel","⇈","\\upuparrows",!0),es(eo,"ams","rel","↿","\\upharpoonleft",!0),es(eo,"ams","rel","⇃","\\downharpoonleft",!0),es(eo,em,"rel","⊶","\\origof",!0),es(eo,em,"rel","⊷","\\imageof",!0),es(eo,"ams","rel","⊸","\\multimap",!0),es(eo,"ams","rel","↭","\\leftrightsquigarrow",!0),es(eo,"ams","rel","⇉","\\rightrightarrows",!0),es(eo,"ams","rel","⇄","\\rightleftarrows",!0),es(eo,"ams","rel","↠","\\twoheadrightarrow",!0),es(eo,"ams","rel","↣","\\rightarrowtail",!0),es(eo,"ams","rel","↬","\\looparrowright",!0),es(eo,"ams","rel","↷","\\curvearrowright",!0),es(eo,"ams","rel","↻","\\circlearrowright",!0),es(eo,"ams","rel","↱","\\Rsh",!0),es(eo,"ams","rel","⇊","\\downdownarrows",!0),es(eo,"ams","rel","↾","\\upharpoonright",!0),es(eo,"ams","rel","⇂","\\downharpoonright",!0),es(eo,"ams","rel","⇝","\\rightsquigarrow",!0),es(eo,"ams","rel","⇝","\\leadsto"),es(eo,"ams","rel","⇛","\\Rrightarrow",!0),es(eo,"ams","rel","↾","\\restriction"),es(eo,em,ex,"‘","`"),es(eo,em,ex,"$","\\$"),es(eh,em,ex,"$","\\$"),es(eh,em,ex,"$","\\textdollar"),es(eo,em,ex,"%","\\%"),es(eh,em,ex,"%","\\%"),es(eo,em,ex,"_","\\_"),es(eh,em,ex,"_","\\_"),es(eh,em,ex,"_","\\textunderscore"),es(eo,em,ex,"∠","\\angle",!0),es(eo,em,ex,"∞","\\infty",!0),es(eo,em,ex,"′","\\prime"),es(eo,em,ex,"△","\\triangle"),es(eo,em,ex,"Γ","\\Gamma",!0),es(eo,em,ex,"Δ","\\Delta",!0),es(eo,em,ex,"Θ","\\Theta",!0),es(eo,em,ex,"Λ","\\Lambda",!0),es(eo,em,ex,"Ξ","\\Xi",!0),es(eo,em,ex,"Π","\\Pi",!0),es(eo,em,ex,"Σ","\\Sigma",!0),es(eo,em,ex,"Υ","\\Upsilon",!0),es(eo,em,ex,"Φ","\\Phi",!0),es(eo,em,ex,"Ψ","\\Psi",!0),es(eo,em,ex,"Ω","\\Omega",!0),es(eo,em,ex,"A","Α"),es(eo,em,ex,"B","Β"),es(eo,em,ex,"E","Ε"),es(eo,em,ex,"Z","Ζ"),es(eo,em,ex,"H","Η"),es(eo,em,ex,"I","Ι"),es(eo,em,ex,"K","Κ"),es(eo,em,ex,"M","Μ"),es(eo,em,ex,"N","Ν"),es(eo,em,ex,"O","Ο"),es(eo,em,ex,"P","Ρ"),es(eo,em,ex,"T","Τ"),es(eo,em,ex,"X","Χ"),es(eo,em,ex,"\xac","\\neg",!0),es(eo,em,ex,"\xac","\\lnot"),es(eo,em,ex,"⊤","\\top"),es(eo,em,ex,"⊥","\\bot"),es(eo,em,ex,"∅","\\emptyset"),es(eo,"ams",ex,"∅","\\varnothing"),es(eo,em,ed,"α","\\alpha",!0),es(eo,em,ed,"β","\\beta",!0),es(eo,em,ed,"γ","\\gamma",!0),es(eo,em,ed,"δ","\\delta",!0),es(eo,em,ed,"ϵ","\\epsilon",!0),es(eo,em,ed,"ζ","\\zeta",!0),es(eo,em,ed,"η","\\eta",!0),es(eo,em,ed,"θ","\\theta",!0),es(eo,em,ed,"ι","\\iota",!0),es(eo,em,ed,"κ","\\kappa",!0),es(eo,em,ed,"λ","\\lambda",!0),es(eo,em,ed,"μ","\\mu",!0),es(eo,em,ed,"ν","\\nu",!0),es(eo,em,ed,"ξ","\\xi",!0),es(eo,em,ed,"ο","\\omicron",!0),es(eo,em,ed,"π","\\pi",!0),es(eo,em,ed,"ρ","\\rho",!0),es(eo,em,ed,"σ","\\sigma",!0),es(eo,em,ed,"τ","\\tau",!0),es(eo,em,ed,"υ","\\upsilon",!0),es(eo,em,ed,"ϕ","\\phi",!0),es(eo,em,ed,"χ","\\chi",!0),es(eo,em,ed,"ψ","\\psi",!0),es(eo,em,ed,"ω","\\omega",!0),es(eo,em,ed,"ε","\\varepsilon",!0),es(eo,em,ed,"ϑ","\\vartheta",!0),es(eo,em,ed,"ϖ","\\varpi",!0),es(eo,em,ed,"ϱ","\\varrho",!0),es(eo,em,ed,"ς","\\varsigma",!0),es(eo,em,ed,"φ","\\varphi",!0),es(eo,em,"bin","∗","*",!0),es(eo,em,"bin","+","+"),es(eo,em,"bin","−","-",!0),es(eo,em,"bin","⋅","\\cdot",!0),es(eo,em,"bin","∘","\\circ",!0),es(eo,em,"bin","\xf7","\\div",!0),es(eo,em,"bin","\xb1","\\pm",!0),es(eo,em,"bin","\xd7","\\times",!0),es(eo,em,"bin","∩","\\cap",!0),es(eo,em,"bin","∪","\\cup",!0),es(eo,em,"bin","∖","\\setminus",!0),es(eo,em,"bin","∧","\\land"),es(eo,em,"bin","∨","\\lor"),es(eo,em,"bin","∧","\\wedge",!0),es(eo,em,"bin","∨","\\vee",!0),es(eo,em,ex,"√","\\surd"),es(eo,em,ef,"⟨","\\langle",!0),es(eo,em,ef,"∣","\\lvert"),es(eo,em,ef,"∥","\\lVert"),es(eo,em,ep,"?","?"),es(eo,em,ep,"!","!"),es(eo,em,ep,"⟩","\\rangle",!0),es(eo,em,ep,"∣","\\rvert"),es(eo,em,ep,"∥","\\rVert"),es(eo,em,"rel","=","="),es(eo,em,"rel",":",":"),es(eo,em,"rel","≈","\\approx",!0),es(eo,em,"rel","≅","\\cong",!0),es(eo,em,"rel","≥","\\ge"),es(eo,em,"rel","≥","\\geq",!0),es(eo,em,"rel","←","\\gets"),es(eo,em,"rel",">","\\gt",!0),es(eo,em,"rel","∈","\\in",!0),es(eo,em,"rel","","\\@not"),es(eo,em,"rel","⊂","\\subset",!0),es(eo,em,"rel","⊃","\\supset",!0),es(eo,em,"rel","⊆","\\subseteq",!0),es(eo,em,"rel","⊇","\\supseteq",!0),es(eo,"ams","rel","⊈","\\nsubseteq",!0),es(eo,"ams","rel","⊉","\\nsupseteq",!0),es(eo,em,"rel","⊨","\\models"),es(eo,em,"rel","←","\\leftarrow",!0),es(eo,em,"rel","≤","\\le"),es(eo,em,"rel","≤","\\leq",!0),es(eo,em,"rel","<","\\lt",!0),es(eo,em,"rel","→","\\rightarrow",!0),es(eo,em,"rel","→","\\to"),es(eo,"ams","rel","≱","\\ngeq",!0),es(eo,"ams","rel","≰","\\nleq",!0),es(eo,em,ey,"\xa0","\\ "),es(eo,em,ey,"\xa0","\\space"),es(eo,em,ey,"\xa0","\\nobreakspace"),es(eh,em,ey,"\xa0","\\ "),es(eh,em,ey,"\xa0"," "),es(eh,em,ey,"\xa0","\\space"),es(eh,em,ey,"\xa0","\\nobreakspace"),es(eo,em,ey,null,"\\nobreak"),es(eo,em,ey,null,"\\allowbreak"),es(eo,em,eb,",",","),es(eo,em,eb,";",";"),es(eo,"ams","bin","⊼","\\barwedge",!0),es(eo,"ams","bin","⊻","\\veebar",!0),es(eo,em,"bin","⊙","\\odot",!0),es(eo,em,"bin","⊕","\\oplus",!0),es(eo,em,"bin","⊗","\\otimes",!0),es(eo,em,ex,"∂","\\partial",!0),es(eo,em,"bin","⊘","\\oslash",!0),es(eo,"ams","bin","⊚","\\circledcirc",!0),es(eo,"ams","bin","⊡","\\boxdot",!0),es(eo,em,"bin","△","\\bigtriangleup"),es(eo,em,"bin","▽","\\bigtriangledown"),es(eo,em,"bin","†","\\dagger"),es(eo,em,"bin","⋄","\\diamond"),es(eo,em,"bin","⋆","\\star"),es(eo,em,"bin","◃","\\triangleleft"),es(eo,em,"bin","▹","\\triangleright"),es(eo,em,ef,"{","\\{"),es(eh,em,ex,"{","\\{"),es(eh,em,ex,"{","\\textbraceleft"),es(eo,em,ep,"}","\\}"),es(eh,em,ex,"}","\\}"),es(eh,em,ex,"}","\\textbraceright"),es(eo,em,ef,"{","\\lbrace"),es(eo,em,ep,"}","\\rbrace"),es(eo,em,ef,"[","\\lbrack",!0),es(eh,em,ex,"[","\\lbrack",!0),es(eo,em,ep,"]","\\rbrack",!0),es(eh,em,ex,"]","\\rbrack",!0),es(eo,em,ef,"(","\\lparen",!0),es(eo,em,ep,")","\\rparen",!0),es(eh,em,ex,"<","\\textless",!0),es(eh,em,ex,">","\\textgreater",!0),es(eo,em,ef,"⌊","\\lfloor",!0),es(eo,em,ep,"⌋","\\rfloor",!0),es(eo,em,ef,"⌈","\\lceil",!0),es(eo,em,ep,"⌉","\\rceil",!0),es(eo,em,ex,"\\","\\backslash"),es(eo,em,ex,"∣","|"),es(eo,em,ex,"∣","\\vert"),es(eh,em,ex,"|","\\textbar",!0),es(eo,em,ex,"∥","\\|"),es(eo,em,ex,"∥","\\Vert"),es(eh,em,ex,"∥","\\textbardbl"),es(eh,em,ex,"~","\\textasciitilde"),es(eh,em,ex,"\\","\\textbackslash"),es(eh,em,ex,"^","\\textasciicircum"),es(eo,em,"rel","↑","\\uparrow",!0),es(eo,em,"rel","⇑","\\Uparrow",!0),es(eo,em,"rel","↓","\\downarrow",!0),es(eo,em,"rel","⇓","\\Downarrow",!0),es(eo,em,"rel","↕","\\updownarrow",!0),es(eo,em,"rel","⇕","\\Updownarrow",!0),es(eo,em,eg,"∐","\\coprod"),es(eo,em,eg,"⋁","\\bigvee"),es(eo,em,eg,"⋀","\\bigwedge"),es(eo,em,eg,"⨄","\\biguplus"),es(eo,em,eg,"⋂","\\bigcap"),es(eo,em,eg,"⋃","\\bigcup"),es(eo,em,eg,"∫","\\int"),es(eo,em,eg,"∫","\\intop"),es(eo,em,eg,"∬","\\iint"),es(eo,em,eg,"∭","\\iiint"),es(eo,em,eg,"∏","\\prod"),es(eo,em,eg,"∑","\\sum"),es(eo,em,eg,"⨂","\\bigotimes"),es(eo,em,eg,"⨁","\\bigoplus"),es(eo,em,eg,"⨀","\\bigodot"),es(eo,em,eg,"∮","\\oint"),es(eo,em,eg,"∯","\\oiint"),es(eo,em,eg,"∰","\\oiiint"),es(eo,em,eg,"⨆","\\bigsqcup"),es(eo,em,eg,"∫","\\smallint"),es(eh,em,eu,"…","\\textellipsis"),es(eo,em,eu,"…","\\mathellipsis"),es(eh,em,eu,"…","\\ldots",!0),es(eo,em,eu,"…","\\ldots",!0),es(eo,em,eu,"⋯","\\@cdots",!0),es(eo,em,eu,"⋱","\\ddots",!0),es(eo,em,ex,"⋮","\\varvdots"),es(eh,em,ex,"⋮","\\varvdots"),es(eo,em,ec,"ˊ","\\acute"),es(eo,em,ec,"ˋ","\\grave"),es(eo,em,ec,"\xa8","\\ddot"),es(eo,em,ec,"~","\\tilde"),es(eo,em,ec,"ˉ","\\bar"),es(eo,em,ec,"˘","\\breve"),es(eo,em,ec,"ˇ","\\check"),es(eo,em,ec,"^","\\hat"),es(eo,em,ec,"⃗","\\vec"),es(eo,em,ec,"˙","\\dot"),es(eo,em,ec,"˚","\\mathring"),es(eo,em,ed,"","\\@imath"),es(eo,em,ed,"","\\@jmath"),es(eo,em,ex,"ı","ı"),es(eo,em,ex,"ȷ","ȷ"),es(eh,em,ex,"ı","\\i",!0),es(eh,em,ex,"ȷ","\\j",!0),es(eh,em,ex,"\xdf","\\ss",!0),es(eh,em,ex,"\xe6","\\ae",!0),es(eh,em,ex,"œ","\\oe",!0),es(eh,em,ex,"\xf8","\\o",!0),es(eh,em,ex,"\xc6","\\AE",!0),es(eh,em,ex,"Œ","\\OE",!0),es(eh,em,ex,"\xd8","\\O",!0),es(eh,em,ec,"ˊ","\\'"),es(eh,em,ec,"ˋ","\\`"),es(eh,em,ec,"ˆ","\\^"),es(eh,em,ec,"˜","\\~"),es(eh,em,ec,"ˉ","\\="),es(eh,em,ec,"˘","\\u"),es(eh,em,ec,"˙","\\."),es(eh,em,ec,"\xb8","\\c"),es(eh,em,ec,"˚","\\r"),es(eh,em,ec,"ˇ","\\v"),es(eh,em,ec,"\xa8",'\\"'),es(eh,em,ec,"˝","\\H"),es(eh,em,ec,"◯","\\textcircled");let ew={"--":!0,"---":!0,"``":!0,"''":!0};es(eh,em,ex,"–","--",!0),es(eh,em,ex,"–","\\textendash"),es(eh,em,ex,"—","---",!0),es(eh,em,ex,"—","\\textemdash"),es(eh,em,ex,"‘","`",!0),es(eh,em,ex,"‘","\\textquoteleft"),es(eh,em,ex,"’","'",!0),es(eh,em,ex,"’","\\textquoteright"),es(eh,em,ex,"“","``",!0),es(eh,em,ex,"“","\\textquotedblleft"),es(eh,em,ex,"”","''",!0),es(eh,em,ex,"”","\\textquotedblright"),es(eo,em,ex,"\xb0","\\degree",!0),es(eh,em,ex,"\xb0","\\degree"),es(eh,em,ex,"\xb0","\\textdegree",!0),es(eo,em,ex,"\xa3","\\pounds"),es(eo,em,ex,"\xa3","\\mathsterling",!0),es(eh,em,ex,"\xa3","\\pounds"),es(eh,em,ex,"\xa3","\\textsterling",!0),es(eo,"ams",ex,"✠","\\maltese"),es(eh,"ams",ex,"✠","\\maltese");let ev='0123456789/@."';for(let e=0;e<ev.length;e++){let t=ev.charAt(e);es(eo,em,ex,t,t)}let ek='0123456789!@*()-=+";:?/.,';for(let e=0;e<ek.length;e++){let t=ek.charAt(e);es(eh,em,ex,t,t)}let eS="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";for(let e=0;e<eS.length;e++){let t=eS.charAt(e);es(eo,em,ed,t,t),es(eh,em,ex,t,t)}es(eo,"ams",ex,"C","ℂ"),es(eh,"ams",ex,"C","ℂ"),es(eo,"ams",ex,"H","ℍ"),es(eh,"ams",ex,"H","ℍ"),es(eo,"ams",ex,"N","ℕ"),es(eh,"ams",ex,"N","ℕ"),es(eo,"ams",ex,"P","ℙ"),es(eh,"ams",ex,"P","ℙ"),es(eo,"ams",ex,"Q","ℚ"),es(eh,"ams",ex,"Q","ℚ"),es(eo,"ams",ex,"R","ℝ"),es(eh,"ams",ex,"R","ℝ"),es(eo,"ams",ex,"Z","ℤ"),es(eh,"ams",ex,"Z","ℤ"),es(eo,em,ed,"h","ℎ"),es(eh,em,ed,"h","ℎ");let eM="";for(let e=0;e<eS.length;e++){let t=eS.charAt(e);es(eo,em,ed,t,eM=String.fromCharCode(55349,56320+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56372+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56424+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56580+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56684+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56736+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56788+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56840+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56944+e)),es(eh,em,ex,t,eM),e<26&&(es(eo,em,ed,t,eM=String.fromCharCode(55349,56632+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,56476+e)),es(eh,em,ex,t,eM))}es(eo,em,ed,"k",eM=String.fromCharCode(55349,56668)),es(eh,em,ex,"k",eM);for(let e=0;e<10;e++){let t=e.toString();es(eo,em,ed,t,eM=String.fromCharCode(55349,57294+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,57314+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,57324+e)),es(eh,em,ex,t,eM),es(eo,em,ed,t,eM=String.fromCharCode(55349,57334+e)),es(eh,em,ex,t,eM)}let ez="\xd0\xde\xfe";for(let e=0;e<ez.length;e++){let t=ez.charAt(e);es(eo,em,ed,t,t),es(eh,em,ex,t,t)}let eA=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],eT=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],eB=function(e,t){let r=(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320)+65536,n=+("math"!==t);if(119808<=r&&r<120484){let e=Math.floor((r-119808)/26);return[eA[e][2],eA[e][n]]}if(120782<=r&&r<=120831){let e=Math.floor((r-120782)/10);return[eT[e][2],eT[e][n]]}if(120485===r||120486===r)return[eA[0][2],eA[0][n]];if(120486<r&&r<120782)return["",""];throw new l("Unsupported character: "+e)},eC=function(e,t,r){return ea[r][e]&&ea[r][e].replace&&(e=ea[r][e].replace),{value:e,metrics:H(e,t,r)}},eq=function(e,t,r,n,l){let i;let a=eC(e,t,r),s=a.metrics;if(e=a.value,s){let t=s.italic;("text"===r||n&&"mathit"===n.font)&&(t=0),i=new Q(e,s.height,s.depth,t,s.skew,s.width,l)}else"undefined"!=typeof console&&console.warn("No character metrics "+("for '"+e+"' in style '"+t+"' and mode '")+r+"'"),i=new Q(e,0,0,0,0,0,l);if(n){i.maxFontSize=n.sizeMultiplier,n.style.isTight()&&i.classes.push("mtight");let e=n.getColor();e&&(i.style.color=e)}return i},eN=(e,t)=>{if(Y(e.classes)!==Y(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(1===e.classes.length){let t=e.classes[0];if("mbin"===t||"mord"===t)return!1}for(let r in e.style)if(e.style.hasOwnProperty(r)&&e.style[r]!==t.style[r])return!1;for(let r in t.style)if(t.style.hasOwnProperty(r)&&e.style[r]!==t.style[r])return!1;return!0},eI=function(e){let t=0,r=0,n=0;for(let l=0;l<e.children.length;l++){let i=e.children[l];i.height>t&&(t=i.height),i.depth>r&&(r=i.depth),i.maxFontSize>n&&(n=i.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=n},eH=function(e,t,r,n){let l=new $(e,t,r,n);return eI(l),l},eR=(e,t,r,n)=>new $(e,t,r,n),eO=function(e){let t=new C(e);return eI(t),t},eE=function(e){let t;if("individualShift"===e.positionType){let t=e.children,r=[t[0]],n=-t[0].shift-t[0].elem.depth,l=n;for(let e=1;e<t.length;e++){let n=-t[e].shift-l-t[e].elem.depth,i=n-(t[e-1].elem.height+t[e-1].elem.depth);l+=n,r.push({type:"kern",size:i}),r.push(t[e])}return{children:r,depth:n}}if("top"===e.positionType){let r=e.positionData;for(let t=0;t<e.children.length;t++){let n=e.children[t];r-="kern"===n.type?n.size:n.elem.height+n.elem.depth}t=r}else if("bottom"===e.positionType)t=-e.positionData;else{let r=e.children[0];if("elem"!==r.type)throw Error('First child must have type "elem".');if("shift"===e.positionType)t=-r.elem.depth-e.positionData;else if("firstBaseline"===e.positionType)t=-r.elem.depth;else throw Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:t}},eL=function(e,t,r){let n="";switch(e){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=e}return n+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},eD={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},eV={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]};var eP={fontMap:eD,makeSymbol:eq,mathsym:function(e,t,r,n){return(void 0===n&&(n=[]),"boldsymbol"===r.font&&eC(e,"Main-Bold",t).metrics)?eq(e,"Main-Bold",t,r,n.concat(["mathbf"])):"\\"===e||"main"===ea[t][e].font?eq(e,"Main-Regular",t,r,n):eq(e,"AMS-Regular",t,r,n.concat(["amsrm"]))},makeSpan:eH,makeSvgSpan:eR,makeLineSpan:function(e,t,r){let n=eH([e],[],t);return n.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),n.style.borderBottomWidth=U(n.height),n.maxFontSize=1,n},makeAnchor:function(e,t,r,n){let l=new Z(e,t,r,n);return eI(l),l},makeFragment:eO,wrapFragment:function(e,t){return e instanceof C?eH([],[e],t):e},makeVList:function(e,t){let r;let{children:n,depth:l}=eE(e),i=0;for(let e=0;e<n.length;e++){let t=n[e];if("elem"===t.type){let e=t.elem;i=Math.max(i,e.maxFontSize,e.height)}}i+=2;let a=eH(["pstrut"],[]);a.style.height=U(i);let s=[],o=l,h=l,m=l;for(let e=0;e<n.length;e++){let t=n[e];if("kern"===t.type)m+=t.size;else{let e=t.elem,r=eH(t.wrapperClasses||[],[a,e],void 0,t.wrapperStyle||{});r.style.top=U(-i-m-e.depth),t.marginLeft&&(r.style.marginLeft=t.marginLeft),t.marginRight&&(r.style.marginRight=t.marginRight),s.push(r),m+=e.height+e.depth}o=Math.min(o,m),h=Math.max(h,m)}let c=eH(["vlist"],s);if(c.style.height=U(h),o<0){let e=eH([],[]),t=eH(["vlist"],[e]);t.style.height=U(-o);let n=eH(["vlist-s"],[new Q("​")]);r=[eH(["vlist-r"],[c,n]),eH(["vlist-r"],[t])]}else r=[eH(["vlist-r"],[c])];let p=eH(["vlist-t"],r);return 2===r.length&&p.classes.push("vlist-t2"),p.height=h,p.depth=-o,p},makeOrd:function(e,t,r){let n=e.mode,l=e.text,i=["mord"],a="math"===n||"text"===n&&t.font,s=a?t.font:t.fontFamily,o="",h="";if(55349===l.charCodeAt(0)&&([o,h]=eB(l,n)),o.length>0)return eq(l,o,n,t,i.concat(h));if(s){let e,o;if("boldsymbol"===s){let t="textord"!==r&&eC(l,"Math-BoldItalic",n).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"};e=t.fontName,o=[t.fontClass]}else a?(e=eD[s].fontName,o=[s]):(e=eL(s,t.fontWeight,t.fontShape),o=[s,t.fontWeight,t.fontShape]);if(eC(l,e,n).metrics)return eq(l,e,n,t,i.concat(o));if(ew.hasOwnProperty(l)&&"Typewriter"===e.slice(0,10)){let r=[];for(let a=0;a<l.length;a++)r.push(eq(l[a],e,n,t,i.concat(o)));return eO(r)}}if("mathord"===r)return eq(l,"Math-Italic",n,t,i.concat(["mathnormal"]));if("textord"===r){let e=ea[n][l]&&ea[n][l].font;if("ams"===e)return eq(l,eL("amsrm",t.fontWeight,t.fontShape),n,t,i.concat("amsrm",t.fontWeight,t.fontShape));if("main"===e||!e)return eq(l,eL("textrm",t.fontWeight,t.fontShape),n,t,i.concat(t.fontWeight,t.fontShape));{let r=eL(e,t.fontWeight,t.fontShape);return eq(l,r,n,t,i.concat(r,t.fontWeight,t.fontShape))}}throw Error("unexpected type: "+r+" in makeOrd")},makeGlue:(e,t)=>{let r=eH(["mspace"],[],t),n=G(e,t);return r.style.marginRight=U(n),r},staticSvg:function(e,t){let[r,n,l]=eV[e],i=eR(["overlay"],[new ee([new et(r)],{width:U(n),height:U(l),style:"width:"+U(n),viewBox:"0 0 "+1e3*n+" "+1e3*l,preserveAspectRatio:"xMinYMin"})],t);return i.height=l,i.style.height=U(l),i.style.width=U(n),i},svgData:eV,tryCombineChars:e=>{for(let t=0;t<e.length-1;t++){let r=e[t],n=e[t+1];r instanceof Q&&n instanceof Q&&eN(r,n)&&(r.text+=n.text,r.height=Math.max(r.height,n.height),r.depth=Math.max(r.depth,n.depth),r.italic=n.italic,e.splice(t+1,1),t--)}return e}};let eF={number:3,unit:"mu"},eG={number:4,unit:"mu"},eU={number:5,unit:"mu"},eY={mord:{mop:eF,mbin:eG,mrel:eU,minner:eF},mop:{mord:eF,mop:eF,mrel:eU,minner:eF},mbin:{mord:eG,mop:eG,mopen:eG,minner:eG},mrel:{mord:eU,mop:eU,mopen:eU,minner:eU},mopen:{},mclose:{mop:eF,mbin:eG,mrel:eU,minner:eF},mpunct:{mord:eF,mop:eF,mrel:eU,mopen:eF,mclose:eF,mpunct:eF,minner:eF},minner:{mord:eF,mop:eF,mbin:eG,mrel:eU,mopen:eF,mpunct:eF,minner:eF}},eX={mord:{mop:eF},mop:{mord:eF,mop:eF},mbin:{},mrel:{},mopen:{},mclose:{mop:eF},mpunct:{},minner:{mop:eF}},e_={},eW={},ej={};function e$(e){let{type:t,names:r,props:n,handler:l,htmlBuilder:i,mathmlBuilder:a}=e,s={type:t,numArgs:n.numArgs,argTypes:n.argTypes,allowedInArgument:!!n.allowedInArgument,allowedInText:!!n.allowedInText,allowedInMath:void 0===n.allowedInMath||n.allowedInMath,numOptionalArgs:n.numOptionalArgs||0,infix:!!n.infix,primitive:!!n.primitive,handler:l};for(let e=0;e<r.length;++e)e_[r[e]]=s;t&&(i&&(eW[t]=i),a&&(ej[t]=a))}function eZ(e){let{type:t,htmlBuilder:r,mathmlBuilder:n}=e;e$({type:t,names:[],props:{numArgs:0},handler(){throw Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:n})}let eK=function(e){return"ordgroup"===e.type&&1===e.body.length?e.body[0]:e},eJ=function(e){return"ordgroup"===e.type?e.body:[e]},eQ=eP.makeSpan,e0=["leftmost","mbin","mopen","mrel","mop","mpunct"],e1=["rightmost","mrel","mclose","mpunct"],e4={display:v.DISPLAY,text:v.TEXT,script:v.SCRIPT,scriptscript:v.SCRIPTSCRIPT},e5={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},e6=function(e,t,r,n){void 0===n&&(n=[null,null]);let l=[];for(let r=0;r<e.length;r++){let n=te(e[r],t);if(n instanceof C){let e=n.children;l.push(...e)}else l.push(n)}if(eP.tryCombineChars(l),!r)return l;let i=t;if(1===e.length){let r=e[0];"sizing"===r.type?i=t.havingSize(r.size):"styling"===r.type&&(i=t.havingStyle(e4[r.style]))}let a=eQ([n[0]||"leftmost"],[],t),s=eQ([n[1]||"rightmost"],[],t),o="root"===r;return e7(l,(e,t)=>{let r=t.classes[0],n=e.classes[0];"mbin"===r&&m.contains(e1,n)?t.classes[0]="mord":"mbin"===n&&m.contains(e0,r)&&(e.classes[0]="mord")},{node:a},s,o),e7(l,(e,t)=>{let r=e2(t),n=e2(e),l=r&&n?e.hasClass("mtight")?eX[r][n]:eY[r][n]:null;if(l)return eP.makeGlue(l,i)},{node:a},s,o),l},e7=function(e,t,r,n,l){n&&e.push(n);let i=0;for(;i<e.length;i++){let n;let a=e[i],s=e3(a);if(s){e7(s.children,t,r,null,l);continue}let o=!a.hasClass("mspace");if(o){let n=t(a,r.node);n&&(r.insertAfter?r.insertAfter(n):(e.unshift(n),i++))}o?r.node=a:l&&a.hasClass("newline")&&(r.node=eQ(["leftmost"])),n=i,r.insertAfter=t=>{e.splice(n+1,0,t),i++}}n&&e.pop()},e3=function(e){return e instanceof C||e instanceof Z||e instanceof $&&e.hasClass("enclosing")?e:null},e8=function(e,t){let r=e3(e);if(r){let e=r.children;if(e.length){if("right"===t)return e8(e[e.length-1],"right");if("left"===t)return e8(e[0],"left")}}return e},e2=function(e,t){return e?(t&&(e=e8(e,t)),e5[e.classes[0]]||null):null},e9=function(e,t){let r=["nulldelimiter"].concat(e.baseSizingClasses());return eQ(t.concat(r))},te=function(e,t,r){if(!e)return eQ();if(eW[e.type]){let n=eW[e.type](e,t);if(r&&t.size!==r.size){n=eQ(t.sizingClasses(r),[n],t);let e=t.sizeMultiplier/r.sizeMultiplier;n.height*=e,n.depth*=e}return n}throw new l("Got group of unknown type: '"+e.type+"'")};function tt(e,t){let r=eQ(["base"],e,t),n=eQ(["strut"]);return n.style.height=U(r.height+r.depth),r.depth&&(n.style.verticalAlign=U(-r.depth)),r.children.unshift(n),r}function tr(e,t){let r,n,l=null;1===e.length&&"tag"===e[0].type&&(l=e[0].tag,e=e[0].body);let i=e6(e,t,"root");2===i.length&&i[1].hasClass("tag")&&(r=i.pop());let a=[],s=[];for(let e=0;e<i.length;e++)if(s.push(i[e]),i[e].hasClass("mbin")||i[e].hasClass("mrel")||i[e].hasClass("allowbreak")){let r=!1;for(;e<i.length-1&&i[e+1].hasClass("mspace")&&!i[e+1].hasClass("newline");)e++,s.push(i[e]),i[e].hasClass("nobreak")&&(r=!0);r||(a.push(tt(s,t)),s=[])}else i[e].hasClass("newline")&&(s.pop(),s.length>0&&(a.push(tt(s,t)),s=[]),a.push(i[e]));s.length>0&&a.push(tt(s,t)),l?((n=tt(e6(l,t,!0))).classes=["tag"],a.push(n)):r&&a.push(r);let o=eQ(["katex-html"],a);if(o.setAttribute("aria-hidden","true"),n){let e=n.children[0];e.style.height=U(o.height+o.depth),o.depth&&(e.style.verticalAlign=U(-o.depth))}return o}function tn(e){return new C(e)}class tl{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){let e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=Y(this.classes));for(let t=0;t<this.children.length;t++)if(this.children[t]instanceof ti&&this.children[t+1]instanceof ti){let r=this.children[t].toText()+this.children[++t].toText();for(;this.children[t+1]instanceof ti;)r+=this.children[++t].toText();e.appendChild(new ti(r).toNode())}else e.appendChild(this.children[t].toNode());return e}toMarkup(){let e="<"+this.type;for(let t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="',e+=m.escape(this.attributes[t]),e+='"');this.classes.length>0&&(e+=' class ="'+m.escape(Y(this.classes))+'"'),e+=">";for(let t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e+("</"+this.type+">")}toText(){return this.children.map(e=>e.toText()).join("")}}class ti{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return m.escape(this.toText())}toText(){return this.text}}class ta{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);{let e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",U(this.width)),e}}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+U(this.width)+'"/>'}toText(){return this.character?this.character:" "}}var ts={MathNode:tl,TextNode:ti,SpaceNode:ta,newDocumentFragment:tn};let to=function(e,t,r){return ea[t][e]&&ea[t][e].replace&&55349!==e.charCodeAt(0)&&!(ew.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.slice(4,6)||r.font&&"tt"===r.font.slice(4,6)))&&(e=ea[t][e].replace),new ts.TextNode(e)},th=function(e){return 1===e.length?e[0]:new ts.MathNode("mrow",e)},tm=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";let r=t.font;if(!r||"mathnormal"===r)return null;let n=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"textord"===e.type?"bold":"bold-italic";if("mathbf"===r)return"bold";if("mathbb"===r)return"double-struck";if("mathsfit"===r)return"sans-serif-italic";else if("mathfrak"===r)return"fraktur";else if("mathscr"===r||"mathcal"===r)return"script";else if("mathsf"===r)return"sans-serif";else if("mathtt"===r)return"monospace";let l=e.text;return m.contains(["\\imath","\\jmath"],l)?null:(ea[n][l]&&ea[n][l].replace&&(l=ea[n][l].replace),H(l,eP.fontMap[r].fontName,n))?eP.fontMap[r].variant:null};function tc(e){if(!e)return!1;if("mi"===e.type&&1===e.children.length){let t=e.children[0];return t instanceof ti&&"."===t.text}if("mo"!==e.type||1!==e.children.length||"true"!==e.getAttribute("separator")||"0em"!==e.getAttribute("lspace")||"0em"!==e.getAttribute("rspace"))return!1;{let t=e.children[0];return t instanceof ti&&","===t.text}}let tp=function(e,t,r){let n;if(1===e.length){let n=td(e[0],t);return r&&n instanceof tl&&"mo"===n.type&&(n.setAttribute("lspace","0em"),n.setAttribute("rspace","0em")),[n]}let l=[];for(let r=0;r<e.length;r++){let i=td(e[r],t);if(i instanceof tl&&n instanceof tl){if("mtext"===i.type&&"mtext"===n.type&&i.getAttribute("mathvariant")===n.getAttribute("mathvariant")){n.children.push(...i.children);continue}if("mn"===i.type&&"mn"===n.type){n.children.push(...i.children);continue}if(tc(i)&&"mn"===n.type){n.children.push(...i.children);continue}else if("mn"===i.type&&tc(n))i.children=[...n.children,...i.children],l.pop();else if(("msup"===i.type||"msub"===i.type)&&i.children.length>=1&&("mn"===n.type||tc(n))){let e=i.children[0];e instanceof tl&&"mn"===e.type&&(e.children=[...n.children,...e.children],l.pop())}else if("mi"===n.type&&1===n.children.length){let e=n.children[0];if(e instanceof ti&&"̸"===e.text&&("mo"===i.type||"mi"===i.type||"mn"===i.type)){let e=i.children[0];e instanceof ti&&e.text.length>0&&(e.text=e.text.slice(0,1)+"̸"+e.text.slice(1),l.pop())}}}l.push(i),n=i}return l},tu=function(e,t,r){return th(tp(e,t,r))},td=function(e,t){if(!e)return new ts.MathNode("mrow");if(ej[e.type])return ej[e.type](e,t);throw new l("Got group of unknown type: '"+e.type+"'")};function tg(e,t,r,n,l){let i;let a=tp(e,r);i=1===a.length&&a[0]instanceof tl&&m.contains(["mrow","mtable"],a[0].type)?a[0]:new ts.MathNode("mrow",a);let s=new ts.MathNode("annotation",[new ts.TextNode(t)]);s.setAttribute("encoding","application/x-tex");let o=new ts.MathNode("semantics",[i,s]),h=new ts.MathNode("math",[o]);return h.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),n&&h.setAttribute("display","block"),eP.makeSpan([l?"katex":"katex-mathml"],[h])}let tf=function(e){return new D({style:e.displayMode?v.DISPLAY:v.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},tb=function(e,t){if(t.displayMode){let r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=eP.makeSpan(r,[e])}return e},ty=function(e,t,r){let n;let l=tf(r);if("mathml"===r.output)return tg(e,t,l,r.displayMode,!0);if("html"===r.output){let t=tr(e,l);n=eP.makeSpan(["katex"],[t])}else{let i=tg(e,t,l,r.displayMode,!1),a=tr(e,l);n=eP.makeSpan(["katex"],[i,a])}return tb(n,r)},tx=function(e,t,r){let n=tr(e,tf(r));return tb(eP.makeSpan(["katex"],[n]),r)},tw={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},tv={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]};var tk={encloseSpan:function(e,t,r,n,l){let i;let a=e.height+e.depth+r+n;if(/fbox|color|angl/.test(t)){if(i=eP.makeSpan(["stretchy",t],[],l),"fbox"===t){let e=l.color&&l.getColor();e&&(i.style.borderColor=e)}}else{let e=[];/^[bx]cancel$/.test(t)&&e.push(new er({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&e.push(new er({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));let r=new ee(e,{width:"100%",height:U(a)});i=eP.makeSvgSpan([],[r],l)}return i.height=a,i.style.height=U(a),i},mathMLnode:function(e){let t=new ts.MathNode("mo",[new ts.TextNode(tw[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},svgSpan:function(e,t){let{span:r,minWidth:n,height:l}=function(){let r=4e5,n=e.label.slice(1);if(m.contains(["widehat","widecheck","widetilde","utilde"],n)){var l;let i,a,s;let o="ordgroup"===(l=e.base).type?l.body.length:1;if(o>5)"widehat"===n||"widecheck"===n?(i=420,r=2364,s=.42,a=n+"4"):(i=312,r=2340,s=.34,a="tilde4");else{let e=[1,1,2,2,3,3][o];"widehat"===n||"widecheck"===n?(r=[0,1062,2364,2364,2364][e],i=[0,239,300,360,420][e],s=[0,.24,.3,.3,.36,.42][e],a=n+e):(r=[0,600,1033,2339,2340][e],i=[0,260,286,306,312][e],s=[0,.26,.286,.3,.306,.34][e],a="tilde"+e)}let h=new ee([new et(a)],{width:"100%",height:U(s),viewBox:"0 0 "+r+" "+i,preserveAspectRatio:"none"});return{span:eP.makeSvgSpan([],[h],t),minWidth:0,height:s}}{let e,l;let i=[],a=tv[n],[s,o,h]=a,m=h/1e3,c=s.length;if(1===c)e=["hide-tail"],l=[a[3]];else if(2===c)e=["halfarrow-left","halfarrow-right"],l=["xMinYMin","xMaxYMin"];else if(3===c)e=["brace-left","brace-center","brace-right"],l=["xMinYMin","xMidYMin","xMaxYMin"];else throw Error("Correct katexImagesData or update code here to support\n                    "+c+" children.");for(let n=0;n<c;n++){let a=new ee([new et(s[n])],{width:"400em",height:U(m),viewBox:"0 0 "+r+" "+h,preserveAspectRatio:l[n]+" slice"}),p=eP.makeSvgSpan([e[n]],[a],t);if(1===c)return{span:p,minWidth:o,height:m};p.style.height=U(m),i.push(p)}return{span:eP.makeSpan(["stretchy"],i,t),minWidth:o,height:m}}}();return r.height=l,r.style.height=U(l),n>0&&(r.style.minWidth=U(n)),r}};function tS(e,t){if(!e||e.type!==t)throw Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return e}function tM(e){let t=tz(e);if(!t)throw Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}function tz(e){return e&&("atom"===e.type||ei.hasOwnProperty(e.type))?e:null}let tA=(e,t)=>{let r,n,l,i;e&&"supsub"===e.type?(r=(n=tS(e.base,"accent")).base,e.base=r,l=function(e){if(e instanceof $)return e;throw Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(te(e,t)),e.base=n):r=(n=tS(e,"accent")).base;let a=te(r,t.havingCrampedStyle()),s=n.isShifty&&m.isCharacterBox(r),o=0;s&&(o=en(te(m.getBaseElem(r),t.havingCrampedStyle())).skew);let h="\\c"===n.label,c=h?a.height+a.depth:Math.min(a.height,t.fontMetrics().xHeight);if(n.isStretchy)i=tk.svgSpan(n,t),i=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"elem",elem:i,wrapperClasses:["svg-align"],wrapperStyle:o>0?{width:"calc(100% - "+U(2*o)+")",marginLeft:U(2*o)}:void 0}]},t);else{let e,r;"\\vec"===n.label?(e=eP.staticSvg("vec",t),r=eP.svgData.vec[1]):((e=en(e=eP.makeOrd({mode:n.mode,text:n.label},t,"textord"))).italic=0,r=e.width,h&&(c+=e.depth)),i=eP.makeSpan(["accent-body"],[e]);let l="\\textcircled"===n.label;l&&(i.classes.push("accent-full"),c=a.height);let s=o;l||(s-=r/2),i.style.left=U(s),"\\textcircled"===n.label&&(i.style.top=".2em"),i=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"kern",size:-c},{type:"elem",elem:i}]},t)}let p=eP.makeSpan(["mord","accent"],[i],t);return l?(l.children[0]=p,l.height=Math.max(p.height,l.height),l.classes[0]="mord",l):p},tT=(e,t)=>{let r=e.isStretchy?tk.mathMLnode(e.label):new ts.MathNode("mo",[to(e.label,e.mode)]),n=new ts.MathNode("mover",[td(e.base,t),r]);return n.setAttribute("accent","true"),n},tB=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(e=>"\\"+e).join("|"));e$({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(e,t)=>{let r=eK(t[0]),n=!tB.test(e.funcName),l=!n||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:n,isShifty:l,base:r}},htmlBuilder:tA,mathmlBuilder:tT}),e$({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(e,t)=>{let r=t[0],n=e.parser.mode;return"math"===n&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),n="text"),{type:"accent",mode:n,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:tA,mathmlBuilder:tT}),e$({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=t[0];return{type:"accentUnder",mode:r.mode,label:n,base:l}},htmlBuilder:(e,t)=>{let r=te(e.base,t),n=tk.svgSpan(e,t),l=.12*("\\utilde"===e.label),i=eP.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:l},{type:"elem",elem:r}]},t);return eP.makeSpan(["mord","accentunder"],[i],t)},mathmlBuilder:(e,t)=>{let r=tk.mathMLnode(e.label),n=new ts.MathNode("munder",[td(e.base,t),r]);return n.setAttribute("accentunder","true"),n}});let tC=e=>{let t=new ts.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};e$({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){let{parser:n,funcName:l}=e;return{type:"xArrow",mode:n.mode,label:l,body:t[0],below:r[0]}},htmlBuilder(e,t){let r,n;let l=t.style,i=t.havingStyle(l.sup()),a=eP.wrapFragment(te(e.body,i,t),t),s="\\x"===e.label.slice(0,2)?"x":"cd";a.classes.push(s+"-arrow-pad"),e.below&&(i=t.havingStyle(l.sub()),(r=eP.wrapFragment(te(e.below,i,t),t)).classes.push(s+"-arrow-pad"));let o=tk.svgSpan(e,t),h=-t.fontMetrics().axisHeight+.5*o.height,m=-t.fontMetrics().axisHeight-.5*o.height-.111;if((a.depth>.25||"\\xleftequilibrium"===e.label)&&(m-=a.depth),r){let e=-t.fontMetrics().axisHeight+r.height+.5*o.height+.111;n=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:m},{type:"elem",elem:o,shift:h},{type:"elem",elem:r,shift:e}]},t)}else n=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:m},{type:"elem",elem:o,shift:h}]},t);return n.children[0].children[0].children[1].classes.push("svg-align"),eP.makeSpan(["mrel","x-arrow"],[n],t)},mathmlBuilder(e,t){let r;let n=tk.mathMLnode(e.label);if(n.setAttribute("minsize","x"===e.label.charAt(0)?"1.75em":"3.0em"),e.body){let l=tC(td(e.body,t));if(e.below){let i=tC(td(e.below,t));r=new ts.MathNode("munderover",[n,i,l])}else r=new ts.MathNode("mover",[n,l])}else if(e.below){let l=tC(td(e.below,t));r=new ts.MathNode("munder",[n,l])}else r=tC(),r=new ts.MathNode("mover",[n,r]);return r}});let tq=eP.makeSpan;function tN(e,t){let r=e6(e.body,t,!0);return tq([e.mclass],r,t)}function tI(e,t){let r;let n=tp(e.body,t);return"minner"===e.mclass?r=new ts.MathNode("mpadded",n):"mord"===e.mclass?e.isCharacterBox?(r=n[0]).type="mi":r=new ts.MathNode("mi",n):(e.isCharacterBox?(r=n[0]).type="mo":r=new ts.MathNode("mo",n),"mbin"===e.mclass?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):"mpunct"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):"mopen"===e.mclass||"mclose"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0em"):"minner"===e.mclass&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}e$({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(e,t){let{parser:r,funcName:n}=e,l=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+n.slice(5),body:eJ(l),isCharacterBox:m.isCharacterBox(l)}},htmlBuilder:tN,mathmlBuilder:tI});let tH=e=>{let t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"===t.type&&("bin"===t.family||"rel"===t.family)?"m"+t.family:"mord"};e$({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(e,t){let{parser:r}=e;return{type:"mclass",mode:r.mode,mclass:tH(t[0]),body:eJ(t[1]),isCharacterBox:m.isCharacterBox(t[1])}}}),e$({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(e,t){let r,{parser:n,funcName:l}=e,i=t[1],a=t[0];r="\\stackrel"!==l?tH(i):"mrel";let s={type:"op",mode:i.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:"\\stackrel"!==l,body:eJ(i)},o={type:"supsub",mode:a.mode,base:s,sup:"\\underset"===l?null:a,sub:"\\underset"===l?a:null};return{type:"mclass",mode:n.mode,mclass:r,body:[o],isCharacterBox:m.isCharacterBox(o)}},htmlBuilder:tN,mathmlBuilder:tI}),e$({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(e,t){let{parser:r}=e;return{type:"pmb",mode:r.mode,mclass:tH(t[0]),body:eJ(t[0])}},htmlBuilder(e,t){let r=e6(e.body,t,!0),n=eP.makeSpan([e.mclass],r,t);return n.style.textShadow="0.02em 0.01em 0.04px",n},mathmlBuilder(e,t){let r=tp(e.body,t),n=new ts.MathNode("mstyle",r);return n.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),n}});let tR={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},tO=()=>({type:"styling",body:[],mode:"math",style:"display"}),tE=e=>"textord"===e.type&&"@"===e.text,tL=(e,t)=>("mathord"===e.type||"atom"===e.type)&&e.text===t;e$({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(e,t){let{parser:r,funcName:n}=e;return{type:"cdlabel",mode:r.mode,side:n.slice(4),label:t[0]}},htmlBuilder(e,t){let r=t.havingStyle(t.style.sup()),n=eP.wrapFragment(te(e.label,r,t),t);return n.classes.push("cd-label-"+e.side),n.style.bottom=U(.8-n.depth),n.height=0,n.depth=0,n},mathmlBuilder(e,t){let r=new ts.MathNode("mrow",[td(e.label,t)]);return(r=new ts.MathNode("mpadded",[r])).setAttribute("width","0"),"left"===e.side&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new ts.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),e$({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(e,t){let{parser:r}=e;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder(e,t){let r=eP.wrapFragment(te(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:(e,t)=>new ts.MathNode("mrow",[td(e.fragment,t)])}),e$({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(e,t){let r,{parser:n}=e,i=tS(t[0],"ordgroup").body,a="";for(let e=0;e<i.length;e++)a+=tS(i[e],"textord").text;let s=parseInt(a);if(isNaN(s))throw new l("\\@char has non-numeric argument "+a);if(s<0||s>=1114111)throw new l("\\@char with invalid code point "+a);return s<=65535?r=String.fromCharCode(s):(s-=65536,r=String.fromCharCode((s>>10)+55296,(1023&s)+56320)),{type:"textord",mode:n.mode,text:r}}});let tD=(e,t)=>{let r=e6(e.body,t.withColor(e.color),!1);return eP.makeFragment(r)},tV=(e,t)=>{let r=tp(e.body,t.withColor(e.color)),n=new ts.MathNode("mstyle",r);return n.setAttribute("mathcolor",e.color),n};e$({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(e,t){let{parser:r}=e,n=tS(t[0],"color-token").color,l=t[1];return{type:"color",mode:r.mode,color:n,body:eJ(l)}},htmlBuilder:tD,mathmlBuilder:tV}),e$({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(e,t){let{parser:r,breakOnTokenText:n}=e,l=tS(t[0],"color-token").color;r.gullet.macros.set("\\current@color",l);let i=r.parseExpression(!0,n);return{type:"color",mode:r.mode,color:l,body:i}},htmlBuilder:tD,mathmlBuilder:tV}),e$({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(e,t,r){let{parser:n}=e,l="["===n.gullet.future().text?n.parseSizeGroup(!0):null,i=!n.settings.displayMode||!n.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:n.mode,newLine:i,size:l&&tS(l,"size").value}},htmlBuilder(e,t){let r=eP.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=U(G(e.size,t)))),r},mathmlBuilder(e,t){let r=new ts.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",U(G(e.size,t)))),r}});let tP={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},tF=e=>{let t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new l("Expected a control sequence",e);return t},tG=e=>{let t=e.gullet.popToken();return"="===t.text&&" "===(t=e.gullet.popToken()).text&&(t=e.gullet.popToken()),t},tU=(e,t,r,n)=>{let l=e.gullet.macros.get(r.text);null==l&&(r.noexpand=!0,l={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,l,n)};e$({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(e){let{parser:t,funcName:r}=e;t.consumeSpaces();let n=t.fetch();if(tP[n.text])return("\\global"===r||"\\\\globallong"===r)&&(n.text=tP[n.text]),tS(t.parseFunction(),"internal");throw new l("Invalid token after macro prefix",n)}}),e$({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){let t,{parser:r,funcName:n}=e,i=r.gullet.popToken(),a=i.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(a))throw new l("Expected a control sequence",i);let s=0,o=[[]];for(;"{"!==r.gullet.future().text;)if("#"===(i=r.gullet.popToken()).text){if("{"===r.gullet.future().text){t=r.gullet.future(),o[s].push("{");break}if(i=r.gullet.popToken(),!/^[1-9]$/.test(i.text))throw new l('Invalid argument number "'+i.text+'"');if(parseInt(i.text)!==s+1)throw new l('Argument number "'+i.text+'" out of order');s++,o.push([])}else if("EOF"===i.text)throw new l("Expected a macro definition");else o[s].push(i.text);let{tokens:h}=r.gullet.consumeArg();return t&&h.unshift(t),("\\edef"===n||"\\xdef"===n)&&(h=r.gullet.expandTokens(h)).reverse(),r.gullet.macros.set(a,{tokens:h,numArgs:s,delimiters:o},n===tP[n]),{type:"internal",mode:r.mode}}}),e$({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){let{parser:t,funcName:r}=e,n=tF(t.gullet.popToken());t.gullet.consumeSpaces();let l=tG(t);return tU(t,n,l,"\\\\globallet"===r),{type:"internal",mode:t.mode}}}),e$({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){let{parser:t,funcName:r}=e,n=tF(t.gullet.popToken()),l=t.gullet.popToken(),i=t.gullet.popToken();return tU(t,n,i,"\\\\globalfuture"===r),t.gullet.pushToken(i),t.gullet.pushToken(l),{type:"internal",mode:t.mode}}});let tY=function(e,t,r){let n=H(ea.math[e]&&ea.math[e].replace||e,t,r);if(!n)throw Error("Unsupported symbol "+e+" and font size "+t+".");return n},tX=function(e,t,r,n){let l=r.havingBaseStyle(t),i=eP.makeSpan(n.concat(l.sizingClasses(r)),[e],r),a=l.sizeMultiplier/r.sizeMultiplier;return i.height*=a,i.depth*=a,i.maxFontSize=l.sizeMultiplier,i},t_=function(e,t,r){let n=t.havingBaseStyle(r),l=(1-t.sizeMultiplier/n.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=U(l),e.height-=l,e.depth+=l},tW=function(e,t,r,n,l,i){let a=tX(eP.makeSymbol(e,"Main-Regular",l,n),t,n,i);return r&&t_(a,n,t),a},tj=function(e,t,r,n,l,i){let a=eP.makeSymbol(e,"Size"+t+"-Regular",l,n),s=tX(eP.makeSpan(["delimsizing","size"+t],[a],n),v.TEXT,n,i);return r&&t_(s,n,v.TEXT),s},t$=function(e,t,r){return{type:"elem",elem:eP.makeSpan(["delimsizinginner","Size1-Regular"===t?"delim-size1":"delim-size4"],[eP.makeSpan([],[eP.makeSymbol(e,t,r)])])}},tZ=function(e,t,r){let n=q["Size4-Regular"][e.charCodeAt(0)]?q["Size4-Regular"][e.charCodeAt(0)][4]:q["Size1-Regular"][e.charCodeAt(0)][4],l=new ee([new et("inner",A(e,Math.round(1e3*t)))],{width:U(n),height:U(t),style:"width:"+U(n),viewBox:"0 0 "+1e3*n+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),i=eP.makeSvgSpan([],[l],r);return i.height=t,i.style.height=U(t),i.style.width=U(n),{type:"elem",elem:i}},tK={type:"kern",size:-.008},tJ=["|","\\lvert","\\rvert","\\vert"],tQ=["\\|","\\lVert","\\rVert","\\Vert"],t0=function(e,t,r,n,l,i){let a,s,o,h;let c="",p=0;a=o=h=e,s=null;let u="Size1-Regular";"\\uparrow"===e?o=h="⏐":"\\Uparrow"===e?o=h="‖":"\\downarrow"===e?a=o="⏐":"\\Downarrow"===e?a=o="‖":"\\updownarrow"===e?(a="\\uparrow",o="⏐",h="\\downarrow"):"\\Updownarrow"===e?(a="\\Uparrow",o="‖",h="\\Downarrow"):m.contains(tJ,e)?(o="∣",c="vert",p=333):m.contains(tQ,e)?(o="∥",c="doublevert",p=556):"["===e||"\\lbrack"===e?(a="⎡",o="⎢",h="⎣",u="Size4-Regular",c="lbrack",p=667):"]"===e||"\\rbrack"===e?(a="⎤",o="⎥",h="⎦",u="Size4-Regular",c="rbrack",p=667):"\\lfloor"===e||"⌊"===e?(o=a="⎢",h="⎣",u="Size4-Regular",c="lfloor",p=667):"\\lceil"===e||"⌈"===e?(a="⎡",o=h="⎢",u="Size4-Regular",c="lceil",p=667):"\\rfloor"===e||"⌋"===e?(o=a="⎥",h="⎦",u="Size4-Regular",c="rfloor",p=667):"\\rceil"===e||"⌉"===e?(a="⎤",o=h="⎥",u="Size4-Regular",c="rceil",p=667):"("===e||"\\lparen"===e?(a="⎛",o="⎜",h="⎝",u="Size4-Regular",c="lparen",p=875):")"===e||"\\rparen"===e?(a="⎞",o="⎟",h="⎠",u="Size4-Regular",c="rparen",p=875):"\\{"===e||"\\lbrace"===e?(a="⎧",s="⎨",h="⎩",o="⎪",u="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(a="⎫",s="⎬",h="⎭",o="⎪",u="Size4-Regular"):"\\lgroup"===e||"⟮"===e?(a="⎧",h="⎩",o="⎪",u="Size4-Regular"):"\\rgroup"===e||"⟯"===e?(a="⎫",h="⎭",o="⎪",u="Size4-Regular"):"\\lmoustache"===e||"⎰"===e?(a="⎧",h="⎭",o="⎪",u="Size4-Regular"):("\\rmoustache"===e||"⎱"===e)&&(a="⎫",h="⎩",o="⎪",u="Size4-Regular");let d=tY(a,u,l),g=d.height+d.depth,f=tY(o,u,l),b=f.height+f.depth,y=tY(h,u,l),x=y.height+y.depth,w=0,k=1;if(null!==s){let e=tY(s,u,l);w=e.height+e.depth,k=2}let S=g+x+w,M=Math.max(0,Math.ceil((t-S)/(k*b))),z=S+M*k*b,A=n.fontMetrics().axisHeight;r&&(A*=n.sizeMultiplier);let T=z/2-A,C=[];if(c.length>0){let e=Math.round(1e3*z),t=B(c,Math.round(1e3*(z-g-x))),r=new et(c,t),l=(p/1e3).toFixed(3)+"em",i=(e/1e3).toFixed(3)+"em",a=new ee([r],{width:l,height:i,viewBox:"0 0 "+p+" "+e}),s=eP.makeSvgSpan([],[a],n);s.height=e/1e3,s.style.width=l,s.style.height=i,C.push({type:"elem",elem:s})}else{if(C.push(t$(h,u,l)),C.push(tK),null===s)C.push(tZ(o,z-g-x+.016,n));else{let e=(z-g-x-w)/2+.016;C.push(tZ(o,e,n)),C.push(tK),C.push(t$(s,u,l)),C.push(tK),C.push(tZ(o,e,n))}C.push(tK),C.push(t$(a,u,l))}let q=n.havingBaseStyle(v.TEXT),N=eP.makeVList({positionType:"bottom",positionData:T,children:C},q);return tX(eP.makeSpan(["delimsizing","mult"],[N],q),v.TEXT,n,i)},t1=function(e,t,r,n,l){let i=z(e,n,r),a=new ee([new et(e,i)],{width:"400em",height:U(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return eP.makeSvgSpan(["hide-tail"],[a],l)},t4=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],t5=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],t6=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],t7=[0,1.2,1.8,2.4,3],t3=[{type:"small",style:v.SCRIPTSCRIPT},{type:"small",style:v.SCRIPT},{type:"small",style:v.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],t8=[{type:"small",style:v.SCRIPTSCRIPT},{type:"small",style:v.SCRIPT},{type:"small",style:v.TEXT},{type:"stack"}],t2=[{type:"small",style:v.SCRIPTSCRIPT},{type:"small",style:v.SCRIPT},{type:"small",style:v.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],t9=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw Error("Add support for delim type '"+e.type+"' here.")},re=function(e,t,r,n){let l=Math.min(2,3-n.style.size);for(let i=l;i<r.length&&"stack"!==r[i].type;i++){let l=tY(e,t9(r[i]),"math"),a=l.height+l.depth;if("small"===r[i].type&&(a*=n.havingBaseStyle(r[i].style).sizeMultiplier),a>t)return r[i]}return r[r.length-1]},rt=function(e,t,r,n,l,i){let a;"<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),a=m.contains(t6,e)?t3:m.contains(t4,e)?t2:t8;let s=re(e,t,a,n);return"small"===s.type?tW(e,s.style,r,n,l,i):"large"===s.type?tj(e,s.size,r,n,l,i):t0(e,t,r,n,l,i)};var rr={sqrtImage:function(e,t){let r,n;let l=t.havingBaseSizing(),i=re("\\surd",e*l.sizeMultiplier,t2,l),a=l.sizeMultiplier,s=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),o=0,h=0,m=0;return"small"===i.type?(m=1e3+1e3*s+80,e<1?a=1:e<1.4&&(a=.7),o=(1+s+.08)/a,h=(1+s)/a,(r=t1("sqrtMain",o,m,s,t)).style.minWidth="0.853em",n=.833/a):"large"===i.type?(m=1080*t7[i.size],h=(t7[i.size]+s)/a,o=(t7[i.size]+s+.08)/a,(r=t1("sqrtSize"+i.size,o,m,s,t)).style.minWidth="1.02em",n=1/a):(o=e+s+.08,h=e+s,(r=t1("sqrtTall",o,m=Math.floor(1e3*e+s)+80,s,t)).style.minWidth="0.742em",n=1.056),r.height=h,r.style.height=U(o),{span:r,advanceWidth:n,ruleWidth:(t.fontMetrics().sqrtRuleThickness+s)*a}},sizedDelim:function(e,t,r,n,i){if("<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),m.contains(t4,e)||m.contains(t6,e))return tj(e,t,!1,r,n,i);if(m.contains(t5,e))return t0(e,t7[t],!1,r,n,i);throw new l("Illegal delimiter: '"+e+"'")},sizeToMaxHeight:t7,customSizedDelim:rt,leftRightDelim:function(e,t,r,n,l,i){let a=n.fontMetrics().axisHeight*n.sizeMultiplier,s=5/n.fontMetrics().ptPerEm,o=Math.max(t-a,r+a);return rt(e,Math.max(o/500*901,2*o-s),!0,n,l,i)}};let rn={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},rl=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function ri(e,t){let r=tz(e);if(r&&m.contains(rl,r.text))return r;if(r)throw new l("Invalid delimiter '"+r.text+"' after '"+t.funcName+"'",e);throw new l("Invalid delimiter type '"+e.type+"'",e)}function ra(e){if(!e.body)throw Error("Bug: The leftright ParseNode wasn't fully parsed.")}e$({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(e,t)=>{let r=ri(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:rn[e.funcName].size,mclass:rn[e.funcName].mclass,delim:r.text}},htmlBuilder:(e,t)=>"."===e.delim?eP.makeSpan([e.mclass]):rr.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass]),mathmlBuilder:e=>{let t=[];"."!==e.delim&&t.push(to(e.delim,e.mode));let r=new ts.MathNode("mo",t);"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");let n=U(rr.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",n),r.setAttribute("maxsize",n),r}}),e$({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let r=e.parser.gullet.macros.get("\\current@color");if(r&&"string"!=typeof r)throw new l("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:ri(t[0],e).text,color:r}}}),e$({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let r=ri(t[0],e),n=e.parser;++n.leftrightDepth;let l=n.parseExpression(!1);--n.leftrightDepth,n.expect("\\right",!1);let i=tS(n.parseFunction(),"leftright-right");return{type:"leftright",mode:n.mode,body:l,left:r.text,right:i.delim,rightColor:i.color}},htmlBuilder:(e,t)=>{let r,n;ra(e);let l=e6(e.body,t,!0,["mopen","mclose"]),i=0,a=0,s=!1;for(let e=0;e<l.length;e++)l[e].isMiddle?s=!0:(i=Math.max(l[e].height,i),a=Math.max(l[e].depth,a));if(i*=t.sizeMultiplier,a*=t.sizeMultiplier,r="."===e.left?e9(t,["mopen"]):rr.leftRightDelim(e.left,i,a,t,e.mode,["mopen"]),l.unshift(r),s)for(let t=1;t<l.length;t++){let r=l[t].isMiddle;r&&(l[t]=rr.leftRightDelim(r.delim,i,a,r.options,e.mode,[]))}if("."===e.right)n=e9(t,["mclose"]);else{let r=e.rightColor?t.withColor(e.rightColor):t;n=rr.leftRightDelim(e.right,i,a,r,e.mode,["mclose"])}return l.push(n),eP.makeSpan(["minner"],l,t)},mathmlBuilder:(e,t)=>{ra(e);let r=tp(e.body,t);if("."!==e.left){let t=new ts.MathNode("mo",[to(e.left,e.mode)]);t.setAttribute("fence","true"),r.unshift(t)}if("."!==e.right){let t=new ts.MathNode("mo",[to(e.right,e.mode)]);t.setAttribute("fence","true"),e.rightColor&&t.setAttribute("mathcolor",e.rightColor),r.push(t)}return th(r)}}),e$({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let r=ri(t[0],e);if(!e.parser.leftrightDepth)throw new l("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:(e,t)=>{let r;if("."===e.delim)r=e9(t,[]);else{r=rr.sizedDelim(e.delim,1,t,e.mode,[]);let n={delim:e.delim,options:t};r.isMiddle=n}return r},mathmlBuilder:(e,t)=>{let r="\\vert"===e.delim||"|"===e.delim?to("|","text"):to(e.delim,e.mode),n=new ts.MathNode("mo",[r]);return n.setAttribute("fence","true"),n.setAttribute("lspace","0.05em"),n.setAttribute("rspace","0.05em"),n}});let rs=(e,t)=>{let r,n;let l=eP.wrapFragment(te(e.body,t),t),i=e.label.slice(1),a=t.sizeMultiplier,s=0,o=m.isCharacterBox(e.body);if("sout"===i)(r=eP.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/a,s=-.5*t.fontMetrics().xHeight;else if("phase"===i){let e=G({number:.6,unit:"pt"},t),n=G({number:.35,unit:"ex"},t);a/=t.havingBaseSizing().sizeMultiplier;let i=l.height+l.depth+e+n;l.style.paddingLeft=U(i/2+e);let o=Math.floor(1e3*i*a),h=new ee([new et("phase","M400000 "+o+" H0 L"+o/2+" 0 l65 45 L145 "+(o-80)+" H400000z")],{width:"400em",height:U(o/1e3),viewBox:"0 0 400000 "+o,preserveAspectRatio:"xMinYMin slice"});(r=eP.makeSvgSpan(["hide-tail"],[h],t)).style.height=U(i),s=l.depth+e+n}else{/cancel/.test(i)?o||l.classes.push("cancel-pad"):"angl"===i?l.classes.push("anglpad"):l.classes.push("boxpad");let n=0,a=0,h=0;/box/.test(i)?(h=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),a=n=t.fontMetrics().fboxsep+("colorbox"===i?0:h)):"angl"===i?(n=4*(h=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness)),a=Math.max(0,.25-l.depth)):a=n=.2*!!o,r=tk.encloseSpan(l,i,n,a,t),/fbox|boxed|fcolorbox/.test(i)?(r.style.borderStyle="solid",r.style.borderWidth=U(h)):"angl"===i&&.049!==h&&(r.style.borderTopWidth=U(h),r.style.borderRightWidth=U(h)),s=l.depth+a,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}if(e.backgroundColor)n=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:s},{type:"elem",elem:l,shift:0}]},t);else{let e=/cancel|phase/.test(i)?["svg-align"]:[];n=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:l,shift:0},{type:"elem",elem:r,shift:s,wrapperClasses:e}]},t)}return(/cancel/.test(i)&&(n.height=l.height,n.depth=l.depth),/cancel/.test(i)&&!o)?eP.makeSpan(["mord","cancel-lap"],[n],t):eP.makeSpan(["mord"],[n],t)},ro=(e,t)=>{let r=0,n=new ts.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[td(e.body,t)]);switch(e.label){case"\\cancel":n.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":n.setAttribute("notation","downdiagonalstrike");break;case"\\phase":n.setAttribute("notation","phasorangle");break;case"\\sout":n.setAttribute("notation","horizontalstrike");break;case"\\fbox":n.setAttribute("notation","box");break;case"\\angl":n.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,n.setAttribute("width","+"+2*r+"pt"),n.setAttribute("height","+"+2*r+"pt"),n.setAttribute("lspace",r+"pt"),n.setAttribute("voffset",r+"pt"),"\\fcolorbox"===e.label){let r=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);n.setAttribute("style","border: "+r+"em solid "+String(e.borderColor))}break;case"\\xcancel":n.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&n.setAttribute("mathbackground",e.backgroundColor),n};e$({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(e,t,r){let{parser:n,funcName:l}=e,i=tS(t[0],"color-token").color,a=t[1];return{type:"enclose",mode:n.mode,label:l,backgroundColor:i,body:a}},htmlBuilder:rs,mathmlBuilder:ro}),e$({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(e,t,r){let{parser:n,funcName:l}=e,i=tS(t[0],"color-token").color,a=tS(t[1],"color-token").color,s=t[2];return{type:"enclose",mode:n.mode,label:l,backgroundColor:a,borderColor:i,body:s}},htmlBuilder:rs,mathmlBuilder:ro}),e$({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(e,t){let{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),e$({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(e,t){let{parser:r,funcName:n}=e,l=t[0];return{type:"enclose",mode:r.mode,label:n,body:l}},htmlBuilder:rs,mathmlBuilder:ro}),e$({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(e,t){let{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});let rh={};function rm(e){let{type:t,names:r,props:n,handler:l,htmlBuilder:i,mathmlBuilder:a}=e,s={type:t,numArgs:n.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:l};for(let e=0;e<r.length;++e)rh[r[e]]=s;i&&(eW[t]=i),a&&(ej[t]=a)}let rc={};class rp{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?e&&e.loc&&t.loc&&e.loc.lexer===t.loc.lexer?new rp(e.loc.lexer,e.loc.start,t.loc.end):null:e&&e.loc}}class ru{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new ru(t,rp.range(this,e))}}function rd(e){let t=[];e.consumeSpaces();let r=e.fetch().text;for("\\relax"===r&&(e.consume(),e.consumeSpaces(),r=e.fetch().text);"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.fetch().text;return t}let rg=e=>{if(!e.parser.settings.displayMode)throw new l("{"+e.envName+"} can be used only in display mode.")};function rf(e){if(-1===e.indexOf("ed"))return -1===e.indexOf("*")}function rb(e,t,r){let{hskipBeforeAndAfter:n,addJot:i,cols:a,arraystretch:s,colSeparationType:o,autoTag:h,singleRow:m,emptySingleRow:c,maxNumCols:p,leqno:u}=t;if(e.gullet.beginGroup(),m||e.gullet.macros.set("\\cr","\\\\\\relax"),!s){let t=e.gullet.expandMacroAsText("\\arraystretch");if(null==t)s=1;else if(!(s=parseFloat(t))||s<0)throw new l("Invalid \\arraystretch: "+t)}e.gullet.beginGroup();let d=[],g=[d],f=[],b=[],y=null!=h?[]:void 0;function x(){h&&e.gullet.macros.set("\\@eqnsw","1",!0)}function w(){y&&(e.gullet.macros.get("\\df@tag")?(y.push(e.subparse([new ru("\\df@tag")])),e.gullet.macros.set("\\df@tag",void 0,!0)):y.push(!!h&&"1"===e.gullet.macros.get("\\@eqnsw")))}for(x(),b.push(rd(e));;){let t=e.parseExpression(!1,m?"\\end":"\\\\");e.gullet.endGroup(),e.gullet.beginGroup(),t={type:"ordgroup",mode:e.mode,body:t},r&&(t={type:"styling",mode:e.mode,style:r,body:[t]}),d.push(t);let n=e.fetch().text;if("&"===n){if(p&&d.length===p){if(m||o)throw new l("Too many tab characters: &",e.nextToken);e.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}e.consume()}else if("\\end"===n){w(),1===d.length&&"styling"===t.type&&0===t.body[0].body.length&&(g.length>1||!c)&&g.pop(),b.length<g.length+1&&b.push([]);break}else if("\\\\"===n){let t;e.consume()," "!==e.gullet.future().text&&(t=e.parseSizeGroup(!0)),f.push(t?t.value:null),w(),b.push(rd(e)),d=[],g.push(d),x()}else throw new l("Expected & or \\\\ or \\cr or \\end",e.nextToken)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:i,arraystretch:s,body:g,cols:a,rowGaps:f,hskipBeforeAndAfter:n,hLinesBeforeRow:b,colSeparationType:o,tags:y,leqno:u}}function ry(e){return"d"===e.slice(0,1)?"display":"text"}let rx=function(e,t){let r,n,i,a;let s=e.body.length,o=e.hLinesBeforeRow,h=0,c=Array(s),p=[],u=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),d=1/t.fontMetrics().ptPerEm,g=5*d;e.colSeparationType&&"small"===e.colSeparationType&&(g=.2778*(t.havingStyle(v.SCRIPT).sizeMultiplier/t.sizeMultiplier));let f="CD"===e.colSeparationType?G({number:3,unit:"ex"},t):12*d,b=3*d,y=e.arraystretch*f,x=.7*y,w=.3*y,k=0;function S(e){for(let t=0;t<e.length;++t)t>0&&(k+=.25),p.push({pos:k,isDashed:e[t]})}for(S(o[0]),r=0;r<e.body.length;++r){let l=e.body[r],i=x,a=w;h<l.length&&(h=l.length);let s=Array(l.length);for(n=0;n<l.length;++n){let e=te(l[n],t);a<e.depth&&(a=e.depth),i<e.height&&(i=e.height),s[n]=e}let m=e.rowGaps[r],p=0;m&&(p=G(m,t))>0&&(a<(p+=w)&&(a=p),p=0),e.addJot&&(a+=b),s.height=i,s.depth=a,s.pos=k+=i,k+=a+p,c[r]=s,S(o[r+1])}let M=k/2+t.fontMetrics().axisHeight,z=e.cols||[],A=[],T=[];if(e.tags&&e.tags.some(e=>e))for(r=0;r<s;++r){let n;let l=c[r],i=l.pos-M,a=e.tags[r];(n=!0===a?eP.makeSpan(["eqn-num"],[],t):!1===a?eP.makeSpan([],[],t):eP.makeSpan([],e6(a,t,!0),t)).depth=l.depth,n.height=l.height,T.push({type:"elem",elem:n,shift:i})}for(n=0,a=0;n<h||a<z.length;++n,++a){let o,p=z[a]||{},d=!0;for(;"separator"===p.type;){if(d||((i=eP.makeSpan(["arraycolsep"],[])).style.width=U(t.fontMetrics().doubleRuleSep),A.push(i)),"|"===p.separator||":"===p.separator){let e="|"===p.separator?"solid":"dashed",r=eP.makeSpan(["vertical-separator"],[],t);r.style.height=U(k),r.style.borderRightWidth=U(u),r.style.borderRightStyle=e,r.style.margin="0 "+U(-u/2);let n=k-M;n&&(r.style.verticalAlign=U(-n)),A.push(r)}else throw new l("Invalid separator type: "+p.separator);p=z[++a]||{},d=!1}if(n>=h)continue;(n>0||e.hskipBeforeAndAfter)&&0!==(o=m.deflt(p.pregap,g))&&((i=eP.makeSpan(["arraycolsep"],[])).style.width=U(o),A.push(i));let f=[];for(r=0;r<s;++r){let e=c[r],t=e[n];if(!t)continue;let l=e.pos-M;t.depth=e.depth,t.height=e.height,f.push({type:"elem",elem:t,shift:l})}f=eP.makeVList({positionType:"individualShift",children:f},t),f=eP.makeSpan(["col-align-"+(p.align||"c")],[f]),A.push(f),(n<h-1||e.hskipBeforeAndAfter)&&0!==(o=m.deflt(p.postgap,g))&&((i=eP.makeSpan(["arraycolsep"],[])).style.width=U(o),A.push(i))}if(c=eP.makeSpan(["mtable"],A),p.length>0){let e=eP.makeLineSpan("hline",t,u),r=eP.makeLineSpan("hdashline",t,u),n=[{type:"elem",elem:c,shift:0}];for(;p.length>0;){let t=p.pop(),l=t.pos-M;t.isDashed?n.push({type:"elem",elem:r,shift:l}):n.push({type:"elem",elem:e,shift:l})}c=eP.makeVList({positionType:"individualShift",children:n},t)}if(0===T.length)return eP.makeSpan(["mord"],[c],t);{let e=eP.makeVList({positionType:"individualShift",children:T},t);return e=eP.makeSpan(["tag"],[e],t),eP.makeFragment([c,e])}},rw={c:"center ",l:"left ",r:"right "},rv=function(e,t){let r=[],n=new ts.MathNode("mtd",[],["mtr-glue"]),l=new ts.MathNode("mtd",[],["mml-eqn-num"]);for(let i=0;i<e.body.length;i++){let a=e.body[i],s=[];for(let e=0;e<a.length;e++)s.push(new ts.MathNode("mtd",[td(a[e],t)]));e.tags&&e.tags[i]&&(s.unshift(n),s.push(n),e.leqno?s.unshift(l):s.push(l)),r.push(new ts.MathNode("mtr",s))}let i=new ts.MathNode("mtable",r),a=.5===e.arraystretch?.1:.16+e.arraystretch-1+.09*!!e.addJot;i.setAttribute("rowspacing",U(a));let s="",o="";if(e.cols&&e.cols.length>0){let t=e.cols,r="",n=!1,l=0,a=t.length;"separator"===t[0].type&&(s+="top ",l=1),"separator"===t[t.length-1].type&&(s+="bottom ",a-=1);for(let e=l;e<a;e++)"align"===t[e].type?(o+=rw[t[e].align],n&&(r+="none "),n=!0):"separator"===t[e].type&&n&&(r+="|"===t[e].separator?"solid ":"dashed ",n=!1);i.setAttribute("columnalign",o.trim()),/[sd]/.test(r)&&i.setAttribute("columnlines",r.trim())}if("align"===e.colSeparationType){let t=e.cols||[],r="";for(let e=1;e<t.length;e++)r+=e%2?"0em ":"1em ";i.setAttribute("columnspacing",r.trim())}else"alignat"===e.colSeparationType||"gather"===e.colSeparationType?i.setAttribute("columnspacing","0em"):"small"===e.colSeparationType?i.setAttribute("columnspacing","0.2778em"):"CD"===e.colSeparationType?i.setAttribute("columnspacing","0.5em"):i.setAttribute("columnspacing","1em");let h="",m=e.hLinesBeforeRow;s+=(m[0].length>0?"left ":"")+(m[m.length-1].length>0?"right ":"");for(let e=1;e<m.length-1;e++)h+=0===m[e].length?"none ":m[e][0]?"dashed ":"solid ";return/[sd]/.test(h)&&i.setAttribute("rowlines",h.trim()),""!==s&&(i=new ts.MathNode("menclose",[i])).setAttribute("notation",s.trim()),e.arraystretch&&e.arraystretch<1&&(i=new ts.MathNode("mstyle",[i])).setAttribute("scriptlevel","1"),i},rk=function(e,t){let r;-1===e.envName.indexOf("ed")&&rg(e);let n=[],i=e.envName.indexOf("at")>-1?"alignat":"align",a="split"===e.envName,s=rb(e.parser,{cols:n,addJot:!0,autoTag:a?void 0:rf(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:a?2:void 0,leqno:e.parser.settings.leqno},"display"),o=0,h={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&"ordgroup"===t[0].type){let e="";for(let r=0;r<t[0].body.length;r++)e+=tS(t[0].body[r],"textord").text;o=2*(r=Number(e))}let m=!o;s.body.forEach(function(e){for(let t=1;t<e.length;t+=2){let r=tS(e[t],"styling");tS(r.body[0],"ordgroup").body.unshift(h)}if(m)o<e.length&&(o=e.length);else{let t=e.length/2;if(r<t)throw new l("Too many math in a row: expected "+r+", but got "+t,e[0])}});for(let e=0;e<o;++e){let t="r",r=0;e%2==1?t="l":e>0&&m&&(r=1),n[e]={type:"align",align:t,pregap:r,postgap:0}}return s.colSeparationType=m?"align":"alignat",s};rm({type:"array",names:["array","darray"],props:{numArgs:1},handler(e,t){let r=(tz(t[0])?[t[0]]:tS(t[0],"ordgroup").body).map(function(e){let t=tM(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new l("Unknown column alignment: "+t,e)}),n={cols:r,hskipBeforeAndAfter:!0,maxNumCols:r.length};return rb(e.parser,n,ry(e.envName))},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(e){let t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],r="c",n={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if("*"===e.envName.charAt(e.envName.length-1)){let t=e.parser;if(t.consumeSpaces(),"["===t.fetch().text){if(t.consume(),t.consumeSpaces(),r=t.fetch().text,-1==="lcr".indexOf(r))throw new l("Expected l or c or r",t.nextToken);t.consume(),t.consumeSpaces(),t.expect("]"),t.consume(),n.cols=[{type:"align",align:r}]}}let i=rb(e.parser,n,ry(e.envName)),a=Math.max(0,...i.body.map(e=>e.length));return i.cols=Array(a).fill({type:"align",align:r}),t?{type:"leftright",mode:e.mode,body:[i],left:t[0],right:t[1],rightColor:void 0}:i},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(e){let t=rb(e.parser,{arraystretch:.5},"script");return t.colSeparationType="small",t},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["subarray"],props:{numArgs:1},handler(e,t){let r=(tz(t[0])?[t[0]]:tS(t[0],"ordgroup").body).map(function(e){let t=tM(e).text;if(-1!=="lc".indexOf(t))return{type:"align",align:t};throw new l("Unknown column alignment: "+t,e)});if(r.length>1)throw new l("{subarray} can contain only one column");let n={cols:r,hskipBeforeAndAfter:!1,arraystretch:.5};if((n=rb(e.parser,n,"script")).body.length>0&&n.body[0].length>1)throw new l("{subarray} can contain only one column");return n},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(e){let t=rb(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},ry(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:rk,htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(e){m.contains(["gather","gather*"],e.envName)&&rg(e);let t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:rf(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return rb(e.parser,t,"display")},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:rk,htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(e){rg(e);let t={autoTag:rf(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return rb(e.parser,t,"display")},htmlBuilder:rx,mathmlBuilder:rv}),rm({type:"array",names:["CD"],props:{numArgs:0},handler:e=>(rg(e),function(e){let t=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){t.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();let r=e.fetch().text;if("&"===r||"\\\\"===r)e.consume();else if("\\end"===r){0===t[t.length-1].length&&t.pop();break}else throw new l("Expected \\\\ or \\cr or \\end",e.nextToken)}let r=[],n=[r];for(let i=0;i<t.length;i++){let a=t[i],s=tO();for(let t=0;t<a.length;t++)if(tE(a[t])){r.push(s);let n=tM(a[t+=1]).text,i=[,,];if(i[0]={type:"ordgroup",mode:"math",body:[]},i[1]={type:"ordgroup",mode:"math",body:[]},"=|.".indexOf(n)>-1);else if("<>AV".indexOf(n)>-1)for(let e=0;e<2;e++){let r=!0;for(let s=t+1;s<a.length;s++){if(tL(a[s],n)){r=!1,t=s;break}if(tE(a[s]))throw new l("Missing a "+n+" character to complete a CD arrow.",a[s]);i[e].body.push(a[s])}if(r)throw new l("Missing a "+n+" character to complete a CD arrow.",a[t])}else throw new l('Expected one of "<>AV=|." after @',a[t]);let o={type:"styling",body:[function(e,t,r){let n=tR[e];switch(n){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(n,[t[0]],[t[1]]);case"\\uparrow":case"\\downarrow":{let e=r.callFunction("\\\\cdleft",[t[0]],[]),l=r.callFunction("\\Big",[{type:"atom",text:n,mode:"math",family:"rel"}],[]),i=r.callFunction("\\\\cdright",[t[1]],[]);return r.callFunction("\\\\cdparent",[{type:"ordgroup",mode:"math",body:[e,l,i]}],[])}case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}(n,i,e)],mode:"math",style:"display"};r.push(o),s=tO()}else s.body.push(a[t]);i%2==0?r.push(s):r.shift(),r=[],n.push(r)}e.gullet.endGroup(),e.gullet.endGroup();let i=Array(n[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25});return{type:"array",mode:"math",body:n,arraystretch:1,addJot:!0,rowGaps:[null],cols:i,colSeparationType:"CD",hLinesBeforeRow:Array(n.length+1).fill([])}}(e.parser)),htmlBuilder:rx,mathmlBuilder:rv}),rc["\\nonumber"]="\\gdef\\@eqnsw{0}",rc["\\notag"]="\\nonumber",e$({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(e,t){throw new l(e.funcName+" valid only within array environment")}}),e$({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(e,t){let{parser:r,funcName:n}=e,i=t[0];if("ordgroup"!==i.type)throw new l("Invalid environment name",i);let a="";for(let e=0;e<i.body.length;++e)a+=tS(i.body[e],"textord").text;if("\\begin"===n){if(!rh.hasOwnProperty(a))throw new l("No such environment: "+a,i);let e=rh[a],{args:t,optArgs:n}=r.parseArguments("\\begin{"+a+"}",e),s={mode:r.mode,envName:a,parser:r},o=e.handler(s,t,n);r.expect("\\end",!1);let h=r.nextToken,m=tS(r.parseFunction(),"environment");if(m.name!==a)throw new l("Mismatch: \\begin{"+a+"} matched by \\end{"+m.name+"}",h);return o}return{type:"environment",mode:r.mode,name:a,nameGroup:i}}});let rS=(e,t)=>{let r=e.font,n=t.withFont(r);return te(e.body,n)},rM=(e,t)=>{let r=e.font,n=t.withFont(r);return td(e.body,n)},rz={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};e$({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=eK(t[0]),i=n;return i in rz&&(i=rz[i]),{type:"font",mode:r.mode,font:i.slice(1),body:l}},htmlBuilder:rS,mathmlBuilder:rM}),e$({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(e,t)=>{let{parser:r}=e,n=t[0],l=m.isCharacterBox(n);return{type:"mclass",mode:r.mode,mclass:tH(n),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:n}],isCharacterBox:l}}}),e$({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{let{parser:r,funcName:n,breakOnTokenText:l}=e,{mode:i}=r,a=r.parseExpression(!0,l);return{type:"font",mode:i,font:"math"+n.slice(1),body:{type:"ordgroup",mode:r.mode,body:a}}},htmlBuilder:rS,mathmlBuilder:rM});let rA=(e,t)=>{let r=t;return"display"===e?r=r.id>=v.SCRIPT.id?r.text():v.DISPLAY:"text"===e&&r.size===v.DISPLAY.size?r=v.TEXT:"script"===e?r=v.SCRIPT:"scriptscript"===e&&(r=v.SCRIPTSCRIPT),r},rT=(e,t)=>{let r,n,l,i,a,s,o,h,m,c,p;let u=rA(e.size,t.style),d=u.fracNum(),g=u.fracDen();r=t.havingStyle(d);let f=te(e.numer,r,t);if(e.continued){let e=8.5/t.fontMetrics().ptPerEm,r=3.5/t.fontMetrics().ptPerEm;f.height=f.height<e?e:f.height,f.depth=f.depth<r?r:f.depth}r=t.havingStyle(g);let b=te(e.denom,r,t);if(e.hasBarLine?(e.barSize?(l=G(e.barSize,t),n=eP.makeLineSpan("frac-line",t,l)):n=eP.makeLineSpan("frac-line",t),l=n.height,i=n.height):(n=null,l=0,i=t.fontMetrics().defaultRuleThickness),u.size===v.DISPLAY.size||"display"===e.size?(a=t.fontMetrics().num1,s=l>0?3*i:7*i,o=t.fontMetrics().denom1):(l>0?(a=t.fontMetrics().num2,s=i):(a=t.fontMetrics().num3,s=3*i),o=t.fontMetrics().denom2),n){let e=t.fontMetrics().axisHeight;a-f.depth-(e+.5*l)<s&&(a+=s-(a-f.depth-(e+.5*l))),e-.5*l-(b.height-o)<s&&(o+=s-(e-.5*l-(b.height-o)));let r=-(e-.5*l);h=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:b,shift:o},{type:"elem",elem:n,shift:r},{type:"elem",elem:f,shift:-a}]},t)}else{let e=a-f.depth-(b.height-o);e<s&&(a+=.5*(s-e),o+=.5*(s-e)),h=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:b,shift:o},{type:"elem",elem:f,shift:-a}]},t)}return r=t.havingStyle(u),h.height*=r.sizeMultiplier/t.sizeMultiplier,h.depth*=r.sizeMultiplier/t.sizeMultiplier,m=u.size===v.DISPLAY.size?t.fontMetrics().delim1:u.size===v.SCRIPTSCRIPT.size?t.havingStyle(v.SCRIPT).fontMetrics().delim2:t.fontMetrics().delim2,c=null==e.leftDelim?e9(t,["mopen"]):rr.customSizedDelim(e.leftDelim,m,!0,t.havingStyle(u),e.mode,["mopen"]),p=e.continued?eP.makeSpan([]):null==e.rightDelim?e9(t,["mclose"]):rr.customSizedDelim(e.rightDelim,m,!0,t.havingStyle(u),e.mode,["mclose"]),eP.makeSpan(["mord"].concat(r.sizingClasses(t)),[c,eP.makeSpan(["mfrac"],[h]),p],t)},rB=(e,t)=>{let r=new ts.MathNode("mfrac",[td(e.numer,t),td(e.denom,t)]);if(e.hasBarLine){if(e.barSize){let n=G(e.barSize,t);r.setAttribute("linethickness",U(n))}}else r.setAttribute("linethickness","0px");let n=rA(e.size,t.style);if(n.size!==t.style.size){r=new ts.MathNode("mstyle",[r]);let e=n.size===v.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",e),r.setAttribute("scriptlevel","0")}if(null!=e.leftDelim||null!=e.rightDelim){let t=[];if(null!=e.leftDelim){let r=new ts.MathNode("mo",[new ts.TextNode(e.leftDelim.replace("\\",""))]);r.setAttribute("fence","true"),t.push(r)}if(t.push(r),null!=e.rightDelim){let r=new ts.MathNode("mo",[new ts.TextNode(e.rightDelim.replace("\\",""))]);r.setAttribute("fence","true"),t.push(r)}return th(t)}return r};e$({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(e,t)=>{let r,{parser:n,funcName:l}=e,i=t[0],a=t[1],s=null,o=null,h="auto";switch(l){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,s="(",o=")";break;case"\\\\bracefrac":r=!1,s="\\{",o="\\}";break;case"\\\\brackfrac":r=!1,s="[",o="]";break;default:throw Error("Unrecognized genfrac command")}switch(l){case"\\dfrac":case"\\dbinom":h="display";break;case"\\tfrac":case"\\tbinom":h="text"}return{type:"genfrac",mode:n.mode,continued:!1,numer:i,denom:a,hasBarLine:r,leftDelim:s,rightDelim:o,size:h,barSize:null}},htmlBuilder:rT,mathmlBuilder:rB}),e$({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=t[0],i=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:l,denom:i,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),e$({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(e){let t,{parser:r,funcName:n,token:l}=e;switch(n){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:l}}});let rC=["display","text","script","scriptscript"],rq=function(e){let t=null;return e.length>0&&(t="."===(t=e)?null:t),t};e$({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(e,t){let r,{parser:n}=e,l=t[4],i=t[5],a=eK(t[0]),s="atom"===a.type&&"open"===a.family?rq(a.text):null,o=eK(t[1]),h="atom"===o.type&&"close"===o.family?rq(o.text):null,m=tS(t[2],"size"),c=null;r=!!m.isBlank||(c=m.value).number>0;let p="auto",u=t[3];return"ordgroup"===u.type?u.body.length>0&&(p=rC[Number(tS(u.body[0],"textord").text)]):p=rC[Number((u=tS(u,"textord")).text)],{type:"genfrac",mode:n.mode,numer:l,denom:i,continued:!1,hasBarLine:r,barSize:c,leftDelim:s,rightDelim:h,size:p}},htmlBuilder:rT,mathmlBuilder:rB}),e$({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(e,t){let{parser:r,funcName:n,token:l}=e;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:tS(t[0],"size").value,token:l}}}),e$({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=t[0],i=h(tS(t[1],"infix").size),a=t[2],s=i.number>0;return{type:"genfrac",mode:r.mode,numer:l,denom:a,continued:!1,hasBarLine:s,barSize:i,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:rT,mathmlBuilder:rB});let rN=(e,t)=>{let r,n,l;let i=t.style;"supsub"===e.type?(r=e.sup?te(e.sup,t.havingStyle(i.sup()),t):te(e.sub,t.havingStyle(i.sub()),t),n=tS(e.base,"horizBrace")):n=tS(e,"horizBrace");let a=te(n.base,t.havingBaseStyle(v.DISPLAY)),s=tk.svgSpan(n,t);if(n.isOver?(l=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:a},{type:"kern",size:.1},{type:"elem",elem:s}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(l=eP.makeVList({positionType:"bottom",positionData:a.depth+.1+s.height,children:[{type:"elem",elem:s},{type:"kern",size:.1},{type:"elem",elem:a}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){let e=eP.makeSpan(["mord",n.isOver?"mover":"munder"],[l],t);l=n.isOver?eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:e},{type:"kern",size:.2},{type:"elem",elem:r}]},t):eP.makeVList({positionType:"bottom",positionData:e.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:e}]},t)}return eP.makeSpan(["mord",n.isOver?"mover":"munder"],[l],t)};e$({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(e,t){let{parser:r,funcName:n}=e;return{type:"horizBrace",mode:r.mode,label:n,isOver:/^\\over/.test(n),base:t[0]}},htmlBuilder:rN,mathmlBuilder:(e,t)=>{let r=tk.mathMLnode(e.label);return new ts.MathNode(e.isOver?"mover":"munder",[td(e.base,t),r])}}),e$({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,n=t[1],l=tS(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:l})?{type:"href",mode:r.mode,href:l,body:eJ(n)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(e,t)=>{let r=e6(e.body,t,!1);return eP.makeAnchor(e.href,[],r,t)},mathmlBuilder:(e,t)=>{let r=tu(e.body,t);return r instanceof tl||(r=new tl("mrow",[r])),r.setAttribute("href",e.href),r}}),e$({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,n=tS(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:n}))return r.formatUnsupportedCmd("\\url");let l=[];for(let e=0;e<n.length;e++){let t=n[e];"~"===t&&(t="\\textasciitilde"),l.push({type:"textord",mode:"text",text:t})}let i={type:"text",mode:r.mode,font:"\\texttt",body:l};return{type:"href",mode:r.mode,href:n,body:eJ(i)}}}),e$({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(e,t){let{parser:r}=e;return{type:"hbox",mode:r.mode,body:eJ(t[0])}},htmlBuilder(e,t){let r=e6(e.body,t,!1);return eP.makeFragment(r)},mathmlBuilder:(e,t)=>new ts.MathNode("mrow",tp(e.body,t))}),e$({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(e,t)=>{let r,{parser:n,funcName:i,token:a}=e,s=tS(t[0],"raw").string,o=t[1];n.settings.strict&&n.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");let h={};switch(i){case"\\htmlClass":h.class=s,r={command:"\\htmlClass",class:s};break;case"\\htmlId":h.id=s,r={command:"\\htmlId",id:s};break;case"\\htmlStyle":h.style=s,r={command:"\\htmlStyle",style:s};break;case"\\htmlData":{let e=s.split(",");for(let t=0;t<e.length;t++){let r=e[t].split("=");if(2!==r.length)throw new l("Error parsing key-value for \\htmlData");h["data-"+r[0].trim()]=r[1].trim()}r={command:"\\htmlData",attributes:h};break}default:throw Error("Unrecognized html command")}return n.settings.isTrusted(r)?{type:"html",mode:n.mode,attributes:h,body:eJ(o)}:n.formatUnsupportedCmd(i)},htmlBuilder:(e,t)=>{let r=e6(e.body,t,!1),n=["enclosing"];e.attributes.class&&n.push(...e.attributes.class.trim().split(/\s+/));let l=eP.makeSpan(n,r,t);for(let t in e.attributes)"class"!==t&&e.attributes.hasOwnProperty(t)&&l.setAttribute(t,e.attributes[t]);return l},mathmlBuilder:(e,t)=>tu(e.body,t)}),e$({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e;return{type:"htmlmathml",mode:r.mode,html:eJ(t[0]),mathml:eJ(t[1])}},htmlBuilder:(e,t)=>{let r=e6(e.html,t,!1);return eP.makeFragment(r)},mathmlBuilder:(e,t)=>tu(e.mathml,t)});let rI=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};{let t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new l("Invalid size: '"+e+"' in \\includegraphics");let r={number:+(t[1]+t[2]),unit:t[3]};if(!F(r))throw new l("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r}};e$({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(e,t,r)=>{let{parser:n}=e,i={number:0,unit:"em"},a={number:.9,unit:"em"},s={number:0,unit:"em"},o="";if(r[0]){let e=tS(r[0],"raw").string.split(",");for(let t=0;t<e.length;t++){let r=e[t].split("=");if(2===r.length){let e=r[1].trim();switch(r[0].trim()){case"alt":o=e;break;case"width":i=rI(e);break;case"height":a=rI(e);break;case"totalheight":s=rI(e);break;default:throw new l("Invalid key: '"+r[0]+"' in \\includegraphics.")}}}}let h=tS(t[0],"url").url;return(""===o&&(o=(o=(o=h).replace(/^.*[\\/]/,"")).substring(0,o.lastIndexOf("."))),n.settings.isTrusted({command:"\\includegraphics",url:h}))?{type:"includegraphics",mode:n.mode,alt:o,width:i,height:a,totalheight:s,src:h}:n.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(e,t)=>{let r=G(e.height,t),n=0;e.totalheight.number>0&&(n=G(e.totalheight,t)-r);let l=0;e.width.number>0&&(l=G(e.width,t));let i={height:U(r+n)};l>0&&(i.width=U(l)),n>0&&(i.verticalAlign=U(-n));let a=new K(e.src,e.alt,i);return a.height=r,a.depth=n,a},mathmlBuilder:(e,t)=>{let r=new ts.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);let n=G(e.height,t),l=0;if(e.totalheight.number>0&&(l=G(e.totalheight,t)-n,r.setAttribute("valign",U(-l))),r.setAttribute("height",U(n+l)),e.width.number>0){let n=G(e.width,t);r.setAttribute("width",U(n))}return r.setAttribute("src",e.src),r}}),e$({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(e,t){let{parser:r,funcName:n}=e,l=tS(t[0],"size");if(r.settings.strict){let e="m"===n[1],t="mu"===l.value.unit;e?(t||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" supports only mu units, not "+l.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" works only in math mode")):t&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:l.value}},htmlBuilder:(e,t)=>eP.makeGlue(e.dimension,t),mathmlBuilder(e,t){let r=G(e.dimension,t);return new ts.SpaceNode(r)}}),e$({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=t[0];return{type:"lap",mode:r.mode,alignment:n.slice(5),body:l}},htmlBuilder:(e,t)=>{let r;"clap"===e.alignment?(r=eP.makeSpan([],[te(e.body,t)]),r=eP.makeSpan(["inner"],[r],t)):r=eP.makeSpan(["inner"],[te(e.body,t)]);let n=eP.makeSpan(["fix"],[]),l=eP.makeSpan([e.alignment],[r,n],t),i=eP.makeSpan(["strut"]);return i.style.height=U(l.height+l.depth),l.depth&&(i.style.verticalAlign=U(-l.depth)),l.children.unshift(i),l=eP.makeSpan(["thinbox"],[l],t),eP.makeSpan(["mord","vbox"],[l],t)},mathmlBuilder:(e,t)=>{let r=new ts.MathNode("mpadded",[td(e.body,t)]);if("rlap"!==e.alignment){let t="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",t+"width")}return r.setAttribute("width","0px"),r}}),e$({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){let{funcName:r,parser:n}=e,l=n.mode;n.switchMode("math");let i="\\("===r?"\\)":"$",a=n.parseExpression(!1,i);return n.expect(i),n.switchMode(l),{type:"styling",mode:n.mode,style:"text",body:a}}}),e$({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){throw new l("Mismatched "+e.funcName)}});let rH=(e,t)=>{switch(t.style.size){case v.DISPLAY.size:return e.display;case v.TEXT.size:return e.text;case v.SCRIPT.size:return e.script;case v.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};e$({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(e,t)=>{let{parser:r}=e;return{type:"mathchoice",mode:r.mode,display:eJ(t[0]),text:eJ(t[1]),script:eJ(t[2]),scriptscript:eJ(t[3])}},htmlBuilder:(e,t)=>{let r=e6(rH(e,t),t,!1);return eP.makeFragment(r)},mathmlBuilder:(e,t)=>tu(rH(e,t),t)});let rR=(e,t,r,n,l,i,a)=>{let s,o,h;e=eP.makeSpan([],[e]);let c=r&&m.isCharacterBox(r);if(t){let e=te(t,n.havingStyle(l.sup()),n);o={elem:e,kern:Math.max(n.fontMetrics().bigOpSpacing1,n.fontMetrics().bigOpSpacing3-e.depth)}}if(r){let e=te(r,n.havingStyle(l.sub()),n);s={elem:e,kern:Math.max(n.fontMetrics().bigOpSpacing2,n.fontMetrics().bigOpSpacing4-e.height)}}if(o&&s){let t=n.fontMetrics().bigOpSpacing5+s.elem.height+s.elem.depth+s.kern+e.depth+a;h=eP.makeVList({positionType:"bottom",positionData:t,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:s.elem,marginLeft:U(-i)},{type:"kern",size:s.kern},{type:"elem",elem:e},{type:"kern",size:o.kern},{type:"elem",elem:o.elem,marginLeft:U(i)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}else if(s){let t=e.height-a;h=eP.makeVList({positionType:"top",positionData:t,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:s.elem,marginLeft:U(-i)},{type:"kern",size:s.kern},{type:"elem",elem:e}]},n)}else{if(!o)return e;let t=e.depth+a;h=eP.makeVList({positionType:"bottom",positionData:t,children:[{type:"elem",elem:e},{type:"kern",size:o.kern},{type:"elem",elem:o.elem,marginLeft:U(i)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}let p=[h];if(s&&0!==i&&!c){let e=eP.makeSpan(["mspace"],[],n);e.style.marginRight=U(i),p.unshift(e)}return eP.makeSpan(["mop","op-limits"],p,n)},rO=["\\smallint"],rE=(e,t)=>{let r,n,l,i;let a=!1;"supsub"===e.type?(r=e.sup,n=e.sub,l=tS(e.base,"op"),a=!0):l=tS(e,"op");let s=t.style,o=!1;if(s.size===v.DISPLAY.size&&l.symbol&&!m.contains(rO,l.name)&&(o=!0),l.symbol){let e=o?"Size2-Regular":"Size1-Regular",r="";if(("\\oiint"===l.name||"\\oiiint"===l.name)&&(r=l.name.slice(1),l.name="oiint"===r?"\\iint":"\\iiint"),i=eP.makeSymbol(l.name,e,"math",t,["mop","op-symbol",o?"large-op":"small-op"]),r.length>0){let e=i.italic,n=eP.staticSvg(r+"Size"+(o?"2":"1"),t);i=eP.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:0},{type:"elem",elem:n,shift:.08*!!o}]},t),l.name="\\"+r,i.classes.unshift("mop"),i.italic=e}}else if(l.body){let e=e6(l.body,t,!0);1===e.length&&e[0]instanceof Q?(i=e[0]).classes[0]="mop":i=eP.makeSpan(["mop"],e,t)}else{let e=[];for(let r=1;r<l.name.length;r++)e.push(eP.mathsym(l.name[r],l.mode,t));i=eP.makeSpan(["mop"],e,t)}let h=0,c=0;return((i instanceof Q||"\\oiint"===l.name||"\\oiiint"===l.name)&&!l.suppressBaseShift&&(h=(i.height-i.depth)/2-t.fontMetrics().axisHeight,c=i.italic),a)?rR(i,r,n,t,s,c,h):(h&&(i.style.position="relative",i.style.top=U(h)),i)},rL=(e,t)=>{let r;if(e.symbol)r=new tl("mo",[to(e.name,e.mode)]),m.contains(rO,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new tl("mo",tp(e.body,t));else{r=new tl("mi",[new ti(e.name.slice(1))]);let t=new tl("mo",[to("⁡","text")]);r=e.parentIsSupSub?new tl("mrow",[r,t]):tn([r,t])}return r},rD={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};e$({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=n;return 1===l.length&&(l=rD[l]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:l}},htmlBuilder:rE,mathmlBuilder:rL}),e$({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{let{parser:r}=e,n=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:eJ(n)}},htmlBuilder:rE,mathmlBuilder:rL});let rV={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};e$({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(e){let{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:rE,mathmlBuilder:rL}),e$({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(e){let{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:rE,mathmlBuilder:rL}),e$({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(e){let{parser:t,funcName:r}=e,n=r;return 1===n.length&&(n=rV[n]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:rE,mathmlBuilder:rL});let rP=(e,t)=>{let r,n,l,i;let a=!1;if("supsub"===e.type?(r=e.sup,n=e.sub,l=tS(e.base,"operatorname"),a=!0):l=tS(e,"operatorname"),l.body.length>0){let e=e6(l.body.map(e=>{let t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e}),t.withFont("mathrm"),!0);for(let t=0;t<e.length;t++){let r=e[t];r instanceof Q&&(r.text=r.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}i=eP.makeSpan(["mop"],e,t)}else i=eP.makeSpan(["mop"],[],t);return a?rR(i,r,n,t,t.style,0,0):i};function rF(e,t,r){let n=e6(e,t,!1),l=t.sizeMultiplier/r.sizeMultiplier;for(let e=0;e<n.length;e++){let i=n[e].classes.indexOf("sizing");i<0?Array.prototype.push.apply(n[e].classes,t.sizingClasses(r)):n[e].classes[i+1]==="reset-size"+t.size&&(n[e].classes[i+1]="reset-size"+r.size),n[e].height*=l,n[e].depth*=l}return eP.makeFragment(n)}e$({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(e,t)=>{let{parser:r,funcName:n}=e,l=t[0];return{type:"operatorname",mode:r.mode,body:eJ(l),alwaysHandleSupSub:"\\operatornamewithlimits"===n,limits:!1,parentIsSupSub:!1}},htmlBuilder:rP,mathmlBuilder:(e,t)=>{let r=tp(e.body,t.withFont("mathrm")),n=!0;for(let e=0;e<r.length;e++){let t=r[e];if(t instanceof ts.SpaceNode);else if(t instanceof ts.MathNode)switch(t.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":{let e=t.children[0];1===t.children.length&&e instanceof ts.TextNode?e.text=e.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):n=!1;break}default:n=!1}else n=!1}if(n){let e=r.map(e=>e.toText()).join("");r=[new ts.TextNode(e)]}let l=new ts.MathNode("mi",r);l.setAttribute("mathvariant","normal");let i=new ts.MathNode("mo",[to("⁡","text")]);return e.parentIsSupSub?new ts.MathNode("mrow",[l,i]):ts.newDocumentFragment([l,i])}}),rc["\\operatorname"]="\\@ifstar\\operatornamewithlimits\\operatorname@",eZ({type:"ordgroup",htmlBuilder:(e,t)=>e.semisimple?eP.makeFragment(e6(e.body,t,!1)):eP.makeSpan(["mord"],e6(e.body,t,!0),t),mathmlBuilder:(e,t)=>tu(e.body,t,!0)}),e$({type:"overline",names:["\\overline"],props:{numArgs:1},handler(e,t){let{parser:r}=e,n=t[0];return{type:"overline",mode:r.mode,body:n}},htmlBuilder(e,t){let r=te(e.body,t.havingCrampedStyle()),n=eP.makeLineSpan("overline-line",t),l=t.fontMetrics().defaultRuleThickness,i=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*l},{type:"elem",elem:n},{type:"kern",size:l}]},t);return eP.makeSpan(["mord","overline"],[i],t)},mathmlBuilder(e,t){let r=new ts.MathNode("mo",[new ts.TextNode("‾")]);r.setAttribute("stretchy","true");let n=new ts.MathNode("mover",[td(e.body,t),r]);return n.setAttribute("accent","true"),n}}),e$({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,n=t[0];return{type:"phantom",mode:r.mode,body:eJ(n)}},htmlBuilder:(e,t)=>{let r=e6(e.body,t.withPhantom(),!1);return eP.makeFragment(r)},mathmlBuilder:(e,t)=>{let r=tp(e.body,t);return new ts.MathNode("mphantom",r)}}),e$({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,n=t[0];return{type:"hphantom",mode:r.mode,body:n}},htmlBuilder:(e,t)=>{let r=eP.makeSpan([],[te(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(let e=0;e<r.children.length;e++)r.children[e].height=0,r.children[e].depth=0;return r=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),eP.makeSpan(["mord"],[r],t)},mathmlBuilder:(e,t)=>{let r=tp(eJ(e.body),t),n=new ts.MathNode("mphantom",r),l=new ts.MathNode("mpadded",[n]);return l.setAttribute("height","0px"),l.setAttribute("depth","0px"),l}}),e$({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{let{parser:r}=e,n=t[0];return{type:"vphantom",mode:r.mode,body:n}},htmlBuilder:(e,t)=>{let r=eP.makeSpan(["inner"],[te(e.body,t.withPhantom())]),n=eP.makeSpan(["fix"],[]);return eP.makeSpan(["mord","rlap"],[r,n],t)},mathmlBuilder:(e,t)=>{let r=tp(eJ(e.body),t),n=new ts.MathNode("mphantom",r),l=new ts.MathNode("mpadded",[n]);return l.setAttribute("width","0px"),l}}),e$({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(e,t){let{parser:r}=e,n=tS(t[0],"size").value,l=t[1];return{type:"raisebox",mode:r.mode,dy:n,body:l}},htmlBuilder(e,t){let r=te(e.body,t),n=G(e.dy,t);return eP.makeVList({positionType:"shift",positionData:-n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){let r=new ts.MathNode("mpadded",[td(e.body,t)]),n=e.dy.number+e.dy.unit;return r.setAttribute("voffset",n),r}}),e$({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(e){let{parser:t}=e;return{type:"internal",mode:t.mode}}}),e$({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(e,t,r){let{parser:n}=e,l=r[0],i=tS(t[0],"size"),a=tS(t[1],"size");return{type:"rule",mode:n.mode,shift:l&&tS(l,"size").value,width:i.value,height:a.value}},htmlBuilder(e,t){let r=eP.makeSpan(["mord","rule"],[],t),n=G(e.width,t),l=G(e.height,t),i=e.shift?G(e.shift,t):0;return r.style.borderRightWidth=U(n),r.style.borderTopWidth=U(l),r.style.bottom=U(i),r.width=n,r.height=l+i,r.depth=-i,r.maxFontSize=1.125*l*t.sizeMultiplier,r},mathmlBuilder(e,t){let r=G(e.width,t),n=G(e.height,t),l=e.shift?G(e.shift,t):0,i=t.color&&t.getColor()||"black",a=new ts.MathNode("mspace");a.setAttribute("mathbackground",i),a.setAttribute("width",U(r)),a.setAttribute("height",U(n));let s=new ts.MathNode("mpadded",[a]);return l>=0?s.setAttribute("height",U(l)):(s.setAttribute("height",U(l)),s.setAttribute("depth",U(-l))),s.setAttribute("voffset",U(l)),s}});let rG=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];e$({type:"sizing",names:rG,props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{let{breakOnTokenText:r,funcName:n,parser:l}=e,i=l.parseExpression(!1,r);return{type:"sizing",mode:l.mode,size:rG.indexOf(n)+1,body:i}},htmlBuilder:(e,t)=>{let r=t.havingSize(e.size);return rF(e.body,r,t)},mathmlBuilder:(e,t)=>{let r=t.havingSize(e.size),n=tp(e.body,r),l=new ts.MathNode("mstyle",n);return l.setAttribute("mathsize",U(r.sizeMultiplier)),l}}),e$({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(e,t,r)=>{let{parser:n}=e,l=!1,i=!1,a=r[0]&&tS(r[0],"ordgroup");if(a){let e="";for(let t=0;t<a.body.length;++t)if("t"===(e=a.body[t].text))l=!0;else if("b"===e)i=!0;else{l=!1,i=!1;break}}else l=!0,i=!0;let s=t[0];return{type:"smash",mode:n.mode,body:s,smashHeight:l,smashDepth:i}},htmlBuilder:(e,t)=>{let r=eP.makeSpan([],[te(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(let e=0;e<r.children.length;e++)r.children[e].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(let e=0;e<r.children.length;e++)r.children[e].depth=0;let n=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return eP.makeSpan(["mord"],[n],t)},mathmlBuilder:(e,t)=>{let r=new ts.MathNode("mpadded",[td(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),e$({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){let{parser:n}=e,l=r[0],i=t[0];return{type:"sqrt",mode:n.mode,body:i,index:l}},htmlBuilder(e,t){let r=te(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=eP.wrapFragment(r,t);let n=t.fontMetrics().defaultRuleThickness,l=n;t.style.id<v.TEXT.id&&(l=t.fontMetrics().xHeight);let i=n+l/4,a=r.height+r.depth+i+n,{span:s,ruleWidth:o,advanceWidth:h}=rr.sqrtImage(a,t),m=s.height-o;m>r.height+r.depth+i&&(i=(i+m-r.height-r.depth)/2);let c=s.height-r.height-i-o;r.style.paddingLeft=U(h);let p=eP.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+c)},{type:"elem",elem:s},{type:"kern",size:o}]},t);if(!e.index)return eP.makeSpan(["mord","sqrt"],[p],t);{let r=t.havingStyle(v.SCRIPTSCRIPT),n=te(e.index,r,t),l=.6*(p.height-p.depth),i=eP.makeVList({positionType:"shift",positionData:-l,children:[{type:"elem",elem:n}]},t),a=eP.makeSpan(["root"],[i]);return eP.makeSpan(["mord","sqrt"],[a,p],t)}},mathmlBuilder(e,t){let{body:r,index:n}=e;return n?new ts.MathNode("mroot",[td(r,t),td(n,t)]):new ts.MathNode("msqrt",[td(r,t)])}});let rU={display:v.DISPLAY,text:v.TEXT,script:v.SCRIPT,scriptscript:v.SCRIPTSCRIPT};e$({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e,t){let{breakOnTokenText:r,funcName:n,parser:l}=e,i=l.parseExpression(!0,r),a=n.slice(1,n.length-5);return{type:"styling",mode:l.mode,style:a,body:i}},htmlBuilder(e,t){let r=rU[e.style],n=t.havingStyle(r).withFont("");return rF(e.body,n,t)},mathmlBuilder(e,t){let r=rU[e.style],n=t.havingStyle(r),l=tp(e.body,n),i=new ts.MathNode("mstyle",l),a={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return i.setAttribute("scriptlevel",a[0]),i.setAttribute("displaystyle",a[1]),i}});let rY=function(e,t){let r=e.base;if(!r)return null;if("op"===r.type)return r.limits&&(t.style.size===v.DISPLAY.size||r.alwaysHandleSupSub)?rE:null;if("operatorname"===r.type)return r.alwaysHandleSupSub&&(t.style.size===v.DISPLAY.size||r.limits)?rP:null;if("accent"===r.type)return m.isCharacterBox(r.base)?tA:null;if("horizBrace"===r.type)return!e.sub===r.isOver?rN:null;else return null};eZ({type:"supsub",htmlBuilder(e,t){let r,n,l,i;let a=rY(e,t);if(a)return a(e,t);let{base:s,sup:o,sub:h}=e,c=te(s,t),p=t.fontMetrics(),u=0,d=0,g=s&&m.isCharacterBox(s);if(o){let e=t.havingStyle(t.style.sup());r=te(o,e,t),g||(u=c.height-e.fontMetrics().supDrop*e.sizeMultiplier/t.sizeMultiplier)}if(h){let e=t.havingStyle(t.style.sub());n=te(h,e,t),g||(d=c.depth+e.fontMetrics().subDrop*e.sizeMultiplier/t.sizeMultiplier)}l=t.style===v.DISPLAY?p.sup1:t.style.cramped?p.sup3:p.sup2;let f=t.sizeMultiplier,b=U(.5/p.ptPerEm/f),y=null;if(n){let t=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(c instanceof Q||t)&&(y=U(-c.italic))}if(r&&n){u=Math.max(u,l,r.depth+.25*p.xHeight),d=Math.max(d,p.sub2);let e=4*p.defaultRuleThickness;if(u-r.depth-(n.height-d)<e){d=e-(u-r.depth)+n.height;let t=.8*p.xHeight-(u-r.depth);t>0&&(u+=t,d-=t)}let a=[{type:"elem",elem:n,shift:d,marginRight:b,marginLeft:y},{type:"elem",elem:r,shift:-u,marginRight:b}];i=eP.makeVList({positionType:"individualShift",children:a},t)}else if(n){d=Math.max(d,p.sub1,n.height-.8*p.xHeight);let e=[{type:"elem",elem:n,marginLeft:y,marginRight:b}];i=eP.makeVList({positionType:"shift",positionData:d,children:e},t)}else if(r)u=Math.max(u,l,r.depth+.25*p.xHeight),i=eP.makeVList({positionType:"shift",positionData:-u,children:[{type:"elem",elem:r,marginRight:b}]},t);else throw Error("supsub must have either sup or sub.");let x=e2(c,"right")||"mord";return eP.makeSpan([x],[c,eP.makeSpan(["msupsub"],[i])],t)},mathmlBuilder(e,t){let r,n,l=!1;e.base&&"horizBrace"===e.base.type&&!!e.sup===e.base.isOver&&(l=!0,r=e.base.isOver),e.base&&("op"===e.base.type||"operatorname"===e.base.type)&&(e.base.parentIsSupSub=!0);let i=[td(e.base,t)];if(e.sub&&i.push(td(e.sub,t)),e.sup&&i.push(td(e.sup,t)),l)n=r?"mover":"munder";else if(e.sub){if(e.sup){let r=e.base;n=r&&"op"===r.type&&r.limits&&t.style===v.DISPLAY?"munderover":r&&"operatorname"===r.type&&r.alwaysHandleSupSub&&(t.style===v.DISPLAY||r.limits)?"munderover":"msubsup"}else{let r=e.base;n=r&&"op"===r.type&&r.limits&&(t.style===v.DISPLAY||r.alwaysHandleSupSub)?"munder":r&&"operatorname"===r.type&&r.alwaysHandleSupSub&&(r.limits||t.style===v.DISPLAY)?"munder":"msub"}}else{let r=e.base;n=r&&"op"===r.type&&r.limits&&(t.style===v.DISPLAY||r.alwaysHandleSupSub)?"mover":r&&"operatorname"===r.type&&r.alwaysHandleSupSub&&(r.limits||t.style===v.DISPLAY)?"mover":"msup"}return new ts.MathNode(n,i)}}),eZ({type:"atom",htmlBuilder:(e,t)=>eP.mathsym(e.text,e.mode,t,["m"+e.family]),mathmlBuilder(e,t){let r=new ts.MathNode("mo",[to(e.text,e.mode)]);if("bin"===e.family){let n=tm(e,t);"bold-italic"===n&&r.setAttribute("mathvariant",n)}else"punct"===e.family?r.setAttribute("separator","true"):("open"===e.family||"close"===e.family)&&r.setAttribute("stretchy","false");return r}});let rX={mi:"italic",mn:"normal",mtext:"normal"};eZ({type:"mathord",htmlBuilder:(e,t)=>eP.makeOrd(e,t,"mathord"),mathmlBuilder(e,t){let r=new ts.MathNode("mi",[to(e.text,e.mode,t)]),n=tm(e,t)||"italic";return n!==rX[r.type]&&r.setAttribute("mathvariant",n),r}}),eZ({type:"textord",htmlBuilder:(e,t)=>eP.makeOrd(e,t,"textord"),mathmlBuilder(e,t){let r;let n=to(e.text,e.mode,t),l=tm(e,t)||"normal";return l!==rX[(r="text"===e.mode?new ts.MathNode("mtext",[n]):/[0-9]/.test(e.text)?new ts.MathNode("mn",[n]):"\\prime"===e.text?new ts.MathNode("mo",[n]):new ts.MathNode("mi",[n])).type]&&r.setAttribute("mathvariant",l),r}});let r_={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},rW={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};eZ({type:"spacing",htmlBuilder(e,t){if(rW.hasOwnProperty(e.text)){let r=rW[e.text].className||"";if("text"!==e.mode)return eP.makeSpan(["mspace",r],[eP.mathsym(e.text,e.mode,t)],t);{let n=eP.makeOrd(e,t,"textord");return n.classes.push(r),n}}if(r_.hasOwnProperty(e.text))return eP.makeSpan(["mspace",r_[e.text]],[],t);throw new l('Unknown type of space "'+e.text+'"')},mathmlBuilder(e,t){let r;if(rW.hasOwnProperty(e.text))r=new ts.MathNode("mtext",[new ts.TextNode("\xa0")]);else if(r_.hasOwnProperty(e.text))return new ts.MathNode("mspace");else throw new l('Unknown type of space "'+e.text+'"');return r}});let rj=()=>{let e=new ts.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};eZ({type:"tag",mathmlBuilder(e,t){let r=new ts.MathNode("mtable",[new ts.MathNode("mtr",[rj(),new ts.MathNode("mtd",[tu(e.body,t)]),rj(),new ts.MathNode("mtd",[tu(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});let r$={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},rZ={"\\textbf":"textbf","\\textmd":"textmd"},rK={"\\textit":"textit","\\textup":"textup"},rJ=(e,t)=>{let r=e.font;return r?r$[r]?t.withTextFontFamily(r$[r]):rZ[r]?t.withTextFontWeight(rZ[r]):"\\emph"===r?"textit"===t.fontShape?t.withTextFontShape("textup"):t.withTextFontShape("textit"):t.withTextFontShape(rK[r]):t};e$({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(e,t){let{parser:r,funcName:n}=e,l=t[0];return{type:"text",mode:r.mode,body:eJ(l),font:n}},htmlBuilder(e,t){let r=rJ(e,t),n=e6(e.body,r,!0);return eP.makeSpan(["mord","text"],n,r)},mathmlBuilder(e,t){let r=rJ(e,t);return tu(e.body,r)}}),e$({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(e,t){let{parser:r}=e;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder(e,t){let r=te(e.body,t),n=eP.makeLineSpan("underline-line",t),l=t.fontMetrics().defaultRuleThickness,i=eP.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:l},{type:"elem",elem:n},{type:"kern",size:3*l},{type:"elem",elem:r}]},t);return eP.makeSpan(["mord","underline"],[i],t)},mathmlBuilder(e,t){let r=new ts.MathNode("mo",[new ts.TextNode("‾")]);r.setAttribute("stretchy","true");let n=new ts.MathNode("munder",[td(e.body,t),r]);return n.setAttribute("accentunder","true"),n}}),e$({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(e,t){let{parser:r}=e;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder(e,t){let r=te(e.body,t),n=t.fontMetrics().axisHeight,l=.5*(r.height-n-(r.depth+n));return eP.makeVList({positionType:"shift",positionData:l,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:(e,t)=>new ts.MathNode("mpadded",[td(e.body,t)],["vcenter"])}),e$({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(e,t,r){throw new l("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(e,t){let r=rQ(e),n=[],l=t.havingStyle(t.style.text());for(let t=0;t<r.length;t++){let i=r[t];"~"===i&&(i="\\textasciitilde"),n.push(eP.makeSymbol(i,"Typewriter-Regular",e.mode,l,["mord","texttt"]))}return eP.makeSpan(["mord","text"].concat(l.sizingClasses(t)),eP.tryCombineChars(n),l)},mathmlBuilder(e,t){let r=new ts.TextNode(rQ(e)),n=new ts.MathNode("mtext",[r]);return n.setAttribute("mathvariant","monospace"),n}});let rQ=e=>e.body.replace(/ /g,e.star?"␣":"\xa0"),r0="[ \r\n	]",r1="[̀-ͯ]",r4=RegExp(r1+"+$"),r5="("+r0+"+)|\\\\(\n|[ \r	]+\n?)[ \r	]*|([!-\\[\\]-‧‪-퟿豈-￿]"+r1+"*|[\uD800-\uDBFF][\uDC00-\uDFFF]"+r1+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|(\\\\[a-zA-Z@]+)"+r0+"*|\\\\[^\uD800-\uDFFF])";class r6{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=RegExp(r5,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){let e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new ru("EOF",new rp(this,t,t));let r=this.tokenRegex.exec(e);if(null===r||r.index!==t)throw new l("Unexpected character: '"+e[t]+"'",new ru(e[t],new rp(this,t,t+1)));let n=r[6]||r[3]||(r[2]?"\\ ":" ");if(14===this.catcodes[n]){let t=e.indexOf("\n",this.tokenRegex.lastIndex);return -1===t?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=t+1,this.lex()}return new ru(n,new rp(this,t,this.tokenRegex.lastIndex))}}class r7{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(0===this.undefStack.length)throw new l("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");let e=this.undefStack.pop();for(let t in e)e.hasOwnProperty(t)&&(null==e[t]?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(void 0===r&&(r=!1),r){for(let t=0;t<this.undefStack.length;t++)delete this.undefStack[t][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{let t=this.undefStack[this.undefStack.length-1];t&&!t.hasOwnProperty(e)&&(t[e]=this.current[e])}null==t?delete this.current[e]:this.current[e]=t}}rc["\\noexpand"]=function(e){let t=e.popToken();return e.isExpandable(t.text)&&(t.noexpand=!0,t.treatAsRelax=!0),{tokens:[t],numArgs:0}},rc["\\expandafter"]=function(e){let t=e.popToken();return e.expandOnce(!0),{tokens:[t],numArgs:0}},rc["\\@firstoftwo"]=function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}},rc["\\@secondoftwo"]=function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}},rc["\\@ifnextchar"]=function(e){let t=e.consumeArgs(3);e.consumeSpaces();let r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}},rc["\\@ifstar"]="\\@ifnextchar *{\\@firstoftwo{#1}}",rc["\\TextOrMath"]=function(e){let t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}};let r3={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};rc["\\char"]=function(e){let t,r=e.popToken(),n="";if("'"===r.text)t=8,r=e.popToken();else if('"'===r.text)t=16,r=e.popToken();else if("`"===r.text){if("\\"===(r=e.popToken()).text[0])n=r.text.charCodeAt(1);else if("EOF"===r.text)throw new l("\\char` missing argument");else n=r.text.charCodeAt(0)}else t=10;if(t){let i;if(null==(n=r3[r.text])||n>=t)throw new l("Invalid base-"+t+" digit "+r.text);for(;null!=(i=r3[e.future().text])&&i<t;)n*=t,n+=i,e.popToken()}return"\\@char{"+n+"}"};let r8=(e,t,r,n)=>{let i=e.consumeArg().tokens;if(1!==i.length)throw new l("\\newcommand's first argument must be a macro name");let a=i[0].text,s=e.isDefined(a);if(s&&!t)throw new l("\\newcommand{"+a+"} attempting to redefine "+a+"; use \\renewcommand");if(!s&&!r)throw new l("\\renewcommand{"+a+"} when command "+a+" does not yet exist; use \\newcommand");let o=0;if(1===(i=e.consumeArg().tokens).length&&"["===i[0].text){let t="",r=e.expandNextToken();for(;"]"!==r.text&&"EOF"!==r.text;)t+=r.text,r=e.expandNextToken();if(!t.match(/^\s*[0-9]+\s*$/))throw new l("Invalid number of arguments: "+t);o=parseInt(t),i=e.consumeArg().tokens}return s&&n||e.macros.set(a,{tokens:i,numArgs:o}),""};rc["\\newcommand"]=e=>r8(e,!1,!0,!1),rc["\\renewcommand"]=e=>r8(e,!0,!1,!1),rc["\\providecommand"]=e=>r8(e,!0,!0,!0),rc["\\message"]=e=>(console.log(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),rc["\\errmessage"]=e=>(console.error(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),rc["\\show"]=e=>{let t=e.popToken(),r=t.text;return console.log(t,e.macros.get(r),e_[r],ea.math[r],ea.text[r]),""},rc["\\bgroup"]="{",rc["\\egroup"]="}",rc["~"]="\\nobreakspace",rc["\\lq"]="`",rc["\\rq"]="'",rc["\\aa"]="\\r a",rc["\\AA"]="\\r A",rc["\\textcopyright"]="\\html@mathml{\\textcircled{c}}{\\char`\xa9}",rc["\\copyright"]="\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}",rc["\\textregistered"]="\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xae}",rc["ℬ"]="\\mathscr{B}",rc["ℰ"]="\\mathscr{E}",rc["ℱ"]="\\mathscr{F}",rc["ℋ"]="\\mathscr{H}",rc["ℐ"]="\\mathscr{I}",rc["ℒ"]="\\mathscr{L}",rc["ℳ"]="\\mathscr{M}",rc["ℛ"]="\\mathscr{R}",rc["ℭ"]="\\mathfrak{C}",rc["ℌ"]="\\mathfrak{H}",rc["ℨ"]="\\mathfrak{Z}",rc["\\Bbbk"]="\\Bbb{k}",rc["\xb7"]="\\cdotp",rc["\\llap"]="\\mathllap{\\textrm{#1}}",rc["\\rlap"]="\\mathrlap{\\textrm{#1}}",rc["\\clap"]="\\mathclap{\\textrm{#1}}",rc["\\mathstrut"]="\\vphantom{(}",rc["\\underbar"]="\\underline{\\text{#1}}",rc["\\not"]='\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}',rc["\\neq"]="\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}",rc["\\ne"]="\\neq",rc["≠"]="\\neq",rc["\\notin"]="\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}",rc["∉"]="\\notin",rc["≘"]="\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}",rc["≙"]="\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}",rc["≚"]="\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}",rc["≛"]="\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}",rc["≝"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}",rc["≞"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}",rc["≟"]="\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}",rc["⟂"]="\\perp",rc["‼"]="\\mathclose{!\\mkern-0.8mu!}",rc["∌"]="\\notni",rc["⌜"]="\\ulcorner",rc["⌝"]="\\urcorner",rc["⌞"]="\\llcorner",rc["⌟"]="\\lrcorner",rc["\xa9"]="\\copyright",rc["\xae"]="\\textregistered",rc["️"]="\\textregistered",rc["\\ulcorner"]='\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}',rc["\\urcorner"]='\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}',rc["\\llcorner"]='\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}',rc["\\lrcorner"]='\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}',rc["\\vdots"]="{\\varvdots\\rule{0pt}{15pt}}",rc["⋮"]="\\vdots",rc["\\varGamma"]="\\mathit{\\Gamma}",rc["\\varDelta"]="\\mathit{\\Delta}",rc["\\varTheta"]="\\mathit{\\Theta}",rc["\\varLambda"]="\\mathit{\\Lambda}",rc["\\varXi"]="\\mathit{\\Xi}",rc["\\varPi"]="\\mathit{\\Pi}",rc["\\varSigma"]="\\mathit{\\Sigma}",rc["\\varUpsilon"]="\\mathit{\\Upsilon}",rc["\\varPhi"]="\\mathit{\\Phi}",rc["\\varPsi"]="\\mathit{\\Psi}",rc["\\varOmega"]="\\mathit{\\Omega}",rc["\\substack"]="\\begin{subarray}{c}#1\\end{subarray}",rc["\\colon"]="\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax",rc["\\boxed"]="\\fbox{$\\displaystyle{#1}$}",rc["\\iff"]="\\DOTSB\\;\\Longleftrightarrow\\;",rc["\\implies"]="\\DOTSB\\;\\Longrightarrow\\;",rc["\\impliedby"]="\\DOTSB\\;\\Longleftarrow\\;",rc["\\dddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}",rc["\\ddddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}";let r2={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};rc["\\dots"]=function(e){let t="\\dotso",r=e.expandAfterFuture().text;return r in r2?t=r2[r]:"\\not"===r.slice(0,4)?t="\\dotsb":r in ea.math&&m.contains(["bin","rel"],ea.math[r].group)&&(t="\\dotsb"),t};let r9={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};rc["\\dotso"]=function(e){return e.future().text in r9?"\\ldots\\,":"\\ldots"},rc["\\dotsc"]=function(e){let t=e.future().text;return t in r9&&","!==t?"\\ldots\\,":"\\ldots"},rc["\\cdots"]=function(e){return e.future().text in r9?"\\@cdots\\,":"\\@cdots"},rc["\\dotsb"]="\\cdots",rc["\\dotsm"]="\\cdots",rc["\\dotsi"]="\\!\\cdots",rc["\\dotsx"]="\\ldots\\,",rc["\\DOTSI"]="\\relax",rc["\\DOTSB"]="\\relax",rc["\\DOTSX"]="\\relax",rc["\\tmspace"]="\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax",rc["\\,"]="\\tmspace+{3mu}{.1667em}",rc["\\thinspace"]="\\,",rc["\\>"]="\\mskip{4mu}",rc["\\:"]="\\tmspace+{4mu}{.2222em}",rc["\\medspace"]="\\:",rc["\\;"]="\\tmspace+{5mu}{.2777em}",rc["\\thickspace"]="\\;",rc["\\!"]="\\tmspace-{3mu}{.1667em}",rc["\\negthinspace"]="\\!",rc["\\negmedspace"]="\\tmspace-{4mu}{.2222em}",rc["\\negthickspace"]="\\tmspace-{5mu}{.277em}",rc["\\enspace"]="\\kern.5em ",rc["\\enskip"]="\\hskip.5em\\relax",rc["\\quad"]="\\hskip1em\\relax",rc["\\qquad"]="\\hskip2em\\relax",rc["\\tag"]="\\@ifstar\\tag@literal\\tag@paren",rc["\\tag@paren"]="\\tag@literal{({#1})}",rc["\\tag@literal"]=e=>{if(e.macros.get("\\df@tag"))throw new l("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"},rc["\\bmod"]="\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}",rc["\\pod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)",rc["\\pmod"]="\\pod{{\\rm mod}\\mkern6mu#1}",rc["\\mod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1",rc["\\newline"]="\\\\\\relax",rc["\\TeX"]="\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}";let ne=U(q["Main-Regular"][84][1]-.7*q["Main-Regular"][65][1]);rc["\\LaTeX"]="\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+ne+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}",rc["\\KaTeX"]="\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+ne+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}",rc["\\hspace"]="\\@ifstar\\@hspacer\\@hspace",rc["\\@hspace"]="\\hskip #1\\relax",rc["\\@hspacer"]="\\rule{0pt}{0pt}\\hskip #1\\relax",rc["\\ordinarycolon"]=":",rc["\\vcentcolon"]="\\mathrel{\\mathop\\ordinarycolon}",rc["\\dblcolon"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}',rc["\\coloneqq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}',rc["\\Coloneqq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}',rc["\\coloneq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}',rc["\\Coloneq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}',rc["\\eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}',rc["\\Eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}',rc["\\eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}',rc["\\Eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}',rc["\\colonapprox"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}',rc["\\Colonapprox"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}',rc["\\colonsim"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}',rc["\\Colonsim"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}',rc["∷"]="\\dblcolon",rc["∹"]="\\eqcolon",rc["≔"]="\\coloneqq",rc["≕"]="\\eqqcolon",rc["⩴"]="\\Coloneqq",rc["\\ratio"]="\\vcentcolon",rc["\\coloncolon"]="\\dblcolon",rc["\\colonequals"]="\\coloneqq",rc["\\coloncolonequals"]="\\Coloneqq",rc["\\equalscolon"]="\\eqqcolon",rc["\\equalscoloncolon"]="\\Eqqcolon",rc["\\colonminus"]="\\coloneq",rc["\\coloncolonminus"]="\\Coloneq",rc["\\minuscolon"]="\\eqcolon",rc["\\minuscoloncolon"]="\\Eqcolon",rc["\\coloncolonapprox"]="\\Colonapprox",rc["\\coloncolonsim"]="\\Colonsim",rc["\\simcolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}",rc["\\simcoloncolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}",rc["\\approxcolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}",rc["\\approxcoloncolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}",rc["\\notni"]="\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}",rc["\\limsup"]="\\DOTSB\\operatorname*{lim\\,sup}",rc["\\liminf"]="\\DOTSB\\operatorname*{lim\\,inf}",rc["\\injlim"]="\\DOTSB\\operatorname*{inj\\,lim}",rc["\\projlim"]="\\DOTSB\\operatorname*{proj\\,lim}",rc["\\varlimsup"]="\\DOTSB\\operatorname*{\\overline{lim}}",rc["\\varliminf"]="\\DOTSB\\operatorname*{\\underline{lim}}",rc["\\varinjlim"]="\\DOTSB\\operatorname*{\\underrightarrow{lim}}",rc["\\varprojlim"]="\\DOTSB\\operatorname*{\\underleftarrow{lim}}",rc["\\gvertneqq"]="\\html@mathml{\\@gvertneqq}{≩}",rc["\\lvertneqq"]="\\html@mathml{\\@lvertneqq}{≨}",rc["\\ngeqq"]="\\html@mathml{\\@ngeqq}{≱}",rc["\\ngeqslant"]="\\html@mathml{\\@ngeqslant}{≱}",rc["\\nleqq"]="\\html@mathml{\\@nleqq}{≰}",rc["\\nleqslant"]="\\html@mathml{\\@nleqslant}{≰}",rc["\\nshortmid"]="\\html@mathml{\\@nshortmid}{∤}",rc["\\nshortparallel"]="\\html@mathml{\\@nshortparallel}{∦}",rc["\\nsubseteqq"]="\\html@mathml{\\@nsubseteqq}{⊈}",rc["\\nsupseteqq"]="\\html@mathml{\\@nsupseteqq}{⊉}",rc["\\varsubsetneq"]="\\html@mathml{\\@varsubsetneq}{⊊}",rc["\\varsubsetneqq"]="\\html@mathml{\\@varsubsetneqq}{⫋}",rc["\\varsupsetneq"]="\\html@mathml{\\@varsupsetneq}{⊋}",rc["\\varsupsetneqq"]="\\html@mathml{\\@varsupsetneqq}{⫌}",rc["\\imath"]="\\html@mathml{\\@imath}{ı}",rc["\\jmath"]="\\html@mathml{\\@jmath}{ȷ}",rc["\\llbracket"]="\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}",rc["\\rrbracket"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}",rc["⟦"]="\\llbracket",rc["⟧"]="\\rrbracket",rc["\\lBrace"]="\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}",rc["\\rBrace"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}",rc["⦃"]="\\lBrace",rc["⦄"]="\\rBrace",rc["\\minuso"]="\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}",rc["⦵"]="\\minuso",rc["\\darr"]="\\downarrow",rc["\\dArr"]="\\Downarrow",rc["\\Darr"]="\\Downarrow",rc["\\lang"]="\\langle",rc["\\rang"]="\\rangle",rc["\\uarr"]="\\uparrow",rc["\\uArr"]="\\Uparrow",rc["\\Uarr"]="\\Uparrow",rc["\\N"]="\\mathbb{N}",rc["\\R"]="\\mathbb{R}",rc["\\Z"]="\\mathbb{Z}",rc["\\alef"]="\\aleph",rc["\\alefsym"]="\\aleph",rc["\\Alpha"]="\\mathrm{A}",rc["\\Beta"]="\\mathrm{B}",rc["\\bull"]="\\bullet",rc["\\Chi"]="\\mathrm{X}",rc["\\clubs"]="\\clubsuit",rc["\\cnums"]="\\mathbb{C}",rc["\\Complex"]="\\mathbb{C}",rc["\\Dagger"]="\\ddagger",rc["\\diamonds"]="\\diamondsuit",rc["\\empty"]="\\emptyset",rc["\\Epsilon"]="\\mathrm{E}",rc["\\Eta"]="\\mathrm{H}",rc["\\exist"]="\\exists",rc["\\harr"]="\\leftrightarrow",rc["\\hArr"]="\\Leftrightarrow",rc["\\Harr"]="\\Leftrightarrow",rc["\\hearts"]="\\heartsuit",rc["\\image"]="\\Im",rc["\\infin"]="\\infty",rc["\\Iota"]="\\mathrm{I}",rc["\\isin"]="\\in",rc["\\Kappa"]="\\mathrm{K}",rc["\\larr"]="\\leftarrow",rc["\\lArr"]="\\Leftarrow",rc["\\Larr"]="\\Leftarrow",rc["\\lrarr"]="\\leftrightarrow",rc["\\lrArr"]="\\Leftrightarrow",rc["\\Lrarr"]="\\Leftrightarrow",rc["\\Mu"]="\\mathrm{M}",rc["\\natnums"]="\\mathbb{N}",rc["\\Nu"]="\\mathrm{N}",rc["\\Omicron"]="\\mathrm{O}",rc["\\plusmn"]="\\pm",rc["\\rarr"]="\\rightarrow",rc["\\rArr"]="\\Rightarrow",rc["\\Rarr"]="\\Rightarrow",rc["\\real"]="\\Re",rc["\\reals"]="\\mathbb{R}",rc["\\Reals"]="\\mathbb{R}",rc["\\Rho"]="\\mathrm{P}",rc["\\sdot"]="\\cdot",rc["\\sect"]="\\S",rc["\\spades"]="\\spadesuit",rc["\\sub"]="\\subset",rc["\\sube"]="\\subseteq",rc["\\supe"]="\\supseteq",rc["\\Tau"]="\\mathrm{T}",rc["\\thetasym"]="\\vartheta",rc["\\weierp"]="\\wp",rc["\\Zeta"]="\\mathrm{Z}",rc["\\argmin"]="\\DOTSB\\operatorname*{arg\\,min}",rc["\\argmax"]="\\DOTSB\\operatorname*{arg\\,max}",rc["\\plim"]="\\DOTSB\\mathop{\\operatorname{plim}}\\limits",rc["\\bra"]="\\mathinner{\\langle{#1}|}",rc["\\ket"]="\\mathinner{|{#1}\\rangle}",rc["\\braket"]="\\mathinner{\\langle{#1}\\rangle}",rc["\\Bra"]="\\left\\langle#1\\right|",rc["\\Ket"]="\\left|#1\\right\\rangle";let nt=e=>t=>{let r=t.consumeArg().tokens,n=t.consumeArg().tokens,l=t.consumeArg().tokens,i=t.consumeArg().tokens,a=t.macros.get("|"),s=t.macros.get("\\|");t.macros.beginGroup();let o=t=>r=>{e&&(r.macros.set("|",a),l.length&&r.macros.set("\\|",s));let i=t;return!t&&l.length&&"|"===r.future().text&&(r.popToken(),i=!0),{tokens:i?l:n,numArgs:0}};t.macros.set("|",o(!1)),l.length&&t.macros.set("\\|",o(!0));let h=t.consumeArg().tokens,m=t.expandTokens([...i,...h,...r]);return t.macros.endGroup(),{tokens:m.reverse(),numArgs:0}};e=nt(!1),rc["\\bra@ket"]=e,t=nt(!0),rc["\\bra@set"]=t,rc["\\Braket"]="\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}",rc["\\Set"]="\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}",rc["\\set"]="\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}",rc["\\angln"]="{\\angl n}",rc["\\blue"]="\\textcolor{##6495ed}{#1}",rc["\\orange"]="\\textcolor{##ffa500}{#1}",rc["\\pink"]="\\textcolor{##ff00af}{#1}",rc["\\red"]="\\textcolor{##df0030}{#1}",rc["\\green"]="\\textcolor{##28ae7b}{#1}",rc["\\gray"]="\\textcolor{gray}{#1}",rc["\\purple"]="\\textcolor{##9d38bd}{#1}",rc["\\blueA"]="\\textcolor{##ccfaff}{#1}",rc["\\blueB"]="\\textcolor{##80f6ff}{#1}",rc["\\blueC"]="\\textcolor{##63d9ea}{#1}",rc["\\blueD"]="\\textcolor{##11accd}{#1}",rc["\\blueE"]="\\textcolor{##0c7f99}{#1}",rc["\\tealA"]="\\textcolor{##94fff5}{#1}",rc["\\tealB"]="\\textcolor{##26edd5}{#1}",rc["\\tealC"]="\\textcolor{##01d1c1}{#1}",rc["\\tealD"]="\\textcolor{##01a995}{#1}",rc["\\tealE"]="\\textcolor{##208170}{#1}",rc["\\greenA"]="\\textcolor{##b6ffb0}{#1}",rc["\\greenB"]="\\textcolor{##8af281}{#1}",rc["\\greenC"]="\\textcolor{##74cf70}{#1}",rc["\\greenD"]="\\textcolor{##1fab54}{#1}",rc["\\greenE"]="\\textcolor{##0d923f}{#1}",rc["\\goldA"]="\\textcolor{##ffd0a9}{#1}",rc["\\goldB"]="\\textcolor{##ffbb71}{#1}",rc["\\goldC"]="\\textcolor{##ff9c39}{#1}",rc["\\goldD"]="\\textcolor{##e07d10}{#1}",rc["\\goldE"]="\\textcolor{##a75a05}{#1}",rc["\\redA"]="\\textcolor{##fca9a9}{#1}",rc["\\redB"]="\\textcolor{##ff8482}{#1}",rc["\\redC"]="\\textcolor{##f9685d}{#1}",rc["\\redD"]="\\textcolor{##e84d39}{#1}",rc["\\redE"]="\\textcolor{##bc2612}{#1}",rc["\\maroonA"]="\\textcolor{##ffbde0}{#1}",rc["\\maroonB"]="\\textcolor{##ff92c6}{#1}",rc["\\maroonC"]="\\textcolor{##ed5fa6}{#1}",rc["\\maroonD"]="\\textcolor{##ca337c}{#1}",rc["\\maroonE"]="\\textcolor{##9e034e}{#1}",rc["\\purpleA"]="\\textcolor{##ddd7ff}{#1}",rc["\\purpleB"]="\\textcolor{##c6b9fc}{#1}",rc["\\purpleC"]="\\textcolor{##aa87ff}{#1}",rc["\\purpleD"]="\\textcolor{##7854ab}{#1}",rc["\\purpleE"]="\\textcolor{##543b78}{#1}",rc["\\mintA"]="\\textcolor{##f5f9e8}{#1}",rc["\\mintB"]="\\textcolor{##edf2df}{#1}",rc["\\mintC"]="\\textcolor{##e0e5cc}{#1}",rc["\\grayA"]="\\textcolor{##f6f7f7}{#1}",rc["\\grayB"]="\\textcolor{##f0f1f2}{#1}",rc["\\grayC"]="\\textcolor{##e3e5e6}{#1}",rc["\\grayD"]="\\textcolor{##d6d8da}{#1}",rc["\\grayE"]="\\textcolor{##babec2}{#1}",rc["\\grayF"]="\\textcolor{##888d93}{#1}",rc["\\grayG"]="\\textcolor{##626569}{#1}",rc["\\grayH"]="\\textcolor{##3b3e40}{#1}",rc["\\grayI"]="\\textcolor{##21242c}{#1}",rc["\\kaBlue"]="\\textcolor{##314453}{#1}",rc["\\kaGreen"]="\\textcolor{##71B307}{#1}";let nr={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};class nn{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new r7(rc,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new r6(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){let t,r,n;if(e){if(this.consumeSpaces(),"["!==this.future().text)return null;t=this.popToken(),{tokens:n,end:r}=this.consumeArg(["]"])}else({tokens:n,start:t,end:r}=this.consumeArg());return this.pushToken(new ru("EOF",r.loc)),this.pushTokens(n),t.range(r,"")}consumeSpaces(){for(;;)if(" "===this.future().text)this.stack.pop();else break}consumeArg(e){let t;let r=[],n=e&&e.length>0;n||this.consumeSpaces();let i=this.future(),a=0,s=0;do{if(t=this.popToken(),r.push(t),"{"===t.text)++a;else if("}"===t.text){if(-1==--a)throw new l("Extra }",t)}else if("EOF"===t.text)throw new l("Unexpected end of input in a macro argument, expected '"+(e&&n?e[s]:"}")+"'",t);if(e&&n){if((0===a||1===a&&"{"===e[s])&&t.text===e[s]){if(++s===e.length){r.splice(-s,s);break}}else s=0}}while(0!==a||n);return"{"===i.text&&"}"===r[r.length-1].text&&(r.pop(),r.shift()),r.reverse(),{tokens:r,start:i,end:t}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new l("The length of delimiters doesn't match the number of args!");let r=t[0];for(let e=0;e<r.length;e++){let t=this.popToken();if(r[e]!==t.text)throw new l("Use of the macro doesn't match its definition",t)}}let r=[];for(let n=0;n<e;n++)r.push(this.consumeArg(t&&t[n+1]).tokens);return r}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new l("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){let t=this.popToken(),r=t.text,n=t.noexpand?null:this._getExpansion(r);if(null==n||e&&n.unexpandable){if(e&&null==n&&"\\"===r[0]&&!this.isDefined(r))throw new l("Undefined control sequence: "+r);return this.pushToken(t),!1}this.countExpansion(1);let i=n.tokens,a=this.consumeArgs(n.numArgs,n.delimiters);if(n.numArgs){i=i.slice();for(let e=i.length-1;e>=0;--e){let t=i[e];if("#"===t.text){if(0===e)throw new l("Incomplete placeholder at end of macro body",t);if("#"===(t=i[--e]).text)i.splice(e+1,1);else if(/^[1-9]$/.test(t.text))i.splice(e,2,...a[+t.text-1]);else throw new l("Not a valid argument number",t)}}}return this.pushTokens(i),i.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(!1===this.expandOnce()){let e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw Error()}expandMacro(e){return this.macros.has(e)?this.expandTokens([new ru(e)]):void 0}expandTokens(e){let t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(!1===this.expandOnce(!0)){let e=this.stack.pop();e.treatAsRelax&&(e.noexpand=!1,e.treatAsRelax=!1),t.push(e)}return this.countExpansion(t.length),t}expandMacroAsText(e){let t=this.expandMacro(e);return t?t.map(e=>e.text).join(""):t}_getExpansion(e){let t=this.macros.get(e);if(null==t)return t;if(1===e.length){let t=this.lexer.catcodes[e];if(null!=t&&13!==t)return}let r="function"==typeof t?t(this):t;if("string"==typeof r){let e=0;if(-1!==r.indexOf("#")){let t=r.replace(/##/g,"");for(;-1!==t.indexOf("#"+(e+1));)++e}let t=new r6(r,this.settings),n=[],l=t.lex();for(;"EOF"!==l.text;)n.push(l),l=t.lex();return n.reverse(),{tokens:n,numArgs:e}}return r}isDefined(e){return this.macros.has(e)||e_.hasOwnProperty(e)||ea.math.hasOwnProperty(e)||ea.text.hasOwnProperty(e)||nr.hasOwnProperty(e)}isExpandable(e){let t=this.macros.get(e);return null!=t?"string"==typeof t||"function"==typeof t||!t.unexpandable:e_.hasOwnProperty(e)&&!e_[e].primitive}}let nl=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,ni=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9",ₐ:"a",ₑ:"e",ₕ:"h",ᵢ:"i",ⱼ:"j",ₖ:"k",ₗ:"l",ₘ:"m",ₙ:"n",ₒ:"o",ₚ:"p",ᵣ:"r",ₛ:"s",ₜ:"t",ᵤ:"u",ᵥ:"v",ₓ:"x",ᵦ:"β",ᵧ:"γ",ᵨ:"ρ",ᵩ:"ϕ",ᵪ:"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","\xb9":"1","\xb2":"2","\xb3":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9",ᴬ:"A",ᴮ:"B",ᴰ:"D",ᴱ:"E",ᴳ:"G",ᴴ:"H",ᴵ:"I",ᴶ:"J",ᴷ:"K",ᴸ:"L",ᴹ:"M",ᴺ:"N",ᴼ:"O",ᴾ:"P",ᴿ:"R",ᵀ:"T",ᵁ:"U",ⱽ:"V",ᵂ:"W",ᵃ:"a",ᵇ:"b",ᶜ:"c",ᵈ:"d",ᵉ:"e",ᶠ:"f",ᵍ:"g",ʰ:"h",ⁱ:"i",ʲ:"j",ᵏ:"k",ˡ:"l",ᵐ:"m",ⁿ:"n",ᵒ:"o",ᵖ:"p",ʳ:"r",ˢ:"s",ᵗ:"t",ᵘ:"u",ᵛ:"v",ʷ:"w",ˣ:"x",ʸ:"y",ᶻ:"z",ᵝ:"β",ᵞ:"γ",ᵟ:"δ",ᵠ:"ϕ",ᵡ:"χ",ᶿ:"θ"}),na={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},ns={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};class no{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new nn(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(void 0===t&&(t=!0),this.fetch().text!==e)throw new l("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return null==this.nextToken&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{let e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){let t=this.nextToken;this.consume(),this.gullet.pushToken(new ru("}")),this.gullet.pushTokens(e);let r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){let r=[];for(;;){"math"===this.mode&&this.consumeSpaces();let n=this.fetch();if(-1!==no.endOfExpression.indexOf(n.text)||t&&n.text===t||e&&e_[n.text]&&e_[n.text].infix)break;let l=this.parseAtom(t);if(l){if("internal"===l.type)continue}else break;r.push(l)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){let t,r=-1;for(let n=0;n<e.length;n++)if("infix"===e[n].type){if(-1!==r)throw new l("only one infix operator per group",e[n].token);r=n,t=e[n].replaceWith}if(-1===r||!t)return e;{let n,l,i;let a=e.slice(0,r),s=e.slice(r+1);return n=1===a.length&&"ordgroup"===a[0].type?a[0]:{type:"ordgroup",mode:this.mode,body:a},l=1===s.length&&"ordgroup"===s[0].type?s[0]:{type:"ordgroup",mode:this.mode,body:s},["\\\\abovefrac"===t?this.callFunction(t,[n,e[r],l],[]):this.callFunction(t,[n,l],[])]}}handleSupSubscript(e){let t;let r=this.fetch(),n=r.text;this.consume(),this.consumeSpaces();do{var i;t=this.parseGroup(e)}while((null==(i=t)?void 0:i.type)==="internal");if(!t)throw new l("Expected group after '"+n+"'",r);return t}formatUnsupportedCmd(e){let t=[];for(let r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});let r={type:"text",mode:this.mode,body:t};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[r]}}parseAtom(e){let t,r;let n=this.parseGroup("atom",e);if((null==n?void 0:n.type)==="internal"||"text"===this.mode)return n;for(;;){this.consumeSpaces();let e=this.fetch();if("\\limits"===e.text||"\\nolimits"===e.text){if(n&&"op"===n.type)n.limits="\\limits"===e.text,n.alwaysHandleSupSub=!0;else if(n&&"operatorname"===n.type)n.alwaysHandleSupSub&&(n.limits="\\limits"===e.text);else throw new l("Limit controls must follow a math operator",e);this.consume()}else if("^"===e.text){if(t)throw new l("Double superscript",e);t=this.handleSupSubscript("superscript")}else if("_"===e.text){if(r)throw new l("Double subscript",e);r=this.handleSupSubscript("subscript")}else if("'"===e.text){if(t)throw new l("Double superscript",e);let r={type:"textord",mode:this.mode,text:"\\prime"},n=[r];for(this.consume();"'"===this.fetch().text;)n.push(r),this.consume();"^"===this.fetch().text&&n.push(this.handleSupSubscript("superscript")),t={type:"ordgroup",mode:this.mode,body:n}}else if(ni[e.text]){let n=nl.test(e.text),l=[];for(l.push(new ru(ni[e.text])),this.consume();;){let e=this.fetch().text;if(!ni[e]||nl.test(e)!==n)break;l.unshift(new ru(ni[e])),this.consume()}let i=this.subparse(l);n?r={type:"ordgroup",mode:"math",body:i}:t={type:"ordgroup",mode:"math",body:i}}else break}return t||r?{type:"supsub",mode:this.mode,base:n,sup:t,sub:r}:n}parseFunction(e,t){let r=this.fetch(),n=r.text,i=e_[n];if(!i)return null;if(this.consume(),t&&"atom"!==t&&!i.allowedInArgument)throw new l("Got function '"+n+"' with no arguments"+(t?" as "+t:""),r);if("text"!==this.mode||i.allowedInText){if("math"===this.mode&&!1===i.allowedInMath)throw new l("Can't use function '"+n+"' in math mode",r)}else throw new l("Can't use function '"+n+"' in text mode",r);let{args:a,optArgs:s}=this.parseArguments(n,i);return this.callFunction(n,a,s,r,e)}callFunction(e,t,r,n,i){let a=e_[e];if(a&&a.handler)return a.handler({funcName:e,parser:this,token:n,breakOnTokenText:i},t,r);throw new l("No function handler for "+e)}parseArguments(e,t){let r=t.numArgs+t.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};let n=[],i=[];for(let a=0;a<r;a++){let r=t.argTypes&&t.argTypes[a],s=a<t.numOptionalArgs;(t.primitive&&null==r||"sqrt"===t.type&&1===a&&null==i[0])&&(r="primitive");let o=this.parseGroupOfType("argument to '"+e+"'",r,s);if(s)i.push(o);else if(null!=o)n.push(o);else throw new l("Null argument, please report this as a bug")}return{args:n,optArgs:i}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":{let e=this.parseArgumentGroup(r,"text");return null!=e?{type:"styling",mode:e.mode,body:[e],style:"text"}:null}case"raw":{let e=this.parseStringGroup("raw",r);return null!=e?{type:"raw",mode:"text",string:e.text}:null}case"primitive":{if(r)throw new l("A primitive argument cannot be optional");let t=this.parseGroup(e);if(null==t)throw new l("Expected group as "+e,this.fetch());return t}case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new l("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;" "===this.fetch().text;)this.consume()}parseStringGroup(e,t){let r;let n=this.gullet.scanArgument(t);if(null==n)return null;let l="";for(;"EOF"!==(r=this.fetch()).text;)l+=r.text,this.consume();return this.consume(),n.text=l,n}parseRegexGroup(e,t){let r;let n=this.fetch(),i=n,a="";for(;"EOF"!==(r=this.fetch()).text&&e.test(a+r.text);)a+=(i=r).text,this.consume();if(""===a)throw new l("Invalid "+t+": '"+n.text+"'",n);return n.range(i,a)}parseColorGroup(e){let t=this.parseStringGroup("color",e);if(null==t)return null;let r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new l("Invalid color: '"+t.text+"'",t);let n=r[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}}parseSizeGroup(e){let t;let r=!1;if(this.gullet.consumeSpaces(),!(t=e||"{"===this.gullet.future().text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",r=!0);let n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!n)throw new l("Invalid size: '"+t.text+"'",t);let i={number:+(n[1]+n[2]),unit:n[3]};if(!F(i))throw new l("Invalid unit: '"+i.unit+"'",t);return{type:"size",mode:this.mode,value:i,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);let t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),null==t)return null;let r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){let r=this.gullet.scanArgument(e);if(null==r)return null;let n=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();let l=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();let i={type:"ordgroup",mode:this.mode,loc:r.loc,body:l};return t&&this.switchMode(n),i}parseGroup(e,t){let r;let n=this.fetch(),i=n.text;if("{"===i||"\\begingroup"===i){this.consume();let e="{"===i?"}":"\\endgroup";this.gullet.beginGroup();let t=this.parseExpression(!1,e),l=this.fetch();this.expect(e),this.gullet.endGroup(),r={type:"ordgroup",mode:this.mode,loc:rp.range(n,l),body:t,semisimple:"\\begingroup"===i||void 0}}else if(null==(r=this.parseFunction(t,e)||this.parseSymbol())&&"\\"===i[0]&&!nr.hasOwnProperty(i)){if(this.settings.throwOnError)throw new l("Undefined control sequence: "+i,n);r=this.formatUnsupportedCmd(i),this.consume()}return r}formLigatures(e){let t=e.length-1;for(let r=0;r<t;++r){let n=e[r],l=n.text;"-"===l&&"-"===e[r+1].text&&(r+1<t&&"-"===e[r+2].text?(e.splice(r,3,{type:"textord",mode:"text",loc:rp.range(n,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:rp.range(n,e[r+1]),text:"--"}),t-=1)),("'"===l||"`"===l)&&e[r+1].text===l&&(e.splice(r,2,{type:"textord",mode:"text",loc:rp.range(n,e[r+1]),text:l+l}),t-=1)}}parseSymbol(){let e;let t=this.fetch(),r=t.text;if(/^\\verb[^a-zA-Z]/.test(r)){this.consume();let e=r.slice(5),t="*"===e.charAt(0);if(t&&(e=e.slice(1)),e.length<2||e.charAt(0)!==e.slice(-1))throw new l("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:e=e.slice(1,-1),star:t}}ns.hasOwnProperty(r[0])&&!ea[this.mode][r[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+r[0]+'" used in math mode',t),r=ns[r[0]]+r.slice(1));let n=r4.exec(r);if(n&&("i"===(r=r.substring(0,n.index))?r="ı":"j"===r&&(r="ȷ")),ea[this.mode][r]){let n;this.settings.strict&&"math"===this.mode&&ez.indexOf(r)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+r[0]+'" used in math mode',t);let l=ea[this.mode][r].group,i=rp.range(t);e=el.hasOwnProperty(l)?{type:"atom",mode:this.mode,family:l,loc:i,text:r}:{type:l,mode:this.mode,loc:i,text:r}}else{if(!(r.charCodeAt(0)>=128))return null;this.settings.strict&&(M(r.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+r[0]+'" used in math mode',t):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+r[0]+'" ('+r.charCodeAt(0)+")",t)),e={type:"textord",mode:"text",loc:rp.range(t),text:r}}if(this.consume(),n)for(let r=0;r<n[0].length;r++){let i=n[0][r];if(!na[i])throw new l("Unknown accent ' "+i+"'",t);let a=na[i][this.mode]||na[i].text;if(!a)throw new l("Accent "+i+" unsupported in "+this.mode+" mode",t);e={type:"accent",mode:this.mode,loc:rp.range(t),label:a,isStretchy:!1,isShifty:!0,base:e}}return e}}no.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var nh=function(e,t){if(!("string"==typeof e||e instanceof String))throw TypeError("KaTeX can only parse string typed expression");let r=new no(e,t);delete r.gullet.macros.current["\\df@tag"];let n=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new l("\\tag works only in display equations");n=[{type:"tag",mode:"text",body:n,tag:r.subparse([new ru("\\df@tag")])}]}return n};let nm=function(e,t,r){t.textContent="";let n=np(e,r).toNode();t.appendChild(n)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),nm=function(){throw new l("KaTeX doesn't work in quirks mode.")});let nc=function(e,t,r){if(r.throwOnError||!(e instanceof l))throw e;let n=eP.makeSpan(["katex-error"],[new Q(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+r.errorColor),n},np=function(e,t){let r=new p(t);try{let t=nh(e,r);return ty(t,e,r)}catch(t){return nc(t,e,r)}};var nu={version:"0.16.22",render:nm,renderToString:function(e,t){return np(e,t).toMarkup()},ParseError:l,SETTINGS_SCHEMA:c,__parse:function(e,t){return nh(e,new p(t))},__renderToDomTree:np,__renderToHTMLTree:function(e,t){let r=new p(t);try{let t=nh(e,r);return tx(t,e,r)}catch(t){return nc(t,e,r)}},__setFontMetrics:function(e,t){q[e]=t},__defineSymbol:es,__defineFunction:e$,__defineMacro:function(e,t){rc[e]=t},__domTree:{Span:$,Anchor:Z,SymbolNode:Q,SvgNode:ee,PathNode:et,LineNode:er}};return n.default}()}}]);