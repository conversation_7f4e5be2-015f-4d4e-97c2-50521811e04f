"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1165],{25519:(e,n,t)=>{t.d(n,{n:()=>c});var r=t(12115),o=t(6101),l=t(63540),a=t(39033),u=t(95155),i="focusScope.autoFocusOnMount",d="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},c=r.forwardRef((e,n)=>{let{loop:t=!1,trapped:c=!1,onMountAutoFocus:h,onUnmountAutoFocus:g,...w}=e,[y,x]=r.useState(null),b=(0,a.c)(h),C=(0,a.c)(g),E=r.useRef(null),R=(0,o.s)(n,e=>x(e)),j=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(c){let e=function(e){if(j.paused||!y)return;let n=e.target;y.contains(n)?E.current=n:v(E.current,{select:!0})},n=function(e){if(j.paused||!y)return;let n=e.relatedTarget;null===n||y.contains(n)||v(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",n);let t=new MutationObserver(function(e){if(document.activeElement===document.body)for(let n of e)n.removedNodes.length>0&&v(y)});return y&&t.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",n),t.disconnect()}}},[c,y,j.paused]),r.useEffect(()=>{if(y){m.add(j);let e=document.activeElement;if(!y.contains(e)){let n=new CustomEvent(i,s);y.addEventListener(i,b),y.dispatchEvent(n),n.defaultPrevented||(function(e){let{select:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=document.activeElement;for(let r of e)if(v(r,{select:n}),document.activeElement!==t)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(y))}return()=>{y.removeEventListener(i,b),setTimeout(()=>{let n=new CustomEvent(d,s);y.addEventListener(d,C),y.dispatchEvent(n),n.defaultPrevented||v(null!=e?e:document.body,{select:!0}),y.removeEventListener(d,C),m.remove(j)},0)}}},[y,b,C,j]);let M=r.useCallback(e=>{if(!t&&!c||j.paused)return;let n="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(n&&r){let n=e.currentTarget,[o,l]=function(e){let n=f(e);return[p(n,e),p(n.reverse(),e)]}(n);o&&l?e.shiftKey||r!==l?e.shiftKey&&r===o&&(e.preventDefault(),t&&v(l,{select:!0})):(e.preventDefault(),t&&v(o,{select:!0})):r===n&&e.preventDefault()}},[t,c,j.paused]);return(0,u.jsx)(l.sG.div,{tabIndex:-1,...w,ref:R,onKeyDown:M})});function f(e){let n=[],t=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;t.nextNode();)n.push(t.currentNode);return n}function p(e,n){for(let t of e)if(!function(e,n){let{upTo:t}=n;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(t,{upTo:n}))return t}function v(e){let{select:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var t;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(t=e)instanceof HTMLInputElement&&"select"in t&&n&&e.select()}}c.displayName="FocusScope";var m=function(){let e=[];return{add(n){let t=e[0];n!==t&&(null==t||t.pause()),(e=h(e,n)).unshift(n)},remove(n){var t;null===(t=(e=h(e,n))[0])||void 0===t||t.resume()}}}();function h(e,n){let t=[...e],r=t.indexOf(n);return -1!==r&&t.splice(r,1),t}},66634:(e,n,t)=>{t.d(n,{Slot:()=>u,TL:()=>a,Dc:()=>d});var r=t(12115);function o(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}var l=t(95155);function a(e){let n=function(e){let n=r.forwardRef((e,n)=>{var t,l,a;let u,i;let{children:d,...s}=e,c=function(...e){return r.useCallback(function(...e){return n=>{let t=!1,r=e.map(e=>{let r=o(e,n);return t||"function"!=typeof r||(t=!0),r});if(t)return()=>{for(let n=0;n<r.length;n++){let t=r[n];"function"==typeof t?t():o(e[n],null)}}}}(...e),e)}(r.isValidElement(d)?(i=(u=null===(l=Object.getOwnPropertyDescriptor((t=d).props,"ref"))||void 0===l?void 0:l.get)&&"isReactWarning"in u&&u.isReactWarning)?t.ref:(i=(u=null===(a=Object.getOwnPropertyDescriptor(t,"ref"))||void 0===a?void 0:a.get)&&"isReactWarning"in u&&u.isReactWarning)?t.props.ref:t.props.ref||t.ref:void 0,n);if(r.isValidElement(d)){let e=function(e,n){let t={...n};for(let r in n){let o=e[r],l=n[r];/^on[A-Z]/.test(r)?o&&l?t[r]=function(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];let r=l(...n);return o(...n),r}:o&&(t[r]=o):"style"===r?t[r]={...o,...l}:"className"===r&&(t[r]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(s,d.props);return d.type!==r.Fragment&&(e.ref=c),r.cloneElement(d,e)}return r.Children.count(d)>1?r.Children.only(null):null});return n.displayName="".concat(e,".SlotClone"),n}(e),t=r.forwardRef((e,t)=>{let{children:o,...a}=e,u=r.Children.toArray(o),i=u.find(s);if(i){let e=i.props.children,o=u.map(n=>n!==i?n:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(n,{...a,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(n,{...a,ref:t,children:o})});return t.displayName="".concat(e,".Slot"),t}var u=a("Slot"),i=Symbol("radix.slottable");function d(e){let n=e=>{let{children:n}=e;return(0,l.jsx)(l.Fragment,{children:n})};return n.displayName="".concat(e,".Slottable"),n.__radixId=i,n}function s(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},74466:(e,n,t)=>{t.d(n,{F:()=>a});var r=t(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=r.$,a=(e,n)=>t=>{var r;if((null==n?void 0:n.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:u}=n,i=Object.keys(a).map(e=>{let n=null==t?void 0:t[e],r=null==u?void 0:u[e];if(null===n)return null;let l=o(n)||o(r);return a[e][l]}),d=t&&Object.entries(t).reduce((e,n)=>{let[t,r]=n;return void 0===r||(e[t]=r),e},{});return l(e,i,null==n?void 0:null===(r=n.compoundVariants)||void 0===r?void 0:r.reduce((e,n)=>{let{class:t,className:r,...o}=n;return Object.entries(o).every(e=>{let[n,t]=e;return Array.isArray(t)?t.includes({...u,...d}[n]):({...u,...d})[n]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},76215:(e,n,t)=>{t.d(n,{UC:()=>eY,q7:()=>eJ,JU:()=>e$,ZL:()=>ez,bL:()=>eX,wv:()=>eQ,l9:()=>eZ});var r=t(12115),o=t(85185),l=t(6101),a=t(46081),u=t(5845),i=t(63540),d=t(76589),s=t(94315),c=t(19178),f=t(92293),p=t(25519),v=t(61285),m=t(22197),h=t(34378),g=t(28905),w=t(89196),y=t(95155),x=r.forwardRef((e,n)=>{let{children:t,...o}=e,l=r.Children.toArray(t),a=l.find(E);if(a){let e=a.props.children,t=l.map(n=>n!==a?n:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,y.jsx)(b,{...o,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,t):null})}return(0,y.jsx)(b,{...o,ref:n,children:t})});x.displayName="Slot";var b=r.forwardRef((e,n)=>{let{children:t,...o}=e;if(r.isValidElement(t)){let e=function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=n&&"isReactWarning"in n&&n.isReactWarning;return t?e.ref:(t=(n=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(t),a=function(e,n){let t={...n};for(let r in n){let o=e[r],l=n[r];/^on[A-Z]/.test(r)?o&&l?t[r]=(...e)=>{l(...e),o(...e)}:o&&(t[r]=o):"style"===r?t[r]={...o,...l}:"className"===r&&(t[r]=[o,l].filter(Boolean).join(" "))}return{...e,...t}}(o,t.props);return t.type!==r.Fragment&&(a.ref=n?(0,l.t)(n,e):e),r.cloneElement(t,a)}return r.Children.count(t)>1?r.Children.only(null):null});b.displayName="SlotClone";var C=({children:e})=>(0,y.jsx)(y.Fragment,{children:e});function E(e){return r.isValidElement(e)&&e.type===C}var R=t(39033),j=t(38168),M=t(31114),D=["Enter"," "],_=["ArrowUp","PageDown","End"],I=["ArrowDown","PageUp","Home",..._],k={ltr:[...D,"ArrowRight"],rtl:[...D,"ArrowLeft"]},N={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[S,T,F]=(0,d.N)(P),[A,O]=(0,a.A)(P,[F,m.Bk,w.RG]),L=(0,m.Bk)(),K=(0,w.RG)(),[G,V]=A(P),[U,B]=A(P),W=e=>{let{__scopeMenu:n,open:t=!1,children:o,dir:l,onOpenChange:a,modal:u=!0}=e,i=L(n),[d,c]=r.useState(null),f=r.useRef(!1),p=(0,R.c)(a),v=(0,s.jH)(l);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",n,{capture:!0,once:!0}),document.addEventListener("pointermove",n,{capture:!0,once:!0})},n=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",n,{capture:!0}),document.removeEventListener("pointermove",n,{capture:!0})}},[]),(0,y.jsx)(m.bL,{...i,children:(0,y.jsx)(G,{scope:n,open:t,onOpenChange:p,content:d,onContentChange:c,children:(0,y.jsx)(U,{scope:n,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:u,children:o})})})};W.displayName=P;var q=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e,o=L(t);return(0,y.jsx)(m.Mz,{...o,...r,ref:n})});q.displayName="MenuAnchor";var H="MenuPortal",[X,Z]=A(H,{forceMount:void 0}),z=e=>{let{__scopeMenu:n,forceMount:t,children:r,container:o}=e,l=V(H,n);return(0,y.jsx)(X,{scope:n,forceMount:t,children:(0,y.jsx)(g.C,{present:t||l.open,children:(0,y.jsx)(h.Z,{asChild:!0,container:o,children:r})})})};z.displayName=H;var Y="MenuContent",[$,J]=A(Y),Q=r.forwardRef((e,n)=>{let t=Z(Y,e.__scopeMenu),{forceMount:r=t.forceMount,...o}=e,l=V(Y,e.__scopeMenu),a=B(Y,e.__scopeMenu);return(0,y.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(g.C,{present:r||l.open,children:(0,y.jsx)(S.Slot,{scope:e.__scopeMenu,children:a.modal?(0,y.jsx)(ee,{...o,ref:n}):(0,y.jsx)(en,{...o,ref:n})})})})}),ee=r.forwardRef((e,n)=>{let t=V(Y,e.__scopeMenu),a=r.useRef(null),u=(0,l.s)(n,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,j.Eq)(e)},[]),(0,y.jsx)(et,{...e,ref:u,trapFocus:t.open,disableOutsidePointerEvents:t.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>t.onOpenChange(!1)})}),en=r.forwardRef((e,n)=>{let t=V(Y,e.__scopeMenu);return(0,y.jsx)(et,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>t.onOpenChange(!1)})}),et=r.forwardRef((e,n)=>{let{__scopeMenu:t,loop:a=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:d,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:C,onDismiss:E,disableOutsideScroll:R,...j}=e,D=V(Y,t),k=B(Y,t),N=L(t),P=K(t),S=T(t),[F,A]=r.useState(null),O=r.useRef(null),G=(0,l.s)(n,O,D.onContentChange),U=r.useRef(0),W=r.useRef(""),q=r.useRef(0),H=r.useRef(null),X=r.useRef("right"),Z=r.useRef(0),z=R?M.A:r.Fragment,J=R?{as:x,allowPinchZoom:!0}:void 0,Q=e=>{var n,t;let r=W.current+e,o=S().filter(e=>!e.disabled),l=document.activeElement,a=null===(n=o.find(e=>e.ref.current===l))||void 0===n?void 0:n.textValue,u=function(e,n,t){var r;let o=n.length>1&&Array.from(n).every(e=>e===n[0])?n[0]:n,l=(r=Math.max(t?e.indexOf(t):-1,0),e.map((n,t)=>e[(r+t)%e.length]));1===o.length&&(l=l.filter(e=>e!==t));let a=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==t?a:void 0}(o.map(e=>e.textValue),r,a),i=null===(t=o.find(e=>e.textValue===u))||void 0===t?void 0:t.ref.current;!function e(n){W.current=n,window.clearTimeout(U.current),""!==n&&(U.current=window.setTimeout(()=>e(""),1e3))}(r),i&&setTimeout(()=>i.focus())};r.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,f.Oh)();let ee=r.useCallback(e=>{var n,t;return X.current===(null===(n=H.current)||void 0===n?void 0:n.side)&&function(e,n){return!!n&&function(e,n){let{x:t,y:r}=e,o=!1;for(let e=0,l=n.length-1;e<n.length;l=e++){let a=n[e].x,u=n[e].y,i=n[l].x,d=n[l].y;u>r!=d>r&&t<(i-a)*(r-u)/(d-u)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},n)}(e,null===(t=H.current)||void 0===t?void 0:t.area)},[]);return(0,y.jsx)($,{scope:t,searchRef:W,onItemEnter:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:r.useCallback(e=>{var n;ee(e)||(null===(n=O.current)||void 0===n||n.focus(),A(null))},[ee]),onTriggerLeave:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:q,onPointerGraceIntentChange:r.useCallback(e=>{H.current=e},[]),children:(0,y.jsx)(z,{...J,children:(0,y.jsx)(p.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var n;e.preventDefault(),null===(n=O.current)||void 0===n||n.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:(0,y.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:C,onDismiss:E,children:(0,y.jsx)(w.bL,{asChild:!0,...P,dir:k.dir,orientation:"vertical",loop:a,currentTabStopId:F,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(v,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,y.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":e_(D.open),"data-radix-menu-content":"",dir:k.dir,...N,...j,ref:G,style:{outline:"none",...j.style},onKeyDown:(0,o.m)(j.onKeyDown,e=>{let n=e.target.closest("[data-radix-menu-content]")===e.currentTarget,t=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;n&&("Tab"===e.key&&e.preventDefault(),!t&&r&&Q(e.key));let o=O.current;if(e.target!==o||!I.includes(e.key))return;e.preventDefault();let l=S().filter(e=>!e.disabled).map(e=>e.ref.current);_.includes(e.key)&&l.reverse(),function(e){let n=document.activeElement;for(let t of e)if(t===n||(t.focus(),document.activeElement!==n))return}(l)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),W.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eN(e=>{let n=e.target,t=Z.current!==e.clientX;e.currentTarget.contains(n)&&t&&(X.current=e.clientX>Z.current?"right":"left",Z.current=e.clientX)}))})})})})})})});Q.displayName=Y;var er=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e;return(0,y.jsx)(i.sG.div,{role:"group",...r,ref:n})});er.displayName="MenuGroup";var eo=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e;return(0,y.jsx)(i.sG.div,{...r,ref:n})});eo.displayName="MenuLabel";var el="MenuItem",ea="menu.itemSelect",eu=r.forwardRef((e,n)=>{let{disabled:t=!1,onSelect:a,...u}=e,d=r.useRef(null),s=B(el,e.__scopeMenu),c=J(el,e.__scopeMenu),f=(0,l.s)(n,d),p=r.useRef(!1);return(0,y.jsx)(ei,{...u,ref:f,disabled:t,onClick:(0,o.m)(e.onClick,()=>{let e=d.current;if(!t&&e){let n=new CustomEvent(ea,{bubbles:!0,cancelable:!0});e.addEventListener(ea,e=>null==a?void 0:a(e),{once:!0}),(0,i.hO)(e,n),n.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:n=>{var t;null===(t=e.onPointerDown)||void 0===t||t.call(e,n),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var n;p.current||null===(n=e.currentTarget)||void 0===n||n.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=""!==c.searchRef.current;!t&&(!n||" "!==e.key)&&D.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eu.displayName=el;var ei=r.forwardRef((e,n)=>{let{__scopeMenu:t,disabled:a=!1,textValue:u,...d}=e,s=J(el,t),c=K(t),f=r.useRef(null),p=(0,l.s)(n,f),[v,m]=r.useState(!1),[h,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var n;g((null!==(n=e.textContent)&&void 0!==n?n:"").trim())}},[d.children]),(0,y.jsx)(S.ItemSlot,{scope:t,disabled:a,textValue:null!=u?u:h,children:(0,y.jsx)(w.q7,{asChild:!0,...c,focusable:!a,children:(0,y.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...d,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,eN(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eN(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),ed=r.forwardRef((e,n)=>{let{checked:t=!1,onCheckedChange:r,...l}=e;return(0,y.jsx)(eg,{scope:e.__scopeMenu,checked:t,children:(0,y.jsx)(eu,{role:"menuitemcheckbox","aria-checked":eI(t)?"mixed":t,...l,ref:n,"data-state":ek(t),onSelect:(0,o.m)(l.onSelect,()=>null==r?void 0:r(!!eI(t)||!t),{checkForDefaultPrevented:!1})})})});ed.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[ec,ef]=A(es,{value:void 0,onValueChange:()=>{}}),ep=r.forwardRef((e,n)=>{let{value:t,onValueChange:r,...o}=e,l=(0,R.c)(r);return(0,y.jsx)(ec,{scope:e.__scopeMenu,value:t,onValueChange:l,children:(0,y.jsx)(er,{...o,ref:n})})});ep.displayName=es;var ev="MenuRadioItem",em=r.forwardRef((e,n)=>{let{value:t,...r}=e,l=ef(ev,e.__scopeMenu),a=t===l.value;return(0,y.jsx)(eg,{scope:e.__scopeMenu,checked:a,children:(0,y.jsx)(eu,{role:"menuitemradio","aria-checked":a,...r,ref:n,"data-state":ek(a),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null===(e=l.onValueChange)||void 0===e?void 0:e.call(l,t)},{checkForDefaultPrevented:!1})})})});em.displayName=ev;var eh="MenuItemIndicator",[eg,ew]=A(eh,{checked:!1}),ey=r.forwardRef((e,n)=>{let{__scopeMenu:t,forceMount:r,...o}=e,l=ew(eh,t);return(0,y.jsx)(g.C,{present:r||eI(l.checked)||!0===l.checked,children:(0,y.jsx)(i.sG.span,{...o,ref:n,"data-state":ek(l.checked)})})});ey.displayName=eh;var ex=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e;return(0,y.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:n})});ex.displayName="MenuSeparator";var eb=r.forwardRef((e,n)=>{let{__scopeMenu:t,...r}=e,o=L(t);return(0,y.jsx)(m.i3,{...o,...r,ref:n})});eb.displayName="MenuArrow";var[eC,eE]=A("MenuSub"),eR="MenuSubTrigger",ej=r.forwardRef((e,n)=>{let t=V(eR,e.__scopeMenu),a=B(eR,e.__scopeMenu),u=eE(eR,e.__scopeMenu),i=J(eR,e.__scopeMenu),d=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:c}=i,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),c(null)}},[s,c]),(0,y.jsx)(q,{asChild:!0,...f,children:(0,y.jsx)(ei,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":t.open,"aria-controls":u.contentId,"data-state":e_(t.open),...e,ref:(0,l.t)(n,u.onTriggerChange),onClick:n=>{var r;null===(r=e.onClick)||void 0===r||r.call(e,n),e.disabled||n.defaultPrevented||(n.currentTarget.focus(),t.open||t.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eN(n=>{i.onItemEnter(n),n.defaultPrevented||e.disabled||t.open||d.current||(i.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{t.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eN(e=>{var n,r;p();let o=null===(n=t.content)||void 0===n?void 0:n.getBoundingClientRect();if(o){let n=null===(r=t.content)||void 0===r?void 0:r.dataset.side,l="right"===n,a=o[l?"left":"right"],u=o[l?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(l?-5:5),y:e.clientY},{x:a,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:a,y:o.bottom}],side:n}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,n=>{let r=""!==i.searchRef.current;if(!e.disabled&&(!r||" "!==n.key)&&k[a.dir].includes(n.key)){var o;t.onOpenChange(!0),null===(o=t.content)||void 0===o||o.focus(),n.preventDefault()}})})})});ej.displayName=eR;var eM="MenuSubContent",eD=r.forwardRef((e,n)=>{let t=Z(Y,e.__scopeMenu),{forceMount:a=t.forceMount,...u}=e,i=V(Y,e.__scopeMenu),d=B(Y,e.__scopeMenu),s=eE(eM,e.__scopeMenu),c=r.useRef(null),f=(0,l.s)(n,c);return(0,y.jsx)(S.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(g.C,{present:a||i.open,children:(0,y.jsx)(S.Slot,{scope:e.__scopeMenu,children:(0,y.jsx)(et,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:f,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var n;d.isUsingKeyboardRef.current&&(null===(n=c.current)||void 0===n||n.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let n=e.currentTarget.contains(e.target),t=N[d.dir].includes(e.key);if(n&&t){var r;i.onOpenChange(!1),null===(r=s.trigger)||void 0===r||r.focus(),e.preventDefault()}})})})})})});function e_(e){return e?"open":"closed"}function eI(e){return"indeterminate"===e}function ek(e){return eI(e)?"indeterminate":e?"checked":"unchecked"}function eN(e){return n=>"mouse"===n.pointerType?e(n):void 0}eD.displayName=eM;var eP="DropdownMenu",[eS,eT]=(0,a.A)(eP,[O]),eF=O(),[eA,eO]=eS(eP),eL=e=>{let{__scopeDropdownMenu:n,children:t,dir:o,open:l,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,s=eF(n),c=r.useRef(null),[f=!1,p]=(0,u.i)({prop:l,defaultProp:a,onChange:i});return(0,y.jsx)(eA,{scope:n,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:d,children:(0,y.jsx)(W,{...s,open:f,onOpenChange:p,dir:o,modal:d,children:t})})};eL.displayName=eP;var eK="DropdownMenuTrigger",eG=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,disabled:r=!1,...a}=e,u=eO(eK,t),d=eF(t);return(0,y.jsx)(q,{asChild:!0,...d,children:(0,y.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,l.t)(n,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eG.displayName=eK;var eV=e=>{let{__scopeDropdownMenu:n,...t}=e,r=eF(n);return(0,y.jsx)(z,{...r,...t})};eV.displayName="DropdownMenuPortal";var eU="DropdownMenuContent",eB=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...l}=e,a=eO(eU,t),u=eF(t),i=r.useRef(!1);return(0,y.jsx)(Q,{id:a.contentId,"aria-labelledby":a.triggerId,...u,...l,ref:n,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;i.current||null===(n=a.triggerRef.current)||void 0===n||n.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey,r=2===n.button||t;(!a.modal||r)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eU,r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(er,{...o,...r,ref:n})}).displayName="DropdownMenuGroup";var eW=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(eo,{...o,...r,ref:n})});eW.displayName="DropdownMenuLabel";var eq=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(eu,{...o,...r,ref:n})});eq.displayName="DropdownMenuItem",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(ed,{...o,...r,ref:n})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(ep,{...o,...r,ref:n})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(em,{...o,...r,ref:n})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(ey,{...o,...r,ref:n})}).displayName="DropdownMenuItemIndicator";var eH=r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(ex,{...o,...r,ref:n})});eH.displayName="DropdownMenuSeparator",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(eb,{...o,...r,ref:n})}).displayName="DropdownMenuArrow",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(ej,{...o,...r,ref:n})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,n)=>{let{__scopeDropdownMenu:t,...r}=e,o=eF(t);return(0,y.jsx)(eD,{...o,...r,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eX=eL,eZ=eG,ez=eV,eY=eB,e$=eW,eJ=eq,eQ=eH},89196:(e,n,t)=>{t.d(n,{RG:()=>b,bL:()=>k,q7:()=>N});var r=t(12115),o=t(85185),l=t(76589),a=t(6101),u=t(46081),i=t(61285),d=t(63540),s=t(39033),c=t(5845),f=t(94315),p=t(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,w,y]=(0,l.N)(h),[x,b]=(0,u.A)(h,[y]),[C,E]=x(h),R=r.forwardRef((e,n)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:n})})}));R.displayName=h;var j=r.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:t,orientation:l,loop:u=!1,dir:i,currentTabStopId:h,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:x,preventScrollOnEntryFocus:b=!1,...E}=e,R=r.useRef(null),j=(0,a.s)(n,R),M=(0,f.jH)(i),[D=null,_]=(0,c.i)({prop:h,defaultProp:g,onChange:y}),[k,N]=r.useState(!1),P=(0,s.c)(x),S=w(t),T=r.useRef(!1),[F,A]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,P),()=>e.removeEventListener(v,P)},[P]),(0,p.jsx)(C,{scope:t,orientation:l,dir:M,loop:u,currentTabStopId:D,onItemFocus:r.useCallback(e=>_(e),[_]),onItemShiftTab:r.useCallback(()=>N(!0),[]),onFocusableItemAdd:r.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>A(e=>e-1),[]),children:(0,p.jsx)(d.sG.div,{tabIndex:k||0===F?-1:0,"data-orientation":l,...E,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let n=!T.current;if(e.target===e.currentTarget&&n&&!k){let n=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(n),!n.defaultPrevented){let e=S().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===D),...e].filter(Boolean).map(e=>e.ref.current),b)}}T.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>N(!1))})})}),M="RovingFocusGroupItem",D=r.forwardRef((e,n)=>{let{__scopeRovingFocusGroup:t,focusable:l=!0,active:a=!1,tabStopId:u,...s}=e,c=(0,i.B)(),f=u||c,v=E(M,t),m=v.currentTabStopId===f,h=w(t),{onFocusableItemAdd:y,onFocusableItemRemove:x}=v;return r.useEffect(()=>{if(l)return y(),()=>x()},[l,y,x]),(0,p.jsx)(g.ItemSlot,{scope:t,id:f,focusable:l,active:a,children:(0,p.jsx)(d.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...s,ref:n,onMouseDown:(0,o.m)(e.onMouseDown,e=>{l?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let n=function(e,n,t){var r;let o=(r=e.key,"rtl"!==t?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===n&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===n&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,v.orientation,v.dir);if(void 0!==n){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let t=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===n)t.reverse();else if("prev"===n||"next"===n){"prev"===n&&t.reverse();let r=t.indexOf(e.currentTarget);t=v.loop?function(e,n){return e.map((t,r)=>e[(n+r)%e.length])}(t,r+1):t.slice(r+1)}setTimeout(()=>I(t))}})})})});D.displayName=M;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],t=document.activeElement;for(let r of e)if(r===t||(r.focus({preventScroll:n}),document.activeElement!==t))return}var k=R,N=D},92293:(e,n,t)=>{t.d(n,{Oh:()=>l});var r=t(12115),o=0;function l(){r.useEffect(()=>{var e,n;let t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=t[0])&&void 0!==e?e:a()),document.body.insertAdjacentElement("beforeend",null!==(n=t[1])&&void 0!==n?n:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}}}]);