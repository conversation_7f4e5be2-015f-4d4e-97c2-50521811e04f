(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[411],{3932:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ee});var a=t(95155),i=t(12115),n=t(12379),l=t(25531),r=t(59434);function c(e){let{selected:s,onClick:t,children:i,className:n,grouped:l=!1,position:c="single"}=e;return(0,a.jsxs)("button",{type:"button",onClick:t,className:(0,r.cn)("flex items-center px-4 py-2 text-sm font-medium transition-colors",l?"left"===c?"rounded-l-sm border border-gray-200":"right"===c?"rounded-r-sm border border-gray-200 border-l-0":"border border-gray-200":"rounded-sm border border-gray-200","bg-white text-gray-900",n),children:[(0,a.jsx)("div",{className:(0,r.cn)("flex h-5 w-5 items-center justify-center rounded-full mr-2",s?"bg-[#05603A]":"border border-gray-300"),children:s&&(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-white"})}),i]})}var o=t(30285),d=t(92138);function u(e){let{onNext:s,onSkip:t,onBack:i,nextDisabled:n=!1,backDisabled:l=!1,nextLabel:r="Next",skipLabel:c="Skip",showSkip:u=!0}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,a.jsxs)(o.$,{onClick:s,disabled:n,className:"w-full max-w-sm h-12 bg-[#05603A] hover:bg-[#04502F]",children:[r," ",(0,a.jsx)(d.A,{className:"ml-2 h-4 w-4"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(o.$,{variant:"ghost",onClick:i,disabled:l,className:"text-sm text-gray-500",children:"Back"}),u&&(0,a.jsx)(o.$,{variant:"ghost",onClick:t,className:"text-sm text-gray-500",children:c})]})]})}function m(e){let{formData:s,updateFormData:t,onNext:i,onSkip:n,onBack:l,backDisabled:r}=e,o=e=>{t({questionType:e})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Question Type"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Select the types of questions you want."})]}),(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-2 w-full max-w-2xl",children:[(0,a.jsx)(c,{selected:"NEET"===s.questionType,onClick:()=>o("NEET"),className:"w-full",children:"NEET"}),(0,a.jsx)(c,{selected:"CET"===s.questionType,onClick:()=>o("CET"),className:"w-full",children:"CET"}),(0,a.jsx)(c,{selected:"JEE"===s.questionType,onClick:()=>o("JEE"),className:"w-full",children:"JEE"}),(0,a.jsx)(c,{selected:"AIIMS"===s.questionType,onClick:()=>o("AIIMS"),className:"w-full",children:"AIIMS"}),(0,a.jsx)(c,{selected:"JIPMER"===s.questionType,onClick:()=>o("JIPMER"),className:"w-full",children:"JIPMER"}),(0,a.jsx)(c,{selected:"CUSTOM"===s.questionType,onClick:()=>o("CUSTOM"),className:"w-full",children:"CUSTOM"})]})}),(0,a.jsx)(u,{onNext:i,onSkip:n,onBack:l,backDisabled:r,nextDisabled:!s.questionType})]})}var x=t(81284);function p(e){let{message:s}=e;return(0,a.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-blue-100 bg-blue-50 p-4 text-sm text-blue-600",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-blue-500"}),(0,a.jsx)("p",{children:s})]})}var h=t(66695),f=t(26126),j=t(5196),b=t(54416);let g=[{display:"Physics",value:"physics"},{display:"Chemistry",value:"chemistry"},{display:"Biology",value:"biology"},{display:"Mathematics",value:"mathematics"}];function v(e){let{formData:s,updateFormData:t,onNext:i,onSkip:n,onBack:l,backDisabled:r}=e,d=e=>{t({paperMode:e,subject:"",subjects:[],subjectConfigs:{}})},m=e=>{t({subject:e})},x=e=>{let a;if(s.subjects.includes(e)){a=s.subjects.filter(s=>s!==e);let i={...s.subjectConfigs};delete i[e],t({subjects:a,subjectConfigs:i})}else t({subjects:a=[...s.subjects,e]})},v="single"===s.paperMode?!s.subject:0===s.subjects.length;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Course & Subject Selection"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Choose between single subject or multi-subject question paper"})]}),(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsxs)("div",{className:"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]",children:[(0,a.jsx)(c,{selected:"single"===s.paperMode,onClick:()=>d("single"),grouped:!0,position:"left",className:"rounded-none border-0",children:"Single Subject"}),(0,a.jsx)("div",{className:"w-px bg-gray-200"}),(0,a.jsx)(c,{selected:"multi"===s.paperMode,onClick:()=>d("multi"),grouped:!0,position:"right",className:"rounded-none border-0",children:"Multi-Subject"})]})}),"single"===s.paperMode&&(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{className:"text-lg",children:"Select Subject"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:g.map(e=>(0,a.jsx)(o.$,{variant:s.subject===e.value?"default":"outline",onClick:()=>m(e.value),className:"h-12",children:e.display},e.value))})})]}),"multi"===s.paperMode&&(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"text-lg flex items-center justify-between",children:[(0,a.jsx)("span",{children:"Select Subjects"}),s.subjects.length>0&&(0,a.jsxs)(f.E,{variant:"secondary",children:[s.subjects.length," selected"]})]})}),(0,a.jsxs)(h.Wu,{children:[(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:g.map(e=>{let t=s.subjects.includes(e.value);return(0,a.jsx)(o.$,{variant:t?"default":"outline",onClick:()=>x(e.value),className:"h-12 relative",children:(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[e.display,t&&(0,a.jsx)(j.A,{className:"h-4 w-4"})]})},e.value)})}),s.subjects.length>0&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-green-50 rounded-md",children:[(0,a.jsx)("p",{className:"text-sm text-green-700 font-medium",children:"Selected Subjects:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:s.subjects.map(e=>{let s=g.find(s=>s.value===e);return(0,a.jsxs)(f.E,{variant:"secondary",className:"flex items-center gap-1",children:[(null==s?void 0:s.display)||e,(0,a.jsx)("button",{onClick:()=>x(e),className:"ml-1 hover:bg-gray-200 rounded-full p-0.5",children:(0,a.jsx)(b.A,{className:"h-3 w-3"})})]},e)})})]})]})]}),(0,a.jsx)(u,{onNext:i,onSkip:n,onBack:l,backDisabled:r,nextDisabled:v}),v&&(0,a.jsx)(p,{message:"single"===s.paperMode?"Please select a subject before proceeding.":"Please select at least one subject before proceeding."})]})}var N=t(87712),y=t(84616);function k(e){let{formData:s,updateFormData:t,onNext:i,onSkip:n,onBack:l,backDisabled:r}=e,d=e=>{"auto"===e?t({difficultyMode:e,difficultyLevels:{easyPercentage:30,mediumPercentage:50,hardPercentage:20}}):t({difficultyMode:e})},m=(e,a)=>{let i=Math.max(0,Math.min(100,s.difficultyLevels[e]+a));t({difficultyLevels:{...s.difficultyLevels,[e]:i}})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Select Difficulty Level"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Choose the complexity of your questions: Easy, Medium, Hard, or Mixed"})]}),(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsxs)("div",{className:"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]",children:[(0,a.jsx)(c,{selected:"auto"===s.difficultyMode,onClick:()=>d("auto"),grouped:!0,position:"left",className:"rounded-none border-0",children:"Auto generation"}),(0,a.jsx)("div",{className:"w-px bg-gray-200"}),(0,a.jsx)(c,{selected:"custom"===s.difficultyMode,onClick:()=>d("custom"),grouped:!0,position:"right",className:"rounded-none border-0",children:"Customization"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-left font-medium",children:"Easy"}),(0,a.jsxs)("div",{className:"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>m("easyPercentage",-10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===s.difficultyMode,children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"w-[50px] text-center font-medium",children:[s.difficultyLevels.easyPercentage,"%"]}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>m("easyPercentage",10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===s.difficultyMode,children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-left font-medium",children:"Medium"}),(0,a.jsxs)("div",{className:"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>m("mediumPercentage",-10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===s.difficultyMode,children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"w-[50px] text-center font-medium",children:[s.difficultyLevels.mediumPercentage,"%"]}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>m("mediumPercentage",10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===s.difficultyMode,children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-left font-medium",children:"Hard"}),(0,a.jsxs)("div",{className:"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>m("hardPercentage",-10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===s.difficultyMode,children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"w-[50px] text-center font-medium",children:[s.difficultyLevels.hardPercentage,"%"]}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>m("hardPercentage",10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===s.difficultyMode,children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(u,{onNext:i,onSkip:n,onBack:l,backDisabled:r}),(0,a.jsx)(p,{message:"Please select a difficulty level before proceeding. Your choice will determine the complexity of the questions generated."})]})}var w=t(62523);function C(e){let{formData:s,updateFormData:t,onNext:i,onSkip:n,onBack:l,backDisabled:r}=e,c=e=>{t({numberOfQuestions:Math.max(1,s.numberOfQuestions+e)})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Question Selection Criteria"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Define the number of questions and total marks. Please select the number of questions to proceed."})]}),(0,a.jsxs)("div",{className:"space-y-4 text-center py-4",children:[(0,a.jsx)("p",{className:"text-left font-medium",children:"Select number of Questions"}),(0,a.jsxs)("div",{className:"flex max-w-[600px]",children:[(0,a.jsx)(w.p,{type:"number",value:0===s.numberOfQuestions?"":s.numberOfQuestions,onChange:e=>{let s=e.target.value;if(""===s)t({numberOfQuestions:0});else{let e=parseInt(s);!isNaN(e)&&e>=1&&t({numberOfQuestions:e})}},onBlur:e=>{(""===e.target.value||0===s.numberOfQuestions)&&t({numberOfQuestions:1})},className:"rounded-l-sm border-[#E5E7EB] h-[54px] text-lg",min:1}),(0,a.jsxs)("div",{className:"flex flex-col -ml-px",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>c(1),className:"rounded-none rounded-tr-sm border-[#E5E7EB] h-[27px]",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>c(-1),disabled:s.numberOfQuestions<=1,className:"rounded-none rounded-br-sm border-[#E5E7EB] h-[27px] -mt-px",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(u,{onNext:i,onSkip:n,onBack:l,backDisabled:r}),(0,a.jsx)(p,{message:"Please specify the number of questions and total marks before proceeding. This ensures accurate question selection."})]})}var S=t(2081);function E(e){let{className:s,defaultValue:t,value:n,min:l=0,max:c=100,...o}=e,[d,u]=i.useState(!1),[m,x]=i.useState(0),p=i.useMemo(()=>Array.isArray(n)?n:Array.isArray(t)?t:[l,c],[n,t,l,c]);return(0,a.jsxs)("div",{className:"relative",onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),children:[d&&(0,a.jsx)("div",{className:"absolute -top-8 transform -translate-x-1/2 bg-white rounded-md shadow-lg px-2 py-1 text-sm font-medium",style:{left:"".concat(m,"%")},children:p[0]}),(0,a.jsxs)(S.bL,{"data-slot":"slider",defaultValue:t,value:n,min:l,max:c,onMouseMove:e=>{let s=e.currentTarget.getBoundingClientRect();x((e.clientX-s.left)/s.width*100)},className:(0,r.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50",s),...o,children:[(0,a.jsx)(S.CC,{"data-slot":"slider-track",className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-gray-200",children:(0,a.jsx)(S.Q6,{"data-slot":"slider-range",className:"absolute h-full bg-[#2563EB]"})}),Array.from({length:p.length},(e,s)=>(0,a.jsx)(S.zi,{"data-slot":"slider-thumb",className:"block h-4 w-4 rounded-full border border-[#2563EB] bg-white shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#2563EB] disabled:pointer-events-none disabled:opacity-50"},s))]})]})}function M(e){let{formData:s,updateFormData:t,onNext:i,onSkip:n,onBack:l,backDisabled:r}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Paper Customization"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Customize your exam paper by selecting the desired format, structure, and preferences."})]}),(0,a.jsxs)("div",{className:"space-y-6 py-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-left font-medium",children:"Select Total Marks of paper"}),(0,a.jsxs)("div",{className:"px-4",children:[(0,a.jsx)("div",{className:"flex justify-end mb-2",children:(0,a.jsx)("span",{className:"font-medium",children:s.totalMarks})}),(0,a.jsx)(E,{defaultValue:[s.totalMarks],max:2e3,min:10,step:5,onValueChange:e=>{t({totalMarks:e[0]})},className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mt-1",children:[(0,a.jsx)("span",{children:"10 marks"}),(0,a.jsx)("span",{children:"2000 marks"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-left font-medium",children:"Select Duration (minutes)"}),(0,a.jsxs)("div",{className:"px-4",children:[(0,a.jsx)("div",{className:"flex justify-end mb-2",children:(0,a.jsxs)("span",{className:"font-medium",children:[s.duration," min (",Math.floor(s.duration/60),"h ",s.duration%60,"m)"]})}),(0,a.jsx)(E,{defaultValue:[s.duration],max:1440,min:30,step:15,onValueChange:e=>{t({duration:e[0]})},className:"w-full"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mt-1",children:[(0,a.jsx)("span",{children:"30 min"}),(0,a.jsx)("span",{children:"1440 min (24 hours)"})]})]})]})]}),(0,a.jsx)(u,{onNext:i,onSkip:n,onBack:l,backDisabled:r}),(0,a.jsx)(p,{message:"Please complete the customization settings before proceeding."})]})}function I(e){let{formData:s,updateFormData:t,onNext:i,onSkip:n,onBack:l,backDisabled:r}=e,o=e=>{t({includeAnswers:e})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Include Answers?"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Choose whether to include answers in the paper. (Default: No)"})]}),(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsxs)("div",{className:"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]",children:[(0,a.jsx)(c,{selected:!0===s.includeAnswers,onClick:()=>o(!0),grouped:!0,position:"left",className:"rounded-none border-0",children:"Yes"}),(0,a.jsx)("div",{className:"w-px bg-gray-200 min-h-full"}),(0,a.jsx)(c,{selected:!1===s.includeAnswers,onClick:()=>o(!1),grouped:!0,position:"right",className:"rounded-none border-0",children:"No"})]})}),(0,a.jsx)(u,{onNext:i,onSkip:n,onBack:l,backDisabled:r}),(0,a.jsx)(p,{message:"Please select an option before proceeding."})]})}var P=t(91788),q=t(35169);function A(e){let{formData:s,onSubmit:t,isLoading:i=!1,onBack:n}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Actions"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Finalize your selections."})]}),(0,a.jsxs)("div",{className:"flex flex-col items-center gap-4 py-4",children:[(0,a.jsxs)(o.$,{className:"w-full max-w-xs flex gap-2 bg-[#05603A] hover:bg-[#04502F]",onClick:t,disabled:i,children:[(0,a.jsx)(P.A,{className:"h-4 w-4"}),i?"Generating PDF...":"Download PDF"]}),n&&(0,a.jsxs)(o.$,{variant:"outline",className:"w-full max-w-xs flex gap-2",onClick:n,disabled:i,children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),"Back to Start"]})]})]})}var O=t(94788),Q=t(5040),D=t(40235),T=t(57434),B=t(49992),L=t(19145),_=t(30664);function z(e){let{currentStep:s,steps:t}=e;return(0,a.jsx)("div",{className:"flex justify-center mb-6",children:(e=>{let s={HelpCircle:O.A,BookOpen:Q.A,BarChart2:D.A,FileText:T.A,FileEdit:B.A,CheckSquare:L.A,FileOutput:_.A}[e]||O.A;return(0,a.jsx)(s,{className:"h-10 w-10 border border-gray-200 rounded-sm p-2",style:{border:"1px solid var(--Components-Button-White-Border-Color, #E5E7EB)"}})})(t[s].icon)})}var F=t(85057),$=t(88539);function R(e){var s;let{formData:t,updateFormData:i,onNext:n,onSkip:l,onBack:r,backDisabled:c}=e;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Paper Details"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Add a title and description for your question paper."})]}),(0,a.jsxs)("div",{className:"space-y-6 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(F.J,{htmlFor:"paper-title",className:"text-sm font-medium",children:["Paper Title ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(w.p,{id:"paper-title",type:"text",placeholder:"Enter the title of your question paper",value:t.title||"",onChange:e=>{i({title:e.target.value})},className:"w-full"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"This will appear at the top of your question paper"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"paper-description",className:"text-sm font-medium",children:"Description (Optional)"}),(0,a.jsx)($.T,{id:"paper-description",placeholder:"Add instructions, time limit, or other details for the paper",value:t.description||"",onChange:e=>{i({description:e.target.value})},className:"w-full min-h-[100px] resize-none"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"Additional instructions or information for students"})]}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"w-5 h-5 text-blue-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-blue-900",children:"Preview"}),(0,a.jsxs)("div",{className:"mt-2 text-sm text-blue-800",children:[(0,a.jsx)("div",{className:"font-semibold",children:t.title||"Your Paper Title"}),t.description&&(0,a.jsx)("div",{className:"mt-1 text-blue-700",children:t.description})]})]})]})})]}),(0,a.jsx)(u,{onNext:n,onSkip:l,onBack:r,backDisabled:c,nextDisabled:!(null===(s=t.title)||void 0===s?void 0:s.trim())})]})}var Z=t(17313),J=t(46102),U=t(50405);let W={physics:"Physics",chemistry:"Chemistry",biology:"Biology",mathematics:"Mathematics"};function H(e){let s,n,{formData:l,updateFormData:r,onNext:c,onSkip:d,onBack:m,backDisabled:j}=e,[b,g]=(0,i.useState)(l.subjects[0]||""),[v,k]=(0,i.useState)({}),[C,S]=(0,i.useState)({}),[E,M]=(0,i.useState)([]),[I,P]=(0,i.useState)(!1),q=e=>W[e]||e;(0,i.useEffect)(()=>{A()},[]);let A=async()=>{P(!0);try{let e=await (0,U.Nj)();M(e)}catch(e){console.error("Error loading subjects:",e)}finally{P(!1)}},O=async e=>{if(!v[e]&&!C[e]){S(s=>({...s,[e]:!0}));try{let{getSubjectsWithTopics:s}=await Promise.resolve().then(t.bind(t,50405)),a=(await s()).find(s=>s.name.toLowerCase()===e.toLowerCase()||s.name.toLowerCase().includes(e.toLowerCase())||e.toLowerCase().includes(s.name.toLowerCase()));if(!a){console.warn("Could not find subject: ".concat(e));return}let{getQuestionCountByTopic:i}=await t.e(4870).then(t.bind(t,14870)),n=await Promise.all(a.topics.map(async e=>{let s=await i(e._id,a._id);return{_id:e._id,name:e.name,subjectId:a._id,description:e.description,questionCount:s}}));k(s=>({...s,[e]:n}))}catch(s){console.error("Error loading topics for ".concat(e,":"),s)}finally{S(s=>({...s,[e]:!1}))}}};(0,i.useEffect)(()=>{b&&E.length>0&&O(b)},[b,E]);let Q=e=>l.subjectConfigs[e]?l.subjectConfigs[e]:{subject:e,difficultyMode:"auto",difficultyLevels:{easyPercentage:30,mediumPercentage:50,hardPercentage:20},numberOfQuestions:10,totalMarks:40,topicId:void 0,chapters:[]},D=(e,s)=>{let t={...l.subjectConfigs[e]||Q(e),...s};r({subjectConfigs:{...l.subjectConfigs,[e]:t}})},T=(e,s)=>{let t=l.subjectConfigs[e]||Q(e),a={chapterId:s._id,chapterName:s.name,numberOfQuestions:1,totalMarks:4};D(e,{chapters:[...t.chapters||[],a]})},B=(e,s)=>{let t=((l.subjectConfigs[e]||Q(e)).chapters||[]).filter(e=>e.chapterId!==s);D(e,{chapters:t})},L=(e,s,t)=>{let a=((l.subjectConfigs[e]||Q(e)).chapters||[]).map(e=>e.chapterId===s?{...e,...t}:e);D(e,{chapters:a})},_=(e,s)=>{var t;let a=l.subjectConfigs[e];return(null==a?void 0:null===(t=a.chapters)||void 0===t?void 0:t.some(e=>e.chapterId===s))||!1},z=(e,s,t)=>{let a=l.subjectConfigs[e]||Q(e),i=Math.max(0,Math.min(100,a.difficultyLevels[s]+t));D(e,{difficultyLevels:{...a.difficultyLevels,[s]:i}})},{totalQuestions:$,totalMarks:R}=(s=0,n=0,l.subjects.forEach(e=>{let t=l.subjectConfigs[e]||Q(e);s+=t.numberOfQuestions,n+=t.totalMarks}),{totalQuestions:s,totalMarks:n}),H=l.subjects.every(e=>{let s=l.subjectConfigs[e]||Q(e);return 100===s.difficultyLevels.easyPercentage+s.difficultyLevels.mediumPercentage+s.difficultyLevels.hardPercentage&&s.numberOfQuestions>0&&s.totalMarks>0});return(0,i.useEffect)(()=>{if(l.subjects.length>0&&0===Object.keys(l.subjectConfigs).length){let e={};l.subjects.forEach(s=>{e[s]=Q(s)}),r({subjectConfigs:e})}},[l.subjects,l.subjectConfigs,r]),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Configure Each Subject"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Set difficulty levels, number of questions, and marks for each selected subject"})]}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{className:"text-lg",children:"Paper Summary"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:l.subjects.length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Subjects"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:$}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Questions"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:R}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Marks"})]})]})})]}),(0,a.jsxs)(Z.tU,{value:b,onValueChange:g,className:"w-full",children:[(0,a.jsx)(Z.j7,{className:"grid w-full grid-cols-2 md:grid-cols-4",children:l.subjects.map(e=>(0,a.jsxs)(Z.Xi,{value:e,className:"text-sm",children:[q(e),l.subjectConfigs[e]&&(0,a.jsxs)(f.E,{variant:"secondary",className:"ml-1 text-xs",children:[l.subjectConfigs[e].numberOfQuestions,"Q"]})]},e))}),l.subjects.map(e=>{var s,t;let i=l.subjectConfigs[e]||Q(e),n=i.difficultyLevels.easyPercentage+i.difficultyLevels.mediumPercentage+i.difficultyLevels.hardPercentage;return(0,a.jsx)(Z.av,{value:e,className:"space-y-6",children:(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("span",{children:[q(e)," Configuration"]}),(0,a.jsx)(f.E,{variant:100===n?"default":"destructive",children:100===n?"Valid":"".concat(n,"% (Need 100%)")})]})}),(0,a.jsxs)(h.Wu,{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{htmlFor:"questions-".concat(e),children:"Number of Questions"}),(0,a.jsx)(w.p,{id:"questions-".concat(e),type:"number",min:"1",max:"200",value:0===i.numberOfQuestions?"":i.numberOfQuestions,onChange:s=>{let t=s.target.value;if(""===t)D(e,{numberOfQuestions:0,totalMarks:0});else{let s=parseInt(t);!isNaN(s)&&s>=1&&D(e,{numberOfQuestions:s,totalMarks:4*s})}},onBlur:s=>{(""===s.target.value||0===i.numberOfQuestions)&&D(e,{numberOfQuestions:1,totalMarks:4})}})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(F.J,{children:"Difficulty Distribution"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{className:"text-sm font-medium",children:"Easy"}),(0,a.jsxs)("div",{className:"flex items-center rounded-sm border border-gray-200 p-1",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>z(e,"easyPercentage",-5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"flex-1 text-center font-medium",children:[i.difficultyLevels.easyPercentage,"%"]}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>z(e,"easyPercentage",5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{className:"text-sm font-medium",children:"Medium"}),(0,a.jsxs)("div",{className:"flex items-center rounded-sm border border-gray-200 p-1",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>z(e,"mediumPercentage",-5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"flex-1 text-center font-medium",children:[i.difficultyLevels.mediumPercentage,"%"]}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>z(e,"mediumPercentage",5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(F.J,{className:"text-sm font-medium",children:"Hard"}),(0,a.jsxs)("div",{className:"flex items-center rounded-sm border border-gray-200 p-1",children:[(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>z(e,"hardPercentage",-5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,a.jsx)(N.A,{className:"h-4 w-4"})}),(0,a.jsxs)("span",{className:"flex-1 text-center font-medium",children:[i.difficultyLevels.hardPercentage,"%"]}),(0,a.jsx)(o.$,{variant:"outline",size:"icon",onClick:()=>z(e,"hardPercentage",5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(F.J,{children:"Chapter Selection (Optional)"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Select specific chapters to generate questions from. If no chapters are selected, questions will be randomly selected from the entire subject."}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(F.J,{className:"text-sm font-medium",children:"Available Chapters"}),C[e]?(0,a.jsx)("div",{className:"text-center py-4 text-sm text-gray-500",children:"Loading chapters..."}):(null===(s=v[e])||void 0===s?void 0:s.length)===0?(0,a.jsx)("div",{className:"text-center py-4 text-sm text-gray-500",children:"No chapters available for this subject."}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:null===(t=v[e])||void 0===t?void 0:t.map(s=>(0,a.jsx)(J.Bc,{children:(0,a.jsxs)(J.m_,{children:[(0,a.jsx)(J.k$,{asChild:!0,children:(0,a.jsxs)(o.$,{variant:_(e,s._id)?"default":"outline",onClick:()=>{_(e,s._id)?B(e,s._id):T(e,s)},className:"h-auto p-3 flex flex-col items-start space-y-1 text-left",disabled:0===s.questionCount,children:[(0,a.jsx)("div",{className:"font-medium",children:s.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(f.E,{variant:"secondary",className:"text-xs",children:[s.questionCount," unused"]}),(0,a.jsx)(x.A,{className:"h-3 w-3"})]})]})}),(0,a.jsxs)(J.ZI,{children:[(0,a.jsxs)("p",{children:[s.questionCount," unused questions available in ",s.name]}),(0,a.jsx)("p",{className:"text-xs mt-1 text-gray-400",children:"Questions not used in previous papers"}),s.description&&(0,a.jsx)("p",{className:"text-xs mt-1",children:s.description})]})]})},s._id))})]}),i.chapters&&i.chapters.length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(F.J,{className:"text-sm font-medium",children:"Selected Chapters Configuration"}),i.chapters.map(s=>{var t;let i=null===(t=v[e])||void 0===t?void 0:t.find(e=>e._id===s.chapterId),n=(null==i?void 0:i.questionCount)||0;return(0,a.jsxs)("div",{className:"border rounded-lg p-3 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h5",{className:"font-medium text-sm",children:s.chapterName}),(0,a.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>B(e,s.chapterId),children:(0,a.jsx)(N.A,{className:"h-3 w-3"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-3",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)(F.J,{className:"text-xs",children:["Questions (Max: ",n,")"]}),(0,a.jsx)(w.p,{type:"number",min:"1",max:n,value:0===s.numberOfQuestions?"":s.numberOfQuestions,onChange:t=>{let a=t.target.value;if(""===a)L(e,s.chapterId,{numberOfQuestions:0,totalMarks:0});else{let t=parseInt(a);!isNaN(t)&&t>=1&&t<=n&&L(e,s.chapterId,{numberOfQuestions:t,totalMarks:4*t})}},onBlur:t=>{(""===t.target.value||0===s.numberOfQuestions)&&L(e,s.chapterId,{numberOfQuestions:1,totalMarks:4})},className:"h-8 text-sm"})]})})]},s.chapterId)})]})]})]})]})},e)})]}),(0,a.jsx)(u,{onNext:c,onSkip:d,onBack:m,backDisabled:j,nextDisabled:!H}),!H&&(0,a.jsx)(p,{message:"Please ensure all subjects have valid configurations (difficulty percentages must sum to 100% and questions/marks must be greater than 0)."})]})}function G(e){let{formData:s,updateFormData:n,onNext:l,onSkip:r,onBack:c,backDisabled:d}=e,[m,j]=(0,i.useState)([]),[b,g]=(0,i.useState)(!1),[v,y]=(0,i.useState)([]);(0,i.useEffect)(()=>{s.subject&&k()},[s.subject]);let k=async()=>{if(s.subject){g(!0);try{let{getSubjectsWithTopics:e}=await Promise.resolve().then(t.bind(t,50405)),a=await e(),i=a.find(e=>e.name.toLowerCase()===s.subject.toLowerCase()||e.name.toLowerCase().includes(s.subject.toLowerCase())||s.subject.toLowerCase().includes(e.name.toLowerCase()));if(!i){console.warn("Could not find subject: ".concat(s.subject,". Available subjects:"),a.map(e=>e.name));return}let{getQuestionCountByTopic:n}=await t.e(4870).then(t.bind(t,14870)),l=await Promise.all(i.topics.map(async e=>{try{let s=await n(e._id,i._id);return{_id:e._id,name:e.name,subjectId:i._id,description:e.description,questionCount:s}}catch(s){return console.error("Error fetching total count for topic ".concat(e.name,":"),s),{_id:e._id,name:e.name,subjectId:i._id,description:e.description,questionCount:0}}}));j(l)}catch(e){console.error("Error loading topics:",e)}finally{g(!1)}}};(0,i.useEffect)(()=>{s.chapters&&s.chapters.length>0&&y(s.chapters)},[s.chapters]);let C=e=>{let s=[...v,{chapterId:e._id,chapterName:e.name,numberOfQuestions:1,totalMarks:4}];y(s);let t=s.map(e=>({topicId:e.chapterId,topicName:e.chapterName,numberOfQuestions:e.numberOfQuestions,totalMarks:e.totalMarks}));n({chapters:s,topics:t})},S=e=>{let s=v.filter(s=>s.chapterId!==e);y(s);let t=s.map(e=>({topicId:e.chapterId,topicName:e.chapterName,numberOfQuestions:e.numberOfQuestions,totalMarks:e.totalMarks}));n({chapters:s,topics:t})},E=(e,s)=>{let t=v.map(t=>t.chapterId===e?{...t,...s}:t);y(t);let a=t.map(e=>({topicId:e.chapterId,topicName:e.chapterName,numberOfQuestions:e.numberOfQuestions,totalMarks:e.totalMarks}));n({chapters:t,topics:a})},M=()=>v.reduce((e,s)=>e+s.numberOfQuestions,0),I=e=>v.some(s=>s.chapterId===e);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold",children:"Select Chapters"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Choose specific chapters and configure question counts for each chapter."})]}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{className:"text-lg",children:"Selection Summary"})}),(0,a.jsx)(h.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:v.length}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Chapters Selected"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:M()}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Questions"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:v.reduce((e,s)=>e+s.totalMarks,0)}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Total Marks"})]})]})})]}),(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{className:"text-lg",children:"Available Chapters"})}),(0,a.jsx)(h.Wu,{children:b?(0,a.jsx)("div",{className:"text-center py-4",children:"Loading chapters..."}):0===m.length?(0,a.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No chapters available for the selected subject."}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:m.map(e=>(0,a.jsx)(J.Bc,{children:(0,a.jsxs)(J.m_,{children:[(0,a.jsx)(J.k$,{asChild:!0,children:(0,a.jsxs)(o.$,{variant:I(e._id)?"default":"outline",onClick:()=>{I(e._id)?S(e._id):C(e)},className:"h-auto p-3 flex flex-col items-start space-y-1",disabled:0===e.questionCount,children:[(0,a.jsx)("div",{className:"font-medium text-left",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(f.E,{variant:"secondary",className:"text-xs",children:[e.questionCount," questions"]}),(0,a.jsx)(x.A,{className:"h-3 w-3"})]})]})}),(0,a.jsxs)(J.ZI,{children:[(0,a.jsxs)("p",{children:[e.questionCount," questions available in ",e.name]}),(0,a.jsx)("p",{className:"text-xs mt-1 text-gray-400",children:"Total questions in this topic"}),e.description&&(0,a.jsx)("p",{className:"text-xs mt-1",children:e.description})]})]})},e._id))})})]}),v.length>0&&(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsx)(h.ZB,{className:"text-lg",children:"Configure Selected Chapters"})}),(0,a.jsx)(h.Wu,{className:"space-y-4",children:v.map(e=>{let s=m.find(s=>s._id===e.chapterId),t=(null==s?void 0:s.questionCount)||0;return(0,a.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.chapterName}),(0,a.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>S(e.chapterId),children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)(F.J,{children:["Number of Questions (Max: ",t,")"]}),(0,a.jsx)(w.p,{type:"number",min:"1",max:t,value:0===e.numberOfQuestions?"":e.numberOfQuestions,onChange:s=>{let a=s.target.value;if(""===a)E(e.chapterId,{numberOfQuestions:0,totalMarks:0});else{let s=parseInt(a);!isNaN(s)&&s>=1&&s<=t&&E(e.chapterId,{numberOfQuestions:s,totalMarks:4*s})}},onBlur:s=>{(""===s.target.value||0===e.numberOfQuestions)&&E(e.chapterId,{numberOfQuestions:1,totalMarks:4})}})]})]},e.chapterId)})})]}),(0,a.jsx)(u,{onNext:l,onSkip:r,onBack:c,backDisabled:d,nextDisabled:!1}),(0,a.jsx)(p,{message:0===v.length?"No chapters selected. Questions will be randomly selected from the entire subject. You can skip this step or select specific chapters for more targeted selection.":"You have selected ".concat(v.length," chapter(s) with ").concat(M()," total questions. Next, you can optionally select specific topics within these chapters.")})]})}var Y=t(16640);function V(e){let{formData:s,updateFormData:t,onNext:n,onSkip:l,onBack:r,backDisabled:c}=e,[d,m]=(0,i.useState)([]),[j,b]=(0,i.useState)(!1),[g,v]=(0,i.useState)([]);(0,i.useEffect)(()=>{s.subject&&(s.chapters||s.chapterId)&&k()},[s.subject,s.chapters,s.chapterId]),(0,i.useEffect)(()=>{s.topics&&v(s.topics)},[s.topics]);let k=async()=>{if(s.subject){b(!0);try{let e=[];if(s.chapters&&s.chapters.length>0)for(let t of s.chapters){let s=(await (0,Y.Kd)(t.chapterId)).map(e=>({...e,questionCount:0}));e=[...e,...s]}else if(s.chapterId)e=(await (0,Y.Kd)(s.chapterId)).map(e=>({...e,questionCount:0}));else{let t=(await (0,U.py)()).find(e=>e.name===s.subject);t&&(e=t.chapters.flatMap(e=>e.topics.map(e=>({...e,questionCount:0}))))}m(e)}catch(e){console.error("Error loading topics:",e),m([])}finally{b(!1)}}},C=e=>{let s=[...g,{topicId:e._id,topicName:e.name,numberOfQuestions:1,totalMarks:10}];v(s),t({topics:s})},S=e=>{let s=g.filter(s=>s.topicId!==e);v(s),t({topics:s})},E=(e,s,a)=>{let i=g.map(t=>t.topicId===e?{...t,[s]:a}:t);v(i),t({topics:i})},M=e=>g.some(s=>s.topicId===e),I=g.length>0;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Select Topics (Optional)"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-2",children:"Choose specific topics for more targeted question selection"})]}),j?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-muted-foreground",children:"Loading topics..."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),"Available Topics"]})}),(0,a.jsx)(h.Wu,{children:d.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:d.map(e=>(0,a.jsx)(J.Bc,{children:(0,a.jsxs)(J.m_,{children:[(0,a.jsx)(J.k$,{asChild:!0,children:(0,a.jsx)(o.$,{variant:M(e._id)?"default":"outline",className:"h-auto p-3 justify-start",onClick:()=>M(e._id)?S(e._id):C(e),disabled:M(e._id),children:(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.questionCount," questions available"]})]})})}),(0,a.jsx)(J.ZI,{children:(0,a.jsx)("p",{children:e.description||"".concat(e.questionCount," questions available in ").concat(e.name)})})]})},e._id))}):(0,a.jsx)(p,{type:"info",message:"No topics available for the selected chapters. You can skip this step to select from all questions in the chosen chapters."})})]}),g.length>0&&(0,a.jsxs)(h.Zp,{children:[(0,a.jsx)(h.aR,{children:(0,a.jsxs)(h.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),"Selected Topics (",g.length,")"]})}),(0,a.jsxs)(h.Wu,{className:"space-y-4",children:[g.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-3 border rounded-lg",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)(f.E,{variant:"secondary",children:e.topicName})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(F.J,{htmlFor:"questions-".concat(e.topicId),className:"text-sm",children:"Questions:"}),(0,a.jsx)(w.p,{id:"questions-".concat(e.topicId),type:"number",min:"1",max:"100",value:e.numberOfQuestions,onChange:s=>E(e.topicId,"numberOfQuestions",parseInt(s.target.value)||1),className:"w-20"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(F.J,{htmlFor:"marks-".concat(e.topicId),className:"text-sm",children:"Marks:"}),(0,a.jsx)(w.p,{id:"marks-".concat(e.topicId),type:"number",min:"1",max:"1000",value:e.totalMarks,onChange:s=>E(e.topicId,"totalMarks",parseInt(s.target.value)||1),className:"w-20"})]}),(0,a.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>S(e.topicId),children:(0,a.jsx)(N.A,{className:"h-4 w-4"})})]},e.topicId)),(0,a.jsx)("div",{className:"pt-2 border-t",children:(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{children:["Total Questions: ",g.reduce((e,s)=>e+s.numberOfQuestions,0)]}),(0,a.jsxs)("span",{children:["Total Marks: ",g.reduce((e,s)=>e+s.totalMarks,0)]})]})})]})]})]}),(0,a.jsx)(u,{onNext:()=>{I&&n()},onSkip:()=>{v([]),t({topics:void 0,topicId:void 0}),l()},onBack:r,nextDisabled:!1,backDisabled:c,nextLabel:g.length>0?"Continue with Selected Topics":"Skip Topic Selection",skipLabel:"Skip Topics",showSkip:!0})]})}let K={questionType:"",title:"",description:"",paperMode:"single",course:"",subject:"",subjects:[],subjectConfigs:{},difficultyMode:"auto",difficultyLevels:{easyPercentage:30,mediumPercentage:50,hardPercentage:20},numberOfQuestions:1,totalMarks:100,includeAnswers:!1,duration:60,instructions:"",topicId:void 0};function X(){let[e,s]=(0,i.useState)(0),[t,n]=(0,i.useState)(K),[r,c]=(0,i.useState)(!1),o=e=>{n(s=>({...s,...e}))},d=()=>{let a=e+1;if("single"===t.paperMode){var i,n;(null===(i=f[a])||void 0===i?void 0:i.title)==="Select Topics (Optional)"&&a++;let e=t.chapters&&t.chapters.length>0,s=t.topics&&t.topics.length>0;(e||s)&&(null===(n=f[a])||void 0===n?void 0:n.title)==="Question Selection Criteria"&&a++}s(Math.min(a,f.length-1))},u=()=>{let a=e-1;if("single"===t.paperMode){var i,n;(null===(i=f[a])||void 0===i?void 0:i.title)==="Select Topics (Optional)"&&a--;let e=t.chapters&&t.chapters.length>0,s=t.topics&&t.topics.length>0;(e||s)&&(null===(n=f[a])||void 0===n?void 0:n.title)==="Question Selection Criteria"&&a--}s(Math.max(a,0))},x=()=>{d()},p=()=>{s(0)},h=async()=>{if(!r)try{var e;let a;c(!0),console.log("Submitting data:",t);let i=localStorage.getItem("backendToken"),r=localStorage.getItem("firebaseToken"),o=localStorage.getItem("token");if(console.log("Available tokens:",{backendToken:i?"".concat(i.substring(0,20),"..."):"None",firebaseToken:r?"".concat(r.substring(0,20),"..."):"None",token:o?"".concat(o.substring(0,20),"..."):"None"}),!(null===(e=t.title)||void 0===e?void 0:e.trim())){c(!1),alert("Please enter a title for the question paper");return}if(!t.questionType){c(!1),alert("Please select an exam type");return}if("single"===t.paperMode){if(!t.subject){c(!1),alert("Please select a subject");return}}else if(0===t.subjects.length){c(!1),alert("Please select at least one subject");return}if("single"===t.paperMode){if(a={title:t.title,description:t.description,subject:t.subject,totalMarks:t.totalMarks,duration:t.duration,examType:t.questionType,instructions:t.instructions,chapterId:t.chapterId,topicId:t.topicId},t.topics&&t.topics.length>0){a.topics=t.topics;let e=t.topics.reduce((e,s)=>e+s.numberOfQuestions,0),s=t.topics.reduce((e,s)=>e+s.totalMarks,0);a.totalMarks=s,a.customise&&(a.customise.numberOfQuestions=e,a.customise.totalMarks=s)}else if(t.chapters&&t.chapters.length>0){a.chapters=t.chapters;let e=t.chapters.reduce((e,s)=>e+s.numberOfQuestions,0),s=t.chapters.reduce((e,s)=>e+s.totalMarks,0);a.totalMarks=s,a.customise&&(a.customise.numberOfQuestions=e,a.customise.totalMarks=s)}"custom"===t.difficultyMode?a.customise={customDifficulty:t.difficultyLevels,numberOfQuestions:t.numberOfQuestions,totalMarks:t.totalMarks,duration:t.duration,includeAnswers:t.includeAnswers}:a.customise={customDifficulty:{easyPercentage:30,mediumPercentage:50,hardPercentage:20},numberOfQuestions:t.numberOfQuestions,totalMarks:t.totalMarks,duration:t.duration,includeAnswers:t.includeAnswers}}else{let e=t.subjects.map(e=>{let s=t.subjectConfigs[e];return{subject:e,numberOfQuestions:s.numberOfQuestions,totalMarks:s.totalMarks,customDifficulty:s.difficultyLevels,chapterId:s.chapterId,topicId:s.topicId,chapters:s.chapters,topics:s.topics}});a={title:t.title,description:t.description,duration:t.duration,examType:t.questionType,instructions:t.instructions,subjects:e,includeAnswers:t.includeAnswers}}console.log("Calling createQuestionPaper API...");let d=await (0,l.ZG)(a);if(console.log("API result:",d),!d.success){console.log("API returned error:",d.error),c(!1);let e=d.error;if(e.includes("Authentication required")||e.includes("Unauthorized"))e="Please log in again to continue. Your session may have expired.";else if(e.includes("Network")||e.includes("fetch"))e="Please check your internet connection and try again.";else if(e.includes("unused questions available")){let s=e.match(/Only (\d+) unused questions available\. Requested: (\d+)/);if(s){let t=s[1],a=s[2];e="Only ".concat(t," questions are available for this subject/topic, but you requested ").concat(a," questions. Please reduce the number of questions or add more questions to the database.")}}alert("Error: ".concat(e)),console.log("Staying on current step due to error");return}console.log("API success! Proceeding with download..."),console.log("Full API response:",d);let u=d.data;if(console.log("Question paper data:",u),!u||!u._id)throw console.error("No question paper ID found in response. Full response:",d),Error("Question paper was created but no ID was returned. Please check the console for details and try again.");let m=await (0,l.cH)(u._id),x=m.questionPaper,p=m.college||{name:"",logoUrl:"",address:""},h={title:x.title,description:x.description||"",duration:x.duration,totalMarks:x.totalMarks,instructions:x.instructions||"",includeAnswers:t.includeAnswers,questions:(()=>{if(x.isMultiSubject&&x.sections){let e=[];return x.sections.forEach(s=>{let t=s.subjectName||s.name||"";s.questions.forEach(s=>{let a=s.question||s;e.push({question:a.content||a.question||"",options:a.options||[],answer:a.answer||"",subject:t,imageUrls:a.imageUrls||[],solution:a.solution||null,hints:a.hints||[]})})}),e}return(x.questions||[]).map(e=>{var s;return{question:e.content||e.question||"",options:e.options||[],answer:e.answer||"",subject:(null===(s=x.subjectId)||void 0===s?void 0:s.name)||"",imageUrls:e.imageUrls||[],solution:e.solution||null,hints:e.hints||[]}})})(),filename:"".concat(x.title.replace(/\s+/g,"_"),"_").concat(Date.now(),".pdf"),collegeName:(null==p?void 0:p.name)||"",collegeLogoUrl:(null==p?void 0:p.logoUrl)||""},f=await fetch("/api/generate-paper-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)});if(!f.ok)throw Error("Server-side PDF generation failed");let j=await f.blob(),b=window.URL.createObjectURL(j),g=document.createElement("a");g.href=b,g.download=h.filename,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(b),alert("Question paper generated and downloaded successfully!"),console.log("Success! About to redirect to first step in 1 second..."),setTimeout(()=>{console.log("Redirecting to first step now..."),s(0),n(K),c(!1),console.log("Redirect completed. Current step should be 0")},1e3)}catch(e){c(!1),alert("Error: An unexpected error occurred. Please try again.")}},f=(()=>{let s=[{title:"Question Type",icon:"HelpCircle",component:(0,a.jsx)(m,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Paper Details",icon:"FileText",component:(0,a.jsx)(R,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Course & Subject Selection",icon:"BookOpen",component:(0,a.jsx)(v,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})}];return"multi"===t.paperMode&&s.push({title:"Configure Subjects",icon:"Settings",component:(0,a.jsx)(H,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Paper Customization",icon:"FileEdit",component:(0,a.jsx)(M,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})}),"single"===t.paperMode&&s.push({title:"Select Chapters (Optional)",icon:"BookOpen",component:(0,a.jsx)(G,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Select Topics (Optional)",icon:"Tag",component:(0,a.jsx)(V,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Select Difficulty Level",icon:"BarChart2",component:(0,a.jsx)(k,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Question Selection Criteria",icon:"FileText",component:(0,a.jsx)(C,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Paper Customization",icon:"FileEdit",component:(0,a.jsx)(M,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})}),s.push({title:"Include Answers?",icon:"CheckSquare",component:(0,a.jsx)(I,{formData:t,updateFormData:o,onNext:d,onSkip:x,onBack:u,backDisabled:0===e})},{title:"Generate Paper",icon:"FileOutput",component:(0,a.jsx)(A,{formData:t,onSubmit:h,isLoading:r,onBack:p})}),s})();return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(z,{currentStep:e,steps:f.map(e=>({title:e.title,icon:e.icon}))}),f[e].component]})}let ee=()=>{let[e,s]=(0,i.useState)("tab1");return(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Generate Questions"}),(0,a.jsx)(n.A,{items:[{label:"Home",href:"/teacher"},{label:"...",href:"#"},{label:"Generate Questions"}],className:"text-sm mt-1"})]}),(0,a.jsx)("div",{className:"flex justify-center mt-6",children:(0,a.jsx)("div",{className:"w-full max-w-2xl p-6",children:(0,a.jsx)(X,{})})})]})})}},17313:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>c,av:()=>o,j7:()=>r,tU:()=>l});var a=t(95155);t(12115);var i=t(60704),n=t(59434);function l(e){let{className:s,...t}=e;return(0,a.jsx)(i.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",s),...t})}function r(e){let{className:s,...t}=e;return(0,a.jsx)(i.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)(i.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)(i.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",s),...t})}},25531:(e,s,t)=>{"use strict";t.d(s,{ZG:()=>n,cH:()=>l,ig:()=>r});let a="http://localhost:3000/api";function i(){let e=localStorage.getItem("backendToken"),s=localStorage.getItem("firebaseToken"),t=localStorage.getItem("token"),a={"Content-Type":"application/json"};if(e)a.Authorization="Bearer ".concat(e);else if(s)a.Authorization="Bearer ".concat(s);else if(t)a.Authorization="Bearer ".concat(t);else throw Error("Authentication required - Please log in again. No valid authentication token found.");return a}async function n(e){try{let s=i(),t=await fetch("".concat(a,"/question-papers"),{method:"POST",headers:s,body:JSON.stringify(e)});if(!t.ok){let e="Error: ".concat(t.status," - ").concat(t.statusText);try{let s=await t.text();if(s)try{let t=JSON.parse(s);e=t&&t.message?t.message:t&&t.error?t.error:s}catch(t){e=s}}catch(e){}if(!e||e==="Error: ".concat(t.status," - ").concat(t.statusText))switch(t.status){case 401:e="Authentication required - Please log in again.";break;case 403:e="Access denied - You don't have permission to perform this action.";break;case 404:e="Resource not found - The requested item could not be found.";break;case 429:e="Too many requests - Please wait a moment before trying again.";break;case 500:e="Server error - Please try again later.";break;case 503:e="Service unavailable - The server is temporarily down.";break;default:t.status>=400&&t.status<500?e="Invalid request - Please check your input and try again.":t.status>=500&&(e="Server error - Please try again later.")}return{success:!1,error:e}}let n=await t.json();if(console.log("Raw API response from createQuestionPaper:",n),n.questionPaper)return{success:!0,data:n.questionPaper};return{success:!0,data:n}}catch(e){return{success:!1,error:"Network error - Please check your connection and try again."}}}async function l(e){try{let s;if(console.log("getQuestionPaperForPDF called with:",e),!e||"undefined"===e||"null"===e)throw Error("Invalid question paper ID: ".concat(e));let t=i(),n=await fetch("".concat(a,"/question-papers/").concat(e),{method:"GET",headers:t});if(!n.ok){let e=await n.text();throw console.error("API Error Response:",e),Error("Failed to fetch question paper: ".concat(n.status," ").concat(n.statusText))}let l=await n.json();if(console.log("Question paper data fetched:",l),console.log("College ID in question paper:",l.collegeId),l.college?(s={name:l.college.name,logoUrl:l.college.logoUrl,address:l.college.address},console.log("College information found in response:",s)):l.collegeId&&"object"==typeof l.collegeId&&l.collegeId.name?(s={name:l.collegeId.name,logoUrl:l.collegeId.logoUrl,address:l.collegeId.address},console.log("College information found in populated collegeId:",s)):console.log("No college information available - PDF will be generated without college branding"),l.questionPaper)return{questionPaper:l.questionPaper,college:l.college||s};return{questionPaper:l,college:s}}catch(e){throw console.error("Error fetching question paper for PDF:",e),e}}async function r(){try{let e=i(),s=await fetch("".concat(a,"/question-papers"),{method:"GET",headers:e});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(s.status," - ").concat(s.statusText))}return await s.json()}catch(e){throw console.error("Error fetching question papers:",e),e}}},46102:(e,s,t)=>{"use strict";t.d(s,{Bc:()=>l,ZI:()=>o,k$:()=>c,m_:()=>r});var a=t(95155);t(12115);var i=t(78082),n=t(59434);function l(e){let{delayDuration:s=0,...t}=e;return(0,a.jsx)(i.Kq,{"data-slot":"tooltip-provider",delayDuration:s,...t})}function r(e){let{...s}=e;return(0,a.jsx)(l,{children:(0,a.jsx)(i.bL,{"data-slot":"tooltip",...s})})}function c(e){let{...s}=e;return(0,a.jsx)(i.l9,{"data-slot":"tooltip-trigger",...s})}function o(e){let{className:s,sideOffset:t=0,children:l,...r}=e;return(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",s),...r,children:[l,(0,a.jsx)(i.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},88539:(e,s,t)=>{"use strict";t.d(s,{T:()=>n});var a=t(95155);t(12115);var i=t(59434);function n(e){let{className:s,...t}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,i.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...t})}},90357:(e,s,t)=>{Promise.resolve().then(t.bind(t,3932))}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6874,1141,6457,8377,415,4973,8441,1684,7358],()=>s(90357)),_N_E=e.O()}]);