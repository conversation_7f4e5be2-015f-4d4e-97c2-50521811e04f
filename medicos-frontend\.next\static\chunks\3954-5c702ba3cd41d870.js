"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3954],{17759:(e,r,t)=>{t.d(r,{C5:()=>w,MJ:()=>p,Rr:()=>v,eI:()=>f,lR:()=>m,lV:()=>c,zB:()=>u});var o=t(95155),a=t(12115),n=t(66634),s=t(62177),i=t(59434),l=t(85057);let c=s.Op,d=a.createContext({}),u=e=>{let{...r}=e;return(0,o.jsx)(d.Provider,{value:{name:r.name},children:(0,o.jsx)(s.xI,{...r})})},h=()=>{let e=a.useContext(d),r=a.useContext(g),{getFieldState:t}=(0,s.xW)(),o=(0,s.lN)({name:e.name}),n=t(e.name,o);if(!e)throw Error("useFormField should be used within <FormField>");let{id:i}=r;return{id:i,name:e.name,formItemId:"".concat(i,"-form-item"),formDescriptionId:"".concat(i,"-form-item-description"),formMessageId:"".concat(i,"-form-item-message"),...n}},g=a.createContext({});function f(e){let{className:r,...t}=e,n=a.useId();return(0,o.jsx)(g.Provider,{value:{id:n},children:(0,o.jsx)("div",{"data-slot":"form-item",className:(0,i.cn)("grid gap-2",r),...t})})}function m(e){let{className:r,...t}=e,{error:a,formItemId:n}=h();return(0,o.jsx)(l.J,{"data-slot":"form-label","data-error":!!a,className:(0,i.cn)("data-[error=true]:text-destructive",r),htmlFor:n,...t})}function p(e){let{...r}=e,{error:t,formItemId:a,formDescriptionId:s,formMessageId:i}=h();return(0,o.jsx)(n.Slot,{"data-slot":"form-control",id:a,"aria-describedby":t?"".concat(s," ").concat(i):"".concat(s),"aria-invalid":!!t,...r})}function v(e){let{className:r,...t}=e,{formDescriptionId:a}=h();return(0,o.jsx)("p",{"data-slot":"form-description",id:a,className:(0,i.cn)("text-muted-foreground text-sm",r),...t})}function w(e){var r;let{className:t,...a}=e,{error:n,formMessageId:s}=h(),l=n?String(null!==(r=null==n?void 0:n.message)&&void 0!==r?r:""):a.children;return l?(0,o.jsx)("p",{"data-slot":"form-message",id:s,className:(0,i.cn)("text-destructive text-sm",t),...a,children:l}):null}},25731:(e,r,t)=>{async function o(e){try{let r=await e.getIdToken(!0);return localStorage.setItem("firebaseToken",r),r}catch(e){throw console.error("Error getting Firebase token:",e),e}}async function a(){let e=localStorage.getItem("firebaseToken");if(!e)throw Error("No Firebase token available");try{let r=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firebaseToken:e})});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(r.status))}let t=await r.json();if(!t||!t.accessToken||!t.user||!t.user.role)throw Error("Invalid response format from server");return t}catch(e){throw console.error("Error in loginWithFirebaseToken:",e),e}}async function n(e,r){try{let t=await fetch("".concat("http://localhost:3000/api","/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:r})});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||"API error: ".concat(t.status))}let o=await t.json();if(!o||!o.accessToken||!o.user||!o.user.role)throw Error("Invalid response format from server");return o}catch(e){throw console.error("Error in loginWithEmailPassword:",e),e}}async function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t="".concat("http://localhost:3000/api").concat(e.startsWith("/")?e:"/".concat(e)),o=localStorage.getItem("firebaseToken"),a=localStorage.getItem("backendToken"),n={"Content-Type":"application/json",...a?{Authorization:"Bearer ".concat(a)}:o?{Authorization:"Bearer ".concat(o)}:{},...r.headers};try{let e=await fetch(t,{...r,headers:n});if(!e.ok){let r=await e.json().catch(()=>({}));throw Error(r.message||"API error: ".concat(e.status))}let o=e.headers.get("content-type");if(o&&o.includes("application/json"))return await e.json();return await e.text()}catch(e){throw console.error("API call failed:",e),e}}t.d(r,{K8:()=>a,V7:()=>o,Xw:()=>n,apiCall:()=>s})},30285:(e,r,t)=>{t.d(r,{$:()=>l,r:()=>i});var o=t(95155);t(12115);var a=t(66634),n=t(74466),s=t(59434);let i=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:n,asChild:l=!1,...c}=e,d=l?a.Slot:"button";return(0,o.jsx)(d,{"data-slot":"button",className:(0,s.cn)(i({variant:t,size:n,className:r})),...c})}},51790:(e,r,t)=>{t.d(r,{AuthProvider:()=>u,A:()=>h,t:()=>g});var o=t(95155),a=t(12115),n=t(16203),s=t(23915);let i=0===(0,s.Dk)().length?(0,s.Wp)({apiKey:"AIzaSyBl6opoMvsIC7CSYu3gQeYfwDPWDkt1_S8",authDomain:"medicos-392d0.firebaseapp.com",projectId:"medicos-392d0",storageBucket:"medicos-392d0.appspot.com",messagingSenderId:"**********",appId:"1:**********:web:abcdef**********",measurementId:"G-ABCDEFGHIJ"}):(0,s.Dk)()[0],l=(0,n.xI)(i);var c=t(25731);let d=(0,a.createContext)(void 0),u=e=>{let{children:r}=e,[t,s]=(0,a.useState)(null),[i,u]=(0,a.useState)(null),[h,g]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=(0,n.hg)(l,async e=>{if(s(e),e){let r=localStorage.getItem("userRole");if(console.log("AuthContext - Retrieved role from localStorage:",r),r)u(r);else try{console.log("No role in localStorage, trying to get from backend");let r=await e.getIdToken();localStorage.setItem("firebaseToken",r);let t=await (0,c.K8)();t&&t.user&&t.user.role&&(console.log("Got role from backend:",t.user.role),localStorage.setItem("userRole",t.user.role),u(t.user.role))}catch(e){console.error("Failed to get role from backend:",e)}}else u(null);g(!1)});return()=>e()},[]);let f=async(e,r,t)=>{try{let o=await (0,n.eJ)(l,e,r);o.user&&(await (0,n.r7)(o.user,{displayName:t}),await (0,c.V7)(o.user))}catch(e){throw console.error("Error signing up:",e),e}},m=async(e,r)=>{try{let t=await (0,n.x9)(l,e,r);await (0,c.V7)(t.user)}catch(e){throw console.error("Error logging in:",e),e}},p=async()=>{try{let e=new n.HF,r=await (0,n.df)(l,e);await (0,c.V7)(r.user)}catch(e){throw console.error("Error signing in with Google:",e),e}},v=async()=>{try{await (0,n.CI)(l),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken")}catch(e){throw console.error("Error logging out:",e),e}},w=async e=>{try{await (0,n.J1)(l,e);try{await fetch("".concat("http://localhost:3000/api","/auth/reset-password-request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e})})}catch(e){console.warn("Failed to notify backend about password reset:",e)}}catch(e){throw console.error("Error resetting password:",e),e}},b=async()=>{try{if(!t)throw Error("No authenticated user found");await (0,c.V7)(t);try{let e=await (0,c.K8)();e.accessToken&&localStorage.setItem("backendToken",e.accessToken)}catch(e){console.warn("Backend authentication after password reset failed:",e)}}catch(e){console.error("Error handling password reset completion:",e)}},y=async()=>{try{let e=l.currentUser;e&&(await (0,n.hG)(e),localStorage.removeItem("backendToken"),localStorage.removeItem("userRole"),localStorage.removeItem("firebaseToken"))}catch(e){throw console.error("Error deleting account:",e),e}};return(0,o.jsx)(d.Provider,{value:{user:t,userRole:i,loading:h,signUp:f,login:m,loginWithGoogle:p,logout:v,resetPassword:w,setUserRole:e=>{localStorage.setItem("userRole",e),u(e)},handlePasswordResetCompletion:b,deleteAccount:y},children:r})};function h(){let e=(0,a.useContext)(d);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function g(){return(0,a.useContext)(d)||{user:null,userRole:null,loading:!0,signUp:async()=>{throw Error("AuthProvider not found")},login:async()=>{throw Error("AuthProvider not found")},loginWithGoogle:async()=>{throw Error("AuthProvider not found")},logout:async()=>{throw Error("AuthProvider not found")},resetPassword:async()=>{throw Error("AuthProvider not found")},setUserRole:()=>{throw Error("AuthProvider not found")},handlePasswordResetCompletion:async()=>{throw Error("AuthProvider not found")},deleteAccount:async()=>{throw Error("AuthProvider not found")}}}},59434:(e,r,t)=>{t.d(r,{b:()=>s,cn:()=>n});var o=t(52596),a=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,o.$)(r))}function s(e){return new Promise((r,t)=>{let o=new FileReader;o.readAsDataURL(e),o.onload=()=>r(o.result),o.onerror=e=>t(e)})}},62523:(e,r,t)=>{t.d(r,{p:()=>n});var o=t(95155);t(12115);var a=t(59434);function n(e){let{className:r,type:t,...n}=e;return(0,o.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...n})}},85057:(e,r,t)=>{t.d(r,{J:()=>s});var o=t(95155);t(12115);var a=t(40968),n=t(59434);function s(e){let{className:r,...t}=e;return(0,o.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}}}]);