"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4870],{14870:(t,o,n)=>{n.d(o,{getQuestionCountByTopic:()=>e});let e=async(t,o)=>{try{let{apiCall:e}=await Promise.resolve().then(n.bind(n,25731));console.log("\uD83D\uDD04 FALLBACK API: Calling old questions API for topic ".concat(t,", subject ").concat(o)),console.log("\uD83D\uDCDE OLD API URL: /questions?subjectId=".concat(o,"&topicId=").concat(t,"&limit=1"));let c=await e("/questions?subjectId=".concat(o,"&topicId=").concat(t,"&limit=1"));if(c&&c.pagination&&"number"==typeof c.pagination.totalItems)return c.pagination.totalItems;if(c&&Array.isArray(c.questions))return+(c.questions.length>0);return 0}catch(n){return console.warn("Could not fetch question count for topic ".concat(t," in subject ").concat(o,":"),n.message||n),0}}}}]);