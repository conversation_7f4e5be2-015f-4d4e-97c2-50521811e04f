(()=>{var e={};e.id=411,e.ids=[411],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5398:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx","default")},6068:(e,t,s)=>{Promise.resolve().then(s.bind(s,86964))},7635:(e,t,s)=>{Promise.resolve().then(s.bind(s,53933))},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13964:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,t,s)=>{"use strict";s.d(t,{tU:()=>P,av:()=>D,j7:()=>A,Xi:()=>q});var r=s(60687),a=s(43210),n=s(70569),i=s(11273),l=s(72942),o=s(46059),c=s(3416),d=s(43),u=s(65551),m=s(96963),p="Tabs",[h,x]=(0,i.A)(p,[l.RG]),f=(0,l.RG)(),[g,b]=h(p),j=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:o,activationMode:p="automatic",...h}=e,x=(0,d.jH)(o),[f,b]=(0,u.i)({prop:a,onChange:n,defaultProp:i});return(0,r.jsx)(g,{scope:s,baseId:(0,m.B)(),value:f,onValueChange:b,orientation:l,dir:x,activationMode:p,children:(0,r.jsx)(c.sG.div,{dir:x,"data-orientation":l,...h,ref:t})})});j.displayName=p;var y="TabsList",v=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...n}=e,i=b(y,s),o=f(s);return(0,r.jsx)(l.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,r.jsx)(c.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});v.displayName=y;var N="TabsTrigger",w=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...o}=e,d=b(N,s),u=f(s),m=S(d.baseId,a),p=M(d.baseId,a),h=a===d.value;return(0,r.jsx)(l.q7,{asChild:!0,...u,focusable:!i,active:h,children:(0,r.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...o,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||i||!e||d.onValueChange(a)})})})});w.displayName=N;var k="TabsContent",C=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:n,forceMount:i,children:l,...d}=e,u=b(k,s),m=S(u.baseId,n),p=M(u.baseId,n),h=n===u.value,x=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(o.C,{present:i||h,children:({present:s})=>(0,r.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!s,id:p,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&l})})});function S(e,t){return`${e}-trigger-${t}`}function M(e,t){return`${e}-content-${t}`}C.displayName=k;var E=s(4780);function P({className:e,...t}){return(0,r.jsx)(j,{"data-slot":"tabs",className:(0,E.cn)("flex flex-col gap-2",e),...t})}function A({className:e,...t}){return(0,r.jsx)(v,{"data-slot":"tabs-list",className:(0,E.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function q({className:e,...t}){return(0,r.jsx)(w,{"data-slot":"tabs-trigger",className:(0,E.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function D({className:e,...t}){return(0,r.jsx)(C,{"data-slot":"tabs-content",className:(0,E.cn)("flex-1 outline-none",e),...t})}},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},39053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["teacher",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,53933)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,5398)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,71182)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/teacher/page",pathname:"/teacher",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44075:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eQ});var r=s(60687),a=s(43210),n=s(63239),i=s(51947),l=s(4780);function o({selected:e,onClick:t,children:s,className:a,grouped:n=!1,position:i="single"}){return(0,r.jsxs)("button",{type:"button",onClick:t,className:(0,l.cn)("flex items-center px-4 py-2 text-sm font-medium transition-colors",n?"left"===i?"rounded-l-sm border border-gray-200":"right"===i?"rounded-r-sm border border-gray-200 border-l-0":"border border-gray-200":"rounded-sm border border-gray-200","bg-white text-gray-900",a),children:[(0,r.jsx)("div",{className:(0,l.cn)("flex h-5 w-5 items-center justify-center rounded-full mr-2",e?"bg-[#05603A]":"border border-gray-300"),children:e&&(0,r.jsx)("div",{className:"h-2 w-2 rounded-full bg-white"})}),s]})}var c=s(29523),d=s(62688);let u=(0,d.A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);function m({onNext:e,onSkip:t,onBack:s,nextDisabled:a=!1,backDisabled:n=!1,nextLabel:i="Next",skipLabel:l="Skip",showSkip:o=!0}){return(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,r.jsxs)(c.$,{onClick:e,disabled:a,className:"w-full max-w-sm h-12 bg-[#05603A] hover:bg-[#04502F]",children:[i," ",(0,r.jsx)(u,{className:"ml-2 h-4 w-4"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(c.$,{variant:"ghost",onClick:s,disabled:n,className:"text-sm text-gray-500",children:"Back"}),o&&(0,r.jsx)(c.$,{variant:"ghost",onClick:t,className:"text-sm text-gray-500",children:l})]})]})}function p({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){let l=e=>{t({questionType:e})};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Question Type"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Select the types of questions you want."})]}),(0,r.jsx)("div",{className:"flex justify-center py-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-2 w-full max-w-2xl",children:[(0,r.jsx)(o,{selected:"NEET"===e.questionType,onClick:()=>l("NEET"),className:"w-full",children:"NEET"}),(0,r.jsx)(o,{selected:"CET"===e.questionType,onClick:()=>l("CET"),className:"w-full",children:"CET"}),(0,r.jsx)(o,{selected:"JEE"===e.questionType,onClick:()=>l("JEE"),className:"w-full",children:"JEE"}),(0,r.jsx)(o,{selected:"AIIMS"===e.questionType,onClick:()=>l("AIIMS"),className:"w-full",children:"AIIMS"}),(0,r.jsx)(o,{selected:"JIPMER"===e.questionType,onClick:()=>l("JIPMER"),className:"w-full",children:"JIPMER"}),(0,r.jsx)(o,{selected:"CUSTOM"===e.questionType,onClick:()=>l("CUSTOM"),className:"w-full",children:"CUSTOM"})]})}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i,nextDisabled:!e.questionType})]})}var h=s(96882);function x({message:e}){return(0,r.jsxs)("div",{className:"flex items-center gap-2 rounded-lg border border-blue-100 bg-blue-50 p-4 text-sm text-blue-600",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-blue-500"}),(0,r.jsx)("p",{children:e})]})}var f=s(44493),g=s(96834),b=s(13964),j=s(11860);let y=[{display:"Physics",value:"physics"},{display:"Chemistry",value:"chemistry"},{display:"Biology",value:"biology"},{display:"Mathematics",value:"mathematics"}];function v({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){let l=e=>{t({paperMode:e,subject:"",subjects:[],subjectConfigs:{}})},d=e=>{t({subject:e})},u=s=>{let r;if(e.subjects.includes(s)){r=e.subjects.filter(e=>e!==s);let a={...e.subjectConfigs};delete a[s],t({subjects:r,subjectConfigs:a})}else t({subjects:r=[...e.subjects,s]})},p="single"===e.paperMode?!e.subject:0===e.subjects.length;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Course & Subject Selection"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Choose between single subject or multi-subject question paper"})]}),(0,r.jsx)("div",{className:"flex justify-center py-4",children:(0,r.jsxs)("div",{className:"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]",children:[(0,r.jsx)(o,{selected:"single"===e.paperMode,onClick:()=>l("single"),grouped:!0,position:"left",className:"rounded-none border-0",children:"Single Subject"}),(0,r.jsx)("div",{className:"w-px bg-gray-200"}),(0,r.jsx)(o,{selected:"multi"===e.paperMode,onClick:()=>l("multi"),grouped:!0,position:"right",className:"rounded-none border-0",children:"Multi-Subject"})]})}),"single"===e.paperMode&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-lg",children:"Select Subject"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:y.map(t=>(0,r.jsx)(c.$,{variant:e.subject===t.value?"default":"outline",onClick:()=>d(t.value),className:"h-12",children:t.display},t.value))})})]}),"multi"===e.paperMode&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"text-lg flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Select Subjects"}),e.subjects.length>0&&(0,r.jsxs)(g.E,{variant:"secondary",children:[e.subjects.length," selected"]})]})}),(0,r.jsxs)(f.Wu,{children:[(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:y.map(t=>{let s=e.subjects.includes(t.value);return(0,r.jsx)(c.$,{variant:s?"default":"outline",onClick:()=>u(t.value),className:"h-12 relative",children:(0,r.jsxs)("span",{className:"flex items-center gap-2",children:[t.display,s&&(0,r.jsx)(b.A,{className:"h-4 w-4"})]})},t.value)})}),e.subjects.length>0&&(0,r.jsxs)("div",{className:"mt-4 p-3 bg-green-50 rounded-md",children:[(0,r.jsx)("p",{className:"text-sm text-green-700 font-medium",children:"Selected Subjects:"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:e.subjects.map(e=>{let t=y.find(t=>t.value===e);return(0,r.jsxs)(g.E,{variant:"secondary",className:"flex items-center gap-1",children:[t?.display||e,(0,r.jsx)("button",{onClick:()=>u(e),className:"ml-1 hover:bg-gray-200 rounded-full p-0.5",children:(0,r.jsx)(j.A,{className:"h-3 w-3"})})]},e)})})]})]})]}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i,nextDisabled:p}),p&&(0,r.jsx)(x,{message:"single"===e.paperMode?"Please select a subject before proceeding.":"Please select at least one subject before proceeding."})]})}let N=(0,d.A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);var w=s(96474);function k({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){let l=e=>{"auto"===e?t({difficultyMode:e,difficultyLevels:{easyPercentage:30,mediumPercentage:50,hardPercentage:20}}):t({difficultyMode:e})},d=(s,r)=>{let a=Math.max(0,Math.min(100,e.difficultyLevels[s]+r));t({difficultyLevels:{...e.difficultyLevels,[s]:a}})};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Select Difficulty Level"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Choose the complexity of your questions: Easy, Medium, Hard, or Mixed"})]}),(0,r.jsx)("div",{className:"flex justify-center py-4",children:(0,r.jsxs)("div",{className:"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]",children:[(0,r.jsx)(o,{selected:"auto"===e.difficultyMode,onClick:()=>l("auto"),grouped:!0,position:"left",className:"rounded-none border-0",children:"Auto generation"}),(0,r.jsx)("div",{className:"w-px bg-gray-200"}),(0,r.jsx)(o,{selected:"custom"===e.difficultyMode,onClick:()=>l("custom"),grouped:!0,position:"right",className:"rounded-none border-0",children:"Customization"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-left font-medium",children:"Easy"}),(0,r.jsxs)("div",{className:"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>d("easyPercentage",-10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===e.difficultyMode,children:(0,r.jsx)(N,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"w-[50px] text-center font-medium",children:[e.difficultyLevels.easyPercentage,"%"]}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>d("easyPercentage",10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===e.difficultyMode,children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-left font-medium",children:"Medium"}),(0,r.jsxs)("div",{className:"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>d("mediumPercentage",-10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===e.difficultyMode,children:(0,r.jsx)(N,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"w-[50px] text-center font-medium",children:[e.difficultyLevels.mediumPercentage,"%"]}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>d("mediumPercentage",10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===e.difficultyMode,children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-left font-medium",children:"Hard"}),(0,r.jsxs)("div",{className:"inline-flex items-center rounded-sm border-[#E5E7EB] border p-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>d("hardPercentage",-10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===e.difficultyMode,children:(0,r.jsx)(N,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"w-[50px] text-center font-medium",children:[e.difficultyLevels.hardPercentage,"%"]}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>d("hardPercentage",10),className:"rounded-sm border-[#E5E7EB] h-8",disabled:"auto"===e.difficultyMode,children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]})]}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i}),(0,r.jsx)(x,{message:"Please select a difficulty level before proceeding. Your choice will determine the complexity of the questions generated."})]})}var C=s(12048);function S({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){let l=s=>{t({numberOfQuestions:Math.max(1,e.numberOfQuestions+s)})};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Question Selection Criteria"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Define the number of questions and total marks. Please select the number of questions to proceed."})]}),(0,r.jsxs)("div",{className:"space-y-4 text-center py-4",children:[(0,r.jsx)("p",{className:"text-left font-medium",children:"Select number of Questions"}),(0,r.jsxs)("div",{className:"flex max-w-[600px]",children:[(0,r.jsx)(C.p,{type:"number",value:0===e.numberOfQuestions?"":e.numberOfQuestions,onChange:e=>{let s=e.target.value;if(""===s)t({numberOfQuestions:0});else{let e=parseInt(s);!isNaN(e)&&e>=1&&t({numberOfQuestions:e})}},onBlur:s=>{(""===s.target.value||0===e.numberOfQuestions)&&t({numberOfQuestions:1})},className:"rounded-l-sm border-[#E5E7EB] h-[54px] text-lg",min:1}),(0,r.jsxs)("div",{className:"flex flex-col -ml-px",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>l(1),className:"rounded-none rounded-tr-sm border-[#E5E7EB] h-[27px]",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>l(-1),disabled:e.numberOfQuestions<=1,className:"rounded-none rounded-br-sm border-[#E5E7EB] h-[27px] -mt-px",children:(0,r.jsx)(N,{className:"h-4 w-4"})})]})]})]}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i}),(0,r.jsx)(x,{message:"Please specify the number of questions and total marks before proceeding. This ensures accurate question selection."})]})}function M(e,[t,s]){return Math.min(s,Math.max(t,e))}function E(e,t,{checkForDefaultPrevented:s=!0}={}){return function(r){if(e?.(r),!1===s||!r.defaultPrevented)return t?.(r)}}function P(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function A(...e){return t=>{let s=!1,r=e.map(e=>{let r=P(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():P(e[t],null)}}}}function q(...e){return a.useCallback(A(...e),e)}function D(e,t=[]){let s=[],n=()=>{let t=s.map(e=>a.createContext(e));return function(s){let r=s?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...s,[e]:r}}),[s,r])}};return n.scopeName=e,[function(t,n){let i=a.createContext(n),l=s.length;s=[...s,n];let o=t=>{let{scope:s,children:n,...o}=t,c=s?.[e]?.[l]||i,d=a.useMemo(()=>o,Object.values(o));return(0,r.jsx)(c.Provider,{value:d,children:n})};return o.displayName=t+"Provider",[o,function(s,r){let o=r?.[e]?.[l]||i,c=a.useContext(o);if(c)return c;if(void 0!==n)return n;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return s.scopeName=t.scopeName,s}(n,...t)]}function I(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}var O=a.createContext(void 0),T=globalThis?.document?a.useLayoutEffect:()=>{};function $(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:s,...r}=e;if(a.isValidElement(s)){var n;let e,i;let l=(n=s,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),o=function(e,t){let s={...t};for(let r in t){let a=e[r],n=t[r];/^on[A-Z]/.test(r)?a&&n?s[r]=(...e)=>{n(...e),a(...e)}:a&&(s[r]=a):"style"===r?s[r]={...a,...n}:"className"===r&&(s[r]=[a,n].filter(Boolean).join(" "))}return{...e,...s}}(r,s.props);return s.type!==a.Fragment&&(o.ref=t?A(t,l):l),a.cloneElement(s,o)}return a.Children.count(s)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=a.forwardRef((e,s)=>{let{children:n,...i}=e,l=a.Children.toArray(n),o=l.find(Q);if(o){let e=o.props.children,n=l.map(t=>t!==o?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,r.jsx)(t,{...i,ref:s,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,r.jsx)(t,{...i,ref:s,children:n})});return s.displayName=`${e}.Slot`,s}s(51215);var R=Symbol("radix.slottable");function Q(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===R}var B=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let s=$(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(n?s:t,{...i,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),_=["PageUp","PageDown"],F=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],L={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},z="Slider",[U,H,J]=function(e){let t=e+"CollectionProvider",[s,n]=D(t),[i,l]=s(t,{collectionRef:{current:null},itemMap:new Map}),o=e=>{let{scope:t,children:s}=e,n=a.useRef(null),l=a.useRef(new Map).current;return(0,r.jsx)(i,{scope:t,itemMap:l,collectionRef:n,children:s})};o.displayName=t;let c=e+"CollectionSlot",d=$(c),u=a.forwardRef((e,t)=>{let{scope:s,children:a}=e,n=q(t,l(c,s).collectionRef);return(0,r.jsx)(d,{ref:n,children:a})});u.displayName=c;let m=e+"CollectionItemSlot",p="data-radix-collection-item",h=$(m),x=a.forwardRef((e,t)=>{let{scope:s,children:n,...i}=e,o=a.useRef(null),c=q(t,o),d=l(m,s);return a.useEffect(()=>(d.itemMap.set(o,{ref:o,...i}),()=>void d.itemMap.delete(o))),(0,r.jsx)(h,{[p]:"",ref:c,children:n})});return x.displayName=m,[{Provider:o,Slot:u,ItemSlot:x},function(t){let s=l(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=s.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${p}]`));return Array.from(s.itemMap.values()).sort((e,s)=>t.indexOf(e.ref.current)-t.indexOf(s.ref.current))},[s.collectionRef,s.itemMap])},n]}(z),[Z,G]=D(z,[J]),[V,W]=Z(z),K=a.forwardRef((e,t)=>{let{name:s,min:n=0,max:i=100,step:l=1,orientation:o="horizontal",disabled:c=!1,minStepsBetweenThumbs:d=0,defaultValue:u=[n],value:m,onValueChange:p=()=>{},onValueCommit:h=()=>{},inverted:x=!1,form:f,...g}=e,b=a.useRef(new Set),j=a.useRef(0),y="horizontal"===o,[v=[],N]=function({prop:e,defaultProp:t,onChange:s=()=>{}}){let[r,n]=function({defaultProp:e,onChange:t}){let s=a.useState(e),[r]=s,n=a.useRef(r),i=I(t);return a.useEffect(()=>{n.current!==r&&(i(r),n.current=r)},[r,n,i]),s}({defaultProp:t,onChange:s}),i=void 0!==e,l=i?e:r,o=I(s);return[l,a.useCallback(t=>{if(i){let s="function"==typeof t?t(e):t;s!==e&&o(s)}else n(t)},[i,e,n,o])]}({prop:m,defaultProp:u,onChange:e=>{let t=[...b.current];t[j.current]?.focus(),p(e)}}),w=a.useRef(v);function k(e,t,{commit:s}={commit:!1}){let r=(String(l).split(".")[1]||"").length,a=M(function(e,t){let s=Math.pow(10,t);return Math.round(e*s)/s}(Math.round((e-n)/l)*l+n,r),[n,i]);N((e=[])=>{let r=function(e=[],t,s){let r=[...e];return r[s]=t,r.sort((e,t)=>e-t)}(e,a,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,s)=>e[s+1]-t))>=t;return!0}(r,d*l))return e;{j.current=r.indexOf(a);let t=String(r)!==String(e);return t&&s&&h(r),t?r:e}})}return(0,r.jsx)(V,{scope:e.__scopeSlider,name:s,disabled:c,min:n,max:i,valueIndexToChangeRef:j,thumbs:b.current,values:v,orientation:o,form:f,children:(0,r.jsx)(U.Provider,{scope:e.__scopeSlider,children:(0,r.jsx)(U.Slot,{scope:e.__scopeSlider,children:(0,r.jsx)(y?ee:et,{"aria-disabled":c,"data-disabled":c?"":void 0,...g,ref:t,onPointerDown:E(g.onPointerDown,()=>{c||(w.current=v)}),min:n,max:i,inverted:x,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let s=e.map(e=>Math.abs(e-t)),r=Math.min(...s);return s.indexOf(r)}(v,e);k(e,t)},onSlideMove:c?void 0:function(e){k(e,j.current)},onSlideEnd:c?void 0:function(){let e=w.current[j.current];v[j.current]!==e&&h(v)},onHomeKeyDown:()=>!c&&k(n,0,{commit:!0}),onEndKeyDown:()=>!c&&k(i,v.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){let s=_.includes(e.key)||e.shiftKey&&F.includes(e.key),r=j.current;k(v[r]+l*(s?10:1)*t,r,{commit:!0})}}})})})})});K.displayName=z;var[Y,X]=Z(z,{startEdge:"left",endEdge:"right",size:"width",direction:1}),ee=a.forwardRef((e,t)=>{let{min:s,max:n,dir:i,inverted:l,onSlideStart:o,onSlideMove:c,onSlideEnd:d,onStepKeyDown:u,...m}=e,[p,h]=a.useState(null),x=q(t,e=>h(e)),f=a.useRef(void 0),g=function(e){let t=a.useContext(O);return e||t||"ltr"}(i),b="ltr"===g,j=b&&!l||!b&&l;function y(e){let t=f.current||p.getBoundingClientRect(),r=em([0,t.width],j?[s,n]:[n,s]);return f.current=t,r(e-t.left)}return(0,r.jsx)(Y,{scope:e.__scopeSlider,startEdge:j?"left":"right",endEdge:j?"right":"left",direction:j?1:-1,size:"width",children:(0,r.jsx)(es,{dir:g,"data-orientation":"horizontal",...m,ref:x,style:{...m.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=y(e.clientX);o?.(t)},onSlideMove:e=>{let t=y(e.clientX);c?.(t)},onSlideEnd:()=>{f.current=void 0,d?.()},onStepKeyDown:e=>{let t=L[j?"from-left":"from-right"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),et=a.forwardRef((e,t)=>{let{min:s,max:n,inverted:i,onSlideStart:l,onSlideMove:o,onSlideEnd:c,onStepKeyDown:d,...u}=e,m=a.useRef(null),p=q(t,m),h=a.useRef(void 0),x=!i;function f(e){let t=h.current||m.current.getBoundingClientRect(),r=em([0,t.height],x?[n,s]:[s,n]);return h.current=t,r(e-t.top)}return(0,r.jsx)(Y,{scope:e.__scopeSlider,startEdge:x?"bottom":"top",endEdge:x?"top":"bottom",size:"height",direction:x?1:-1,children:(0,r.jsx)(es,{"data-orientation":"vertical",...u,ref:p,style:{...u.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=f(e.clientY);l?.(t)},onSlideMove:e=>{let t=f(e.clientY);o?.(t)},onSlideEnd:()=>{h.current=void 0,c?.()},onStepKeyDown:e=>{let t=L[x?"from-bottom":"from-top"].includes(e.key);d?.({event:e,direction:t?-1:1})}})})}),es=a.forwardRef((e,t)=>{let{__scopeSlider:s,onSlideStart:a,onSlideMove:n,onSlideEnd:i,onHomeKeyDown:l,onEndKeyDown:o,onStepKeyDown:c,...d}=e,u=W(z,s);return(0,r.jsx)(B.span,{...d,ref:t,onKeyDown:E(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(o(e),e.preventDefault()):_.concat(F).includes(e.key)&&(c(e),e.preventDefault())}),onPointerDown:E(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),u.thumbs.has(t)?t.focus():a(e)}),onPointerMove:E(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&n(e)}),onPointerUp:E(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),er="SliderTrack",ea=a.forwardRef((e,t)=>{let{__scopeSlider:s,...a}=e,n=W(er,s);return(0,r.jsx)(B.span,{"data-disabled":n.disabled?"":void 0,"data-orientation":n.orientation,...a,ref:t})});ea.displayName=er;var en="SliderRange",ei=a.forwardRef((e,t)=>{let{__scopeSlider:s,...n}=e,i=W(en,s),l=X(en,s),o=q(t,a.useRef(null)),c=i.values.length,d=i.values.map(e=>eu(e,i.min,i.max)),u=c>1?Math.min(...d):0,m=100-Math.max(...d);return(0,r.jsx)(B.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...n,ref:o,style:{...e.style,[l.startEdge]:u+"%",[l.endEdge]:m+"%"}})});ei.displayName=en;var el="SliderThumb",eo=a.forwardRef((e,t)=>{let s=H(e.__scopeSlider),[n,i]=a.useState(null),l=q(t,e=>i(e)),o=a.useMemo(()=>n?s().findIndex(e=>e.ref.current===n):-1,[s,n]);return(0,r.jsx)(ec,{...e,ref:l,index:o})}),ec=a.forwardRef((e,t)=>{let{__scopeSlider:s,index:n,name:i,...l}=e,o=W(el,s),c=X(el,s),[d,u]=a.useState(null),m=q(t,e=>u(e)),p=!d||o.form||!!d.closest("form"),h=function(e){let[t,s]=a.useState(void 0);return T(()=>{if(e){s({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,a;if(!Array.isArray(t)||!t.length)return;let n=t[0];if("borderBoxSize"in n){let e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,a=t.blockSize}else r=e.offsetWidth,a=e.offsetHeight;s({width:r,height:a})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}s(void 0)},[e]),t}(d),x=o.values[n],f=void 0===x?0:eu(x,o.min,o.max),g=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(n,o.values.length),b=h?.[c.size],j=b?function(e,t,s){let r=e/2,a=em([0,50],[0,r]);return(r-a(t)*s)*s}(b,f,c.direction):0;return a.useEffect(()=>{if(d)return o.thumbs.add(d),()=>{o.thumbs.delete(d)}},[d,o.thumbs]),(0,r.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:`calc(${f}% + ${j}px)`},children:[(0,r.jsx)(U.ItemSlot,{scope:e.__scopeSlider,children:(0,r.jsx)(B.span,{role:"slider","aria-label":e["aria-label"]||g,"aria-valuemin":o.min,"aria-valuenow":x,"aria-valuemax":o.max,"aria-orientation":o.orientation,"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,tabIndex:o.disabled?void 0:0,...l,ref:m,style:void 0===x?{display:"none"}:e.style,onFocus:E(e.onFocus,()=>{o.valueIndexToChangeRef.current=n})})}),p&&(0,r.jsx)(ed,{name:i??(o.name?o.name+(o.values.length>1?"[]":""):void 0),form:o.form,value:x},n)]})});eo.displayName=el;var ed=e=>{let{value:t,...s}=e,n=a.useRef(null),i=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(t);return a.useEffect(()=>{let e=n.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(i!==t&&s){let r=new Event("input",{bubbles:!0});s.call(e,t),e.dispatchEvent(r)}},[i,t]),(0,r.jsx)("input",{style:{display:"none"},...s,ref:n,defaultValue:t})};function eu(e,t,s){return M(100/(s-t)*(e-t),[0,100])}function em(e,t){return s=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(s-e[0])}}function ep({className:e,defaultValue:t,value:s,min:n=0,max:i=100,...o}){let[c,d]=a.useState(!1),[u,m]=a.useState(0),p=a.useMemo(()=>Array.isArray(s)?s:Array.isArray(t)?t:[n,i],[s,t,n,i]);return(0,r.jsxs)("div",{className:"relative",onMouseEnter:()=>d(!0),onMouseLeave:()=>d(!1),children:[c&&(0,r.jsx)("div",{className:"absolute -top-8 transform -translate-x-1/2 bg-white rounded-md shadow-lg px-2 py-1 text-sm font-medium",style:{left:`${u}%`},children:p[0]}),(0,r.jsxs)(K,{"data-slot":"slider",defaultValue:t,value:s,min:n,max:i,onMouseMove:e=>{let t=e.currentTarget.getBoundingClientRect();m((e.clientX-t.left)/t.width*100)},className:(0,l.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50",e),...o,children:[(0,r.jsx)(ea,{"data-slot":"slider-track",className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-gray-200",children:(0,r.jsx)(ei,{"data-slot":"slider-range",className:"absolute h-full bg-[#2563EB]"})}),Array.from({length:p.length},(e,t)=>(0,r.jsx)(eo,{"data-slot":"slider-thumb",className:"block h-4 w-4 rounded-full border border-[#2563EB] bg-white shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#2563EB] disabled:pointer-events-none disabled:opacity-50"},t))]})]})}function eh({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Paper Customization"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Customize your exam paper by selecting the desired format, structure, and preferences."})]}),(0,r.jsxs)("div",{className:"space-y-6 py-4",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-left font-medium",children:"Select Total Marks of paper"}),(0,r.jsxs)("div",{className:"px-4",children:[(0,r.jsx)("div",{className:"flex justify-end mb-2",children:(0,r.jsx)("span",{className:"font-medium",children:e.totalMarks})}),(0,r.jsx)(ep,{defaultValue:[e.totalMarks],max:2e3,min:10,step:5,onValueChange:e=>{t({totalMarks:e[0]})},className:"w-full"}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mt-1",children:[(0,r.jsx)("span",{children:"10 marks"}),(0,r.jsx)("span",{children:"2000 marks"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-left font-medium",children:"Select Duration (minutes)"}),(0,r.jsxs)("div",{className:"px-4",children:[(0,r.jsx)("div",{className:"flex justify-end mb-2",children:(0,r.jsxs)("span",{className:"font-medium",children:[e.duration," min (",Math.floor(e.duration/60),"h ",e.duration%60,"m)"]})}),(0,r.jsx)(ep,{defaultValue:[e.duration],max:1440,min:30,step:15,onValueChange:e=>{t({duration:e[0]})},className:"w-full"}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-500 mt-1",children:[(0,r.jsx)("span",{children:"30 min"}),(0,r.jsx)("span",{children:"1440 min (24 hours)"})]})]})]})]}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i}),(0,r.jsx)(x,{message:"Please complete the customization settings before proceeding."})]})}function ex({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){let l=e=>{t({includeAnswers:e})};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Include Answers?"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Choose whether to include answers in the paper. (Default: No)"})]}),(0,r.jsx)("div",{className:"flex justify-center py-4",children:(0,r.jsxs)("div",{className:"inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]",children:[(0,r.jsx)(o,{selected:!0===e.includeAnswers,onClick:()=>l(!0),grouped:!0,position:"left",className:"rounded-none border-0",children:"Yes"}),(0,r.jsx)("div",{className:"w-px bg-gray-200 min-h-full"}),(0,r.jsx)(o,{selected:!1===e.includeAnswers,onClick:()=>l(!1),grouped:!0,position:"right",className:"rounded-none border-0",children:"No"})]})}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i}),(0,r.jsx)(x,{message:"Please select an option before proceeding."})]})}var ef=s(31158);let eg=(0,d.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);function eb({formData:e,onSubmit:t,isLoading:s=!1,onBack:a}){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Actions"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Finalize your selections."})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center gap-4 py-4",children:[(0,r.jsxs)(c.$,{className:"w-full max-w-xs flex gap-2 bg-[#05603A] hover:bg-[#04502F]",onClick:t,disabled:s,children:[(0,r.jsx)(ef.A,{className:"h-4 w-4"}),s?"Generating PDF...":"Download PDF"]}),a&&(0,r.jsxs)(c.$,{variant:"outline",className:"w-full max-w-xs flex gap-2",onClick:a,disabled:s,children:[(0,r.jsx)(eg,{className:"h-4 w-4"}),"Back to Start"]})]})]})}var ej=s(65668),ey=s(82080);let ev=(0,d.A)("chart-no-axes-column",[["line",{x1:"18",x2:"18",y1:"20",y2:"10",key:"1xfpm4"}],["line",{x1:"12",x2:"12",y1:"20",y2:"4",key:"be30l9"}],["line",{x1:"6",x2:"6",y1:"20",y2:"14",key:"1r4le6"}]]);var eN=s(10022);let ew=(0,d.A)("file-pen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]]),ek=(0,d.A)("square-check-big",[["path",{d:"M21 10.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.5",key:"1uzm8b"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),eC=(0,d.A)("file-output",[["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M4 7V4a2 2 0 0 1 2-2 2 2 0 0 0-2 2",key:"1vk7w2"}],["path",{d:"M4.063 20.999a2 2 0 0 0 2 1L18 22a2 2 0 0 0 2-2V7l-5-5H6",key:"1jink5"}],["path",{d:"m5 11-3 3",key:"1dgrs4"}],["path",{d:"m5 17-3-3h10",key:"1mvvaf"}]]);function eS({currentStep:e,steps:t}){return(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(e=>{let t={HelpCircle:ej.A,BookOpen:ey.A,BarChart2:ev,FileText:eN.A,FileEdit:ew,CheckSquare:ek,FileOutput:eC}[e]||ej.A;return(0,r.jsx)(t,{className:"h-10 w-10 border border-gray-200 rounded-sm p-2",style:{border:"1px solid var(--Components-Button-White-Border-Color, #E5E7EB)"}})})(t[e].icon)})}var eM=s(80013),eE=s(34729);function eP({formData:e,updateFormData:t,onNext:s,onSkip:a,onBack:n,backDisabled:i}){return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Paper Details"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Add a title and description for your question paper."})]}),(0,r.jsxs)("div",{className:"space-y-6 max-w-2xl mx-auto",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(eM.J,{htmlFor:"paper-title",className:"text-sm font-medium",children:["Paper Title ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)(C.p,{id:"paper-title",type:"text",placeholder:"Enter the title of your question paper",value:e.title||"",onChange:e=>{t({title:e.target.value})},className:"w-full"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"This will appear at the top of your question paper"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eM.J,{htmlFor:"paper-description",className:"text-sm font-medium",children:"Description (Optional)"}),(0,r.jsx)(eE.T,{id:"paper-description",placeholder:"Add instructions, time limit, or other details for the paper",value:e.description||"",onChange:e=>{t({description:e.target.value})},className:"w-full min-h-[100px] resize-none"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Additional instructions or information for students"})]}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-600 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900",children:"Preview"}),(0,r.jsxs)("div",{className:"mt-2 text-sm text-blue-800",children:[(0,r.jsx)("div",{className:"font-semibold",children:e.title||"Your Paper Title"}),e.description&&(0,r.jsx)("div",{className:"mt-1 text-blue-700",children:e.description})]})]})]})})]}),(0,r.jsx)(m,{onNext:s,onSkip:a,onBack:n,backDisabled:i,nextDisabled:!e.title?.trim()})]})}var eA=s(29844),eq=s(76242);s(82013);let eD={physics:"Physics",chemistry:"Chemistry",biology:"Biology",mathematics:"Mathematics"};function eI({formData:e,updateFormData:t,onNext:s,onSkip:n,onBack:i,backDisabled:l}){let o,d;let[u,p]=(0,a.useState)(e.subjects[0]||""),[b,j]=(0,a.useState)({}),[y,v]=(0,a.useState)({}),[k,S]=(0,a.useState)([]),[M,E]=(0,a.useState)(!1),P=e=>eD[e]||e,A=t=>e.subjectConfigs[t]?e.subjectConfigs[t]:{subject:t,difficultyMode:"auto",difficultyLevels:{easyPercentage:30,mediumPercentage:50,hardPercentage:20},numberOfQuestions:10,totalMarks:40,topicId:void 0,chapters:[]},q=(s,r)=>{let a={...e.subjectConfigs[s]||A(s),...r};t({subjectConfigs:{...e.subjectConfigs,[s]:a}})},D=(t,s)=>{let r=e.subjectConfigs[t]||A(t),a={chapterId:s._id,chapterName:s.name,numberOfQuestions:1,totalMarks:4};q(t,{chapters:[...r.chapters||[],a]})},I=(t,s)=>{let r=((e.subjectConfigs[t]||A(t)).chapters||[]).filter(e=>e.chapterId!==s);q(t,{chapters:r})},O=(t,s,r)=>{let a=((e.subjectConfigs[t]||A(t)).chapters||[]).map(e=>e.chapterId===s?{...e,...r}:e);q(t,{chapters:a})},T=(t,s)=>{let r=e.subjectConfigs[t];return r?.chapters?.some(e=>e.chapterId===s)||!1},$=(t,s,r)=>{let a=e.subjectConfigs[t]||A(t),n=Math.max(0,Math.min(100,a.difficultyLevels[s]+r));q(t,{difficultyLevels:{...a.difficultyLevels,[s]:n}})},{totalQuestions:R,totalMarks:Q}=(o=0,d=0,e.subjects.forEach(t=>{let s=e.subjectConfigs[t]||A(t);o+=s.numberOfQuestions,d+=s.totalMarks}),{totalQuestions:o,totalMarks:d}),B=e.subjects.every(t=>{let s=e.subjectConfigs[t]||A(t);return 100===s.difficultyLevels.easyPercentage+s.difficultyLevels.mediumPercentage+s.difficultyLevels.hardPercentage&&s.numberOfQuestions>0&&s.totalMarks>0});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Configure Each Subject"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Set difficulty levels, number of questions, and marks for each selected subject"})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-lg",children:"Paper Summary"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.subjects.length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Subjects"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:R}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Questions"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:Q}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Marks"})]})]})})]}),(0,r.jsxs)(eA.tU,{value:u,onValueChange:p,className:"w-full",children:[(0,r.jsx)(eA.j7,{className:"grid w-full grid-cols-2 md:grid-cols-4",children:e.subjects.map(t=>(0,r.jsxs)(eA.Xi,{value:t,className:"text-sm",children:[P(t),e.subjectConfigs[t]&&(0,r.jsxs)(g.E,{variant:"secondary",className:"ml-1 text-xs",children:[e.subjectConfigs[t].numberOfQuestions,"Q"]})]},t))}),e.subjects.map(t=>{let s=e.subjectConfigs[t]||A(t),a=s.difficultyLevels.easyPercentage+s.difficultyLevels.mediumPercentage+s.difficultyLevels.hardPercentage;return(0,r.jsx)(eA.av,{value:t,className:"space-y-6",children:(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{children:[P(t)," Configuration"]}),(0,r.jsx)(g.E,{variant:100===a?"default":"destructive",children:100===a?"Valid":`${a}% (Need 100%)`})]})}),(0,r.jsxs)(f.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eM.J,{htmlFor:`questions-${t}`,children:"Number of Questions"}),(0,r.jsx)(C.p,{id:`questions-${t}`,type:"number",min:"1",max:"200",value:0===s.numberOfQuestions?"":s.numberOfQuestions,onChange:e=>{let s=e.target.value;if(""===s)q(t,{numberOfQuestions:0,totalMarks:0});else{let e=parseInt(s);!isNaN(e)&&e>=1&&q(t,{numberOfQuestions:e,totalMarks:4*e})}},onBlur:e=>{(""===e.target.value||0===s.numberOfQuestions)&&q(t,{numberOfQuestions:1,totalMarks:4})}})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(eM.J,{children:"Difficulty Distribution"}),(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eM.J,{className:"text-sm font-medium",children:"Easy"}),(0,r.jsxs)("div",{className:"flex items-center rounded-sm border border-gray-200 p-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>$(t,"easyPercentage",-5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,r.jsx)(N,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"flex-1 text-center font-medium",children:[s.difficultyLevels.easyPercentage,"%"]}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>$(t,"easyPercentage",5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eM.J,{className:"text-sm font-medium",children:"Medium"}),(0,r.jsxs)("div",{className:"flex items-center rounded-sm border border-gray-200 p-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>$(t,"mediumPercentage",-5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,r.jsx)(N,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"flex-1 text-center font-medium",children:[s.difficultyLevels.mediumPercentage,"%"]}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>$(t,"mediumPercentage",5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(eM.J,{className:"text-sm font-medium",children:"Hard"}),(0,r.jsxs)("div",{className:"flex items-center rounded-sm border border-gray-200 p-1",children:[(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>$(t,"hardPercentage",-5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,r.jsx)(N,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"flex-1 text-center font-medium",children:[s.difficultyLevels.hardPercentage,"%"]}),(0,r.jsx)(c.$,{variant:"outline",size:"icon",onClick:()=>$(t,"hardPercentage",5),className:"h-8 w-8 rounded-sm border-gray-200",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(eM.J,{children:"Chapter Selection (Optional)"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Select specific chapters to generate questions from. If no chapters are selected, questions will be randomly selected from the entire subject."}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(eM.J,{className:"text-sm font-medium",children:"Available Chapters"}),y[t]?(0,r.jsx)("div",{className:"text-center py-4 text-sm text-gray-500",children:"Loading chapters..."}):b[t]?.length===0?(0,r.jsx)("div",{className:"text-center py-4 text-sm text-gray-500",children:"No chapters available for this subject."}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:b[t]?.map(e=>r.jsx(eq.Bc,{children:r.jsxs(eq.m_,{children:[r.jsx(eq.k$,{asChild:!0,children:r.jsxs(c.$,{variant:T(t,e._id)?"default":"outline",onClick:()=>{T(t,e._id)?I(t,e._id):D(t,e)},className:"h-auto p-3 flex flex-col items-start space-y-1 text-left",disabled:0===e.questionCount,children:[r.jsx("div",{className:"font-medium",children:e.name}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs(g.E,{variant:"secondary",className:"text-xs",children:[e.questionCount," unused"]}),r.jsx(h.A,{className:"h-3 w-3"})]})]})}),r.jsxs(eq.ZI,{children:[r.jsxs("p",{children:[e.questionCount," unused questions available in ",e.name]}),r.jsx("p",{className:"text-xs mt-1 text-gray-400",children:"Questions not used in previous papers"}),e.description&&r.jsx("p",{className:"text-xs mt-1",children:e.description})]})]})},e._id))})]}),s.chapters&&s.chapters.length>0&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(eM.J,{className:"text-sm font-medium",children:"Selected Chapters Configuration"}),s.chapters.map(e=>{let s=b[t]?.find(t=>t._id===e.chapterId),a=s?.questionCount||0;return(0,r.jsxs)("div",{className:"border rounded-lg p-3 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h5",{className:"font-medium text-sm",children:e.chapterName}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>I(t,e.chapterId),children:(0,r.jsx)(N,{className:"h-3 w-3"})})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-3",children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)(eM.J,{className:"text-xs",children:["Questions (Max: ",a,")"]}),(0,r.jsx)(C.p,{type:"number",min:"1",max:a,value:0===e.numberOfQuestions?"":e.numberOfQuestions,onChange:s=>{let r=s.target.value;if(""===r)O(t,e.chapterId,{numberOfQuestions:0,totalMarks:0});else{let s=parseInt(r);!isNaN(s)&&s>=1&&s<=a&&O(t,e.chapterId,{numberOfQuestions:s,totalMarks:4*s})}},onBlur:s=>{(""===s.target.value||0===e.numberOfQuestions)&&O(t,e.chapterId,{numberOfQuestions:1,totalMarks:4})},className:"h-8 text-sm"})]})})]},e.chapterId)})]})]})]})]})},t)})]}),(0,r.jsx)(m,{onNext:s,onSkip:n,onBack:i,backDisabled:l,nextDisabled:!B}),!B&&(0,r.jsx)(x,{message:"Please ensure all subjects have valid configurations (difficulty percentages must sum to 100% and questions/marks must be greater than 0)."})]})}function eO({formData:e,updateFormData:t,onNext:s,onSkip:n,onBack:i,backDisabled:l}){let[o,d]=(0,a.useState)([]),[u,p]=(0,a.useState)(!1),[b,j]=(0,a.useState)([]),y=e=>{let s=[...b,{chapterId:e._id,chapterName:e.name,numberOfQuestions:1,totalMarks:4}];j(s);let r=s.map(e=>({topicId:e.chapterId,topicName:e.chapterName,numberOfQuestions:e.numberOfQuestions,totalMarks:e.totalMarks}));t({chapters:s,topics:r})},v=e=>{let s=b.filter(t=>t.chapterId!==e);j(s);let r=s.map(e=>({topicId:e.chapterId,topicName:e.chapterName,numberOfQuestions:e.numberOfQuestions,totalMarks:e.totalMarks}));t({chapters:s,topics:r})},w=(e,s)=>{let r=b.map(t=>t.chapterId===e?{...t,...s}:t);j(r);let a=r.map(e=>({topicId:e.chapterId,topicName:e.chapterName,numberOfQuestions:e.numberOfQuestions,totalMarks:e.totalMarks}));t({chapters:r,topics:a})},k=()=>b.reduce((e,t)=>e+t.numberOfQuestions,0),S=e=>b.some(t=>t.chapterId===e);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center space-y-2",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold",children:"Select Chapters"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Choose specific chapters and configure question counts for each chapter."})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-lg",children:"Selection Summary"})}),(0,r.jsx)(f.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:b.length}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Chapters Selected"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:k()}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Questions"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:b.reduce((e,t)=>e+t.totalMarks,0)}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"Total Marks"})]})]})})]}),(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-lg",children:"Available Chapters"})}),(0,r.jsx)(f.Wu,{children:u?(0,r.jsx)("div",{className:"text-center py-4",children:"Loading chapters..."}):0===o.length?(0,r.jsx)("div",{className:"text-center py-4 text-gray-500",children:"No chapters available for the selected subject."}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:o.map(e=>(0,r.jsx)(eq.Bc,{children:(0,r.jsxs)(eq.m_,{children:[(0,r.jsx)(eq.k$,{asChild:!0,children:(0,r.jsxs)(c.$,{variant:S(e._id)?"default":"outline",onClick:()=>{S(e._id)?v(e._id):y(e)},className:"h-auto p-3 flex flex-col items-start space-y-1",disabled:0===e.questionCount,children:[(0,r.jsx)("div",{className:"font-medium text-left",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(g.E,{variant:"secondary",className:"text-xs",children:[e.questionCount," questions"]}),(0,r.jsx)(h.A,{className:"h-3 w-3"})]})]})}),(0,r.jsxs)(eq.ZI,{children:[(0,r.jsxs)("p",{children:[e.questionCount," questions available in ",e.name]}),(0,r.jsx)("p",{className:"text-xs mt-1 text-gray-400",children:"Total questions in this topic"}),e.description&&(0,r.jsx)("p",{className:"text-xs mt-1",children:e.description})]})]})},e._id))})})]}),b.length>0&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsx)(f.ZB,{className:"text-lg",children:"Configure Selected Chapters"})}),(0,r.jsx)(f.Wu,{className:"space-y-4",children:b.map(e=>{let t=o.find(t=>t._id===e.chapterId),s=t?.questionCount||0;return(0,r.jsxs)("div",{className:"border rounded-lg p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h4",{className:"font-medium",children:e.chapterName}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>v(e.chapterId),children:(0,r.jsx)(N,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(eM.J,{children:["Number of Questions (Max: ",s,")"]}),(0,r.jsx)(C.p,{type:"number",min:"1",max:s,value:0===e.numberOfQuestions?"":e.numberOfQuestions,onChange:t=>{let r=t.target.value;if(""===r)w(e.chapterId,{numberOfQuestions:0,totalMarks:0});else{let t=parseInt(r);!isNaN(t)&&t>=1&&t<=s&&w(e.chapterId,{numberOfQuestions:t,totalMarks:4*t})}},onBlur:t=>{(""===t.target.value||0===e.numberOfQuestions)&&w(e.chapterId,{numberOfQuestions:1,totalMarks:4})}})]})]},e.chapterId)})})]}),(0,r.jsx)(m,{onNext:s,onSkip:n,onBack:i,backDisabled:l,nextDisabled:!1}),(0,r.jsx)(x,{message:0===b.length?"No chapters selected. Questions will be randomly selected from the entire subject. You can skip this step or select specific chapters for more targeted selection.":`You have selected ${b.length} chapter(s) with ${k()} total questions. Next, you can optionally select specific topics within these chapters.`})]})}function eT({formData:e,updateFormData:t,onNext:s,onSkip:n,onBack:i,backDisabled:l}){let[o,d]=(0,a.useState)([]),[u,p]=(0,a.useState)(!1),[b,j]=(0,a.useState)([]),y=e=>{let s=[...b,{topicId:e._id,topicName:e.name,numberOfQuestions:1,totalMarks:10}];j(s),t({topics:s})},v=e=>{let s=b.filter(t=>t.topicId!==e);j(s),t({topics:s})},k=(e,s,r)=>{let a=b.map(t=>t.topicId===e?{...t,[s]:r}:t);j(a),t({topics:a})},S=e=>b.some(t=>t.topicId===e),M=b.length>0;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Select Topics (Optional)"}),(0,r.jsx)("p",{className:"text-muted-foreground mt-2",children:"Choose specific topics for more targeted question selection"})]}),u?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"}),(0,r.jsx)("p",{className:"mt-2 text-muted-foreground",children:"Loading topics..."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5"}),"Available Topics"]})}),(0,r.jsx)(f.Wu,{children:o.length>0?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:o.map(e=>(0,r.jsx)(eq.Bc,{children:(0,r.jsxs)(eq.m_,{children:[(0,r.jsx)(eq.k$,{asChild:!0,children:(0,r.jsx)(c.$,{variant:S(e._id)?"default":"outline",className:"h-auto p-3 justify-start",onClick:()=>S(e._id)?v(e._id):y(e),disabled:S(e._id),children:(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:e.name}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:[e.questionCount," questions available"]})]})})}),(0,r.jsx)(eq.ZI,{children:(0,r.jsx)("p",{children:e.description||`${e.questionCount} questions available in ${e.name}`})})]})},e._id))}):(0,r.jsx)(x,{type:"info",message:"No topics available for the selected chapters. You can skip this step to select from all questions in the chosen chapters."})})]}),b.length>0&&(0,r.jsxs)(f.Zp,{children:[(0,r.jsx)(f.aR,{children:(0,r.jsxs)(f.ZB,{className:"flex items-center gap-2",children:[(0,r.jsx)(w.A,{className:"h-5 w-5"}),"Selected Topics (",b.length,")"]})}),(0,r.jsxs)(f.Wu,{className:"space-y-4",children:[b.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-4 p-3 border rounded-lg",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(g.E,{variant:"secondary",children:e.topicName})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(eM.J,{htmlFor:`questions-${e.topicId}`,className:"text-sm",children:"Questions:"}),(0,r.jsx)(C.p,{id:`questions-${e.topicId}`,type:"number",min:"1",max:"100",value:e.numberOfQuestions,onChange:t=>k(e.topicId,"numberOfQuestions",parseInt(t.target.value)||1),className:"w-20"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(eM.J,{htmlFor:`marks-${e.topicId}`,className:"text-sm",children:"Marks:"}),(0,r.jsx)(C.p,{id:`marks-${e.topicId}`,type:"number",min:"1",max:"1000",value:e.totalMarks,onChange:t=>k(e.topicId,"totalMarks",parseInt(t.target.value)||1),className:"w-20"})]}),(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>v(e.topicId),children:(0,r.jsx)(N,{className:"h-4 w-4"})})]},e.topicId)),(0,r.jsx)("div",{className:"pt-2 border-t",children:(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{children:["Total Questions: ",b.reduce((e,t)=>e+t.numberOfQuestions,0)]}),(0,r.jsxs)("span",{children:["Total Marks: ",b.reduce((e,t)=>e+t.totalMarks,0)]})]})})]})]})]}),(0,r.jsx)(m,{onNext:()=>{M&&s()},onSkip:()=>{j([]),t({topics:void 0,topicId:void 0}),n()},onBack:i,nextDisabled:!1,backDisabled:l,nextLabel:b.length>0?"Continue with Selected Topics":"Skip Topic Selection",skipLabel:"Skip Topics",showSkip:!0})]})}s(63564);let e$={questionType:"",title:"",description:"",paperMode:"single",course:"",subject:"",subjects:[],subjectConfigs:{},difficultyMode:"auto",difficultyLevels:{easyPercentage:30,mediumPercentage:50,hardPercentage:20},numberOfQuestions:1,totalMarks:100,includeAnswers:!1,duration:60,instructions:"",topicId:void 0};function eR(){let[e,t]=(0,a.useState)(0),[s,n]=(0,a.useState)(e$),[l,o]=(0,a.useState)(!1),c=e=>{n(t=>({...t,...e}))},d=()=>{let r=e+1;if("single"===s.paperMode){f[r]?.title==="Select Topics (Optional)"&&r++;let e=s.chapters&&s.chapters.length>0,t=s.topics&&s.topics.length>0;(e||t)&&f[r]?.title==="Question Selection Criteria"&&r++}t(Math.min(r,f.length-1))},u=()=>{let r=e-1;if("single"===s.paperMode){f[r]?.title==="Select Topics (Optional)"&&r--;let e=s.chapters&&s.chapters.length>0,t=s.topics&&s.topics.length>0;(e||t)&&f[r]?.title==="Question Selection Criteria"&&r--}t(Math.max(r,0))},m=()=>{d()},h=()=>{t(0)},x=async()=>{if(!l)try{let e;o(!0),console.log("Submitting data:",s);let r=localStorage.getItem("backendToken"),a=localStorage.getItem("firebaseToken"),l=localStorage.getItem("token");if(console.log("Available tokens:",{backendToken:r?`${r.substring(0,20)}...`:"None",firebaseToken:a?`${a.substring(0,20)}...`:"None",token:l?`${l.substring(0,20)}...`:"None"}),!s.title?.trim()){o(!1),alert("Please enter a title for the question paper");return}if(!s.questionType){o(!1),alert("Please select an exam type");return}if("single"===s.paperMode){if(!s.subject){o(!1),alert("Please select a subject");return}}else if(0===s.subjects.length){o(!1),alert("Please select at least one subject");return}if("single"===s.paperMode){if(e={title:s.title,description:s.description,subject:s.subject,totalMarks:s.totalMarks,duration:s.duration,examType:s.questionType,instructions:s.instructions,chapterId:s.chapterId,topicId:s.topicId},s.topics&&s.topics.length>0){e.topics=s.topics;let t=s.topics.reduce((e,t)=>e+t.numberOfQuestions,0),r=s.topics.reduce((e,t)=>e+t.totalMarks,0);e.totalMarks=r,e.customise&&(e.customise.numberOfQuestions=t,e.customise.totalMarks=r)}else if(s.chapters&&s.chapters.length>0){e.chapters=s.chapters;let t=s.chapters.reduce((e,t)=>e+t.numberOfQuestions,0),r=s.chapters.reduce((e,t)=>e+t.totalMarks,0);e.totalMarks=r,e.customise&&(e.customise.numberOfQuestions=t,e.customise.totalMarks=r)}"custom"===s.difficultyMode?e.customise={customDifficulty:s.difficultyLevels,numberOfQuestions:s.numberOfQuestions,totalMarks:s.totalMarks,duration:s.duration,includeAnswers:s.includeAnswers}:e.customise={customDifficulty:{easyPercentage:30,mediumPercentage:50,hardPercentage:20},numberOfQuestions:s.numberOfQuestions,totalMarks:s.totalMarks,duration:s.duration,includeAnswers:s.includeAnswers}}else{let t=s.subjects.map(e=>{let t=s.subjectConfigs[e];return{subject:e,numberOfQuestions:t.numberOfQuestions,totalMarks:t.totalMarks,customDifficulty:t.difficultyLevels,chapterId:t.chapterId,topicId:t.topicId,chapters:t.chapters,topics:t.topics}});e={title:s.title,description:s.description,duration:s.duration,examType:s.questionType,instructions:s.instructions,subjects:t,includeAnswers:s.includeAnswers}}console.log("Calling createQuestionPaper API...");let c=await (0,i.ZG)(e);if(console.log("API result:",c),!c.success){console.log("API returned error:",c.error),o(!1);let e=c.error;if(e.includes("Authentication required")||e.includes("Unauthorized"))e="Please log in again to continue. Your session may have expired.";else if(e.includes("Network")||e.includes("fetch"))e="Please check your internet connection and try again.";else if(e.includes("unused questions available")){let t=e.match(/Only (\d+) unused questions available\. Requested: (\d+)/);if(t){let s=t[1],r=t[2];e=`Only ${s} questions are available for this subject/topic, but you requested ${r} questions. Please reduce the number of questions or add more questions to the database.`}}alert(`Error: ${e}`),console.log("Staying on current step due to error");return}console.log("API success! Proceeding with download..."),console.log("Full API response:",c);let d=c.data;if(console.log("Question paper data:",d),!d||!d._id)throw console.error("No question paper ID found in response. Full response:",c),Error("Question paper was created but no ID was returned. Please check the console for details and try again.");let u=await (0,i.cH)(d._id),m=u.questionPaper,p=u.college||{name:"",logoUrl:"",address:""},h={title:m.title,description:m.description||"",duration:m.duration,totalMarks:m.totalMarks,instructions:m.instructions||"",includeAnswers:s.includeAnswers,questions:(()=>{if(m.isMultiSubject&&m.sections){let e=[];return m.sections.forEach(t=>{let s=t.subjectName||t.name||"";t.questions.forEach(t=>{let r=t.question||t;e.push({question:r.content||r.question||"",options:r.options||[],answer:r.answer||"",subject:s,imageUrls:r.imageUrls||[],solution:r.solution||null,hints:r.hints||[]})})}),e}return(m.questions||[]).map(e=>({question:e.content||e.question||"",options:e.options||[],answer:e.answer||"",subject:m.subjectId?.name||"",imageUrls:e.imageUrls||[],solution:e.solution||null,hints:e.hints||[]}))})(),filename:`${m.title.replace(/\s+/g,"_")}_${Date.now()}.pdf`,collegeName:p?.name||"",collegeLogoUrl:p?.logoUrl||""},x=await fetch("/api/generate-paper-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(h)});if(!x.ok)throw Error("Server-side PDF generation failed");let f=await x.blob(),g=window.URL.createObjectURL(f),b=document.createElement("a");b.href=g,b.download=h.filename,document.body.appendChild(b),b.click(),document.body.removeChild(b),window.URL.revokeObjectURL(g),alert("Question paper generated and downloaded successfully!"),console.log("Success! About to redirect to first step in 1 second..."),setTimeout(()=>{console.log("Redirecting to first step now..."),t(0),n(e$),o(!1),console.log("Redirect completed. Current step should be 0")},1e3)}catch(e){o(!1),alert("Error: An unexpected error occurred. Please try again.")}},f=(()=>{let t=[{title:"Question Type",icon:"HelpCircle",component:(0,r.jsx)(p,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Paper Details",icon:"FileText",component:(0,r.jsx)(eP,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Course & Subject Selection",icon:"BookOpen",component:(0,r.jsx)(v,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})}];return"multi"===s.paperMode&&t.push({title:"Configure Subjects",icon:"Settings",component:(0,r.jsx)(eI,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Paper Customization",icon:"FileEdit",component:(0,r.jsx)(eh,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})}),"single"===s.paperMode&&t.push({title:"Select Chapters (Optional)",icon:"BookOpen",component:(0,r.jsx)(eO,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Select Topics (Optional)",icon:"Tag",component:(0,r.jsx)(eT,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Select Difficulty Level",icon:"BarChart2",component:(0,r.jsx)(k,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Question Selection Criteria",icon:"FileText",component:(0,r.jsx)(S,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Paper Customization",icon:"FileEdit",component:(0,r.jsx)(eh,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})}),t.push({title:"Include Answers?",icon:"CheckSquare",component:(0,r.jsx)(ex,{formData:s,updateFormData:c,onNext:d,onSkip:m,onBack:u,backDisabled:0===e})},{title:"Generate Paper",icon:"FileOutput",component:(0,r.jsx)(eb,{formData:s,onSubmit:x,isLoading:l,onBack:h})}),t})();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(eS,{currentStep:e,steps:f.map(e=>({title:e.title,icon:e.icon}))}),f[e].component]})}let eQ=()=>{let[e,t]=(0,a.useState)("tab1");return(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Generate Questions"}),(0,r.jsx)(n.A,{items:[{label:"Home",href:"/teacher"},{label:"...",href:"#"},{label:"Generate Questions"}],className:"text-sm mt-1"})]}),(0,r.jsx)("div",{className:"flex justify-center mt-6",children:(0,r.jsx)("div",{className:"w-full max-w-2xl p-6",children:(0,r.jsx)(eR,{})})})]})})}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},44587:(e,t,s)=>{Promise.resolve().then(s.bind(s,44075))},51947:(e,t,s)=>{"use strict";s.d(t,{ZG:()=>n,cH:()=>i,ig:()=>l});let r="http://localhost:3000/api";function a(){let e=localStorage.getItem("backendToken"),t=localStorage.getItem("firebaseToken"),s=localStorage.getItem("token"),r={"Content-Type":"application/json"};if(e)r.Authorization=`Bearer ${e}`;else if(t)r.Authorization=`Bearer ${t}`;else if(s)r.Authorization=`Bearer ${s}`;else throw Error("Authentication required - Please log in again. No valid authentication token found.");return r}async function n(e){try{let t=a(),s=await fetch(`${r}/question-papers`,{method:"POST",headers:t,body:JSON.stringify(e)});if(!s.ok){let e=`Error: ${s.status} - ${s.statusText}`;try{let t=await s.text();if(t)try{let s=JSON.parse(t);e=s&&s.message?s.message:s&&s.error?s.error:t}catch(s){e=t}}catch(e){}if(!e||e===`Error: ${s.status} - ${s.statusText}`)switch(s.status){case 401:e="Authentication required - Please log in again.";break;case 403:e="Access denied - You don't have permission to perform this action.";break;case 404:e="Resource not found - The requested item could not be found.";break;case 429:e="Too many requests - Please wait a moment before trying again.";break;case 500:e="Server error - Please try again later.";break;case 503:e="Service unavailable - The server is temporarily down.";break;default:s.status>=400&&s.status<500?e="Invalid request - Please check your input and try again.":s.status>=500&&(e="Server error - Please try again later.")}return{success:!1,error:e}}let n=await s.json();if(console.log("Raw API response from createQuestionPaper:",n),n.questionPaper)return{success:!0,data:n.questionPaper};return{success:!0,data:n}}catch(e){return{success:!1,error:"Network error - Please check your connection and try again."}}}async function i(e){try{let t;if(console.log("getQuestionPaperForPDF called with:",e),!e||"undefined"===e||"null"===e)throw Error(`Invalid question paper ID: ${e}`);let s=a(),n=await fetch(`${r}/question-papers/${e}`,{method:"GET",headers:s});if(!n.ok){let e=await n.text();throw console.error("API Error Response:",e),Error(`Failed to fetch question paper: ${n.status} ${n.statusText}`)}let i=await n.json();if(console.log("Question paper data fetched:",i),console.log("College ID in question paper:",i.collegeId),i.college?(t={name:i.college.name,logoUrl:i.college.logoUrl,address:i.college.address},console.log("College information found in response:",t)):i.collegeId&&"object"==typeof i.collegeId&&i.collegeId.name?(t={name:i.collegeId.name,logoUrl:i.collegeId.logoUrl,address:i.collegeId.address},console.log("College information found in populated collegeId:",t)):console.log("No college information available - PDF will be generated without college branding"),i.questionPaper)return{questionPaper:i.questionPaper,college:i.college||t};return{questionPaper:i,college:t}}catch(e){throw console.error("Error fetching question paper for PDF:",e),e}}async function l(){try{let e=a(),t=await fetch(`${r}/question-papers`,{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||`Error: ${t.status} - ${t.statusText}`)}return await t.json()}catch(e){throw console.error("Error fetching question papers:",e),e}}},53933:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(60687),a=s(43210),n=s.n(a),i=s(85814),l=s.n(i),o=s(4780),c=s(14952),d=s(93661);let u=({items:e,maxItems:t=4,className:s})=>{let a=n().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,o.cn)("flex items-center text-sm",s),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:a.map((e,t)=>{let s=t===a.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):s?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(l(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},63564:(e,t,s)=>{"use strict";s.d(t,{Kd:()=>a,OY:()=>n,Tw:()=>i,do:()=>l});var r=s(62185);let a=async(e,t)=>{try{let s=new URLSearchParams;e&&s.append("chapterId",e),t&&s.append("subjectId",t);let a=s.toString(),n=a?`/topics?${a}`:"/topics";return await (0,r.apiCall)(n)}catch(e){throw console.error("Error fetching topics:",e),Error(e.message||"Failed to fetch topics")}},n=async e=>{try{return await (0,r.apiCall)("/topics",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating topic:",e),Error(e.message||"Failed to create topic")}},i=async(e,t)=>{try{return await (0,r.apiCall)(`/topics/${e}`,{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error(`Error updating topic ${e}:`,t),Error(t.message||"Failed to update topic")}},l=async e=>{try{await (0,r.apiCall)(`/topics/${e}`,{method:"DELETE"})}catch(t){throw console.error(`Error deleting topic ${e}:`,t),Error(t.message||"Failed to delete topic")}}},65668:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-help",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},69684:(e,t,s)=>{Promise.resolve().then(s.bind(s,5398))},71182:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(92555);function n(){return(0,r.jsx)(a.W,{message:"Loading teacher dashboard..."})}},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,t,s)=>{"use strict";s.d(t,{b:()=>l});var r=s(43210),a=s(3416),n=s(60687),i=r.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},79551:e=>{"use strict";e.exports=require("url")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var r=s(60687);s(43210);var a=s(78148),n=s(4780);function i({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},82013:(e,t,s)=>{"use strict";s.d(t,{CG:()=>n,Fb:()=>o,Nj:()=>a,getSubjectsWithTopics:()=>l,gg:()=>d,iQ:()=>c,py:()=>i});var r=s(62185);let a=async()=>{try{return await (0,r.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},n=async()=>{try{return(await (0,r.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},i=async()=>{try{return await (0,r.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},l=async()=>{try{return await (0,r.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},o=async e=>{try{return await (0,r.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},c=async(e,t)=>{try{return await (0,r.apiCall)(`/subjects/${e}`,{method:"PATCH",body:JSON.stringify(t)})}catch(t){throw console.error(`Error updating subject ${e}:`,t),Error(t.message||"Failed to update subject")}},d=async e=>{try{await (0,r.apiCall)(`/subjects/${e}`,{method:"DELETE"})}catch(t){throw console.error(`Error deleting subject ${e}:`,t),Error(t.message||"Failed to delete subject")}}},83997:e=>{"use strict";e.exports=require("tty")},86964:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(60687),a=s(66327),n=s(53355),i=s(99557),l=s(45285);function o({children:e}){return(0,r.jsx)(l.A,{allowedRoles:[i.g.TEACHER],children:(0,r.jsx)(n.default,{children:(0,r.jsx)(a.N,{role:i.g.TEACHER,children:e})})})}},93661:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96882:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,4619,3287,9592,4707,6658],()=>s(39053));module.exports=r})();