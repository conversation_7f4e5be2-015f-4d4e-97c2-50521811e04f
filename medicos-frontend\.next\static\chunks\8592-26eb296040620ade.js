"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8592],{5623:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},28905:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(12115),i=n(6101),o=n(52712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[i,a]=r.useState(),u=r.useRef({}),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,i=l(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==i?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,o.N)(()=>{if(i){var e;let t;let n=null!==(e=i.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===i&&r&&(f("ANIMATION_END"),!s.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},o=e=>{e.target===i&&(c.current=l(u.current))};return i.addEventListener("animationstart",o),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{n.clearTimeout(t),i.removeEventListener("animationstart",o),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,i.s)(a.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,i=r&&"isReactWarning"in r&&r.isReactWarning;return i?e.ref:(i=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},35563:(e,t,n)=>{n.d(t,{rc:()=>_,ZD:()=>I,UC:()=>C,VY:()=>P,hJ:()=>T,ZL:()=>R,bL:()=>E,hE:()=>U});var r=n(12115),i=n(95155);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return r.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}(...e),e)}var l=n(4033),u=n(66634),s="AlertDialog",[c,d]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let i=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return o.scopeName=e,[function(t,o){let a=r.createContext(o),l=n.length;n=[...n,o];let u=t=>{let{scope:n,children:o,...u}=t,s=n?.[e]?.[l]||a,c=r.useMemo(()=>u,Object.values(u));return(0,i.jsx)(s.Provider,{value:c,children:o})};return u.displayName=t+"Provider",[u,function(n,i){let u=i?.[e]?.[l]||a,s=r.useContext(u);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:r})=>{let i=n(e)[`__scope${r}`];return{...t,...i}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(o,...t)]}(s,[l.Hs]),f=(0,l.Hs)(),p=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,i.jsx)(l.bL,{...r,...n,modal:!0})};p.displayName=s,r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,i.jsx)(l.l9,{...o,...r,ref:t})}).displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,i.jsx)(l.ZL,{...r,...n})};m.displayName="AlertDialogPortal";var y=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,i.jsx)(l.hJ,{...o,...r,ref:t})});y.displayName="AlertDialogOverlay";var v="AlertDialogContent",[h,g]=c(v),N=(0,u.Dc)("AlertDialogContent"),A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:o,...u}=e,s=f(n),c=r.useRef(null),d=a(t,c),p=r.useRef(null);return(0,i.jsx)(l.G$,{contentName:v,titleName:x,docsSlug:"alert-dialog",children:(0,i.jsx)(h,{scope:n,cancelRef:p,children:(0,i.jsxs)(l.UC,{role:"alertdialog",...s,...u,ref:d,onOpenAutoFocus:function(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}(u.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=p.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,i.jsx)(N,{children:o}),(0,i.jsx)(j,{contentRef:c})]})})})});A.displayName=v;var x="AlertDialogTitle",k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,i.jsx)(l.hE,{...o,...r,ref:t})});k.displayName=x;var w="AlertDialogDescription",b=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,i.jsx)(l.VY,{...o,...r,ref:t})});b.displayName=w;var M=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,o=f(n);return(0,i.jsx)(l.bm,{...o,...r,ref:t})});M.displayName="AlertDialogAction";var D="AlertDialogCancel",O=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=g(D,n),u=f(n),s=a(t,o);return(0,i.jsx)(l.bm,{...u,...r,ref:s})});O.displayName=D;var j=e=>{let{contentRef:t}=e,n="`".concat(v,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(v,"` by passing a `").concat(w,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(v,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},E=p,R=m,T=y,C=A,_=M,I=O,U=k,P=b},42355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},44020:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},91788:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}}]);