(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2856],{2564:(e,t,a)=>{"use strict";a.d(t,{b:()=>n,s:()=>o});var r=a(12115),l=a(63540),s=a(95155),o=r.forwardRef((e,t)=>(0,s.jsx)(l.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));o.displayName="VisuallyHidden";var n=o},6600:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>z});var r=a(95155),l=a(12115),s=a(15356),o=a(17580);let n=(0,a(19946).A)("user-check",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var i=a(57434),c=a(55509),d=a(91788),u=a(66695),h=a(59409),g=a(83540),m=a(94517),f=a(24026),p=a(59434);let x={light:"",dark:".dark"},v=l.createContext(null);function y(e){let{id:t,className:a,children:s,config:o,...n}=e,i=l.useId(),c="chart-".concat(t||i.replace(/:/g,""));return(0,r.jsx)(v.Provider,{value:{config:o},children:(0,r.jsxs)("div",{"data-slot":"chart","data-chart":c,className:(0,p.cn)("[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden",a),...n,children:[(0,r.jsx)(j,{id:c,config:o}),(0,r.jsx)(g.u,{children:s})]})})}let j=e=>{let{id:t,config:a}=e,l=Object.entries(a).filter(e=>{let[,t]=e;return t.theme||t.color});return l.length?(0,r.jsx)("style",{dangerouslySetInnerHTML:{__html:Object.entries(x).map(e=>{let[a,r]=e;return"\n".concat(r," [data-chart=").concat(t,"] {\n").concat(l.map(e=>{var t;let[r,l]=e,s=(null===(t=l.theme)||void 0===t?void 0:t[a])||l.color;return s?"  --color-".concat(r,": ").concat(s,";"):null}).join("\n"),"\n}\n")}).join("\n")}}):null};function b(e){let{active:t,payload:a,className:s,indicator:o="dot",hideLabel:n=!1,hideIndicator:i=!1,label:c,labelFormatter:d,labelClassName:u,formatter:h,color:g,nameKey:m,labelKey:f}=e,{config:x}=function(){let e=l.useContext(v);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}(),y=l.useMemo(()=>{var e;if(n||!(null==a?void 0:a.length))return null;let[t]=a,l="".concat(f||(null==t?void 0:t.dataKey)||(null==t?void 0:t.name)||"value"),s=w(x,t,l),o=f||"string"!=typeof c?null==s?void 0:s.label:(null===(e=x[c])||void 0===e?void 0:e.label)||c;return d?(0,r.jsx)("div",{className:(0,p.cn)("font-medium",u),children:d(o,a)}):o?(0,r.jsx)("div",{className:(0,p.cn)("font-medium",u),children:o}):null},[c,d,a,n,u,x,f]);if(!t||!(null==a?void 0:a.length))return null;let j=1===a.length&&"dot"!==o;return(0,r.jsxs)("div",{className:(0,p.cn)("border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl",s),children:[j?null:y,(0,r.jsx)("div",{className:"grid gap-1.5",children:a.map((e,t)=>{let a="".concat(m||e.name||e.dataKey||"value"),l=w(x,e,a),s=g||e.payload.fill||e.color;return(0,r.jsx)("div",{className:(0,p.cn)("[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5","dot"===o&&"items-center"),children:h&&(null==e?void 0:e.value)!==void 0&&e.name?h(e.value,e.name,e,t,e.payload):(0,r.jsxs)(r.Fragment,{children:[(null==l?void 0:l.icon)?(0,r.jsx)(l.icon,{}):!i&&(0,r.jsx)("div",{className:(0,p.cn)("shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)",{"h-2.5 w-2.5":"dot"===o,"w-1":"line"===o,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===o,"my-0.5":j&&"dashed"===o}),style:{"--color-bg":s,"--color-border":s}}),(0,r.jsxs)("div",{className:(0,p.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,r.jsxs)("div",{className:"grid gap-1.5",children:[j?y:null,(0,r.jsx)("span",{className:"text-muted-foreground",children:(null==l?void 0:l.label)||e.name})]}),e.value&&(0,r.jsx)("span",{className:"text-foreground font-mono font-medium tabular-nums",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})}function w(e,t,a){if("object"!=typeof t||null===t)return;let r="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,l=a;return a in t&&"string"==typeof t[a]?l=t[a]:r&&a in r&&"string"==typeof r[a]&&(l=r[a]),l in e?e[l]:e[a]}m.m,f.s;var N=a(3401),k=a(94754),S=a(96025),A=a(16238),E=a(83394),T=a(74125);let C=e=>{let{title:t,collegeId:a,subjectColors:s,timeRanges:o=["Daily","Weekly","Monthly","Yearly"],defaultTimeRange:n="Monthly",className:i}=e,[c,d]=(0,l.useState)(n),[f,p]=(0,l.useState)([]),[x,v]=(0,l.useState)(!1),j=s.reduce((e,t)=>{let{subject:a,color:r}=t;return{...e,[a]:{color:r}}},{}),w=e=>{let t=new Date,a=new Date(t),r=new Date(t);switch(e){case"Daily":r.setDate(t.getDate()-7);break;case"Weekly":r.setDate(t.getDate()-30);break;case"Monthly":default:r.setMonth(t.getMonth()-6);break;case"Yearly":r.setFullYear(t.getFullYear()-1)}return{startDate:r.toISOString(),endDate:a.toISOString()}},C=async()=>{if(!a)return;let{startDate:e,endDate:t}=w(c);v(!0);try{let r=await (0,T.mi)(a,e,t);if(!Array.isArray(r)){console.warn("API did not return an array. Got:",r),p([]);return}let l=r.map(e=>({name:e.dateLabel,...e.subjectCounts}));p(l)}catch(e){console.error("Failed to fetch chart data:",e),p([])}finally{v(!1)}};return(0,l.useEffect)(()=>{C()},[a,c]),(0,r.jsxs)(u.Zp,{className:"p-4 h-full ".concat(i||""),children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:t}),(0,r.jsxs)(h.l6,{value:c,onValueChange:e=>d(e),children:[(0,r.jsx)(h.bq,{className:"w-[120px]",children:(0,r.jsx)(h.yv,{placeholder:c})}),(0,r.jsx)(h.gC,{children:o.map(e=>(0,r.jsx)(h.eb,{value:e,children:e},e))})]})]}),(0,r.jsx)("div",{className:"space-y-2 mb-2",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-4",children:s.map(e=>{let{subject:t,color:a}=e;return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:a}}),(0,r.jsx)("span",{className:"text-sm text-muted-foreground",children:t})]},t)})})}),(0,r.jsx)("div",{className:"h-[calc(100%-80px)] min-h-[250px] w-full",children:(0,r.jsx)(y,{config:j,children:(0,r.jsx)(g.u,{width:"98%",height:"100%",children:(0,r.jsxs)(N.E,{data:f.length?f:[{name:"No Data"}],margin:{top:5,right:10,left:10,bottom:20},barGap:4,layout:"horizontal",children:[(0,r.jsx)(k.d,{strokeDasharray:"3 3",vertical:!1}),(0,r.jsx)(S.W,{dataKey:"name",axisLine:!1,tickLine:!1,tick:{fontSize:10},dy:8,height:20}),(0,r.jsx)(A.h,{axisLine:!1,tickLine:!1,tick:{fontSize:10},domain:[0,"dataMax"],width:40,tickFormatter:e=>e>=1e3?"".concat((e/1e3).toFixed(1),"K"):e.toString(),padding:{top:10}}),(0,r.jsx)(m.m,{content:e=>e.active&&e.payload&&e.payload.length?(0,r.jsx)(b,{indicator:"line",payload:e.payload}):null,cursor:{fill:"rgba(0, 0, 0, 0.05)"}}),s.map(e=>{let{subject:t,color:a}=e;return(0,r.jsx)(E.y,{dataKey:t,fill:a,radius:[2,2,0,0],maxBarSize:25},t)})]})})})})]})};var P=a(62523),_=a(47924),F=a(71007);let I=e=>{let{title:t,teachers:a,className:s,onSearch:o,onTeacherClick:n}=e,[i,c]=l.useState("");return(0,r.jsxs)(u.Zp,{className:"p-6 ".concat(s),children:[(0,r.jsx)("h3",{className:"text-lg font-medium mb-6",children:t}),(0,r.jsxs)("div",{className:"relative mb-6",children:[(0,r.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(P.p,{placeholder:"Search...",className:"pl-9",value:i,onChange:e=>{c(e.target.value),o&&o(e.target.value)}})]}),(0,r.jsx)("div",{className:"space-y-4",children:a.map(e=>(0,r.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer hover:bg-muted/50 rounded-md p-2 transition-colors",onClick:()=>null==n?void 0:n(e),children:[(0,r.jsxs)("div",{className:"relative",children:[e.avatar?(0,r.jsx)("img",{src:e.avatar,alt:e.name,className:"w-10 h-10 rounded-full object-cover"}):(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(F.A,{className:"w-6 h-6 text-gray-400"})}),e.status&&(0,r.jsx)("div",{className:"w-2.5 h-2.5 rounded-full absolute right-0 bottom-0 border-2 border-background ".concat("online"===e.status?"bg-green-500":"away"===e.status?"bg-amber-500":"bg-gray-300")})]}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:e.name}),e.meta&&(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e.meta})]})]},e.id))})]})};function z(){var e,t,a,u,h;let[g,m]=(0,l.useState)(null),[f,p]=(0,l.useState)([]),[x,v]=(0,l.useState)(!1),[y,j]=(0,l.useState)(null);(0,l.useEffect)(()=>{let e=localStorage.getItem("collegeId");if(e){console.log("Found collegeId in localStorage:",e),m(e);return}try{for(let e of["token","backendToken","authToken","jwtToken"]){let t=localStorage.getItem(e);if(t)try{let a=t.split(".");if(3===a.length){let t=JSON.parse(atob(a[1]));if(console.log("JWT payload:",t),t.collegeId){console.log("Found collegeId in ".concat(e,":"),t.collegeId),m(t.collegeId),localStorage.setItem("collegeId",t.collegeId);return}}}catch(t){console.error("Error parsing token from ".concat(e,":"),t)}}console.log("All localStorage keys:",Object.keys(localStorage)),console.error("Could not find collegeId in any token or localStorage")}catch(e){console.error("Error getting collegeId:",e)}},[]);let[b,w]=(0,l.useState)(null),[N,k]=(0,l.useState)(!1),[S,A]=(0,l.useState)(null);return(0,l.useEffect)(()=>{(async()=>{if(g){k(!0),A(null);try{let e=await (0,T.mS)(g);w(e)}catch(e){A(e.message||"Failed to load summary")}finally{k(!1)}}})()},[g]),(0,l.useEffect)(()=>{(async()=>{if(g){v(!0),j(null);try{let e=await (0,T.$T)(g,10);p(e)}catch(e){j(e.message||"Failed to load top teachers"),console.error("Error fetching top teachers:",e)}finally{v(!1)}}})()},[g]),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-5",children:[(0,r.jsx)(s.A,{icon:(0,r.jsx)(o.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Teachers",value:null!==(e=null==b?void 0:b.totalTeachers)&&void 0!==e?e:0,loading:N,error:!!S}),(0,r.jsx)(s.A,{icon:(0,r.jsx)(n,{className:"h-5 w-5 text-muted-foreground"}),label:"Active Teachers",value:null!==(t=null==b?void 0:b.activeTeachers)&&void 0!==t?t:0,loading:N,error:!!S,iconClassName:"bg-green-100",valueClassName:"text-green-600"}),(0,r.jsx)(s.A,{icon:(0,r.jsx)(i.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Question Papers",value:null!==(a=null==b?void 0:b.totalQuestionPapers)&&void 0!==a?a:0,loading:N,error:!!S,iconClassName:"bg-green-100",valueClassName:"text-green-600"}),(0,r.jsx)(s.A,{icon:(0,r.jsx)(c.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Questions",value:null!==(u=null==b?void 0:b.totalQuestions)&&void 0!==u?u:0,loading:N,error:!!S,iconClassName:"bg-amber-100",valueClassName:"text-amber-600"}),(0,r.jsx)(s.A,{icon:(0,r.jsx)(d.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Downloads",value:null!==(h=null==b?void 0:b.totalDownloads)&&void 0!==h?h:0,loading:N,error:!!S,iconClassName:"bg-purple-100",valueClassName:"text-purple-600"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3 mb-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2 overflow-hidden",children:(0,r.jsx)(C,{title:"Questions created per subject",collegeId:g||"",subjectColors:[{subject:"Math",color:"#4F46E5"},{subject:"Physics",color:"#10B981"},{subject:"Chemistry",color:"#F59E0B"},{subject:"Biology",color:"#EC4899"}],defaultTimeRange:"Monthly",className:"w-full h-full"})}),(0,r.jsxs)("div",{className:"lg:col-span-1",children:[(0,r.jsx)(I,{title:"Top Teachers Generating Papers",teachers:x?[]:f,onTeacherClick:e=>{if("online"===e.status||"offline"===e.status)console.log("Clicked on teacher:",e.name);else{var t;console.warn("Teacher status is not clickable:",null!==(t=e.status)&&void 0!==t?t:"unknown")}}}),x&&(0,r.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,r.jsx)("div",{className:"text-sm text-muted-foreground",children:"Loading top teachers..."})}),y&&(0,r.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,r.jsxs)("div",{className:"text-sm text-red-500",children:["Error: ",y]})})]})]})]})}},15356:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(95155);a(12115);var l=a(59434),s=a(94788),o=a(85339),n=a(66695),i=a(68856);let c=e=>{let{icon:t=(0,r.jsx)(s.A,{className:"h-5 w-5 text-muted-foreground"}),label:a,value:c,className:d,iconClassName:u,labelClassName:h,valueClassName:g,loading:m=!1,error:f=!1}=e;return(0,r.jsx)(n.Zp,{className:(0,l.cn)("p-6",d),children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:(0,l.cn)("inline-flex h-10 w-10 items-center justify-center rounded-lg bg-muted",u),children:f?(0,r.jsx)(o.A,{className:"h-5 w-5 text-destructive"}):t}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:(0,l.cn)("text-sm font-medium text-muted-foreground",h),children:a}),m?(0,r.jsx)(i.E,{className:"h-9 w-24"}):f?(0,r.jsx)("p",{className:(0,l.cn)("text-sm font-medium text-destructive"),children:"Failed to load"}):(0,r.jsx)("p",{className:(0,l.cn)("text-3xl font-bold",g),children:c})]})]})})}},32958:(e,t,a)=>{Promise.resolve().then(a.bind(a,6600))},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},55097:(e,t,a)=>{"use strict";a.d(t,{$y:()=>s,cY:()=>o,hS:()=>l});var r=a(56671);function l(e){var t,a,l,s;let o,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=n;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.message)&&(c=e.data.message),(null==e?void 0:e.status)?o=e.status:(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)&&(o=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===o?c="Server error. Please try again later.":503===o&&(c="Service temporarily unavailable. Please try again later.")),i&&r.oR.error(c),{success:!1,error:c,statusCode:o}}function s(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&r.oR.success(a),{success:!0,data:e}}function o(e){return!0===e.success}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>g,gC:()=>h,l6:()=>c,yv:()=>d});var r=a(95155);a(12115);var l=a(4011),s=a(66474),o=a(5196),n=a(47863),i=a(59434);function c(e){let{...t}=e;return(0,r.jsx)(l.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,r.jsx)(l.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:o,...n}=e;return(0,r.jsxs)(l.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[o,(0,r.jsx)(l.In,{asChild:!0,children:(0,r.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:a,position:s="popper",...o}=e;return(0,r.jsx)(l.ZL,{children:(0,r.jsxs)(l.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,...o,children:[(0,r.jsx)(m,{}),(0,r.jsx)(l.LM,{className:(0,i.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(f,{})]})})}function g(e){let{className:t,children:a,...s}=e;return(0,r.jsxs)(l.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(l.VF,{children:(0,r.jsx)(o.A,{className:"size-4"})})}),(0,r.jsx)(l.p4,{children:a})]})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(l.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(l.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(s.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{b:()=>o,cn:()=>s});var r=a(52596),l=a(17307);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,l.QP)((0,r.$)(t))}function o(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(95155);a(12115);var l=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>n,Zp:()=>s,aR:()=>o});var r=a(95155);a(12115);var l=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",t),...a})}},68856:(e,t,a)=>{"use strict";a.d(t,{E:()=>s});var r=a(95155),l=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"skeleton",className:(0,l.cn)("bg-accent animate-pulse rounded-md",t),...a})}},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74125:(e,t,a)=>{"use strict";a.d(t,{$T:()=>u,JY:()=>s,M0:()=>l,N0:()=>i,mS:()=>c,mi:()=>d,pZ:()=>o,qk:()=>n});var r=a(55097);async function l(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to create college. Please try again.")}let l=await a.json();return(0,r.$y)(l,!0,"College created successfully!")}catch(e){return console.error("Error creating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to create college. Please try again.","Failed to create college. Please try again.")}}async function s(){let e=localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let t=await fetch("".concat("http://localhost:3000/api","/colleges"),{method:"GET",headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(t.status),"Failed to load colleges. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching colleges:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load colleges. Please try again.","Failed to load colleges. Please try again.")}}async function o(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to delete college. Please try again.")}let l=await a.json();return(0,r.$y)(l,!0,"College deleted successfully!")}catch(e){return console.error("Error deleting college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to delete college. Please try again.","Failed to delete college. Please try again.")}}async function n(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to load college. Please try again.")}let l=await a.json();return(0,r.$y)(l)}catch(e){return console.error("Error fetching college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load college. Please try again.","Failed to load college. Please try again.")}}async function i(e,t){let a=localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let l=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(l.status),"Failed to update college. Please try again.")}let s=await l.json();return(0,r.$y)(s,!0,"College updated successfully!")}catch(e){return console.error("Error updating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to update college. Please try again.","Failed to update college. Please try again.")}}async function c(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let a=await fetch("".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/summary"),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(a.status))}return await a.json()}catch(e){throw console.error("Error fetching college:",e),e}}async function d(e,t,a){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let l="".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/question-papers?startDate=").concat(encodeURIComponent(t),"&endDate=").concat(encodeURIComponent(a)),s=await fetch(l,{method:"GET",headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(s.status))}return await s.json()}catch(e){throw console.error("Error fetching question paper stats:",e),e}}async function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=localStorage.getItem("backendToken");if(!a)throw Error("Authentication required");try{let r="".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/top-teachers?limit=").concat(t),l=await fetch(r,{method:"GET",headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}});if(!l.ok){let e=await l.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(l.status))}return await l.json()}catch(e){throw console.error("Error fetching top teachers:",e),e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6671,1141,2571,6457,5267,5559,8441,1684,7358],()=>t(32958)),_N_E=e.O()}]);