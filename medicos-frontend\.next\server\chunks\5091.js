exports.id=5091,exports.ids=[5091],exports.modules={15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>p,gC:()=>h,l6:()=>c,yv:()=>d});var a=r(60687);r(43210);var s=r(17403),o=r(78272),n=r(13964),i=r(3589),l=r(4780);function c({...e}){return(0,a.jsx)(s.bL,{"data-slot":"select",...e})}function d({...e}){return(0,a.jsx)(s.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...n}){return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function h({className:e,children:t,position:r="popper",...o}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...o,children:[(0,a.jsx)(m,{}),(0,a.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(f,{})]})})}function p({className:e,children:t,...r}){return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:t})]})}function m({className:e,...t}){return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function f({className:e,...t}){return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}},20943:(e,t,r)=>{Promise.resolve().then(r.bind(r,99111))},31981:(e,t,r)=>{"use strict";r.d(t,{$y:()=>o,cY:()=>n,hS:()=>s});var a=r(52581);function s(e,t="An error occurred. Please try again.",r=!0){let o,n=t;return e?.message?n=e.message:"string"==typeof e?n=e:e?.response?.data?.message?n=e.response.data.message:e?.data?.message&&(n=e.data.message),e?.status?o=e.status:e?.response?.status&&(o=e.response.status),n.includes("already exists")||(n.includes("Authentication")||n.includes("Unauthorized")?n="Please log in again to continue. Your session may have expired.":n.includes("Network")||n.includes("fetch")?n="Please check your internet connection and try again.":n.includes("not found")?n="The requested resource was not found.":n.includes("Forbidden")?n="You do not have permission to perform this action.":500===o?n="Server error. Please try again later.":503===o&&(n="Service temporarily unavailable. Please try again later.")),r&&a.oR.error(n),{success:!1,error:n,statusCode:o}}function o(e,t=!1,r){return t&&r&&a.oR.success(r),{success:!0,data:e}}function n(e){return!0===e.success}},42505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(37413),s=r(92555);function o(){return(0,a.jsx)(s.W,{message:"Loading admin dashboard..."})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>n});var a=r(60687);r(43210);var s=r(4780);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},61193:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(60687),s=r(66327),o=r(99557),n=r(45285),i=r(53355);function l({children:e}){return(0,a.jsx)(n.A,{allowedRoles:[o.g.SUPER_ADMIN],children:(0,a.jsx)(i.default,{children:(0,a.jsx)(s.N,{role:o.g.SUPER_ADMIN,children:e})})})}},63239:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(60687),s=r(43210),o=r.n(s),n=r(85814),i=r.n(n),l=r(4780),c=r(14952),d=r(93661);let u=({items:e,maxItems:t=4,className:r})=>{let s=o().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,a.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,l.cn)("flex items-center text-sm",r),children:(0,a.jsx)("ol",{className:"flex items-center space-x-1",children:s.map((e,t)=>{let r=t===s.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):r?(0,a.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,a.jsx)(i(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},80775:(e,t,r)=>{Promise.resolve().then(r.bind(r,61193))},94487:(e,t,r)=>{"use strict";r.d(t,{N_:()=>d,hG:()=>s,ly:()=>c,sr:()=>n,ul:()=>i,v5:()=>l,zi:()=>o});var a=r(31981);async function s(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r={...e};r.explanation&&""!==r.explanation.trim()||delete r.explanation,delete r.status,delete r.reviewStatus,r.type||(r.type="multiple-choice"),console.log("Sending question data:",JSON.stringify(r,null,2));let s=await fetch("http://localhost:3000/api/questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(r)});if(!s.ok){let e=await s.json().catch(()=>({}));if(console.error("API error response:",e),e.details){let t=Object.entries(e.details).map(([e,t])=>`${e}: ${t}`).join(", ");return(0,a.hS)(t||e.message||`Error: ${s.status}`,"Failed to create question. Please check your input and try again.")}return(0,a.hS)(e.message||`Error: ${s.status}`,"Failed to create question. Please try again.")}let o=await s.json();return(0,a.$y)(o,!0,"Question created successfully!")}catch(e){return console.error("Error creating question:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to create question. Please try again.","Failed to create question. Please try again.")}}async function o(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let r=await fetch(`http://localhost:3000/api/questions/${e}`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||`Error: ${r.status}`)}return await r.json()}catch(e){throw console.error("Error fetching question:",e),e}}async function n(e,t){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let a=await fetch(`http://localhost:3000/api/questions/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||`Error: ${a.status}`)}return await a.json()}catch(e){throw console.error("Error updating question:",e),e}}async function i(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch(`http://localhost:3000/api/questions/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${r.status}`,"Failed to delete question. Please try again.")}let s=await r.json();return(0,a.$y)(s,!0,"Question deleted successfully!")}catch(e){return console.error("Error deleting question:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to delete question. Please try again.","Failed to delete question. Please try again.")}}async function l(e,t){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let a=await fetch(`http://localhost:3000/api/questions/${e}/review`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify({status:t})});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||`Error: ${a.status}`)}return await a.json()}catch(e){throw console.error("Error reviewing question:",e),e}}async function c(e,t,r,a="gemini"){let s=localStorage.getItem("backendToken");if(!s)throw Error("Authentication required");try{let o=new FormData;o.append("file",e),o.append("subjectId",t),o.append("aiProvider",a),r&&o.append("topicId",r);let n=await fetch("http://localhost:3000/api/questions/bulk-upload-pdf",{method:"POST",headers:{Authorization:`Bearer ${s}`},body:o});if(!n.ok){let e=await n.json().catch(()=>({}));if(console.error("PDF upload error response:",e),e.details){let t=Object.entries(e.details).map(([e,t])=>`${e}: ${t}`).join(", ");throw Error(t||e.message||`Error: ${n.status}`)}throw Error(e.message||`Error: ${n.status}`)}return await n.json()}catch(e){throw console.error("Error uploading PDF questions:",e),e}}async function d(e,t,r,a="gemini"){let s=localStorage.getItem("backendToken");if(!s)throw Error("Authentication required");try{let o=new FormData;o.append("file",e),o.append("subjectId",t),o.append("aiProvider",a),r&&o.append("topicId",r);let n=await fetch("http://localhost:3000/api/questions/bulk-upload-chemical-pdf",{method:"POST",headers:{Authorization:`Bearer ${s}`},body:o});if(!n.ok){let e=await n.json().catch(()=>({}));if(console.error("Chemical PDF upload error response:",e),e.details){let t=Object.entries(e.details).map(([e,t])=>`${e}: ${t}`).join(", ");throw Error(t||e.message||`Error: ${n.status}`)}throw Error(e.message||`Error: ${n.status}`)}return await n.json()}catch(e){throw console.error("Error uploading chemical PDF questions:",e),e}}},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx","default")}};