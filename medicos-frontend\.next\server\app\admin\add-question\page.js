(()=>{var e={};e.id=5489,e.ids=[5489],e.modules={464:(e,s,t)=>{Promise.resolve().then(t.bind(t,4483))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4483:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>z});var r=t(60687),a=t(43210),i=t(63239),n=t(30474),l=t(63442),o=t(27605),c=t(45880),d=t(63143),u=t(10022),m=t(96882),x=t(16023),h=t(11860),p=t(41862),j=t(29523),g=t(44493),f=t(71669),b=t(12048),v=t(50812),y=t(15079),N=t(34729),w=t(76242),C=t(20140),A=t(29844);t(82013);var q=t(94487),F=t(31981);let P=c.z.object({subject:c.z.string().min(1,{message:"Please select a subject"}),chapter:c.z.string().min(1,{message:"Please select a chapter"}),questionText:c.z.string().min(5,{message:"Question must be at least 5 characters"}),optionA:c.z.string().optional(),optionB:c.z.string().optional(),optionC:c.z.string().optional(),optionD:c.z.string().optional(),correctAnswer:c.z.enum(["A","B","C","D"],{required_error:"Please select the correct answer"}),explanation:c.z.string().optional(),difficulty:c.z.enum(["Easy","Medium","Hard"],{required_error:"Please select a difficulty level"})}),D=c.z.object({subject:c.z.string().min(1,{message:"Please select a subject"}),chapter:c.z.string().optional(),aiProvider:c.z.enum(["mistral","gemini"]).default("gemini")});function k(){let[e,s]=(0,a.useState)("manual"),[t,i]=(0,a.useState)(!1),[c,k]=(0,a.useState)([]),[z,E]=(0,a.useState)(!0),[$,I]=(0,a.useState)(null),[R,B]=(0,a.useState)({A:null,B:null,C:null,D:null}),[S,_]=(0,a.useState)([]),[M,T]=(0,a.useState)({A:!1,B:!1,C:!1,D:!1}),[L,V]=(0,a.useState)([]),[U,J]=(0,a.useState)(null),[G,O]=(0,a.useState)(!1),Q=(0,a.useRef)(null),H={A:(0,a.useRef)(null),B:(0,a.useRef)(null),C:(0,a.useRef)(null),D:(0,a.useRef)(null)},Z=(0,o.mN)({resolver:(0,l.u)(P),defaultValues:{subject:"",chapter:"",questionText:"",optionA:"",optionB:"",optionC:"",optionD:"",correctAnswer:"",explanation:"",difficulty:""}}),K=(0,o.mN)({resolver:(0,l.u)(D),defaultValues:{subject:"",chapter:"",aiProvider:"gemini"}}),X=e=>{Z.setValue("subject",e),Z.setValue("chapter","");let s=c.find(s=>s._id===e);s?_(s.topics||[]):_([])},W=e=>{K.setValue("subject",e),K.setValue("chapter","");let s=c.find(s=>s._id===e);s?V(s.topics||[]):V([])},Y=(e,s)=>{let t=e.target.files?.[0];if(!t)return;let r=new FileReader;r.onload=e=>{"question"===s?I(e.target?.result):(B(t=>({...t,[s]:e.target?.result})),T(e=>({...e,[s]:!1})))},r.readAsDataURL(t)},ee=e=>{"question"===e?(I(null),Q.current&&(Q.current.value="")):(B(s=>({...s,[e]:null})),H[e]?.current&&(H[e].current.value=""),Z.clearErrors(`option${e}`),T(s=>({...s,[e]:!1})))},es=e=>{let s=[],t={};for(let r of["A","B","C","D"]){let a=e[`option${r}`]&&""!==e[`option${r}`].trim(),i=null!==R[r];a||i?t[r]=!1:(s.push(`Option ${r} must have either text or an image`),t[r]=!0)}return T(t),s},et=async e=>{i(!0);try{let s;console.log("Manual form data:",e);let t=es(e);if(t.length>0){(0,C.o)({title:"Validation Error",description:t.join(", "),variant:"destructive"}),i(!1);return}let r=[e.optionA?.trim()||R.A||"",e.optionB?.trim()||R.B||"",e.optionC?.trim()||R.C||"",e.optionD?.trim()||R.D||""],a=r[({A:0,B:1,C:2,D:3})[e.correctAnswer]],n=e.difficulty.toLowerCase(),l=localStorage.getItem("userData");try{if(l){let e=JSON.parse(l);s=e._id||e.id}}catch(e){console.error("Error parsing user data:",e)}let o={content:e.questionText,options:r,answer:a,subjectId:e.subject,topicId:e.chapter,difficulty:n,type:"multiple-choice"};s&&(o.createdBy=s);let c=e.explanation&&""!==e.explanation.trim()?{...o,explanation:e.explanation}:o,d={...c};$&&(d.content=`${c.content}
${$}`);let u=await (0,q.hG)(d);(0,F.cY)(u)&&ea()}catch(e){console.error("Unexpected error adding question:",e),(0,C.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{i(!1)}},er=async e=>{if(!U){(0,C.o)({title:"Error",description:"Please select a PDF file to upload.",variant:"destructive"});return}i(!0);try{console.log("PDF form data:",e,"Chemical extraction:",G);let s=G?await (0,q.N_)(U,e.subject,e.chapter||void 0,e.aiProvider):await (0,q.ly)(U,e.subject,e.chapter||void 0,e.aiProvider),t=G?"chemical questions with molecular structures":"questions",r=s.questionsAdded||s.questionsCreated||"questions";(0,C.o)({title:"PDF Upload Successful",description:`Successfully uploaded ${r} ${t} from PDF.`}),ei()}catch(e){console.error("Error uploading PDF:",e),(0,C.o)({title:"Error",description:e.message||"Failed to upload PDF. Please try again.",variant:"destructive"})}finally{i(!1)}},ea=()=>{Z.reset({subject:"",chapter:"",questionText:"",optionA:"",optionB:"",optionC:"",optionD:"",correctAnswer:"",explanation:"",difficulty:""}),I(null),B({A:null,B:null,C:null,D:null}),_([]),T({A:!1,B:!1,C:!1,D:!1})},ei=()=>{K.reset({subject:"",chapter:"",aiProvider:"gemini"}),J(null),V([])};return(0,r.jsxs)(g.Zp,{className:"w-full",children:[(0,r.jsx)(g.aR,{children:(0,r.jsx)(g.ZB,{children:"Add Questions"})}),(0,r.jsx)(g.Wu,{children:(0,r.jsxs)(A.tU,{value:e,onValueChange:s,className:"w-full",children:[(0,r.jsxs)(A.j7,{className:"grid w-full grid-cols-2",children:[(0,r.jsxs)(A.Xi,{value:"manual",className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Manual Entry"]}),(0,r.jsxs)(A.Xi,{value:"pdf",className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"Upload PDF"]})]}),(0,r.jsx)(A.av,{value:"manual",className:"space-y-6",children:(0,r.jsx)(f.lV,{...Z,children:(0,r.jsxs)("form",{onSubmit:Z.handleSubmit(et),className:"space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(f.zB,{control:Z.control,name:"subject",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{children:"Subject *"}),(0,r.jsxs)(y.l6,{onValueChange:X,value:e.value,children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(y.bq,{children:(0,r.jsx)(y.yv,{placeholder:z?"Loading subjects...":"Select a subject"})})}),(0,r.jsx)(y.gC,{children:z?(0,r.jsx)(y.eb,{value:"loading",disabled:!0,children:"Loading subjects..."}):c.map(e=>(0,r.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,r.jsx)(f.C5,{})]})}),(0,r.jsx)(f.zB,{control:Z.control,name:"chapter",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{children:"Chapter *"}),(0,r.jsxs)(y.l6,{onValueChange:e.onChange,value:e.value,disabled:0===S.length,children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(y.bq,{children:(0,r.jsx)(y.yv,{placeholder:z?"Loading chapters...":S.length>0?"Select a chapter":"Select a subject first"})})}),(0,r.jsx)(y.gC,{children:S.map(e=>(0,r.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,r.jsx)(f.C5,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(f.zB,{control:Z.control,name:"questionText",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{children:"Question Text *"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(N.T,{placeholder:"Enter your question here...",className:"min-h-[100px]",...e})}),(0,r.jsx)(f.C5,{})]})}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Question Image (Optional)"}),(0,r.jsx)(w.Bc,{children:(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,r.jsx)(w.ZI,{children:(0,r.jsx)("p",{children:"Upload an image to accompany your question"})})]})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(j.$,{type:"button",variant:"outline",size:"sm",className:"h-9",onClick:()=>Q.current?.click(),children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Upload Image"]}),(0,r.jsx)("input",{type:"file",ref:Q,className:"hidden",accept:"image/*",onChange:e=>Y(e,"question")}),$&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.default,{src:$||"/placeholder.svg",alt:"Question image",width:100,height:100,className:"object-cover rounded-md border h-[100px] w-[100px]"}),(0,r.jsx)(j.$,{type:"button",variant:"destructive",size:"icon",className:"h-6 w-6 absolute -top-2 -right-2 rounded-full",onClick:()=>ee("question"),children:(0,r.jsx)(h.A,{className:"h-3 w-3"})})]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Answer Options"}),["A","B","C","D"].map(e=>(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[1fr,auto] gap-4 items-start border-b pb-4 last:border-0",children:[(0,r.jsx)(f.zB,{control:Z.control,name:`option${e}`,render:({field:s})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsxs)(f.lR,{children:["Option ",e,R[e]&&(0,r.jsx)("span",{className:"text-sm text-green-600 ml-2",children:"(Image uploaded)"})]}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(b.p,{placeholder:R[e]?`Option ${e} text (optional - image uploaded)`:`Enter option ${e} text or upload an image...`,...s})}),(0,r.jsx)(f.C5,{}),M[e]&&(0,r.jsxs)("p",{className:"text-sm text-red-600",children:["Option ",e," requires either text or an image"]})]})}),(0,r.jsx)("div",{className:"space-y-2 mt-8 md:mt-0",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(j.$,{type:"button",variant:"outline",size:"sm",className:"h-9 text-xs",onClick:()=>H[e].current?.click(),children:[(0,r.jsx)(x.A,{className:"h-3 w-3 mr-1"}),"Image"]}),(0,r.jsx)("input",{type:"file",ref:H[e],className:"hidden",accept:"image/*",onChange:s=>Y(s,e)}),R[e]&&(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(n.default,{src:R[e]||"/placeholder.svg",alt:`Option ${e} image`,width:60,height:60,className:"object-cover rounded-md border h-[60px] w-[60px]"}),(0,r.jsx)(j.$,{type:"button",variant:"destructive",size:"icon",className:"h-5 w-5 absolute -top-2 -right-2 rounded-full",onClick:()=>ee(e),children:(0,r.jsx)(h.A,{className:"h-3 w-3"})})]})]})})]},e))]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(f.zB,{control:Z.control,name:"correctAnswer",render:({field:e})=>(0,r.jsxs)(f.eI,{className:"space-y-3",children:[(0,r.jsx)(f.lR,{children:"Correct Answer *"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(v.z,{onValueChange:e.onChange,value:e.value||"",className:"flex space-x-4",children:["A","B","C","D"].map(e=>(0,r.jsxs)(f.eI,{className:"flex items-center space-x-1",children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(v.C,{value:e,id:`manual-option-${e}`})}),(0,r.jsx)(f.lR,{className:"font-normal",htmlFor:`manual-option-${e}`,children:e})]},e))})}),(0,r.jsx)(f.C5,{})]})}),(0,r.jsx)(f.zB,{control:Z.control,name:"difficulty",render:({field:e})=>(0,r.jsxs)(f.eI,{className:"space-y-3",children:[(0,r.jsx)(f.lR,{children:"Difficulty Level *"}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(v.z,{onValueChange:e.onChange,value:e.value||"",className:"flex space-x-4",children:["Easy","Medium","Hard"].map(e=>(0,r.jsxs)(f.eI,{className:"flex items-center space-x-1",children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(v.C,{value:e,id:`manual-level-${e}`})}),(0,r.jsx)(f.lR,{className:"font-normal",htmlFor:`manual-level-${e}`,children:e})]},e))})}),(0,r.jsx)(f.C5,{})]})})]}),(0,r.jsx)(f.zB,{control:Z.control,name:"explanation",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.lR,{children:"Explanation (Optional)"}),(0,r.jsx)(w.Bc,{children:(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,r.jsx)(w.ZI,{children:(0,r.jsx)("p",{children:"Provide an explanation for the correct answer"})})]})})]}),(0,r.jsx)(f.MJ,{children:(0,r.jsx)(N.T,{placeholder:"Explain why the correct answer is right...",className:"min-h-[80px]",...e})}),(0,r.jsx)(f.Rr,{children:"This will be shown to students after they answer the question."}),(0,r.jsx)(f.C5,{})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-2",children:[(0,r.jsx)(j.$,{type:"submit",className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:t,children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding Question..."]}):"Add Question"}),(0,r.jsx)(j.$,{type:"button",variant:"outline",className:"w-full sm:w-auto",onClick:ea,disabled:t,children:"Reset Form"})]})]})})}),(0,r.jsx)(A.av,{value:"pdf",className:"space-y-6",children:(0,r.jsx)(f.lV,{...K,children:(0,r.jsxs)("form",{onSubmit:K.handleSubmit(er),className:"space-y-8",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(f.zB,{control:K.control,name:"subject",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{children:"Subject *"}),(0,r.jsxs)(y.l6,{onValueChange:W,value:e.value,children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(y.bq,{children:(0,r.jsx)(y.yv,{placeholder:z?"Loading subjects...":"Select a subject"})})}),(0,r.jsx)(y.gC,{children:z?(0,r.jsx)(y.eb,{value:"loading",disabled:!0,children:"Loading subjects..."}):c.map(e=>(0,r.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,r.jsx)(f.C5,{})]})}),(0,r.jsx)(f.zB,{control:K.control,name:"chapter",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{children:"Chapter (Optional)"}),(0,r.jsxs)(y.l6,{onValueChange:e.onChange,value:e.value,disabled:0===L.length,children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(y.bq,{children:(0,r.jsx)(y.yv,{placeholder:z?"Loading chapters...":L.length>0?"Select a chapter (optional)":"Select a subject first"})})}),(0,r.jsx)(y.gC,{children:L.map(e=>(0,r.jsx)(y.eb,{value:e._id,children:e.name},e._id))})]}),(0,r.jsx)(f.C5,{})]})}),(0,r.jsx)(f.zB,{control:K.control,name:"aiProvider",render:({field:e})=>(0,r.jsxs)(f.eI,{children:[(0,r.jsx)(f.lR,{children:"AI Provider"}),(0,r.jsxs)(y.l6,{onValueChange:e.onChange,value:e.value,children:[(0,r.jsx)(f.MJ,{children:(0,r.jsx)(y.bq,{children:(0,r.jsx)(y.yv,{placeholder:"Select AI provider"})})}),(0,r.jsxs)(y.gC,{children:[(0,r.jsx)(y.eb,{value:"gemini",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:"\uD83E\uDD16"}),(0,r.jsx)("span",{children:"Gemini (Recommended)"})]})}),(0,r.jsx)(y.eb,{value:"mistral",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:"⚡"}),(0,r.jsx)("span",{children:"Mistral"})]})})]})]}),(0,r.jsx)(f.Rr,{children:"Choose the AI provider for question extraction. Gemini is recommended for better accuracy."}),(0,r.jsx)(f.C5,{})]})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(f.lR,{children:"PDF File *"}),(0,r.jsx)(w.Bc,{children:(0,r.jsxs)(w.m_,{children:[(0,r.jsx)(w.k$,{asChild:!0,children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})}),(0,r.jsx)(w.ZI,{children:(0,r.jsx)("p",{children:"Upload a PDF file containing questions to be extracted and added to the question bank"})})]})})]}),(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("label",{htmlFor:"pdf-upload",className:"cursor-pointer",children:[(0,r.jsx)("span",{className:"mt-2 block text-sm font-medium text-gray-900",children:U?U.name:"Choose PDF file or drag and drop"}),(0,r.jsx)("span",{className:"mt-1 block text-xs text-gray-500",children:"PDF up to 50MB"})]}),(0,r.jsx)("input",{id:"pdf-upload",type:"file",className:"sr-only",accept:".pdf",onChange:e=>{let s=e.target.files?.[0];if(s){if("application/pdf"!==s.type){(0,C.o)({title:"Invalid File Type",description:"Please select a PDF file.",variant:"destructive"});return}if(s.size>0x3200000){(0,C.o)({title:"File Too Large",description:"File size must be less than 50MB.",variant:"destructive"});return}J(s)}}})]}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>document.getElementById("pdf-upload")?.click(),children:"Select PDF File"})})]})}),U&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-sm text-green-800",children:U.name}),(0,r.jsxs)("span",{className:"text-xs text-green-600",children:["(",(U.size/1024/1024).toFixed(2)," MB)"]})]}),(0,r.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>J(null),children:(0,r.jsx)(h.A,{className:"h-4 w-4"})})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"chemical-extraction",checked:G,onChange:e=>O(e.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,r.jsx)("label",{htmlFor:"chemical-extraction",className:"text-sm font-medium text-gray-700",children:"Use Chemical Extraction (for Chemistry PDFs with molecular structures)"})]}),G&&(0,r.jsx)("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{className:"text-xs text-blue-800",children:[(0,r.jsx)("p",{className:"font-medium mb-1",children:"Chemical Extraction Mode"}),(0,r.jsx)("p",{children:"This mode is optimized for chemistry PDFs containing:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside mt-1 space-y-0.5",children:[(0,r.jsx)("li",{children:"Molecular structures and chemical diagrams"}),(0,r.jsx)("li",{children:"Chemical equations and formulas"}),(0,r.jsx)("li",{children:"Reaction mechanisms"}),(0,r.jsx)("li",{children:"Complex chemical images"})]}),(0,r.jsx)("p",{className:"mt-2 text-blue-700",children:"Processing may take longer but provides better accuracy for chemical content."})]})]})})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 pt-2",children:[(0,r.jsx)(j.$,{type:"submit",className:"w-full bg-[#05603A] hover:bg-[#04502F] sm:w-auto",disabled:t||!U,children:t?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Uploading PDF..."]}):"Upload PDF"}),(0,r.jsx)(j.$,{type:"button",variant:"outline",className:"w-full sm:w-auto",onClick:ei,disabled:t,children:"Reset Form"})]})]})})})]})})]})}let z=()=>(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Add Question"}),(0,r.jsx)(i.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Add Question"}],className:"text-sm mt-1"})]}),(0,r.jsx)("div",{className:"container mx-auto py-10",children:(0,r.jsx)(k,{})})]})})},4597:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["admin",{children:["add-question",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,51969)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/add-question/page",pathname:"/admin/add-question",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29844:(e,s,t)=>{"use strict";t.d(s,{tU:()=>D,av:()=>E,j7:()=>k,Xi:()=>z});var r=t(60687),a=t(43210),i=t(70569),n=t(11273),l=t(72942),o=t(46059),c=t(3416),d=t(43),u=t(65551),m=t(96963),x="Tabs",[h,p]=(0,n.A)(x,[l.RG]),j=(0,l.RG)(),[g,f]=h(x),b=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:x="automatic",...h}=e,p=(0,d.jH)(o),[j,f]=(0,u.i)({prop:a,onChange:i,defaultProp:n});return(0,r.jsx)(g,{scope:t,baseId:(0,m.B)(),value:j,onValueChange:f,orientation:l,dir:p,activationMode:x,children:(0,r.jsx)(c.sG.div,{dir:p,"data-orientation":l,...h,ref:s})})});b.displayName=x;var v="TabsList",y=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...i}=e,n=f(v,t),o=j(t);return(0,r.jsx)(l.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:a,children:(0,r.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:s})})});y.displayName=v;var N="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:n=!1,...o}=e,d=f(N,t),u=j(t),m=q(d.baseId,a),x=F(d.baseId,a),h=a===d.value;return(0,r.jsx)(l.q7,{asChild:!0,...u,focusable:!n,active:h,children:(0,r.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":x,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...o,ref:s,onMouseDown:(0,i.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||n||!e||d.onValueChange(a)})})})});w.displayName=N;var C="TabsContent",A=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:i,forceMount:n,children:l,...d}=e,u=f(C,t),m=q(u.baseId,i),x=F(u.baseId,i),h=i===u.value,p=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(o.C,{present:n||h,children:({present:t})=>(0,r.jsx)(c.sG.div,{"data-state":h?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:x,tabIndex:0,...d,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&l})})});function q(e,s){return`${e}-trigger-${s}`}function F(e,s){return`${e}-content-${s}`}A.displayName=C;var P=t(4780);function D({className:e,...s}){return(0,r.jsx)(b,{"data-slot":"tabs",className:(0,P.cn)("flex flex-col gap-2",e),...s})}function k({className:e,...s}){return(0,r.jsx)(y,{"data-slot":"tabs-list",className:(0,P.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...s})}function z({className:e,...s}){return(0,r.jsx)(w,{"data-slot":"tabs-trigger",className:(0,P.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function E({className:e,...s}){return(0,r.jsx)(A,{"data-slot":"tabs-content",className:(0,P.cn)("flex-1 outline-none",e),...s})}},33873:e=>{"use strict";e.exports=require("path")},34729:(e,s,t)=>{"use strict";t.d(s,{T:()=>i});var r=t(60687);t(43210);var a=t(4780);function i({className:e,...s}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...s})}},50812:(e,s,t)=>{"use strict";t.d(s,{C:()=>o,z:()=>l});var r=t(60687);t(43210);var a=t(74797),i=t(65822),n=t(4780);function l({className:e,...s}){return(0,r.jsx)(a.bL,{"data-slot":"radio-group",className:(0,n.cn)("grid gap-3",e),...s})}function o({className:e,...s}){return(0,r.jsx)(a.q7,{"data-slot":"radio-group-item",className:(0,n.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...s,children:(0,r.jsx)(a.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,r.jsx)(i.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},51969:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\add-question\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},71669:(e,s,t)=>{"use strict";t.d(s,{C5:()=>f,MJ:()=>j,Rr:()=>g,eI:()=>h,lR:()=>p,lV:()=>c,zB:()=>u});var r=t(60687),a=t(43210),i=t(11329),n=t(27605),l=t(4780),o=t(80013);let c=n.Op,d=a.createContext({}),u=({...e})=>(0,r.jsx)(d.Provider,{value:{name:e.name},children:(0,r.jsx)(n.xI,{...e})}),m=()=>{let e=a.useContext(d),s=a.useContext(x),{getFieldState:t}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),i=t(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=s;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},x=a.createContext({});function h({className:e,...s}){let t=a.useId();return(0,r.jsx)(x.Provider,{value:{id:t},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...s})})}function p({className:e,...s}){let{error:t,formItemId:a}=m();return(0,r.jsx)(o.J,{"data-slot":"form-label","data-error":!!t,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:a,...s})}function j({...e}){let{error:s,formItemId:t,formDescriptionId:a,formMessageId:n}=m();return(0,r.jsx)(i.Slot,{"data-slot":"form-control",id:t,"aria-describedby":s?`${a} ${n}`:`${a}`,"aria-invalid":!!s,...e})}function g({className:e,...s}){let{formDescriptionId:t}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:t,className:(0,l.cn)("text-muted-foreground text-sm",e),...s})}function f({className:e,...s}){let{error:t,formMessageId:a}=m(),i=t?String(t?.message??""):s.children;return i?(0,r.jsx)("p",{"data-slot":"form-message",id:a,className:(0,l.cn)("text-destructive text-sm",e),...s,children:i}):null}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},80013:(e,s,t)=>{"use strict";t.d(s,{J:()=>n});var r=t(60687);t(43210);var a=t(78148),i=t(4780);function n({className:e,...s}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},81630:e=>{"use strict";e.exports=require("http")},82013:(e,s,t)=>{"use strict";t.d(s,{CG:()=>i,Fb:()=>o,Nj:()=>a,getSubjectsWithTopics:()=>l,gg:()=>d,iQ:()=>c,py:()=>n});var r=t(62185);let a=async()=>{try{return await (0,r.apiCall)("/subjects")}catch(e){throw console.error("Error fetching subjects:",e),Error(e.message||"Failed to fetch subjects")}},i=async()=>{try{return(await (0,r.apiCall)("/subjects/with-topics")).map(e=>({...e,chapters:e.topics||[]}))}catch(e){throw console.error("Error fetching subjects with chapters:",e),Error(e.message||"Failed to fetch subjects with chapters")}},n=async()=>{try{return await (0,r.apiCall)("/subjects/with-chapters-and-topics")}catch(e){throw console.error("Error fetching subjects with chapters and topics:",e),Error(e.message||"Failed to fetch subjects with chapters and topics")}},l=async()=>{try{return await (0,r.apiCall)("/subjects/with-topics")}catch(e){throw console.error("Error fetching subjects with topics:",e),Error(e.message||"Failed to fetch subjects with topics")}},o=async e=>{try{return await (0,r.apiCall)("/subjects",{method:"POST",body:JSON.stringify(e)})}catch(e){throw console.error("Error creating subject:",e),Error(e.message||"Failed to create subject")}},c=async(e,s)=>{try{return await (0,r.apiCall)(`/subjects/${e}`,{method:"PATCH",body:JSON.stringify(s)})}catch(s){throw console.error(`Error updating subject ${e}:`,s),Error(s.message||"Failed to update subject")}},d=async e=>{try{await (0,r.apiCall)(`/subjects/${e}`,{method:"DELETE"})}catch(s){throw console.error(`Error deleting subject ${e}:`,s),Error(s.message||"Failed to delete subject")}}},83997:e=>{"use strict";e.exports=require("tty")},90736:(e,s,t)=>{Promise.resolve().then(t.bind(t,51969))},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4447,4619,3287,9592,2581,1991,3442,6822,5361,694,4707,6658,5091],()=>t(4597));module.exports=r})();