(()=>{var e={};e.id=5687,e.ids=[5687],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4053:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c={children:["",{children:["teacher",{children:["downloaded-papers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,58469)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,5398)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,71182)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/teacher/downloaded-papers/page",pathname:"/teacher/downloaded-papers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5398:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx","default")},6068:(e,t,s)=>{Promise.resolve().then(s.bind(s,86964))},8086:e=>{"use strict";e.exports=require("module")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14583:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var r=s(60687);s(43210);var a=s(29523),n=s(47033),o=s(14952);function i({currentPage:e,totalPages:t,onPageChange:s,pageSize:i,totalItems:l,onPageSizeChange:c,pageSizeOptions:d=[5,10,20,50]}){let u=Math.min(l,(e-1)*i+1),p=Math.min(l,e*i);return(0,r.jsxs)("div",{className:"flex items-center justify-between px-2 py-4",children:[(0,r.jsx)("div",{className:"flex-1 text-sm text-muted-foreground",children:l>0?(0,r.jsxs)("p",{children:["Showing ",u," to ",p," of ",l," items"]}):(0,r.jsx)("p",{children:"No items"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[c&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Rows per page"}),(0,r.jsx)("select",{className:"h-8 w-[70px] rounded-md border border-input bg-background px-3 py-1 text-sm",value:i,onChange:e=>c(Number(e.target.value)),children:d.map(e=>(0,r.jsx)("option",{value:e,children:e},e))})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(a.$,{variant:"outline",size:"sm",onClick:()=>s(e-1),disabled:1===e,className:"h-8 w-8 p-0",children:[(0,r.jsx)(n.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Previous page"})]}),(()=>{let s=[];if(t<=5)for(let e=1;e<=t;e++)s.push(e);else{s.push(1),e>3&&s.push("ellipsis");let r=Math.max(2,e-1),a=Math.min(t-1,e+1);for(let e=r;e<=a;e++)s.push(e);e<t-2&&s.push("ellipsis"),t>1&&s.push(t)}return s})().map((t,n)=>"ellipsis"===t?(0,r.jsx)("span",{className:"px-2",children:"..."},`ellipsis-${n}`):(0,r.jsx)(a.$,{variant:e===t?"default":"outline",size:"sm",onClick:()=>s(t),className:"h-8 w-8 p-0",children:t},`page-${t}`)),(0,r.jsxs)(a.$,{variant:"outline",size:"sm",onClick:()=>s(e+1),disabled:e===t||0===t,className:"h-8 w-8 p-0",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Next page"})]})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},51947:(e,t,s)=>{"use strict";s.d(t,{ZG:()=>n,cH:()=>o,ig:()=>i});let r="http://localhost:3000/api";function a(){let e=localStorage.getItem("backendToken"),t=localStorage.getItem("firebaseToken"),s=localStorage.getItem("token"),r={"Content-Type":"application/json"};if(e)r.Authorization=`Bearer ${e}`;else if(t)r.Authorization=`Bearer ${t}`;else if(s)r.Authorization=`Bearer ${s}`;else throw Error("Authentication required - Please log in again. No valid authentication token found.");return r}async function n(e){try{let t=a(),s=await fetch(`${r}/question-papers`,{method:"POST",headers:t,body:JSON.stringify(e)});if(!s.ok){let e=`Error: ${s.status} - ${s.statusText}`;try{let t=await s.text();if(t)try{let s=JSON.parse(t);e=s&&s.message?s.message:s&&s.error?s.error:t}catch(s){e=t}}catch(e){}if(!e||e===`Error: ${s.status} - ${s.statusText}`)switch(s.status){case 401:e="Authentication required - Please log in again.";break;case 403:e="Access denied - You don't have permission to perform this action.";break;case 404:e="Resource not found - The requested item could not be found.";break;case 429:e="Too many requests - Please wait a moment before trying again.";break;case 500:e="Server error - Please try again later.";break;case 503:e="Service unavailable - The server is temporarily down.";break;default:s.status>=400&&s.status<500?e="Invalid request - Please check your input and try again.":s.status>=500&&(e="Server error - Please try again later.")}return{success:!1,error:e}}let n=await s.json();if(console.log("Raw API response from createQuestionPaper:",n),n.questionPaper)return{success:!0,data:n.questionPaper};return{success:!0,data:n}}catch(e){return{success:!1,error:"Network error - Please check your connection and try again."}}}async function o(e){try{let t;if(console.log("getQuestionPaperForPDF called with:",e),!e||"undefined"===e||"null"===e)throw Error(`Invalid question paper ID: ${e}`);let s=a(),n=await fetch(`${r}/question-papers/${e}`,{method:"GET",headers:s});if(!n.ok){let e=await n.text();throw console.error("API Error Response:",e),Error(`Failed to fetch question paper: ${n.status} ${n.statusText}`)}let o=await n.json();if(console.log("Question paper data fetched:",o),console.log("College ID in question paper:",o.collegeId),o.college?(t={name:o.college.name,logoUrl:o.college.logoUrl,address:o.college.address},console.log("College information found in response:",t)):o.collegeId&&"object"==typeof o.collegeId&&o.collegeId.name?(t={name:o.collegeId.name,logoUrl:o.collegeId.logoUrl,address:o.collegeId.address},console.log("College information found in populated collegeId:",t)):console.log("No college information available - PDF will be generated without college branding"),o.questionPaper)return{questionPaper:o.questionPaper,college:o.college||t};return{questionPaper:o,college:t}}catch(e){throw console.error("Error fetching question paper for PDF:",e),e}}async function i(){try{let e=a(),t=await fetch(`${r}/question-papers`,{method:"GET",headers:e});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.message||`Error: ${t.status} - ${t.statusText}`)}return await t.json()}catch(e){throw console.error("Error fetching question papers:",e),e}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58469:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(60687),a=s(43210),n=s.n(a),o=s(85814),i=s.n(o),l=s(4780),c=s(14952),d=s(93661);let u=({items:e,maxItems:t=4,className:s})=>{let a=n().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,l.cn)("flex items-center text-sm",s),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:a.map((e,t)=>{let s=t===a.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):s?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(i(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},65443:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(60687),a=s(43210),n=s(55533),o=s(63239),i=s(29523),l=s(10022),c=s(62688);let d=(0,c.A)("hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]),u=(0,c.A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);var p=s(40228),m=s(31158);let h=(0,c.A)("table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]);var x=s(82080),g=s(21342),f=s(51947),y=s(14583);let j=()=>{let[e,t]=(0,a.useState)(1),[s,c]=(0,a.useState)(10),[j,b]=(0,a.useState)(new Set),{data:w=[],isLoading:v,error:N,refetch:k}=(0,n.I)({queryKey:["question-papers"],queryFn:f.ig,staleTime:3e5}),q=Math.ceil(w.length/s),P=(e-1)*s,C=w.slice(P,P+s),A=e=>{let t=[];return e.isMultiSubject&&e.sections?e.sections.forEach(e=>{e.questions&&Array.isArray(e.questions)&&e.questions.forEach(s=>{let r=s.question||s;t.push({question:r.content||r.question||"",options:r.options||[],answer:r.answer||"",subject:e.subjectName||e.name||"General",imageUrls:r.imageUrls||[],solution:r.solution||null,hints:r.hints||[]})})}):e.questions&&Array.isArray(e.questions)&&e.questions.forEach(s=>{t.push({question:s.content||s.question||"",options:s.options||[],answer:s.answer||"",subject:e.subjectId?.name||"General",imageUrls:s.imageUrls||[],solution:s.solution||null,hints:s.hints||[]})}),t},_=async(e,t,s)=>{let r=A(e),a={title:e.title||"Question Paper",description:e.description||"",duration:e.duration||60,totalMarks:e.totalMarks||100,questions:r,includeAnswers:!1,filename:t,collegeName:s?.name||"",collegeLogoUrl:s?.logoUrl||""},n=await fetch("/api/generate-paper-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error("Failed to generate questions PDF");return n.blob()},S=async(e,t,s)=>{let r=A(e),a={title:e.title||"Question Paper",description:e.description||"",duration:e.duration||60,totalMarks:e.totalMarks||100,questions:r,filename:t,collegeName:s?.name||"",collegeLogoUrl:s?.logoUrl||""};console.log("Calling /api/generate-solutions-pdf with payload:",a);let n=await fetch("/api/generate-solutions-pdf",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error("Failed to generate solutions PDF");return n.blob()},U=async(e,t)=>{let s=A(e),r={title:e.title||"Question Paper",questions:s,filename:t},a=await fetch("/api/generate-answers-excel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok)throw Error("Failed to generate answers Excel");return a.blob()},E=async(e,t)=>{try{let s;b(t=>new Set(t).add(e._id));let r=await (0,f.cH)(e._id),a=r.questionPaper,n=r.college,o=a.questions&&a.questions.length>0,i=a.sections&&a.sections.some(e=>e.questions&&e.questions.length>0);if(!o&&!i)throw Error("This question paper does not contain any questions. It may have been created incorrectly.");let l=new Date().toISOString().slice(0,19).replace(/[:-]/g,""),c=Math.random().toString(36).substring(2,8),d=e.title.replace(/[^a-zA-Z0-9\s]/g,"").replace(/\s+/g,"_");switch(t){case"questions":s=`${d}_Questions_${l}_${c}.pdf`;let u=await _(a,s,n),p=window.URL.createObjectURL(u),m=document.createElement("a");m.href=p,m.download=s,document.body.appendChild(m),m.click(),document.body.removeChild(m),window.URL.revokeObjectURL(p);break;case"answers":s=`${d}_Answers_${l}_${c}.xlsx`;let h=await U(a,s),x=window.URL.createObjectURL(h),g=document.createElement("a");g.href=x,g.download=s,document.body.appendChild(g),g.click(),document.body.removeChild(g),window.URL.revokeObjectURL(x);break;case"solutions":s=`${d}_Solutions_${l}_${c}.pdf`,console.log("Generating solutions PDF with filename:",s);let y=await S(a,s,n),j=window.URL.createObjectURL(y),w=document.createElement("a");w.href=j,w.download=s,w.setAttribute("download",s),document.body.appendChild(w),w.click(),document.body.removeChild(w),window.URL.revokeObjectURL(j),console.log("Solutions PDF download initiated with filename:",s);break;default:throw Error("Invalid download type")}console.log("Download completed successfully")}catch(e){console.error("Download failed:",e),alert(e instanceof Error?e.message:"Failed to download the question paper. Please try again.")}finally{b(t=>{let s=new Set(t);return s.delete(e._id),s})}},$=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Downloaded Papers"}),(0,r.jsx)(o.A,{items:[{label:"Home",href:"/teacher"},{label:"...",href:"#"},{label:"Downloaded Papers"}],className:"text-sm mt-1"})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Question Papers"}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[w.length," total papers available for download"]})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsxs)(i.$,{variant:"outline",onClick:()=>k(),disabled:v,className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),v?"Refreshing...":"Refresh"]})})]})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:v?(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Loading question papers..."})]})}):N?(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-500 mb-4",children:"Failed to load question papers"}),(0,r.jsx)(i.$,{onClick:()=>k(),variant:"outline",children:"Try Again"})]})}):0===w.length?(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(l.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-500 mb-2",children:"No question papers found"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Generate your first question paper to see it here"})]})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-gray-200 bg-gray-50",children:[(0,r.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Paper Title"}),(0,r.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Subject"}),(0,r.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Details"}),(0,r.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Created"}),(0,r.jsx)("th",{className:"py-3 px-6 text-left font-medium text-gray-700",children:"Status"}),(0,r.jsx)("th",{className:"py-3 px-6 text-center font-medium text-gray-700",children:"Actions"})]})}),(0,r.jsx)("tbody",{children:C.map(e=>(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50 transition-colors",children:[(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(l.A,{className:"w-5 h-5 text-blue-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 line-clamp-2",children:e.title}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["ID: ",e._id.slice(-8)]})]})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:e.subjectId?(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.subjectId.name}):(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800",children:["Multi-Subject",e.subjectCount?` (${e.subjectCount})`:""]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[(0,r.jsx)(d,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:[e.totalMarks," marks"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[(0,r.jsx)(u,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:[e.duration," min"]})]})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-center gap-1 text-sm text-gray-600",children:[(0,r.jsx)(p.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:$(e.createdAt)})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${"active"===e.status.toLowerCase()?"bg-emerald-100 text-emerald-800":"bg-gray-100 text-gray-800"}`,children:e.status.charAt(0).toUpperCase()+e.status.slice(1)})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)(g.rI,{children:[(0,r.jsx)(g.ty,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",size:"sm",disabled:j.has(e._id),className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4"}),j.has(e._id)?"Downloading...":"Download"]})}),(0,r.jsxs)(g.SQ,{align:"end",className:"w-48",children:[(0,r.jsxs)(g._2,{onClick:()=>E(e,"questions"),disabled:j.has(e._id),className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)(l.A,{className:"w-4 h-4"}),"Download Questions"]}),(0,r.jsxs)(g._2,{onClick:()=>E(e,"answers"),disabled:j.has(e._id),className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)(h,{className:"w-4 h-4"}),"Download Answers"]}),(0,r.jsxs)(g._2,{onClick:()=>E(e,"solutions"),disabled:j.has(e._id),className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)(x.A,{className:"w-4 h-4"}),"Download Solutions"]})]})]})})})]},e._id))})]})}),q>1&&(0,r.jsx)("div",{className:"px-6 py-4 border-t border-gray-200",children:(0,r.jsx)(y.d,{currentPage:e,totalPages:q,onPageChange:t,pageSize:s,totalItems:w.length,onPageSizeChange:c})})]})})]})]})})}},69684:(e,t,s)=>{Promise.resolve().then(s.bind(s,5398))},71182:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(92555);function n(){return(0,r.jsx)(a.W,{message:"Loading teacher dashboard..."})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86964:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(60687),a=s(66327),n=s(53355),o=s(99557),i=s(45285);function l({children:e}){return(0,r.jsx)(i.A,{allowedRoles:[o.g.TEACHER],children:(0,r.jsx)(n.default,{children:(0,r.jsx)(a.N,{role:o.g.TEACHER,children:e})})})}},94735:e=>{"use strict";e.exports=require("events")},96824:(e,t,s)=>{Promise.resolve().then(s.bind(s,58469))},97496:(e,t,s)=>{Promise.resolve().then(s.bind(s,65443))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4447,4619,3287,9592,4654,4707,6658],()=>s(4053));module.exports=r})();