"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5267],{4011:(e,t,n)=>{n.d(t,{UC:()=>eA,In:()=>eP,q7:()=>eM,VF:()=>e_,p4:()=>eF,ZL:()=>eL,bL:()=>ek,wn:()=>eO,PP:()=>eH,l9:()=>eR,WT:()=>eI,LM:()=>eD});var r=n(12115),l=n(47650);function o(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(85185),i=n(76589),s=n(6101),u=n(46081),d=n(94315),c=n(19178),p=n(92293),f=n(25519),v=n(61285),m=n(22197),h=n(34378),g=n(63540),y=n(95155),w=r.forwardRef((e,t)=>{let{children:n,...l}=e,o=r.Children.toArray(n),a=o.find(S);if(a){let e=a.props.children,n=o.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,y.jsx)(x,{...l,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,y.jsx)(x,{...l,ref:t,children:n})});w.displayName="Slot";var x=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),o=function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{o(...e),l(...e)}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==r.Fragment&&(o.ref=t?(0,s.t)(t,e):e),r.cloneElement(n,o)}return r.Children.count(n)>1?r.Children.only(null):null});x.displayName="SlotClone";var b=({children:e})=>(0,y.jsx)(y.Fragment,{children:e});function S(e){return r.isValidElement(e)&&e.type===b}var C=n(39033),E=n(5845),j=n(52712),N=n(2564),T=n(38168),k=n(31114),R=[" ","Enter","ArrowUp","ArrowDown"],I=[" ","Enter"],P="Select",[L,A,D]=(0,i.N)(P),[M,F]=(0,u.A)(P,[D,m.Bk]),_=(0,m.Bk)(),[H,O]=M(P),[B,K]=M(P),V=e=>{let{__scopeSelect:t,children:n,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:h,required:g,form:w}=e,x=_(t),[b,S]=r.useState(null),[C,j]=r.useState(null),[N,T]=r.useState(!1),k=(0,d.jH)(c),[R=!1,I]=(0,E.i)({prop:l,defaultProp:o,onChange:a}),[P,A]=(0,E.i)({prop:i,defaultProp:s,onChange:u}),D=r.useRef(null),M=!b||w||!!b.closest("form"),[F,O]=r.useState(new Set),K=Array.from(F).map(e=>e.props.value).join(";");return(0,y.jsx)(m.bL,{...x,children:(0,y.jsxs)(H,{required:g,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:j,valueNodeHasChildren:N,onValueNodeHasChildrenChange:T,contentId:(0,v.B)(),value:P,onValueChange:A,open:R,onOpenChange:I,dir:k,triggerPointerDownPosRef:D,disabled:h,children:[(0,y.jsx)(L.Provider,{scope:t,children:(0,y.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:r.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,y.jsxs)(ej,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:P,onChange:e=>A(e.target.value),disabled:h,form:w,children:[void 0===P?(0,y.jsx)("option",{value:""}):null,Array.from(F)]},K):null]})})};V.displayName=P;var W="SelectTrigger",G=r.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:l=!1,...o}=e,i=_(n),u=O(W,n),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=A(n),f=r.useRef("touch"),[v,h,w]=eN(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),r=eT(t,e,n);void 0!==r&&u.onValueChange(r.value)}),x=e=>{d||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,y.jsx)(m.Mz,{asChild:!0,...i,children:(0,y.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eE(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),(!t||" "!==e.key)&&R.includes(e.key)&&(x(),e.preventDefault())})})})});G.displayName=W;var U="SelectValue",q=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,children:o,placeholder:a="",...i}=e,u=O(U,n),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,j.N)(()=>{d(c)},[d,c]),(0,y.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:eE(u.value)?(0,y.jsx)(y.Fragment,{children:a}):o})});q.displayName=U;var z=r.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...l}=e;return(0,y.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:r||"▼"})});z.displayName="SelectIcon";var Z=e=>(0,y.jsx)(h.Z,{asChild:!0,...e});Z.displayName="SelectPortal";var X="SelectContent",Y=r.forwardRef((e,t)=>{let n=O(X,e.__scopeSelect),[o,a]=r.useState();return((0,j.N)(()=>{a(new DocumentFragment)},[]),n.open)?(0,y.jsx)($,{...e,ref:t}):o?l.createPortal((0,y.jsx)(J,{scope:e.__scopeSelect,children:(0,y.jsx)(L.Slot,{scope:e.__scopeSelect,children:(0,y.jsx)("div",{children:e.children})})}),o):null});Y.displayName=X;var[J,Q]=M(X),$=r.forwardRef((e,t)=>{let{__scopeSelect:n,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:m,alignOffset:h,arrowPadding:g,collisionBoundary:x,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:E,...j}=e,N=O(X,n),[R,I]=r.useState(null),[P,L]=r.useState(null),D=(0,s.s)(t,e=>I(e)),[M,F]=r.useState(null),[_,H]=r.useState(null),B=A(n),[K,V]=r.useState(!1),W=r.useRef(!1);r.useEffect(()=>{if(R)return(0,T.Eq)(R)},[R]),(0,p.Oh)();let G=r.useCallback(e=>{let[t,...n]=B().map(e=>e.ref.current),[r]=n.slice(-1),l=document.activeElement;for(let n of e)if(n===l||(null==n||n.scrollIntoView({block:"nearest"}),n===t&&P&&(P.scrollTop=0),n===r&&P&&(P.scrollTop=P.scrollHeight),null==n||n.focus(),document.activeElement!==l))return},[B,P]),U=r.useCallback(()=>G([M,R]),[G,M,R]);r.useEffect(()=>{K&&U()},[K,U]);let{onOpenChange:q,triggerPointerDownPosRef:z}=N;r.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var n,r,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(n=z.current)||void 0===n?void 0:n.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(r=z.current)||void 0===r?void 0:r.y)&&void 0!==o?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||q(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,q,z]),r.useEffect(()=>{let e=()=>q(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[q]);let[Z,Y]=eN(e=>{let t=B().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=eT(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),Q=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==N.value&&N.value===t||r)&&(F(e),r&&(W.current=!0))},[N.value]),$=r.useCallback(()=>null==R?void 0:R.focus(),[R]),en=r.useCallback((e,t,n)=>{let r=!W.current&&!n;(void 0!==N.value&&N.value===t||r)&&H(e)},[N.value]),er="popper"===l?et:ee,el=er===et?{side:d,sideOffset:v,align:m,alignOffset:h,arrowPadding:g,collisionBoundary:x,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:E}:{};return(0,y.jsx)(J,{scope:n,content:R,viewport:P,onViewportChange:L,itemRefCallback:Q,selectedItem:M,onItemLeave:$,itemTextRefCallback:en,focusSelectedItem:U,selectedItemText:_,position:l,isPositioned:K,searchRef:Z,children:(0,y.jsx)(k.A,{as:w,allowPinchZoom:!0,children:(0,y.jsx)(f.n,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null===(t=N.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,y.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,y.jsx)(er,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...j,...el,onPlaced:()=>V(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,a.m)(j.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=B().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>G(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var ee=r.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:l,...a}=e,i=O(X,n),u=Q(X,n),[d,c]=r.useState(null),[p,f]=r.useState(null),v=(0,s.s)(t,e=>f(e)),m=A(n),h=r.useRef(!1),w=r.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:S,focusSelectedItem:C}=u,E=r.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&b&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),r=S.getBoundingClientRect();if("rtl"!==i.dir){let l=r.left-t.left,a=n.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-r.right,a=window.innerWidth-n.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=m(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),y=f+v+u+parseInt(c.paddingBottom,10)+g,w=Math.min(5*b.offsetHeight,y),C=window.getComputedStyle(x),E=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),N=e.top+e.height/2-10,T=b.offsetHeight/2,k=f+v+(b.offsetTop+T);if(k<=N){let e=a.length>0&&b===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-N,T+(e?j:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+g);d.style.height=k+t+"px"}else{let e=a.length>0&&b===a[0].ref.current;d.style.top="0px";let t=Math.max(N,f+x.offsetTop+(e?E:0)+T);d.style.height=t+(y-k)+"px",x.scrollTop=k-N+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=w+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>h.current=!0)}},[m,i.trigger,i.valueNode,d,p,x,b,S,i.dir,l]);(0,j.N)(()=>E(),[E]);let[N,T]=r.useState();(0,j.N)(()=>{p&&T(window.getComputedStyle(p).zIndex)},[p]);let k=r.useCallback(e=>{e&&!0===w.current&&(E(),null==C||C(),w.current=!1)},[E,C]);return(0,y.jsx)(en,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:h,onScrollButtonChange:k,children:(0,y.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:(0,y.jsx)(g.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});ee.displayName="SelectItemAlignedPosition";var et=r.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:l=10,...o}=e,a=_(n);return(0,y.jsx)(m.UC,{...a,...o,ref:t,align:r,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});et.displayName="SelectPopperPosition";var[en,er]=M(X,{}),el="SelectViewport",eo=r.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:l,...o}=e,i=Q(el,n),u=er(el,n),d=(0,s.s)(t,i.onViewportChange),c=r.useRef(0);return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,y.jsx)(L.Slot,{scope:n,children:(0,y.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=u;if((null==r?void 0:r.current)&&n){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,l=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(l<r){let o=l+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=el;var ea="SelectGroup",[ei,es]=M(ea);r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=(0,v.B)();return(0,y.jsx)(ei,{scope:n,id:l,children:(0,y.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...r,ref:t})})}).displayName=ea;var eu="SelectLabel";r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=es(eu,n);return(0,y.jsx)(g.sG.div,{id:l.id,...r,ref:t})}).displayName=eu;var ed="SelectItem",[ec,ep]=M(ed),ef=r.forwardRef((e,t)=>{let{__scopeSelect:n,value:l,disabled:o=!1,textValue:i,...u}=e,d=O(ed,n),c=Q(ed,n),p=d.value===l,[f,m]=r.useState(null!=i?i:""),[h,w]=r.useState(!1),x=(0,s.s)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),b=(0,v.B)(),S=r.useRef("touch"),C=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,y.jsx)(ec,{scope:n,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:r.useCallback(e=>{m(t=>{var n;return t||(null!==(n=null==e?void 0:e.textContent)&&void 0!==n?n:"").trim()})},[]),children:(0,y.jsx)(L.ItemSlot,{scope:n,value:l,disabled:o,textValue:f,children:(0,y.jsx)(g.sG.div,{role:"option","aria-labelledby":b,"data-highlighted":h?"":void 0,"aria-selected":p&&h,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:x,onFocus:(0,a.m)(u.onFocus,()=>w(!0)),onBlur:(0,a.m)(u.onBlur,()=>w(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==S.current&&C()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===S.current&&C()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(S.current=e.pointerType,o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null===(t=c.searchRef)||void 0===t?void 0:t.current)===""||" "!==e.key)&&(I.includes(e.key)&&C()," "===e.key&&e.preventDefault())})})})})});ef.displayName=ed;var ev="SelectItemText",em=r.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:a,...i}=e,u=O(ev,n),d=Q(ev,n),c=ep(ev,n),p=K(ev,n),[f,v]=r.useState(null),m=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),h=null==f?void 0:f.textContent,w=r.useMemo(()=>(0,y.jsx)("option",{value:c.value,disabled:c.disabled,children:h},c.value),[c.disabled,c.value,h]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return(0,j.N)(()=>(x(w),()=>b(w)),[x,b,w]),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(g.sG.span,{id:c.textId,...i,ref:m}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});em.displayName=ev;var eh="SelectItemIndicator",eg=r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return ep(eh,n).isSelected?(0,y.jsx)(g.sG.span,{"aria-hidden":!0,...r,ref:t}):null});eg.displayName=eh;var ey="SelectScrollUpButton",ew=r.forwardRef((e,t)=>{let n=Q(ey,e.__scopeSelect),l=er(ey,e.__scopeSelect),[o,a]=r.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,j.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){a(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,y.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=ey;var ex="SelectScrollDownButton",eb=r.forwardRef((e,t)=>{let n=Q(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=r.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,j.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),o?(0,y.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eb.displayName=ex;var eS=r.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:l,...o}=e,i=Q("SelectScrollButton",n),s=r.useRef(null),u=A(n),d=r.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return r.useEffect(()=>()=>d(),[d]),(0,j.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,y.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null===(e=i.onItemLeave)||void 0===e||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,y.jsx)(g.sG.div,{"aria-hidden":!0,...r,ref:t})}).displayName="SelectSeparator";var eC="SelectArrow";function eE(e){return""===e||void 0===e}r.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,l=_(n),o=O(eC,n),a=Q(eC,n);return o.open&&"popper"===a.position?(0,y.jsx)(m.i3,{...l,...r,ref:t}):null}).displayName=eC;var ej=r.forwardRef((e,t)=>{let{value:n,...l}=e,o=r.useRef(null),a=(0,s.s)(t,o),i=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n);return r.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[i,n]),(0,y.jsx)(N.s,{asChild:!0,children:(0,y.jsx)("select",{...l,ref:a,defaultValue:n})})});function eN(e){let t=(0,C.c)(e),n=r.useRef(""),l=r.useRef(0),o=r.useCallback(e=>{let r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(r)},[t]),a=r.useCallback(()=>{n.current="",window.clearTimeout(l.current)},[]);return r.useEffect(()=>()=>window.clearTimeout(l.current),[]),[n,o,a]}function eT(e,t,n){var r,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=n?e.indexOf(n):-1,i=(r=e,l=Math.max(a,0),r.map((e,t)=>r[(l+t)%r.length]));1===o.length&&(i=i.filter(e=>e!==n));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==n?s:void 0}ej.displayName="BubbleSelect";var ek=V,eR=G,eI=q,eP=z,eL=Z,eA=Y,eD=eo,eM=ef,eF=em,e_=eg,eH=ew,eO=eb},5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},25519:(e,t,n)=>{n.d(t,{n:()=>c});var r=n(12115),l=n(6101),o=n(63540),a=n(39033),i=n(95155),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},c=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:c=!1,onMountAutoFocus:h,onUnmountAutoFocus:g,...y}=e,[w,x]=r.useState(null),b=(0,a.c)(h),S=(0,a.c)(g),C=r.useRef(null),E=(0,l.s)(t,e=>x(e)),j=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(c){let e=function(e){if(j.paused||!w)return;let t=e.target;w.contains(t)?C.current=t:v(C.current,{select:!0})},t=function(e){if(j.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||v(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[c,w,j.paused]),r.useEffect(()=>{if(w){m.add(j);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(s,d);w.addEventListener(s,b),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(p(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(w))}return()=>{w.removeEventListener(s,b),setTimeout(()=>{let t=new CustomEvent(u,d);w.addEventListener(u,S),w.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),w.removeEventListener(u,S),m.remove(j)},0)}}},[w,b,S,j]);let N=r.useCallback(e=>{if(!n&&!c||j.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[l,o]=function(e){let t=p(e);return[f(t,e),f(t.reverse(),e)]}(t);l&&o?e.shiftKey||r!==o?e.shiftKey&&r===l&&(e.preventDefault(),n&&v(o,{select:!0})):(e.preventDefault(),n&&v(l,{select:!0})):r===t&&e.preventDefault()}},[n,c,j.paused]);return(0,i.jsx)(o.sG.div,{tabIndex:-1,...y,ref:E,onKeyDown:N})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}c.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=h(e,t)).unshift(t)},remove(t){var n;null===(n=(e=h(e,t))[0])||void 0===n||n.resume()}}}();function h(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},47863:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},66474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},92293:(e,t,n)=>{n.d(t,{Oh:()=>o});var r=n(12115),l=0;function o(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:a()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:a()),l++,()=>{1===l&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),l--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}}}]);