(()=>{var e={};e.id=7188,e.ids=[7188],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(60687),a=r(43210),o=r(63239),n=r(29523),i=r(96474);let l=(0,r(62688).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var c=r(63143),d=r(88233),u=r(31158),h=r(30474),p=r(16189),m=r(21342),g=r(6862),x=r(26423),f=r(20140);function y({university:e,onDelete:t}){let{id:r,name:o,location:n,status:i,logo:y,contactDetails:j,downloadedQuestions:v}=e,[b,w]=(0,a.useState)(!1),[k,N]=(0,a.useState)(!1),P=(0,p.useRouter)(),A=y&&(y.startsWith("http://")||y.startsWith("https://")),E=A?"/placeholder.svg":y||"/placeholder.svg",C=async()=>{try{N(!0),await (0,x.pZ)(r),(0,f.o)({title:"Success",description:"College deleted successfully"}),t&&t()}catch(e){console.error("Error deleting college:",e),(0,f.o)({title:"Error",description:e.message||"Failed to delete college",variant:"destructive"})}finally{N(!1),w(!1)}};return(0,s.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white overflow-hidden",children:[(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("span",{className:"text-[13px] font-medium leading-[16px] text-[#98A2B3]",children:["Status :",(0,s.jsx)("span",{className:`ml-1 ${"Active"===i?"text-[#039855]":"text-[#EF4444]"}`,children:i})]})}),(0,s.jsxs)(m.rI,{children:[(0,s.jsx)(m.ty,{asChild:!0,children:(0,s.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,s.jsx)(l,{size:20})})}),(0,s.jsxs)(m.SQ,{align:"end",children:[(0,s.jsxs)(m._2,{onClick:()=>{P.push(`/admin/edit-college/${r}`)},className:"cursor-pointer",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,s.jsxs)(m._2,{onClick:()=>w(!0),className:"cursor-pointer text-red-600 focus:text-red-600",children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"bg-gray-100 p-3 rounded-md w-20 h-20 flex items-center justify-center",children:A?(0,s.jsx)("img",{src:y,alt:`${o} logo`,className:"object-contain w-[60px] h-[60px]"}):(0,s.jsx)(h.default,{src:E,alt:`${o} logo`,width:60,height:60,className:"object-contain"})})}),(0,s.jsxs)("div",{className:"text-center space-y-1",children:[(0,s.jsx)("h3",{className:"font-[600] text-[17px] leading-[24px] tracking-[-0.5%] text-[#333333]",children:o}),(0,s.jsxs)("p",{className:"font-[400] text-[15px] leading-[20px] tracking-[-0.5%] text-gray-600",children:[n.city,", ",n.state]}),(0,s.jsxs)("p",{className:"text-[#98A2B3] text-[13px] font-[500] leading-[16px]",children:["Contact details : ",j]})]}),(0,s.jsx)("div",{className:"border-t pt-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,s.jsx)(u.A,{size:16,className:"text-gray-500"}),(0,s.jsx)("span",{className:"font-[600] text-[12px] leading-[100%] tracking-[0.5%]",children:"Downloaded questions"}),(0,s.jsxs)("span",{className:"bg-[#EF4444] text-white text-[12px] font-medium px-2 py-0.5 rounded-full",children:[v.current,"/",v.total]})]})})]}),(0,s.jsx)(g.Lt,{open:b,onOpenChange:w,children:(0,s.jsxs)(g.EO,{children:[(0,s.jsxs)(g.wd,{children:[(0,s.jsx)(g.r7,{children:"Are you sure you want to delete this college?"}),(0,s.jsx)(g.$v,{children:"This action cannot be undone. This will permanently delete the college and all associated data."})]}),(0,s.jsxs)(g.ck,{children:[(0,s.jsx)(g.Zr,{children:"Cancel"}),(0,s.jsx)(g.Rx,{onClick:C,className:"bg-red-600 hover:bg-red-700",disabled:k,children:k?"Deleting...":"Delete"})]})]})})]})}var j=r(47033),v=r(14952);function b({currentPage:e,totalPages:t,onPageChange:r}){let a=[];for(let e=1;e<=t;e++)a.push(e);return(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsxs)("button",{onClick:()=>e>1&&r(e-1),disabled:1===e,className:"flex items-center px-4 py-2 text-[15px] font-[500] text-[#667085] disabled:opacity-50",children:[(0,s.jsx)(j.A,{className:"mr-1 h-4 w-4"}),"PREVIOUS"]}),a.map(t=>(0,s.jsx)("button",{onClick:()=>r(t),className:`w-8 h-8 flex items-center justify-center rounded-md text-[15px] font-medium
            ${e===t?"bg-[#6B7280] text-white":"text-[#6B7280] hover:bg-gray-100"}`,children:t},t)),(0,s.jsxs)("button",{onClick:()=>e<t&&r(e+1),disabled:e===t,className:"flex items-center px-4 py-2 text-[15px] font-[500] text-[#667085] disabled:opacity-50",children:["NEXT",(0,s.jsx)(v.A,{className:"ml-1 h-4 w-4"})]})]})}function w({universities:e,itemsPerPage:t,onCollegeDeleted:r}){let[o,n]=(0,a.useState)(1),i=o*t,l=e.slice(i-t,i),c=Math.ceil(e.length/t);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:l.map((e,t)=>(0,s.jsx)(y,{university:e,onDelete:r},t))}),(0,s.jsx)("div",{className:"flex justify-center mt-8",children:(0,s.jsx)(b,{currentPage:o,totalPages:c,onPageChange:e=>{n(e)}})})]})}var k=r(85814),N=r.n(k),P=r(31981);let A=()=>{let[e,t]=(0,a.useState)([]),[r,l]=(0,a.useState)(!0),c=async()=>{try{l(!0);let e=await (0,x.JY)();if((0,P.cY)(e)){let r=e.data.map(e=>({id:e._id,name:e.name,location:{city:e.city||"",state:e.state||""},status:e.status||"Active",logo:e.logoUrl||"/placeholder.svg?height=60&width=60",contactDetails:e.contactPhone||"",downloadedQuestions:{current:0,total:100}}));t(r)}}catch(e){console.error("Unexpected error fetching colleges:",e),(0,f.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{l(!1)}};return(0,a.useEffect)(()=>{c()},[]),(0,s.jsxs)("main",{className:"min-h-screen bg-gray-50 py-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-xl font-semibold text-black",children:"College Management List"}),(0,s.jsx)(o.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"College Management"}],className:"text-sm mt-1"})]}),(0,s.jsx)(N(),{href:"/admin/add-college",children:(0,s.jsxs)(n.$,{className:"bg-[#05603A] hover:bg-[#04502F] text-white",children:["Add college",(0,s.jsx)(i.A,{className:"w-4 h-4 ml-2"})]})})]}),(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:r?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#05603A]"})}):(0,s.jsx)(w,{universities:e,itemsPerPage:8,onCollegeDeleted:()=>{c()}})})]})}},20943:(e,t,r)=>{Promise.resolve().then(r.bind(r,99111))},21820:e=>{"use strict";e.exports=require("os")},26423:(e,t,r)=>{"use strict";r.d(t,{$T:()=>u,JY:()=>o,M0:()=>a,N0:()=>l,mS:()=>c,mi:()=>d,pZ:()=>n,qk:()=>i});var s=r(31981);async function a(e){let t=localStorage.getItem("backendToken");if(!t)return(0,s.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch("http://localhost:3000/api/colleges",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${r.status}`,"Failed to create college. Please try again.")}let a=await r.json();return(0,s.$y)(a,!0,"College created successfully!")}catch(e){return console.error("Error creating college:",e),(0,s.hS)(e instanceof Error?e.message:"Failed to create college. Please try again.","Failed to create college. Please try again.")}}async function o(){let e=localStorage.getItem("backendToken");if(!e)return(0,s.hS)("Authentication required","Authentication required. Please log in again.");try{let t=await fetch("http://localhost:3000/api/colleges",{method:"GET",headers:{Authorization:`Bearer ${e}`}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${t.status}`,"Failed to load colleges. Please try again.")}let r=await t.json();return(0,s.$y)(r)}catch(e){return console.error("Error fetching colleges:",e),(0,s.hS)(e instanceof Error?e.message:"Failed to load colleges. Please try again.","Failed to load colleges. Please try again.")}}async function n(e){let t=localStorage.getItem("backendToken");if(!t)return(0,s.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${r.status}`,"Failed to delete college. Please try again.")}let a=await r.json();return(0,s.$y)(a,!0,"College deleted successfully!")}catch(e){return console.error("Error deleting college:",e),(0,s.hS)(e instanceof Error?e.message:"Failed to delete college. Please try again.","Failed to delete college. Please try again.")}}async function i(e){let t=localStorage.getItem("backendToken");if(!t)return(0,s.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${r.status}`,"Failed to load college. Please try again.")}let a=await r.json();return(0,s.$y)(a)}catch(e){return console.error("Error fetching college:",e),(0,s.hS)(e instanceof Error?e.message:"Failed to load college. Please try again.","Failed to load college. Please try again.")}}async function l(e,t){let r=localStorage.getItem("backendToken");if(!r)return(0,s.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,s.hS)(e.message||`Error: ${a.status}`,"Failed to update college. Please try again.")}let o=await a.json();return(0,s.$y)(o,!0,"College updated successfully!")}catch(e){return console.error("Error updating college:",e),(0,s.hS)(e instanceof Error?e.message:"Failed to update college. Please try again.","Failed to update college. Please try again.")}}async function c(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let r=await fetch(`http://localhost:3000/api/analytics/college/${e}/summary`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||`Error: ${r.status}`)}return await r.json()}catch(e){throw console.error("Error fetching college:",e),e}}async function d(e,t,r){let s=localStorage.getItem("backendToken");if(!s)throw Error("Authentication required");try{let a=`http://localhost:3000/api/analytics/college/${e}/question-papers?startDate=${encodeURIComponent(t)}&endDate=${encodeURIComponent(r)}`,o=await fetch(a,{method:"GET",headers:{Authorization:`Bearer ${s}`,"Content-Type":"application/json"}});if(!o.ok){let e=await o.json().catch(()=>({}));throw Error(e.message||`Error: ${o.status}`)}return await o.json()}catch(e){throw console.error("Error fetching question paper stats:",e),e}}async function u(e,t=10){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let s=`http://localhost:3000/api/analytics/college/${e}/top-teachers?limit=${t}`,a=await fetch(s,{method:"GET",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||`Error: ${a.status}`)}return await a.json()}catch(e){throw console.error("Error fetching top teachers:",e),e}}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},31981:(e,t,r)=>{"use strict";r.d(t,{$y:()=>o,cY:()=>n,hS:()=>a});var s=r(52581);function a(e,t="An error occurred. Please try again.",r=!0){let o,n=t;return e?.message?n=e.message:"string"==typeof e?n=e:e?.response?.data?.message?n=e.response.data.message:e?.data?.message&&(n=e.data.message),e?.status?o=e.status:e?.response?.status&&(o=e.response.status),n.includes("already exists")||(n.includes("Authentication")||n.includes("Unauthorized")?n="Please log in again to continue. Your session may have expired.":n.includes("Network")||n.includes("fetch")?n="Please check your internet connection and try again.":n.includes("not found")?n="The requested resource was not found.":n.includes("Forbidden")?n="You do not have permission to perform this action.":500===o?n="Server error. Please try again later.":503===o&&(n="Service temporarily unavailable. Please try again later.")),r&&s.oR.error(n),{success:!1,error:n,statusCode:o}}function o(e,t=!1,r){return t&&r&&s.oR.success(r),{success:!0,data:e}}function n(e){return!0===e.success}},33873:e=>{"use strict";e.exports=require("path")},37043:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),a=r(48088),o=r(88170),n=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["college",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,92490)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/college/page",pathname:"/admin/college",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},42505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(37413),a=r(92555);function o(){return(0,s.jsx)(a.W,{message:"Loading admin dashboard..."})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61193:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),a=r(66327),o=r(99557),n=r(45285),i=r(53355);function l({children:e}){return(0,s.jsx)(n.A,{allowedRoles:[o.g.SUPER_ADMIN],children:(0,s.jsx)(i.default,{children:(0,s.jsx)(a.N,{role:o.g.SUPER_ADMIN,children:e})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var s=r(60687),a=r(43210),o=r.n(a),n=r(85814),i=r.n(n),l=r(4780),c=r(14952),d=r(93661);let u=({items:e,maxItems:t=4,className:r})=>{let a=o().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,s.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,l.cn)("flex items-center text-sm",r),children:(0,s.jsx)("ol",{className:"flex items-center space-x-1",children:a.map((e,t)=>{let r=t===a.length-1;return(0,s.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,s.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):r?(0,s.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,s.jsx)(i(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,s.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},74075:e=>{"use strict";e.exports=require("zlib")},77611:(e,t,r)=>{Promise.resolve().then(r.bind(r,19738))},79551:e=>{"use strict";e.exports=require("url")},80775:(e,t,r)=>{Promise.resolve().then(r.bind(r,61193))},81630:e=>{"use strict";e.exports=require("http")},83187:(e,t,r)=>{Promise.resolve().then(r.bind(r,92490))},83997:e=>{"use strict";e.exports=require("tty")},92490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\college\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,4619,3287,9592,2581,5361,4707,6658,9371],()=>r(37043));module.exports=s})();