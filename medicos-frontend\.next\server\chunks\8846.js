"use strict";exports.id=8846,exports.ids=[8846],exports.modules={28846:(t,e,o)=>{o.d(e,{getQuestionCountByTopic:()=>i});let i=async(t,e)=>{try{let{apiCall:i}=await Promise.resolve().then(o.bind(o,62185));console.log(`🔄 FALLBACK API: Calling old questions API for topic ${t}, subject ${e}`),console.log(`📞 OLD API URL: /questions?subjectId=${e}&topicId=${t}&limit=1`);let s=await i(`/questions?subjectId=${e}&topicId=${t}&limit=1`);if(s&&s.pagination&&"number"==typeof s.pagination.totalItems)return s.pagination.totalItems;if(s&&Array.isArray(s.questions))return+(s.questions.length>0);return 0}catch(o){return console.warn(`Could not fetch question count for topic ${t} in subject ${e}:`,o.message||o),0}}}};