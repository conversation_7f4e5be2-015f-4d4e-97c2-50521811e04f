(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7514],{65764:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>C});var r=l(95155),a=l(12115),n=l(12379),t=l(90221),d=l(62177),i=l(55594),o=l(46104),c=l(30285),m=l(17759),x=l(62523),h=l(88539),j=l(95984),g=l(88262),u=l(35695),p=l(74125),v=l(55097),b=l(59434);let f=["image/jpeg","image/jpg","image/png","image/svg+xml"],N=i.Ik({collegeName:i.Yj().min(2,{message:"College name must be at least 2 characters."}),phone:i.Yj().min(10,{message:"Phone number must be valid."}),email:i.Yj().email({message:"Please enter a valid email address."}),address:i.Yj().max(100,{message:"Address must not exceed 100 characters."}),logo:i.bz().optional().refine(e=>{var s;return!e||0===e.length||(null===(s=e[0])||void 0===s?void 0:s.size)<=0x3200000},"Max file size is 50MB.").refine(e=>{var s;return!e||0===e.length||f.includes(null===(s=e[0])||void 0===s?void 0:s.type)},"Only .jpg, .jpeg, .png and .svg formats are supported.")});function y(){let[e,s]=(0,a.useState)([]),[l,n]=(0,a.useState)(!1),i=(0,u.useRouter)(),y=(0,d.mN)({resolver:(0,t.u)(N),defaultValues:{collegeName:"",phone:"",email:"",address:""}});async function C(s){n(!0);try{let l="";e.length>0&&(l=await (0,b.b)(e[0]));let r={name:s.collegeName,address:s.address,contactPhone:s.phone,contactEmail:s.email,logoUrl:l},a=await (0,p.M0)(r);(0,v.cY)(a)&&(z(),i.push("/admin/college"))}catch(e){console.error("Unexpected error adding college:",e),(0,g.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{n(!1)}}function z(){y.reset(),s([])}return(0,r.jsx)(m.lV,{...y,children:(0,r.jsxs)("form",{onSubmit:y.handleSubmit(C),className:"space-y-8 mx-auto",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(m.zB,{control:y.control,name:"collegeName",render:e=>{let{field:s}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"College name"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(x.p,{placeholder:"",...s})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:y.control,name:"phone",render:e=>{let{field:s}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsxs)(m.lR,{children:["Phone",(0,r.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Required"})]}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(j.L,{...s})}),(0,r.jsx)(m.C5,{})]})}})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsx)(m.zB,{control:y.control,name:"email",render:e=>{let{field:s}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{children:"Email address"}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(x.p,{placeholder:"",...s})}),(0,r.jsx)(m.Rr,{children:"We'll never share your details."}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:y.control,name:"address",render:e=>{let{field:s}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(m.lR,{children:"Address details"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[s.value.length,"/100"]})]}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(h.T,{placeholder:"",className:"resize-none",maxLength:100,...s})}),(0,r.jsx)(m.C5,{})]})}})]}),(0,r.jsx)(m.zB,{control:y.control,name:"logo",render:e=>{let{field:l}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsxs)(m.lR,{children:["Upload logo",(0,r.jsx)("span",{className:"text-sm text-muted-foreground font-normal ml-2",children:"Optional"})]}),(0,r.jsx)(m.MJ,{children:(0,r.jsx)(o.l,{value:l.value,onChange:e=>{l.onChange(e),s(Array.from(e||[]))},maxSize:0x3200000,acceptedTypes:f})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(c.$,{type:"submit",className:"bg-[#05603A] hover:bg-[#04502F]",disabled:l,children:l?"Adding...":"Add college"}),(0,r.jsx)(c.$,{type:"button",variant:"destructive",onClick:()=>y.reset(),disabled:l,children:"Cancel"}),(0,r.jsx)(c.$,{type:"button",variant:"outline",onClick:z,disabled:l,children:"Reset"})]})]})})}let C=()=>(0,r.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-black",children:"Add Colleges"}),(0,r.jsx)(n.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"Add Colleges"}],className:"text-sm mt-1"})]}),(0,r.jsx)("div",{className:"container mx-auto py-10",children:(0,r.jsx)(y,{})})]})})},81963:(e,s,l)=>{Promise.resolve().then(l.bind(l,65764))}},e=>{var s=s=>e(e.s=s);e.O(0,[4277,6874,6671,1141,2571,7117,5631,221,6106,124,9078,8441,1684,7358],()=>s(81963)),_N_E=e.O()}]);