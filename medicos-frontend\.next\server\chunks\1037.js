exports.id=1037,exports.ids=[1037],exports.modules={20943:(e,t,a)=>{Promise.resolve().then(a.bind(a,99111))},26423:(e,t,a)=>{"use strict";a.d(t,{$T:()=>u,JY:()=>o,M0:()=>s,N0:()=>i,mS:()=>c,mi:()=>d,pZ:()=>n,qk:()=>l});var r=a(31981);async function s(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("http://localhost:3000/api/colleges",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||`Error: ${a.status}`,"Failed to create college. Please try again.")}let s=await a.json();return(0,r.$y)(s,!0,"College created successfully!")}catch(e){return console.error("Error creating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to create college. Please try again.","Failed to create college. Please try again.")}}async function o(){let e=localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let t=await fetch("http://localhost:3000/api/colleges",{method:"GET",headers:{Authorization:`Bearer ${e}`}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||`Error: ${t.status}`,"Failed to load colleges. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching colleges:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load colleges. Please try again.","Failed to load colleges. Please try again.")}}async function n(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||`Error: ${a.status}`,"Failed to delete college. Please try again.")}let s=await a.json();return(0,r.$y)(s,!0,"College deleted successfully!")}catch(e){return console.error("Error deleting college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to delete college. Please try again.","Failed to delete college. Please try again.")}}async function l(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||`Error: ${a.status}`,"Failed to load college. Please try again.")}let s=await a.json();return(0,r.$y)(s)}catch(e){return console.error("Error fetching college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load college. Please try again.","Failed to load college. Please try again.")}}async function i(e,t){let a=localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let s=await fetch(`http://localhost:3000/api/colleges/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify(t)});if(!s.ok){let e=await s.json().catch(()=>({}));return(0,r.hS)(e.message||`Error: ${s.status}`,"Failed to update college. Please try again.")}let o=await s.json();return(0,r.$y)(o,!0,"College updated successfully!")}catch(e){return console.error("Error updating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to update college. Please try again.","Failed to update college. Please try again.")}}async function c(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let a=await fetch(`http://localhost:3000/api/analytics/college/${e}/summary`,{method:"GET",headers:{Authorization:`Bearer ${t}`}});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||`Error: ${a.status}`)}return await a.json()}catch(e){throw console.error("Error fetching college:",e),e}}async function d(e,t,a){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let s=`http://localhost:3000/api/analytics/college/${e}/question-papers?startDate=${encodeURIComponent(t)}&endDate=${encodeURIComponent(a)}`,o=await fetch(s,{method:"GET",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"}});if(!o.ok){let e=await o.json().catch(()=>({}));throw Error(e.message||`Error: ${o.status}`)}return await o.json()}catch(e){throw console.error("Error fetching question paper stats:",e),e}}async function u(e,t=10){let a=localStorage.getItem("backendToken");if(!a)throw Error("Authentication required");try{let r=`http://localhost:3000/api/analytics/college/${e}/top-teachers?limit=${t}`,s=await fetch(r,{method:"GET",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"}});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||`Error: ${s.status}`)}return await s.json()}catch(e){throw console.error("Error fetching top teachers:",e),e}}},31981:(e,t,a)=>{"use strict";a.d(t,{$y:()=>o,cY:()=>n,hS:()=>s});var r=a(52581);function s(e,t="An error occurred. Please try again.",a=!0){let o,n=t;return e?.message?n=e.message:"string"==typeof e?n=e:e?.response?.data?.message?n=e.response.data.message:e?.data?.message&&(n=e.data.message),e?.status?o=e.status:e?.response?.status&&(o=e.response.status),n.includes("already exists")||(n.includes("Authentication")||n.includes("Unauthorized")?n="Please log in again to continue. Your session may have expired.":n.includes("Network")||n.includes("fetch")?n="Please check your internet connection and try again.":n.includes("not found")?n="The requested resource was not found.":n.includes("Forbidden")?n="You do not have permission to perform this action.":500===o?n="Server error. Please try again later.":503===o&&(n="Service temporarily unavailable. Please try again later.")),a&&r.oR.error(n),{success:!1,error:n,statusCode:o}}function o(e,t=!1,a){return t&&a&&r.oR.success(a),{success:!0,data:e}}function n(e){return!0===e.success}},34729:(e,t,a)=>{"use strict";a.d(t,{T:()=>o});var r=a(60687);a(43210);var s=a(4780);function o({className:e,...t}){return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},42505:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(37413),s=a(92555);function o(){return(0,r.jsx)(s.W,{message:"Loading admin dashboard..."})}},61193:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(60687),s=a(66327),o=a(99557),n=a(45285),l=a(53355);function i({children:e}){return(0,r.jsx)(n.A,{allowedRoles:[o.g.SUPER_ADMIN],children:(0,r.jsx)(l.default,{children:(0,r.jsx)(s.N,{role:o.g.SUPER_ADMIN,children:e})})})}},63239:(e,t,a)=>{"use strict";a.d(t,{A:()=>u});var r=a(60687),s=a(43210),o=a.n(s),n=a(85814),l=a.n(n),i=a(4780),c=a(14952),d=a(93661);let u=({items:e,maxItems:t=4,className:a})=>{let s=o().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",a),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:s.map((e,t)=>{let a=t===s.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):a?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(l(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},71669:(e,t,a)=>{"use strict";a.d(t,{C5:()=>y,MJ:()=>p,Rr:()=>x,eI:()=>g,lR:()=>f,lV:()=>c,zB:()=>u});var r=a(60687),s=a(43210),o=a(11329),n=a(27605),l=a(4780),i=a(80013);let c=n.Op,d=s.createContext({}),u=({...e})=>(0,r.jsx)(d.Provider,{value:{name:e.name},children:(0,r.jsx)(n.xI,{...e})}),m=()=>{let e=s.useContext(d),t=s.useContext(h),{getFieldState:a}=(0,n.xW)(),r=(0,n.lN)({name:e.name}),o=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...o}},h=s.createContext({});function g({className:e,...t}){let a=s.useId();return(0,r.jsx)(h.Provider,{value:{id:a},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,l.cn)("grid gap-2",e),...t})})}function f({className:e,...t}){let{error:a,formItemId:s}=m();return(0,r.jsx)(i.J,{"data-slot":"form-label","data-error":!!a,className:(0,l.cn)("data-[error=true]:text-destructive",e),htmlFor:s,...t})}function p({...e}){let{error:t,formItemId:a,formDescriptionId:s,formMessageId:n}=m();return(0,r.jsx)(o.Slot,{"data-slot":"form-control",id:a,"aria-describedby":t?`${s} ${n}`:`${s}`,"aria-invalid":!!t,...e})}function x({className:e,...t}){let{formDescriptionId:a}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:a,className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}function y({className:e,...t}){let{error:a,formMessageId:s}=m(),o=a?String(a?.message??""):t.children;return o?(0,r.jsx)("p",{"data-slot":"form-message",id:s,className:(0,l.cn)("text-destructive text-sm",e),...t,children:o}):null}},78632:(e,t,a)=>{"use strict";a.d(t,{L:()=>f});var r=a(60687),s=a(43210),o=a(78272),n=a(29523),l=a(41784),i=a(12048),c=a(36141),d=a(4780);function u({...e}){return(0,r.jsx)(c.bL,{"data-slot":"popover",...e})}function m({...e}){return(0,r.jsx)(c.l9,{"data-slot":"popover-trigger",...e})}function h({className:e,align:t="center",sideOffset:a=4,...s}){return(0,r.jsx)(c.ZL,{children:(0,r.jsx)(c.UC,{"data-slot":"popover-content",align:t,sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...s})})}let g=[{id:"us",name:"United States",code:"+1",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{id:"ca",name:"Canada",code:"+1",flag:"\uD83C\uDDE8\uD83C\uDDE6"},{id:"gb",name:"United Kingdom",code:"+44",flag:"\uD83C\uDDEC\uD83C\uDDE7"},{id:"au",name:"Australia",code:"+61",flag:"\uD83C\uDDE6\uD83C\uDDFA"}];function f({value:e,onChange:t,className:a}){let[c,f]=(0,s.useState)(!1),[p,x]=(0,s.useState)(g[0]),y=e=>{let t=e.replace(/\D/g,"");if("us"===p.id||"ca"===p.id){if(t.length<=3);else if(t.length<=6)return`(${t.slice(0,3)}) ${t.slice(3)}`;else return`(${t.slice(0,3)}) ${t.slice(3,6)}-${t.slice(6,10)}`}return t},j=e=>{x(e),f(!1),t("")};return(0,r.jsxs)("div",{className:(0,d.cn)("flex",a),children:[(0,r.jsxs)(u,{open:c,onOpenChange:f,children:[(0,r.jsx)(m,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",role:"combobox","aria-expanded":c,className:"w-[80px] justify-between px-2 border-r-0 rounded-r-none",children:[(0,r.jsx)("span",{className:"mr-1",children:p.flag}),(0,r.jsx)(o.A,{className:"h-4 w-4 opacity-50"})]})}),(0,r.jsx)(h,{className:"w-[200px] p-0",children:(0,r.jsxs)(l.uB,{children:[(0,r.jsx)(l.G7,{placeholder:"Search country..."}),(0,r.jsxs)(l.oI,{children:[(0,r.jsx)(l.xL,{children:"No country found."}),(0,r.jsx)(l.L$,{className:"max-h-[200px] overflow-y-auto",children:g.map(e=>(0,r.jsxs)(l.h_,{value:e.name,onSelect:()=>j(e),children:[(0,r.jsx)("span",{className:"mr-2",children:e.flag}),(0,r.jsx)("span",{children:e.name}),(0,r.jsx)("span",{className:"ml-auto text-muted-foreground",children:e.code})]},e.id))})]})]})})]}),(0,r.jsx)(i.p,{type:"tel",value:e,onChange:e=>{t(y(e.target.value))},className:"rounded-l-none",placeholder:"us"===p.id?"(*************":"Phone number"})]})}},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var r=a(60687);a(43210);var s=a(78148),o=a(4780);function n({className:e,...t}){return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},80775:(e,t,a)=>{Promise.resolve().then(a.bind(a,61193))},87834:(e,t,a)=>{"use strict";a.d(t,{l:()=>h});var r=a(60687),s=a(43210),o=a(9005),n=a(78464),l=a(11860),i=a(43649),c=a(29523),d=a(14819),u=a(4780);let m=s.forwardRef(({className:e,value:t,...a},s)=>(0,r.jsx)(d.bL,{ref:s,className:(0,u.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...a,children:(0,r.jsx)(d.C1,{className:"h-full w-full flex-1 bg-red-500 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));function h({value:e,onChange:t,maxSize:a=0x3200000,acceptedTypes:d=["image/jpeg","image/jpg","image/png","image/svg+xml"]}){let[h,g]=(0,s.useState)(!1),[f,p]=(0,s.useState)(0),[x,y]=(0,s.useState)(null),j=(0,s.useRef)(null),v=()=>{p(0);let e=setInterval(()=>{p(t=>t>=100?(clearInterval(e),100):t+5)},100)},b=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?g(!0):"dragleave"===e.type&&g(!1)},w=e=>e.size>a?(y(`File size exceeds ${a/1048576}MB limit.`),!1):d.includes(e.type)?(y(null),!0):(y("File type not supported."),!1),N=()=>{t(null),p(0),j.current&&(j.current.value="")},$=e=>e<1024?e+" B":e<1048576?(e/1024).toFixed(1)+" KB":(e/1048576).toFixed(1)+" MB",S=e?Array.from(e):[],E=S.length>0;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)("div",{className:(0,u.cn)("border-2 border-dashed rounded-lg p-6 transition-colors",h?"border-primary bg-primary/5":"border-muted-foreground/25",E?"bg-muted/50":""),onDragEnter:b,onDragLeave:b,onDragOver:b,onDrop:e=>{e.preventDefault(),e.stopPropagation(),g(!1),e.dataTransfer.files&&e.dataTransfer.files.length>0&&w(e.dataTransfer.files[0])&&(t(e.dataTransfer.files),v())},children:(0,r.jsx)("div",{className:"flex flex-col items-center justify-center gap-2 text-center",children:E?(0,r.jsxs)("div",{className:"w-full",children:[S.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded-md mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"bg-muted p-1 rounded",children:(0,r.jsx)(n.A,{className:"h-5 w-5 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-primary",children:e.name}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:$(e.size)})]})]}),(0,r.jsxs)(c.$,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:N,children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Remove file"})]})]},t)),(0,r.jsxs)("div",{className:"w-full mt-2",children:[(0,r.jsx)(m,{value:f,className:"h-2"}),(0,r.jsxs)("p",{className:"text-xs text-right mt-1 text-muted-foreground",children:[f,"%"]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"rounded-full bg-primary/10 p-3 text-primary",children:(0,r.jsx)(o.A,{className:"h-10 w-10"})}),(0,r.jsxs)("p",{className:"text-sm font-medium",children:["Drop your files here or"," ",(0,r.jsx)(c.$,{type:"button",variant:"link",className:"p-0 h-auto text-primary",onClick:()=>j.current?.click(),children:"browse"})]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Maximum size: ",a/1048576,"MB"]})]})})}),x&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-2 text-destructive text-sm",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsx)("p",{children:x})]}),(0,r.jsx)("input",{ref:j,type:"file",className:"hidden",onChange:e=>{e.preventDefault(),e.target.files&&e.target.files.length>0&&w(e.target.files[0])&&(t(e.target.files),v())},accept:d.join(",")})]})}m.displayName=d.bL.displayName},99111:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx","default")}};