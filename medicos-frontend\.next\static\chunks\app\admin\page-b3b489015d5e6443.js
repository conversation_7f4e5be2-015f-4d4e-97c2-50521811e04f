(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{15356:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var s=a(95155);a(12115);var r=a(59434),l=a(94788),n=a(85339),o=a(66695),i=a(68856);let c=e=>{let{icon:t=(0,s.jsx)(l.A,{className:"h-5 w-5 text-muted-foreground"}),label:a,value:c,className:d,iconClassName:u,labelClassName:m,valueClassName:h,loading:x=!1,error:g=!1}=e;return(0,s.jsx)(o.Zp,{className:(0,r.cn)("p-6",d),children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:(0,r.cn)("inline-flex h-10 w-10 items-center justify-center rounded-lg bg-muted",u),children:g?(0,s.jsx)(n.A,{className:"h-5 w-5 text-destructive"}):t}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:(0,r.cn)("text-sm font-medium text-muted-foreground",m),children:a}),x?(0,s.jsx)(i.E,{className:"h-9 w-24"}):g?(0,s.jsx)("p",{className:(0,r.cn)("text-sm font-medium text-destructive"),children:"Failed to load"}):(0,s.jsx)("p",{className:(0,r.cn)("text-3xl font-bold",h),children:c})]})]})})}},17313:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>i,av:()=>c,j7:()=>o,tU:()=>n});var s=a(95155);a(12115);var r=a(60704),l=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"tabs",className:(0,l.cn)("flex flex-col gap-2",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,l.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,l.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,l.cn)("flex-1 outline-none",t),...a})}},46769:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var s=a(95155),r=a(12115),l=a(61884),n=a(71987),o=a(66695),i=a(83540),c=a(99445),d=a(94754),u=a(96025),m=a(16238),h=a(94517),x=a(62341),g=a(17313),f=a(69074),v=a(30285),p=a(25731);a(55097);let j=async()=>{try{return await (0,p.apiCall)("/analytics/platform-summary")}catch(e){throw console.error("Error fetching platform summary:",e),Error(e.message||"Failed to fetch platform summary")}},b=async(e,t,a)=>{try{let s="";e&&(s+="year=".concat(e)),t&&(s+="".concat(s?"&":"","startDate=").concat(t)),a&&(s+="".concat(s?"&":"","endDate=").concat(a));let r="/analytics/usage-trends".concat(s?"?".concat(s):"");return await (0,p.apiCall)(r)}catch(e){throw console.error("Error fetching usage trends:",e),Error(e.message||"Failed to fetch usage trends")}},y=async(e,t,a,s)=>{try{let r="";e&&(r+="year=".concat(e)),t&&(r+="".concat(r?"&":"","startDate=").concat(t)),a&&(r+="".concat(r?"&":"","endDate=").concat(a)),s&&(r+="".concat(r?"&":"","view=").concat(s));let l="/analytics/college-growth".concat(r?"?".concat(r):"");return await (0,p.apiCall)(l)}catch(e){throw console.error("Error fetching college growth:",e),Error(e.message||"Failed to fetch college growth")}},N=e=>{let{active:t,payload:a,label:r}=e;return t&&a&&a.length?(0,s.jsxs)("div",{className:"bg-white p-3 border border-gray-200 shadow-sm rounded-md",children:[(0,s.jsx)("p",{className:"text-sm font-medium mb-1",children:r}),a.map((e,t)=>(0,s.jsxs)("p",{className:"text-sm",style:{color:e.color},children:[(0,s.jsxs)("span",{className:"font-medium",children:[e.name,": "]}),e.value]},"item-".concat(t)))]}):null},w=e=>{let{title:t="Total Colleges",subtitle:a="Target you've set for each month",data:l,lines:n=[{key:"target",name:"Target",color:"#4F46E5",gradientId:"colorTarget",startColor:"#4F46E5",endColor:"#4F46E5"},{key:"actual",name:"Actual",color:"#60A5FA",gradientId:"colorActual",startColor:"#60A5FA",endColor:"#60A5FA"}],xAxisKey:p="month",yAxisDomain:j=[0,1e3],yAxisTicks:b=[0,200,400,600,800,1e3],showTabs:w=!0,tabOptions:k=[{value:"overview",label:"Overview"},{value:"sales",label:"Sales"},{value:"revenue",label:"Revenue"}],defaultTabValue:A="overview",showDateRange:C=!0,dateRangeLabel:F="05 Feb - 06 March"}=e,[E,S]=(0,r.useState)([]),[D,M]=(0,r.useState)(!1),[T,L]=(0,r.useState)(null),[_,z]=(0,r.useState)(A);return(0,r.useEffect)(()=>{(async()=>{if(l){S(l);return}M(!0),L(null);try{let e=new Date().getFullYear().toString(),t=(await y(e,void 0,void 0,_)).data.map(e=>({month:e.monthName,target:e.monthlyTarget,actual:e.cumulativeColleges,collegesAdded:e.collegesAdded,targetAchievement:e.targetAchievement,revenue:e.revenue}));S(t)}catch(e){console.error("Error fetching college growth:",e),L(e instanceof Error?e.message:"Failed to fetch college growth data"),S(["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((e,t)=>{let a=30*Math.sin(.5*t)+10*t,s=20*Math.sin((t+1)*.7)+12*t;return{month:e,target:Math.round(720+a+5*t),actual:Math.round(480+s+6*t)}}))}finally{M(!1)}})()},[l,_]),(0,s.jsxs)(o.Zp,{className:"w-full",children:[(0,s.jsxs)(o.aR,{className:"pb-2 pt-6 px-6 flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-800",children:t}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:a})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between w-full sm:w-auto sm:justify-end gap-4",children:[w&&(0,s.jsx)(g.tU,{value:_,onValueChange:z,children:(0,s.jsx)(g.j7,{className:"bg-gray-100/80",children:k.map(e=>(0,s.jsx)(g.Xi,{value:e.value,children:e.label},e.value))})}),C&&(0,s.jsxs)(v.$,{variant:"outline",className:"gap-1",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline-block text-sm font-normal",children:F})]})]})]}),(0,s.jsx)(o.Wu,{className:"pt-0 px-3 sm:px-6",children:(0,s.jsx)("div",{className:"h-[350px] w-full mt-4",children:D?(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):T?(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-red-500",children:(0,s.jsx)("p",{children:"Failed to load chart data"})}):(0,s.jsx)(i.u,{width:"100%",height:"100%",children:(0,s.jsxs)(c.Q,{data:E,margin:{top:10,right:10,left:0,bottom:20},children:[(0,s.jsx)("defs",{children:n.map(e=>(0,s.jsxs)("linearGradient",{id:e.gradientId,x1:"0",y1:"0",x2:"0",y2:"1",children:[(0,s.jsx)("stop",{offset:"5%",stopColor:e.startColor,stopOpacity:.2}),(0,s.jsx)("stop",{offset:"95%",stopColor:e.endColor,stopOpacity:0})]},e.gradientId))}),(0,s.jsx)(d.d,{vertical:!1,strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,s.jsx)(u.W,{dataKey:p,axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},dy:10}),(0,s.jsx)(m.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},domain:j,ticks:b}),(0,s.jsx)(h.m,{content:(0,s.jsx)(N,{})}),n.map(e=>(0,s.jsx)(x.G,{type:"monotone",dataKey:e.key,name:e.name,stroke:e.color,strokeWidth:2.5,fillOpacity:1,fill:"url(#".concat(e.gradientId,")"),activeDot:{r:6,strokeWidth:0}},e.key))]})})})})]})};var k=a(3401),A=a(83394),C=a(54811),F=a(5623);let E=e=>{let{active:t,payload:a,label:r}=e;return t&&a&&a.length?(0,s.jsx)("div",{className:"bg-white p-2 border border-gray-200 shadow-sm rounded-md",children:(0,s.jsx)("p",{className:"text-sm font-medium",children:"".concat(r,": ").concat(a[0].value)})}):null},S=e=>{let{title:t="Usage trends",data:a,dataKey:l="totalUsage",xAxisKey:n="monthName",yAxisDomain:c,yAxisTicks:x,barSize:g=20,barGap:f=2,maxBarSize:p=20,highlightIndex:j,highlightColor:y="#4F46E5",defaultBarColor:N="#F3F4F6",showMoreButton:w=!0}=e,[S,D]=(0,r.useState)([]),[M,T]=(0,r.useState)(!1),[L,_]=(0,r.useState)(null);(0,r.useEffect)(()=>{(async()=>{if(a){D(a);return}T(!0),_(null);try{let e=new Date().getFullYear().toString(),t=(await b(e)).data.map(e=>({month:e.monthName,[l]:e[l]||e.totalUsage,questionsCreated:e.questionsCreated,papersGenerated:e.papersGenerated}));if(D(t),!c||!x){let e=Math.max(...t.map(e=>e[l])),a=200*Math.ceil(e/200);if(c||(c=[0,a]),!x){let e=a/4;x=Array.from({length:5},(t,a)=>a*e)}}}catch(e){console.error("Error fetching usage trends:",e),_(e instanceof Error?e.message:"Failed to fetch usage data"),D(function(){let e=[450,750,380,620,350,680,500,650,580,600,400,520];return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].map((t,a)=>{let s={month:t};return s[l]=e[a],s})}())}finally{T(!1)}})()},[a,l]);let z=c||[0,800],O=x||[0,200,400,600,800];return(0,s.jsxs)(o.Zp,{className:"w-full",children:[(0,s.jsxs)(o.aR,{className:"flex flex-row items-center justify-between pb-0 pt-4 px-4",children:[(0,s.jsx)(o.ZB,{className:"text-base font-medium text-gray-800",children:t}),w&&(0,s.jsx)(v.$,{variant:"ghost",size:"icon",className:"h-8 w-8",children:(0,s.jsx)(F.A,{className:"h-5 w-5 text-gray-400"})})]}),(0,s.jsx)(o.Wu,{className:"p-4",children:(0,s.jsx)("div",{className:"h-[200px] w-full",children:M?(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-teacher-blue"})}):L?(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-red-500",children:(0,s.jsx)("p",{children:"Failed to load chart data"})}):(0,s.jsx)(i.u,{width:"100%",height:"100%",children:(0,s.jsxs)(k.E,{data:S,margin:{top:5,right:10,left:-20,bottom:5},barSize:g,barGap:f,maxBarSize:p,children:[(0,s.jsx)(d.d,{vertical:!1,horizontal:!0,strokeDasharray:"3 3",stroke:"#f0f0f0"}),(0,s.jsx)(u.W,{dataKey:n,axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},dy:10}),(0,s.jsx)(m.h,{axisLine:!1,tickLine:!1,tick:{fontSize:12,fill:"#6b7280"},tickCount:O.length,domain:z,ticks:O,dx:-10}),(0,s.jsx)(h.m,{content:(0,s.jsx)(E,{}),cursor:!1}),(0,s.jsx)(A.y,{dataKey:l,radius:[4,4,0,0],children:S.map((e,t)=>(0,s.jsx)(C.f,{fill:void 0!==j?t===j?y:N:y},"cell-".concat(t)))})]})})})})]})};var D=a(15356),M=a(48136),T=a(17580),L=a(55509),_=a(91788),z=a(56671);function O(){var e,t,a,l;let n=new Date().getMonth(),[o,i]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),[u,m]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async()=>{try{d(!0);let e=await j();i(e),m(null)}catch(e){console.error("Error fetching platform summary:",e),m(e.message||"Failed to load dashboard data"),z.oR.error("Failed to load dashboard data. Please try again.")}finally{d(!1)}})()},[]),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Dashboard Overview"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4",children:[(0,s.jsx)(D.A,{icon:(0,s.jsx)(M.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Colleges",value:null!==(e=null==o?void 0:o.totalColleges)&&void 0!==e?e:0,loading:c,error:!!u}),(0,s.jsx)(D.A,{icon:(0,s.jsx)(T.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Teachers",value:null!==(t=null==o?void 0:o.totalTeachers)&&void 0!==t?t:0,loading:c,error:!!u,iconClassName:"bg-blue-100",valueClassName:"text-blue-600"}),(0,s.jsx)(D.A,{icon:(0,s.jsx)(L.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Questions",value:null!==(a=null==o?void 0:o.totalQuestions)&&void 0!==a?a:0,loading:c,error:!!u,iconClassName:"bg-green-100",valueClassName:"text-green-600"}),(0,s.jsx)(D.A,{icon:(0,s.jsx)(_.A,{className:"h-5 w-5 text-muted-foreground"}),label:"Total Downloads",value:null!==(l=null==o?void 0:o.totalDownloads)&&void 0!==l?l:0,loading:c,error:!!u,iconClassName:"bg-amber-100",valueClassName:"text-amber-600"})]}),(0,s.jsx)("div",{className:"max-w-full mx-auto",children:(0,s.jsxs)("div",{className:"grid gap-8",children:[(0,s.jsx)(S,{highlightIndex:n}),(0,s.jsx)(w,{})]})})]})}O.getLayout=function(e){return(0,s.jsx)(l.N,{role:n.g.COLLEGE_ADMIN,children:e})}},48100:(e,t,a)=>{Promise.resolve().then(a.bind(a,46769))},55097:(e,t,a)=>{"use strict";a.d(t,{$y:()=>l,cY:()=>n,hS:()=>r});var s=a(56671);function r(e){var t,a,r,l;let n,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=o;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)&&(c=e.data.message),(null==e?void 0:e.status)?n=e.status:(null==e?void 0:null===(l=e.response)||void 0===l?void 0:l.status)&&(n=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===n?c="Server error. Please try again later.":503===n&&(c="Service temporarily unavailable. Please try again later.")),i&&s.oR.error(c),{success:!1,error:c,statusCode:n}}function l(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&s.oR.success(a),{success:!0,data:e}}function n(e){return!0===e.success}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>n});var s=a(95155);a(12115);var r=a(59434);function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7146,4277,6874,6671,1141,2571,6457,685,5631,8377,1165,9641,6106,506,5559,9915,1884,8441,1684,7358],()=>t(48100)),_N_E=e.O()}]);