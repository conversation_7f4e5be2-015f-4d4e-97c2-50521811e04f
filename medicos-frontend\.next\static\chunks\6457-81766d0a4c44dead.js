"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6457],{5845:(e,t,r)=>{r.d(t,{i:()=>i});var n=r(12115),o=r(39033);function i({prop:e,defaultProp:t,onChange:r=()=>{}}){let[i,l]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[i]=r,l=n.useRef(i),a=(0,o.c)(t);return n.useEffect(()=>{l.current!==i&&(a(i),l.current=i)},[i,l,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,s=a?e:i,u=(0,o.c)(r);return[s,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else l(t)},[a,e,l,u])]}},19178:(e,t,r)=>{r.d(t,{qW:()=>f});var n,o=r(12115),i=r(85185),l=r(63540),a=r(6101),s=r(39033),u=r(95155),d="dismissableLayer.update",c=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:h,onPointerDownOutside:y,onFocusOutside:g,onInteractOutside:w,onDismiss:x,...b}=e,E=o.useContext(c),[C,R]=o.useState(null),P=null!==(f=null==C?void 0:C.ownerDocument)&&void 0!==f?f:null===(r=globalThis)||void 0===r?void 0:r.document,[,A]=o.useState({}),N=(0,a.s)(t,e=>R(e)),S=Array.from(E.layers),[j]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),O=S.indexOf(j),D=C?S.indexOf(C):-1,L=E.layersWithOutsidePointerEventsDisabled.size>0,k=D>=O,W=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.c)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){v("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",l.current),l.current=t,r.addEventListener("click",l.current,{once:!0})):t()}else r.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",l.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...E.branches].some(e=>e.contains(t));!k||r||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},P),z=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,s.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&v("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==x||x())},P);return!function(e,t=globalThis?.document){let r=(0,s.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{D===E.layers.size-1&&(null==h||h(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},P),o.useEffect(()=>{if(C)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(n=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),p(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=n)}},[C,P,m,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),p())},[C,E]),o.useEffect(()=>{let e=()=>A({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,u.jsx)(l.sG.div,{...b,ref:N,style:{pointerEvents:L?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,z.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,z.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,W.onPointerDownCapture)})});function p(){let e=new CustomEvent(d);document.dispatchEvent(e)}function v(e,t,r,n){let{discrete:o}=n,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),o?(0,l.hO)(i,a):i.dispatchEvent(a)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let r=o.useContext(c),n=o.useRef(null),i=(0,a.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(l.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},22197:(e,t,r)=>{r.d(t,{Mz:()=>L,i3:()=>W,UC:()=>k,bL:()=>D,Bk:()=>m});var n=r(12115),o=r(84945),i=r(22475),l=r(63540),a=r(95155),s=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:o=5,...i}=e;return(0,a.jsx)(l.sG.svg,{...i,ref:t,width:n,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,a.jsx)("polygon",{points:"0,0 30,0 15,10"})})});s.displayName="Arrow";var u=r(6101),d=r(46081),c=r(39033),f=r(52712),p="Popper",[v,m]=(0,d.A)(p),[h,y]=v(p),g=e=>{let{__scopePopper:t,children:r}=e,[o,i]=n.useState(null);return(0,a.jsx)(h,{scope:t,anchor:o,onAnchorChange:i,children:r})};g.displayName=p;var w="PopperAnchor",x=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:o,...i}=e,s=y(w,r),d=n.useRef(null),c=(0,u.s)(t,d);return n.useEffect(()=>{s.onAnchorChange((null==o?void 0:o.current)||d.current)}),o?null:(0,a.jsx)(l.sG.div,{...i,ref:c})});x.displayName=w;var b="PopperContent",[E,C]=v(b),R=n.forwardRef((e,t)=>{var r,s,d,p,v,m,h,g;let{__scopePopper:w,side:x="bottom",sideOffset:C=0,align:R="center",alignOffset:P=0,arrowPadding:A=0,avoidCollisions:N=!0,collisionBoundary:D=[],collisionPadding:L=0,sticky:k="partial",hideWhenDetached:W=!1,updatePositionStrategy:z="optimized",onPlaced:M,...B}=e,_=y(b,w),[F,H]=n.useState(null),T=(0,u.s)(t,e=>H(e)),[G,Y]=n.useState(null),$=function(e){let[t,r]=n.useState(void 0);return(0,f.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(G),I=null!==(h=null==$?void 0:$.width)&&void 0!==h?h:0,U=null!==(g=null==$?void 0:$.height)&&void 0!==g?g:0,V="number"==typeof L?L:{top:0,right:0,bottom:0,left:0,...L},X=Array.isArray(D)?D:[D],q=X.length>0,Z={padding:V,boundary:X.filter(S),altBoundary:q},{refs:J,floatingStyles:K,placement:Q,isPositioned:ee,middlewareData:et}=(0,o.we)({strategy:"fixed",placement:x+("center"!==R?"-"+R:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.ll)(...t,{animationFrame:"always"===z})},elements:{reference:_.anchor},middleware:[(0,o.cY)({mainAxis:C+U,alignmentAxis:P}),N&&(0,o.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===k?(0,o.ER)():void 0,...Z}),N&&(0,o.UU)({...Z}),(0,o.Ej)({...Z,apply:e=>{let{elements:t,rects:r,availableWidth:n,availableHeight:o}=e,{width:i,height:l}=r.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(n,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),G&&(0,o.UE)({element:G,padding:A}),j({arrowWidth:I,arrowHeight:U}),W&&(0,o.jD)({strategy:"referenceHidden",...Z})]}),[er,en]=O(Q),eo=(0,c.c)(M);(0,f.N)(()=>{ee&&(null==eo||eo())},[ee,eo]);let ei=null===(r=et.arrow)||void 0===r?void 0:r.x,el=null===(s=et.arrow)||void 0===s?void 0:s.y,ea=(null===(d=et.arrow)||void 0===d?void 0:d.centerOffset)!==0,[es,eu]=n.useState();return(0,f.N)(()=>{F&&eu(window.getComputedStyle(F).zIndex)},[F]),(0,a.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:ee?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:es,"--radix-popper-transform-origin":[null===(p=et.transformOrigin)||void 0===p?void 0:p.x,null===(v=et.transformOrigin)||void 0===v?void 0:v.y].join(" "),...(null===(m=et.hide)||void 0===m?void 0:m.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,a.jsx)(E,{scope:w,placedSide:er,onArrowChange:Y,arrowX:ei,arrowY:el,shouldHideArrow:ea,children:(0,a.jsx)(l.sG.div,{"data-side":er,"data-align":en,...B,ref:T,style:{...B.style,animation:ee?void 0:"none"}})})})});R.displayName=b;var P="PopperArrow",A={top:"bottom",right:"left",bottom:"top",left:"right"},N=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,o=C(P,r),i=A[o.placedSide];return(0,a.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,a.jsx)(s,{...n,ref:t,style:{...n.style,display:"block"}})})});function S(e){return null!==e}N.displayName=P;var j=e=>({name:"transformOrigin",options:e,fn(t){var r,n,o,i,l;let{placement:a,rects:s,middlewareData:u}=t,d=(null===(r=u.arrow)||void 0===r?void 0:r.centerOffset)!==0,c=d?0:e.arrowWidth,f=d?0:e.arrowHeight,[p,v]=O(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(i=null===(n=u.arrow)||void 0===n?void 0:n.x)&&void 0!==i?i:0)+c/2,y=(null!==(l=null===(o=u.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+f/2,g="",w="";return"bottom"===p?(g=d?m:"".concat(h,"px"),w="".concat(-f,"px")):"top"===p?(g=d?m:"".concat(h,"px"),w="".concat(s.floating.height+f,"px")):"right"===p?(g="".concat(-f,"px"),w=d?m:"".concat(y,"px")):"left"===p&&(g="".concat(s.floating.width+f,"px"),w=d?m:"".concat(y,"px")),{data:{x:g,y:w}}}});function O(e){let[t,r="center"]=e.split("-");return[t,r]}var D=g,L=x,k=R,W=N},34378:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(12115),o=r(47650),i=r(63540),l=r(52712),a=r(95155),s=n.forwardRef((e,t)=>{var r,s;let{container:u,...d}=e,[c,f]=n.useState(!1);(0,l.N)(()=>f(!0),[]);let p=u||c&&(null===(s=globalThis)||void 0===s?void 0:null===(r=s.document)||void 0===r?void 0:r.body);return p?o.createPortal((0,a.jsx)(i.sG.div,{...d,ref:t}),p):null});s.displayName="Portal"},39033:(e,t,r)=>{r.d(t,{c:()=>o});var n=r(12115);function o(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(12115),o=r(95155);function i(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,i){let l=n.createContext(i),a=r.length;r=[...r,i];let s=t=>{let{scope:r,children:i,...s}=t,u=r?.[e]?.[a]||l,d=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(u.Provider,{value:d,children:i})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[a]||l,u=n.useContext(s);if(u)return u;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},76589:(e,t,r)=>{r.d(t,{N:()=>c});var n=r(12115),o=r(46081),i=r(6101),l=r(95155),a=n.forwardRef((e,t)=>{let{children:r,...o}=e,i=n.Children.toArray(r),a=i.find(d);if(a){let e=a.props.children,r=i.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(s,{...o,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,l.jsx)(s,{...o,ref:t,children:r})});a.displayName="Slot";var s=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r),l=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(l.ref=t?(0,i.t)(t,e):e),n.cloneElement(r,l)}return n.Children.count(r)>1?n.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function d(e){return n.isValidElement(e)&&e.type===u}function c(e){let t=e+"CollectionProvider",[r,s]=(0,o.A)(t),[u,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,o=n.useRef(null),i=n.useRef(new Map).current;return(0,l.jsx)(u,{scope:t,itemMap:i,collectionRef:o,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=d(f,r),s=(0,i.s)(t,o.collectionRef);return(0,l.jsx)(a,{ref:s,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",h=n.forwardRef((e,t)=>{let{scope:r,children:o,...s}=e,u=n.useRef(null),c=(0,i.s)(t,u),f=d(v,r);return n.useEffect(()=>(f.itemMap.set(u,{ref:u,...s}),()=>void f.itemMap.delete(u))),(0,l.jsx)(a,{[m]:"",ref:c,children:o})});return h.displayName=v,[{Provider:c,Slot:p,ItemSlot:h},function(t){let r=d(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},s]}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},94315:(e,t,r)=>{r.d(t,{jH:()=>i});var n=r(12115);r(95155);var o=n.createContext(void 0);function i(e){let t=n.useContext(o);return e||t||"ltr"}}}]);