"use strict";exports.id=6658,exports.ids=[6658],exports.modules={12048:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(60687);a(43210);var r=a(4780);function n({className:e,type:t,...a}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},21342:(e,t,a)=>{a.d(t,{SQ:()=>d,_2:()=>l,lp:()=>c,mB:()=>u,rI:()=>i,ty:()=>o});var s=a(60687);a(43210);var r=a(12325),n=a(4780);function i({...e}){return(0,s.jsx)(r.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,s.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...e})}function d({className:e,sideOffset:t=4,...a}){return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function l({className:e,inset:t,variant:a="default",...i}){return(0,s.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function c({className:e,inset:t,...a}){return(0,s.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...a})}function u({className:e,...t}){return(0,s.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},29523:(e,t,a)=>{a.d(t,{$:()=>d,r:()=>o});var s=a(60687);a(43210);var r=a(11329),n=a(24224),i=a(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:a,asChild:n=!1,...d}){let l=n?r.Slot:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:a,className:e})),...d})}},32584:(e,t,a)=>{a.d(t,{BK:()=>o,eu:()=>i,q5:()=>d});var s=a(60687);a(43210);var r=a(92951),n=a(4780);function i({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function o({className:e,...t}){return(0,s.jsx)(r._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",e),...t})}function d({className:e,...t}){return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},35950:(e,t,a)=>{a.d(t,{w:()=>i});var s=a(60687);a(43210);var r=a(62369),n=a(4780);function i({className:e,orientation:t="horizontal",decorative:a=!0,...i}){return(0,s.jsx)(r.b,{"data-slot":"separator-root",decorative:a,orientation:t,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...i})}},41784:(e,t,a)=>{a.d(t,{G7:()=>c,Gj:()=>l,L$:()=>m,h_:()=>g,oI:()=>u,uB:()=>d,xL:()=>h});var s=a(60687);a(43210);var r=a(70965),n=a(99270),i=a(4780),o=a(63503);function d({className:e,...t}){return(0,s.jsx)(r.uB,{"data-slot":"command",className:(0,i.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...t})}function l({title:e="Command Palette",description:t="Search for a command to run...",children:a,...r}){return(0,s.jsxs)(o.lG,{...r,children:[(0,s.jsxs)(o.c7,{className:"sr-only",children:[(0,s.jsx)(o.L3,{children:e}),(0,s.jsx)(o.rr,{children:t})]}),(0,s.jsx)(o.Cf,{className:"overflow-hidden p-0",children:(0,s.jsx)(d,{className:"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:a})})]})}function c({className:e,...t}){return(0,s.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,s.jsx)(n.A,{className:"size-4 shrink-0 opacity-50"}),(0,s.jsx)(r.uB.Input,{"data-slot":"command-input",className:(0,i.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...t})]})}function u({className:e,...t}){return(0,s.jsx)(r.uB.List,{"data-slot":"command-list",className:(0,i.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...t})}function h({...e}){return(0,s.jsx)(r.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function m({className:e,...t}){return(0,s.jsx)(r.uB.Group,{"data-slot":"command-group",className:(0,i.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t})}function g({className:e,...t}){return(0,s.jsx)(r.uB.Item,{"data-slot":"command-item",className:(0,i.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}},45285:(e,t,a)=>{a.d(t,{A:()=>o});var s=a(60687),r=a(43210),n=a(16189),i=a(60478);function o({children:e,allowedRoles:t,redirectTo:a="/login"}){let{user:o,userRole:d,loading:l}=(0,i.A)();(0,n.useRouter)();let[c,u]=(0,r.useState)(!1);return l||!c?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):(0,s.jsx)(s.Fragment,{children:e})}a(99557)},63503:(e,t,a)=>{a.d(t,{Cf:()=>c,L3:()=>h,c7:()=>u,lG:()=>o,rr:()=>m});var s=a(60687);a(43210);var r=a(6491),n=a(11860),i=a(4780);function o({...e}){return(0,s.jsx)(r.bL,{"data-slot":"dialog",...e})}function d({...e}){return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...e})}function l({className:e,...t}){return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function c({className:e,children:t,...a}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(l,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...a,children:[t,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function m({className:e,...t}){return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},66327:(e,t,a)=>{a.d(t,{N:()=>ei});var s=a(60687),r=a(43210),n=a(85814),i=a.n(n),o=a(16189),d=a(11329),l=a(24224);function c(){let[e,t]=r.useState(void 0);return r.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}var u=a(4780),h=a(29523),m=a(12048);a(35950);var g=a(6491),f=a(11860);function p({...e}){return(0,s.jsx)(g.bL,{"data-slot":"sheet",...e})}function x({...e}){return(0,s.jsx)(g.ZL,{"data-slot":"sheet-portal",...e})}function b({className:e,...t}){return(0,s.jsx)(g.hJ,{"data-slot":"sheet-overlay",className:(0,u.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function v({className:e,children:t,side:a="right",...r}){return(0,s.jsxs)(x,{children:[(0,s.jsx)(b,{}),(0,s.jsxs)(g.UC,{"data-slot":"sheet-content",className:(0,u.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,(0,s.jsxs)(g.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(f.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function j({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,u.cn)("flex flex-col gap-1.5 p-4",e),...t})}function w({className:e,...t}){return(0,s.jsx)(g.hE,{"data-slot":"sheet-title",className:(0,u.cn)("text-foreground font-semibold",e),...t})}function N({className:e,...t}){return(0,s.jsx)(g.VY,{"data-slot":"sheet-description",className:(0,u.cn)("text-muted-foreground text-sm",e),...t})}a(85726);var y=a(76242);let E=r.createContext(null);function _(){let e=r.useContext(E);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function A({defaultOpen:e=!0,open:t,onOpenChange:a,className:n,style:i,children:o,...d}){let l=c(),[h,m]=r.useState(!1),[g,f]=r.useState(e),p=t??g,x=r.useCallback(e=>{let t="function"==typeof e?e(p):e;a?a(t):f(t),document.cookie=`sidebar_state=${t}; path=/; max-age=604800`},[a,p]),b=r.useCallback(()=>l?m(e=>!e):x(e=>!e),[l,x,m]);r.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),b())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[b]);let v=p?"expanded":"collapsed",j=r.useMemo(()=>({state:v,open:p,setOpen:x,isMobile:l,openMobile:h,setOpenMobile:m,toggleSidebar:b}),[v,p,x,l,h,m,b]);return(0,s.jsx)(E.Provider,{value:j,children:(0,s.jsx)(y.Bc,{delayDuration:0,children:(0,s.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,u.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",n),...d,children:o})})})}function k({side:e="left",variant:t="sidebar",collapsible:a="offcanvas",className:r,children:n,...i}){let{isMobile:o,state:d,openMobile:l,setOpenMobile:c}=_();return"none"===a?(0,s.jsx)("div",{"data-slot":"sidebar",className:(0,u.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...i,children:n}):o?(0,s.jsx)(p,{open:l,onOpenChange:c,...i,children:(0,s.jsxs)(v,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,s.jsxs)(j,{className:"sr-only",children:[(0,s.jsx)(w,{children:"Sidebar"}),(0,s.jsx)(N,{children:"Displays the mobile sidebar."})]}),(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:n})]})}):(0,s.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":d,"data-collapsible":"collapsed"===d?a:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[(0,s.jsx)("div",{"data-slot":"sidebar-gap",className:(0,u.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,s.jsx)("div",{"data-slot":"sidebar-container",className:(0,u.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...i,children:(0,s.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:n})})]})}function C({className:e,onClick:t,...a}){let{toggleSidebar:r}=_();return(0,s.jsxs)(h.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,u.cn)("size-10",e),onClick:e=>{t?.(e),r()},...a,children:[(0,s.jsx)("img",{src:"/assets/icons/sidebar-trigger.svg",alt:"icon",className:"h-9 w-9"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function S({className:e,...t}){let{toggleSidebar:a}=_();return(0,s.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:a,title:"Toggle Sidebar",className:(0,u.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})}function L({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,u.cn)("flex flex-col gap-2 p-2",e),...t})}function I({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,u.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function P({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,u.cn)("relative flex w-full min-w-0 flex-col p-2",e),...t})}function z({className:e,asChild:t=!1,...a}){let r=t?d.Slot:"div";return(0,s.jsx)(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,u.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...a})}function D({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,u.cn)("w-full text-sm",e),...t})}function M({className:e,...t}){return(0,s.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,u.cn)("flex w-full min-w-0 flex-col gap-1",e),...t})}function R({className:e,...t}){return(0,s.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,u.cn)("group/menu-item relative",e),...t})}let T=(0,l.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function U({asChild:e=!1,isActive:t=!1,variant:a="default",size:r="default",tooltip:n,className:i,...o}){let l=e?d.Slot:"button",{isMobile:c,state:h}=_(),m=(0,s.jsx)(l,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:(0,u.cn)(T({variant:a,size:r}),i),...o});return n?("string"==typeof n&&(n={children:n}),(0,s.jsxs)(y.m_,{children:[(0,s.jsx)(y.k$,{asChild:!0,children:m}),(0,s.jsx)(y.ZI,{side:"right",align:"center",hidden:"collapsed"!==h||c,...n})]})):m}var O=a(96834),G=a(99557),B=a(49625);let H=[{title:"MENU",items:[{title:"Dashboard",icon:null,iconPath:"/assets/icons/dashboard.svg",href:"/admin",submenu:[{title:"Overview",href:"/dashboard"}],roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Colleges",icon:null,href:"/admin/college",roles:[G.g.SUPER_ADMIN]},{title:"Add College",icon:null,href:"/admin/add-college",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Add Subjects, Chapters & Topics",icon:null,href:"/admin/add-subjectandtopic",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Question Bank",icon:null,href:"/admin/question-bank",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Add Question",icon:null,href:"/admin/add-question",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]},{title:"Analytics",icon:null,href:"/admin",badge:"NEW",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]},{title:"SUPPORT",items:[{title:"Chat",iconPath:"/assets/icons/chat.svg",icon:null,href:"/dashboard/chat",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Email",iconPath:"/assets/icons/email.svg",icon:null,href:"/dashboard/email",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Invoice",iconPath:"/assets/icons/invoice.svg",icon:null,href:"/dashboard/invoice",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]}],F=[{title:"MENU",items:[{title:"Generate Questions",iconPath:"/assets/icons/dashboard.svg",href:"/teacher",submenu:[{title:"Overview",href:"/dashboard"}],roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Downloaded Papers",iconPath:"/assets/icons/download.svg",href:"/teacher/downloaded-papers",roles:[G.g.TEACHER]},{title:"Settings",iconPath:"",href:"/teacher/settings",roles:[G.g.SUPER_ADMIN]},{title:"Profile",iconPath:"",href:"/teacher/profile",roles:[G.g.SUPER_ADMIN]}]},{title:"SUPPORT",items:[{title:"Chat",iconPath:"/assets/icons/chat.svg",icon:null,href:"/dashboard/chat",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Email",iconPath:"/assets/icons/email.svg",icon:null,href:"/dashboard/email",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Invoice",iconPath:"/assets/icons/invoice.svg",icon:null,href:"/dashboard/invoice",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]}],$=[{title:"MENU",items:[{title:"Dashboard",icon:B.A,href:"/college",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Teacher management",icon:null,href:"/college/teachers-list",roles:[G.g.SUPER_ADMIN]}]},{title:"SUPPORT",items:[{title:"Chat",iconPath:"/assets/icons/chat.svg",icon:null,href:"/dashboard/chat",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Email",iconPath:"/assets/icons/email.svg",icon:null,href:"/dashboard/email",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN,G.g.TEACHER]},{title:"Invoice",iconPath:"/assets/icons/invoice.svg",icon:null,href:"/dashboard/invoice",roles:[G.g.SUPER_ADMIN,G.g.COLLEGE_ADMIN]}]}];function q({role:e}){let t=(0,o.usePathname)(),a=H;switch(e){case G.g.COLLEGE_ADMIN:a=$;break;case G.g.TEACHER:a=F;break;case G.g.SUPER_ADMIN:default:a=H}return(0,s.jsxs)(k,{children:[(0,s.jsx)(L,{className:"p-4",children:(0,s.jsx)(i(),{href:"/dashboard",className:"flex items-center gap-2",children:(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("img",{src:"/assets/logo/medicos-logo.svg",alt:"Logo",className:"h-[70px] w-auto"})})})}),(0,s.jsx)(I,{children:a.map(e=>(0,s.jsxs)(P,{children:[(0,s.jsx)(z,{className:"font-outfit font-normal text-[12px] leading-[20px] uppercase mb-3 text-[#98A2B3]",children:e.title}),(0,s.jsx)(D,{children:(0,s.jsx)(M,{className:"space-y-2",children:e.items.map(e=>{let a=t===e.href||e.submenu&&e.submenu.some(e=>t===e.href);return(0,s.jsx)(R,{children:(0,s.jsx)(U,{asChild:!0,isActive:a,className:(0,u.cn)(a&&"!bg-[#E8F5E8] hover:!bg-[#E8F5E8]"),style:a?{background:"#E8F5E8"}:{},children:(0,s.jsxs)(i(),{href:e.href,className:(0,u.cn)("flex items-center",a&&"bg-[#E8F5E8]"),children:[e.iconPath?(0,s.jsx)("img",{src:e.iconPath,alt:`${e.title} icon`,className:"mr-2 h-5 w-5 object-contain"}):e.icon?(0,s.jsx)(e.icon,{className:`mr-2 h-5 w-5 ${a?"text-[#05603A]":"text-white"}`}):(0,s.jsx)("div",{className:"mr-2 h-5 w-5"}),(0,s.jsx)("span",{className:(0,u.cn)("text-sm font-medium",a?"text-[#05603A]":"text-white"),children:e.title}),e.badge&&(0,s.jsx)(O.E,{variant:"secondary",className:"ml-auto text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",children:e.badge})]})})},e.title)})})})]},e.title))}),(0,s.jsx)(S,{})]})}var Q=a(99270),K=a(78272),V=a(58869),Z=a(84027),W=a(40083),J=a(21342),Y=a(32584),X=a(10022),ee=a(82080),et=a(41312),ea=a(41784);function es({open:e,onOpenChange:t,role:a}){let r=(0,o.useRouter)(),n=e=>{t(!1),e()};return(0,s.jsxs)(ea.Gj,{open:e,onOpenChange:t,children:[(0,s.jsx)(ea.G7,{placeholder:"Type a command or search..."}),(0,s.jsxs)(ea.oI,{children:[(0,s.jsx)(ea.xL,{children:"No results found."}),(0,s.jsxs)(ea.L$,{heading:"Navigation",children:[a===G.g.TEACHER&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/teacher")),children:[(0,s.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Generate Questions"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/teacher/downloaded-papers")),children:[(0,s.jsx)(X.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Downloaded Papers"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/teacher/settings")),children:[(0,s.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/teacher/profile")),children:[(0,s.jsx)(V.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]})]}),a===G.g.SUPER_ADMIN&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/admin")),children:[(0,s.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Dashboard"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/admin/college")),children:[(0,s.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Colleges"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/admin/question-bank")),children:[(0,s.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Question Bank"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/admin/add-question")),children:[(0,s.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Add Question"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/admin/add-college")),children:[(0,s.jsx)(ee.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Add College"})]})]}),a===G.g.COLLEGE_ADMIN&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/college")),children:[(0,s.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Dashboard"})]}),(0,s.jsxs)(ea.h_,{onSelect:()=>n(()=>r.push("/college/teachers-list")),children:[(0,s.jsx)(et.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Teachers"})]})]})]})]})]})}var er=a(60478);function en(){var e;let[t,a]=(0,r.useState)(!1),[n,i]=(0,r.useState)(3),[d,l]=(0,r.useState)(null),{logout:c}=(0,er.A)(),u=(0,o.useRouter)();return(0,s.jsxs)("header",{className:"sticky top-0 z-30 flex h-16 w-full items-center justify-between border-b bg-background px-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(C,{className:"mr-2"}),(0,s.jsxs)("div",{className:"relative hidden md:block",children:[(0,s.jsx)(Q.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(m.p,{type:"search",placeholder:"Search or type command...",className:"w-[300px] pl-8 md:w-[300px] lg:w-[400px]",onClick:()=>a(!0),style:{fontFamily:"Typeface/family/family",fontWeight:400,fontSize:"Typeface/size/Text sm",lineHeight:"Typeface/line height/Text sm",letterSpacing:"0%"}}),(0,s.jsxs)("kbd",{className:"pointer-events-none absolute right-2 top-2 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-xs font-medium opacity-100 sm:flex",children:[(0,s.jsx)("span",{className:"text-xs",children:"⌘"}),"K"]})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)(J.rI,{children:[(0,s.jsx)(J.ty,{asChild:!0,children:(0,s.jsxs)(h.$,{variant:"ghost",className:"flex items-center gap-2",children:[(0,s.jsxs)(Y.eu,{className:"h-8 w-8",children:[(0,s.jsx)(Y.BK,{src:"/placeholder.svg?height=32&width=32",alt:"User"}),(0,s.jsx)(Y.q5,{children:d?.displayName&&(e=d.displayName)?e.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U"})]}),(0,s.jsx)("div",{className:"hidden md:block text-left",style:{fontFamily:"Typeface/family/family",fontWeight:500,fontSize:"Typeface/size/Text sm",lineHeight:"Typeface/line height/Text sm",letterSpacing:"0%"},children:(0,s.jsx)("p",{className:"text-sm font-medium",children:d?.displayName||"Loading..."})}),(0,s.jsx)(K.A,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,s.jsxs)(J.SQ,{align:"end",className:"w-56",children:[(0,s.jsx)(J.lp,{children:"My Account"}),d?.role==="teacher"&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(J.mB,{}),(0,s.jsxs)(J._2,{onClick:()=>u.push("/profile"),children:[(0,s.jsx)(V.A,{className:"mr-2 h-4 w-4"}),"Profile"]}),(0,s.jsxs)(J._2,{onClick:()=>u.push("/settings"),children:[(0,s.jsx)(Z.A,{className:"mr-2 h-4 w-4"}),"Settings"]}),(0,s.jsx)(J.mB,{})]}),(0,s.jsxs)(J._2,{onClick:async()=>{await c(),u.push("/login")},children:[(0,s.jsx)(W.A,{className:"mr-2 h-4 w-4"}),"Log out"]})]})]})}),t&&(0,s.jsx)(es,{open:t,onOpenChange:a,role:d?.role||""})]})}function ei({children:e,role:t}){let a=c(),[n,i]=(0,r.useState)(!a);return(0,s.jsx)(A,{defaultOpen:!a,children:(0,s.jsxs)("div",{className:"flex min-h-screen w-full bg-background",children:[(0,s.jsx)(q,{role:t}),(0,s.jsxs)("div",{className:"flex flex-1 flex-col w-full",children:[(0,s.jsx)(en,{}),(0,s.jsx)("main",{className:"flex-1 p-6",children:e})]})]})})}a(99802)},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var s=a(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},76242:(e,t,a)=>{a.d(t,{Bc:()=>i,ZI:()=>l,k$:()=>d,m_:()=>o});var s=a(60687);a(43210);var r=a(58730),n=a(4780);function i({delayDuration:e=0,...t}){return(0,s.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function o({...e}){return(0,s.jsx)(i,{children:(0,s.jsx)(r.bL,{"data-slot":"tooltip",...e})})}function d({...e}){return(0,s.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e})}function l({className:e,sideOffset:t=0,children:a,...i}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...i,children:[a,(0,s.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},85726:(e,t,a)=>{a.d(t,{E:()=>n});var s=a(60687),r=a(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"skeleton",className:(0,r.cn)("bg-accent animate-pulse rounded-md",e),...t})}},96834:(e,t,a)=>{a.d(t,{E:()=>d});var s=a(60687);a(43210);var r=a(11329),n=a(24224),i=a(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:a=!1,...n}){let d=a?r.Slot:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},99557:(e,t,a)=>{a.d(t,{g:()=>s});var s=function(e){return e.SUPER_ADMIN="superAdmin",e.COLLEGE_ADMIN="collegeAdmin",e.TEACHER="teacher",e}({})},99802:(e,t,a)=>{a.d(t,{p:()=>r});let s=a(51060).A.create({baseURL:"http://localhost:3000/api",headers:{"Content-Type":"application/json"}});async function r(){try{let e=localStorage.getItem("backendToken");console.log("Token being sent:",e);let t=await fetch("http://localhost:3000/api/users/me",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json",...e?{Authorization:`Bearer ${e}`}:{}}});if(!t.ok)return console.error("Failed to fetch current user:",t.statusText),null;return await t.json()}catch(e){return console.error("Error fetching current user:",e),null}}s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)))}};