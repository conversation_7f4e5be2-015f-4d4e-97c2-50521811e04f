(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7188],{12379:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(95155),s=a(12115),n=a(6874),o=a.n(n),l=a(59434),i=a(13052),c=a(5623);let d=e=>{let{items:t,maxItems:a=4,className:n}=e,d=s.useMemo(()=>t.length<=a?t:[t[0],{label:"..."},...t.slice(-2)],[t,a]);return(0,r.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,l.cn)("flex items-center text-sm",n),children:(0,r.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let a=t===d.length-1;return(0,r.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,r.jsx)(i.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,r.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}):a?(0,r.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,r.jsx)(o(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,r.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>l});var r=a(95155);a(12115);var s=a(66634),n=a(74466),o=a(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:n,asChild:i=!1,...c}=e,d=i?s.Slot:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:a,size:n,className:t})),...c})}},44838:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>i,_2:()=>c,lp:()=>d,mB:()=>u,rI:()=>o,ty:()=>l});var r=a(95155);a(12115);var s=a(76215),n=a(59434);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dropdown-menu",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t})}function i(e){let{className:t,sideOffset:a=4,...o}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...o})})}function c(e){let{className:t,inset:a,variant:o="default",...l}=e;return(0,r.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":o,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function d(e){let{className:t,inset:a,...o}=e;return(0,r.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...o})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},49730:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>A});var r=a(95155),s=a(12115),n=a(12379),o=a(30285),l=a(84616),i=a(44020),c=a(13717),d=a(62525),u=a(91788),h=a(66766),g=a(35695),m=a(44838),x=a(90010),f=a(74125),p=a(88262);function v(e){let{university:t,onDelete:a}=e,{id:n,name:o,location:l,status:v,logo:y,contactDetails:j,downloadedQuestions:b}=t,[w,N]=(0,s.useState)(!1),[k,E]=(0,s.useState)(!1),A=(0,g.useRouter)(),S=y&&(y.startsWith("http://")||y.startsWith("https://")),P=S?"/placeholder.svg":y||"/placeholder.svg",C=async()=>{try{E(!0),await (0,f.pZ)(n),(0,p.o)({title:"Success",description:"College deleted successfully"}),a&&a()}catch(e){console.error("Error deleting college:",e),(0,p.o)({title:"Error",description:e.message||"Failed to delete college",variant:"destructive"})}finally{E(!1),N(!1)}};return(0,r.jsxs)("div",{className:"rounded-lg border border-gray-200 bg-white overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{className:"text-[13px] font-medium leading-[16px] text-[#98A2B3]",children:["Status :",(0,r.jsx)("span",{className:"ml-1 ".concat("Active"===v?"text-[#039855]":"text-[#EF4444]"),children:v})]})}),(0,r.jsxs)(m.rI,{children:[(0,r.jsx)(m.ty,{asChild:!0,children:(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)(i.A,{size:20})})}),(0,r.jsxs)(m.SQ,{align:"end",children:[(0,r.jsxs)(m._2,{onClick:()=>{A.push("/admin/edit-college/".concat(n))},className:"cursor-pointer",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Edit"]}),(0,r.jsxs)(m._2,{onClick:()=>N(!0),className:"cursor-pointer text-red-600 focus:text-red-600",children:[(0,r.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Delete"]})]})]})]}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"bg-gray-100 p-3 rounded-md w-20 h-20 flex items-center justify-center",children:S?(0,r.jsx)("img",{src:y,alt:"".concat(o," logo"),className:"object-contain w-[60px] h-[60px]"}):(0,r.jsx)(h.default,{src:P,alt:"".concat(o," logo"),width:60,height:60,className:"object-contain"})})}),(0,r.jsxs)("div",{className:"text-center space-y-1",children:[(0,r.jsx)("h3",{className:"font-[600] text-[17px] leading-[24px] tracking-[-0.5%] text-[#333333]",children:o}),(0,r.jsxs)("p",{className:"font-[400] text-[15px] leading-[20px] tracking-[-0.5%] text-gray-600",children:[l.city,", ",l.state]}),(0,r.jsxs)("p",{className:"text-[#98A2B3] text-[13px] font-[500] leading-[16px]",children:["Contact details : ",j]})]}),(0,r.jsx)("div",{className:"border-t pt-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)(u.A,{size:16,className:"text-gray-500"}),(0,r.jsx)("span",{className:"font-[600] text-[12px] leading-[100%] tracking-[0.5%]",children:"Downloaded questions"}),(0,r.jsxs)("span",{className:"bg-[#EF4444] text-white text-[12px] font-medium px-2 py-0.5 rounded-full",children:[b.current,"/",b.total]})]})})]}),(0,r.jsx)(x.Lt,{open:w,onOpenChange:N,children:(0,r.jsxs)(x.EO,{children:[(0,r.jsxs)(x.wd,{children:[(0,r.jsx)(x.r7,{children:"Are you sure you want to delete this college?"}),(0,r.jsx)(x.$v,{children:"This action cannot be undone. This will permanently delete the college and all associated data."})]}),(0,r.jsxs)(x.ck,{children:[(0,r.jsx)(x.Zr,{children:"Cancel"}),(0,r.jsx)(x.Rx,{onClick:C,className:"bg-red-600 hover:bg-red-700",disabled:k,children:k?"Deleting...":"Delete"})]})]})})]})}var y=a(42355),j=a(13052);function b(e){let{currentPage:t,totalPages:a,onPageChange:s}=e,n=[];for(let e=1;e<=a;e++)n.push(e);return(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsxs)("button",{onClick:()=>t>1&&s(t-1),disabled:1===t,className:"flex items-center px-4 py-2 text-[15px] font-[500] text-[#667085] disabled:opacity-50",children:[(0,r.jsx)(y.A,{className:"mr-1 h-4 w-4"}),"PREVIOUS"]}),n.map(e=>(0,r.jsx)("button",{onClick:()=>s(e),className:"w-8 h-8 flex items-center justify-center rounded-md text-[15px] font-medium\n            ".concat(t===e?"bg-[#6B7280] text-white":"text-[#6B7280] hover:bg-gray-100"),children:e},e)),(0,r.jsxs)("button",{onClick:()=>t<a&&s(t+1),disabled:t===a,className:"flex items-center px-4 py-2 text-[15px] font-[500] text-[#667085] disabled:opacity-50",children:["NEXT",(0,r.jsx)(j.A,{className:"ml-1 h-4 w-4"})]})]})}function w(e){let{universities:t,itemsPerPage:a,onCollegeDeleted:n}=e,[o,l]=(0,s.useState)(1),i=o*a,c=t.slice(i-a,i),d=Math.ceil(t.length/a);return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:c.map((e,t)=>(0,r.jsx)(v,{university:e,onDelete:n},t))}),(0,r.jsx)("div",{className:"flex justify-center mt-8",children:(0,r.jsx)(b,{currentPage:o,totalPages:d,onPageChange:e=>{l(e)}})})]})}var N=a(6874),k=a.n(N),E=a(55097);let A=()=>{let[e,t]=(0,s.useState)([]),[a,i]=(0,s.useState)(!0),c=async()=>{try{i(!0);let e=await (0,f.JY)();if((0,E.cY)(e)){let a=e.data.map(e=>({id:e._id,name:e.name,location:{city:e.city||"",state:e.state||""},status:e.status||"Active",logo:e.logoUrl||"/placeholder.svg?height=60&width=60",contactDetails:e.contactPhone||"",downloadedQuestions:{current:0,total:100}}));t(a)}}catch(e){console.error("Unexpected error fetching colleges:",e),(0,p.o)({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{i(!1)}};return(0,s.useEffect)(()=>{c()},[]),(0,r.jsxs)("main",{className:"min-h-screen bg-gray-50 py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-semibold text-black",children:"College Management List"}),(0,r.jsx)(n.A,{items:[{label:"Home",href:"/"},{label:"...",href:"#"},{label:"College Management"}],className:"text-sm mt-1"})]}),(0,r.jsx)(k(),{href:"/admin/add-college",children:(0,r.jsxs)(o.$,{className:"bg-[#05603A] hover:bg-[#04502F] text-white",children:["Add college",(0,r.jsx)(l.A,{className:"w-4 h-4 ml-2"})]})})]}),(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:a?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-[#05603A]"})}):(0,r.jsx)(w,{universities:e,itemsPerPage:8,onCollegeDeleted:()=>{c()}})})]})}},55097:(e,t,a)=>{"use strict";a.d(t,{$y:()=>n,cY:()=>o,hS:()=>s});var r=a(56671);function s(e){var t,a,s,n;let o,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=l;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(s=e.data)||void 0===s?void 0:s.message)&&(c=e.data.message),(null==e?void 0:e.status)?o=e.status:(null==e?void 0:null===(n=e.response)||void 0===n?void 0:n.status)&&(o=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===o?c="Server error. Please try again later.":503===o&&(c="Service temporarily unavailable. Please try again later.")),i&&r.oR.error(c),{success:!1,error:c,statusCode:o}}function n(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&r.oR.success(a),{success:!0,data:e}}function o(e){return!0===e.success}},58421:(e,t,a)=>{Promise.resolve().then(a.bind(a,49730))},59434:(e,t,a)=>{"use strict";a.d(t,{b:()=>o,cn:()=>n});var r=a(52596),s=a(17307);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function o(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},74125:(e,t,a)=>{"use strict";a.d(t,{$T:()=>u,JY:()=>n,M0:()=>s,N0:()=>i,mS:()=>c,mi:()=>d,pZ:()=>o,qk:()=>l});var r=a(55097);async function s(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to create college. Please try again.")}let s=await a.json();return(0,r.$y)(s,!0,"College created successfully!")}catch(e){return console.error("Error creating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to create college. Please try again.","Failed to create college. Please try again.")}}async function n(){let e=localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let t=await fetch("".concat("http://localhost:3000/api","/colleges"),{method:"GET",headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(t.status),"Failed to load colleges. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching colleges:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load colleges. Please try again.","Failed to load colleges. Please try again.")}}async function o(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to delete college. Please try again.")}let s=await a.json();return(0,r.$y)(s,!0,"College deleted successfully!")}catch(e){return console.error("Error deleting college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to delete college. Please try again.","Failed to delete college. Please try again.")}}async function l(e){let t=localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let a=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to load college. Please try again.")}let s=await a.json();return(0,r.$y)(s)}catch(e){return console.error("Error fetching college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to load college. Please try again.","Failed to load college. Please try again.")}}async function i(e,t){let a=localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication required","Authentication required. Please log in again.");try{let s=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!s.ok){let e=await s.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(s.status),"Failed to update college. Please try again.")}let n=await s.json();return(0,r.$y)(n,!0,"College updated successfully!")}catch(e){return console.error("Error updating college:",e),(0,r.hS)(e instanceof Error?e.message:"Failed to update college. Please try again.","Failed to update college. Please try again.")}}async function c(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let a=await fetch("".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/summary"),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(a.status))}return await a.json()}catch(e){throw console.error("Error fetching college:",e),e}}async function d(e,t,a){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let s="".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/question-papers?startDate=").concat(encodeURIComponent(t),"&endDate=").concat(encodeURIComponent(a)),n=await fetch(s,{method:"GET",headers:{Authorization:"Bearer ".concat(r),"Content-Type":"application/json"}});if(!n.ok){let e=await n.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(n.status))}return await n.json()}catch(e){throw console.error("Error fetching question paper stats:",e),e}}async function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=localStorage.getItem("backendToken");if(!a)throw Error("Authentication required");try{let r="".concat("http://localhost:3000/api","/analytics/college/").concat(e,"/top-teachers?limit=").concat(t),s=await fetch(r,{method:"GET",headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"}});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(s.status))}return await s.json()}catch(e){throw console.error("Error fetching top teachers:",e),e}}},88262:(e,t,a)=>{"use strict";a.d(t,{d:()=>c,o:()=>d});var r=a(12115);new EventTarget;let s=[],n=[];function o(){n.forEach(e=>e([...s]))}function l(e){let t=e.id||Math.random().toString(36).substring(2,9),a={...e,id:t};return s=[...s,a],o(),setTimeout(()=>{i(t)},5e3),t}function i(e){s=s.filter(t=>t.id!==e),o()}function c(){let[e,t]=r.useState(s);return r.useEffect(()=>(n.push(t),t([...s]),()=>{n=n.filter(e=>e!==t)}),[]),{toast:e=>l(e),dismiss:e=>{e?i(e):s.forEach(e=>e.id&&i(e.id))},toasts:e}}let d=e=>l(e)},90010:(e,t,a)=>{"use strict";a.d(t,{$v:()=>m,EO:()=>d,Lt:()=>l,Rx:()=>x,Zr:()=>f,ck:()=>h,r7:()=>g,wd:()=>u});var r=a(95155);a(12115);var s=a(35563),n=a(59434),o=a(30285);function l(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"alert-dialog",...t})}function i(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"alert-dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"alert-dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsxs)(i,{children:[(0,r.jsx)(c,{}),(0,r.jsx)(s.UC,{"data-slot":"alert-dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"alert-dialog-title",className:(0,n.cn)("text-lg font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"alert-dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.rc,{className:(0,n.cn)((0,o.r)(),t),...a})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.ZD,{className:(0,n.cn)((0,o.r)({variant:"outline"}),t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6874,6671,1141,2571,6457,5631,1165,8661,8592,8441,1684,7358],()=>t(58421)),_N_E=e.O()}]);