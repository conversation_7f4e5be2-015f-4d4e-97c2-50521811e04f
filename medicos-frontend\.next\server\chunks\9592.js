exports.id=9592,exports.ids=[9592],exports.modules={43:(e,t,n)=>{"use strict";n.d(t,{jH:()=>o});var a=n(43210);n(60687);var i=a.createContext(void 0);function o(e){let t=a.useContext(i);return e||t||"ltr"}},1359:(e,t,n)=>{"use strict";n.d(t,{Oh:()=>o});var a=n(43210),i=0;function o(){a.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??r()),document.body.insertAdjacentElement("beforeend",e[1]??r()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function r(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},3361:e=>{"use strict";e.exports=Object},3416:(e,t,n)=>{"use strict";n.d(t,{sG:()=>u,hO:()=>d});var a=n(43210),i=n(51215),o=n(98599),r=n(60687),s=a.forwardRef((e,t)=>{let{children:n,...i}=e,o=a.Children.toArray(n),s=o.find(p);if(s){let e=s.props.children,n=o.map(t=>t!==s?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,r.jsx)(c,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,r.jsx)(c,{...i,ref:t,children:n})});s.displayName="Slot";var c=a.forwardRef((e,t)=>{let{children:n,...i}=e;if(a.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),r=function(e,t){let n={...t};for(let a in t){let i=e[a],o=t[a];/^on[A-Z]/.test(a)?i&&o?n[a]=(...e)=>{o(...e),i(...e)}:i&&(n[a]=i):"style"===a?n[a]={...i,...o}:"className"===a&&(n[a]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==a.Fragment&&(r.ref=t?(0,o.t)(t,e):e),a.cloneElement(n,r)}return a.Children.count(n)>1?a.Children.only(null):null});c.displayName="SlotClone";var l=({children:e})=>(0,r.jsx)(r.Fragment,{children:e});function p(e){return a.isValidElement(e)&&e.type===l}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=a.forwardRef((e,n)=>{let{asChild:a,...i}=e,o=a?s:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,r.jsx)(o,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function d(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},4503:(e,t,n)=>{"use strict";n.d(t,{BN:()=>f,ER:()=>x,Ej:()=>h,UE:()=>g,UU:()=>v,cY:()=>m,jD:()=>b,we:()=>u});var a=n(25605),i=n(43210),o=n(51215),r="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function s(e,t){let n,a,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(a=n;0!=a--;)if(!s(e[a],t[a]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(a=n;0!=a--;)if(!({}).hasOwnProperty.call(t,i[a]))return!1;for(a=n;0!=a--;){let n=i[a];if(("_owner"!==n||!e.$$typeof)&&!s(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function c(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function l(e,t){let n=c(e);return Math.round(t*n)/n}function p(e){let t=i.useRef(e);return r(()=>{t.current=e}),t}function u(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:u=[],platform:d,elements:{reference:m,floating:f}={},transform:x=!0,whileElementsMounted:v,open:h}=e,[b,g]=i.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[y,w]=i.useState(u);s(y,u)||w(u);let[E,k]=i.useState(null),[j,R]=i.useState(null),C=i.useCallback(e=>{e!==O.current&&(O.current=e,k(e))},[]),S=i.useCallback(e=>{e!==T.current&&(T.current=e,R(e))},[]),A=m||E,_=f||j,O=i.useRef(null),T=i.useRef(null),P=i.useRef(b),L=null!=v,N=p(v),F=p(d),D=p(h),M=i.useCallback(()=>{if(!O.current||!T.current)return;let e={placement:t,strategy:n,middleware:y};F.current&&(e.platform=F.current),(0,a.rD)(O.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==D.current};I.current&&!s(P.current,t)&&(P.current=t,o.flushSync(()=>{g(t)}))})},[y,t,n,F,D]);r(()=>{!1===h&&P.current.isPositioned&&(P.current.isPositioned=!1,g(e=>({...e,isPositioned:!1})))},[h]);let I=i.useRef(!1);r(()=>(I.current=!0,()=>{I.current=!1}),[]),r(()=>{if(A&&(O.current=A),_&&(T.current=_),A&&_){if(N.current)return N.current(A,_,M);M()}},[A,_,M,N,L]);let B=i.useMemo(()=>({reference:O,floating:T,setReference:C,setFloating:S}),[C,S]),z=i.useMemo(()=>({reference:A,floating:_}),[A,_]),U=i.useMemo(()=>{let e={position:n,left:0,top:0};if(!z.floating)return e;let t=l(z.floating,b.x),a=l(z.floating,b.y);return x?{...e,transform:"translate("+t+"px, "+a+"px)",...c(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:a}},[n,x,z.floating,b.x,b.y]);return i.useMemo(()=>({...b,update:M,refs:B,elements:z,floatingStyles:U}),[b,M,B,z,U])}let d=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:i}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,a.UE)({element:n.current,padding:i}).fn(t):{}:n?(0,a.UE)({element:n,padding:i}).fn(t):{}}}),m=(e,t)=>({...(0,a.cY)(e),options:[e,t]}),f=(e,t)=>({...(0,a.BN)(e),options:[e,t]}),x=(e,t)=>({...(0,a.ER)(e),options:[e,t]}),v=(e,t)=>({...(0,a.UU)(e),options:[e,t]}),h=(e,t)=>({...(0,a.Ej)(e),options:[e,t]}),b=(e,t)=>({...(0,a.jD)(e),options:[e,t]}),g=(e,t)=>({...d(e),options:[e,t]})},6491:(e,t,n)=>{"use strict";n.d(t,{bm:()=>eC,UC:()=>ek,VY:()=>eR,hJ:()=>eE,ZL:()=>ew,bL:()=>eg,hE:()=>ej,l9:()=>ey,G$:()=>ex,Hs:()=>q});var a,i=n(43210),o=n.t(i,2);function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(a){if(e?.(a),!1===n||!a.defaultPrevented)return t?.(a)}}function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function c(...e){return i.useCallback(function(...e){return t=>{let n=!1,a=e.map(e=>{let a=s(e,t);return n||"function"!=typeof a||(n=!0),a});if(n)return()=>{for(let t=0;t<a.length;t++){let n=a[t];"function"==typeof n?n():s(e[t],null)}}}}(...e),e)}var l=n(60687),p=globalThis?.document?i.useLayoutEffect:()=>{},u=o[" useId ".trim().toString()]||(()=>void 0),d=0;function m(e){let[t,n]=i.useState(u());return p(()=>{e||n(e=>e??String(d++))},[e]),e||(t?`radix-${t}`:"")}var f=o[" useInsertionEffect ".trim().toString()]||p,x=(Symbol("RADIX:SYNC_STATE"),n(51215)),v=n(11329),h=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,v.TL)(`Primitive.${t}`),a=i.forwardRef((e,a)=>{let{asChild:i,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i?n:t,{...o,ref:a})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function b(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var g="dismissableLayer.update",y=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:p,onInteractOutside:u,onDismiss:d,...m}=e,f=i.useContext(y),[x,v]=i.useState(null),w=x?.ownerDocument??globalThis?.document,[,j]=i.useState({}),R=c(t,e=>v(e)),C=Array.from(f.layers),[S]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),A=C.indexOf(S),_=x?C.indexOf(x):-1,O=f.layersWithOutsidePointerEventsDisabled.size>0,T=_>=A,P=function(e,t=globalThis?.document){let n=b(e),a=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let a=function(){k("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);a.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...f.branches].some(e=>e.contains(t));!T||n||(s?.(e),u?.(e),e.defaultPrevented||d?.())},w),L=function(e,t=globalThis?.document){let n=b(e),a=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!a.current&&k("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...f.branches].some(e=>e.contains(t))||(p?.(e),u?.(e),e.defaultPrevented||d?.())},w);return function(e,t=globalThis?.document){let n=b(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{_===f.layers.size-1&&(o?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},w),i.useEffect(()=>{if(x)return n&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(a=w.body.style.pointerEvents,w.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(x)),f.layers.add(x),E(),()=>{n&&1===f.layersWithOutsidePointerEventsDisabled.size&&(w.body.style.pointerEvents=a)}},[x,w,n,f]),i.useEffect(()=>()=>{x&&(f.layers.delete(x),f.layersWithOutsidePointerEventsDisabled.delete(x),E())},[x,f]),i.useEffect(()=>{let e=()=>j({});return document.addEventListener(g,e),()=>document.removeEventListener(g,e)},[]),(0,l.jsx)(h.div,{...m,ref:R,style:{pointerEvents:O?T?"auto":"none":void 0,...e.style},onFocusCapture:r(e.onFocusCapture,L.onFocusCapture),onBlurCapture:r(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:r(e.onPointerDownCapture,P.onPointerDownCapture)})});function E(){let e=new CustomEvent(g);document.dispatchEvent(e)}function k(e,t,n,{discrete:a}){let i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});if(t&&i.addEventListener(e,t,{once:!0}),a)i&&x.flushSync(()=>i.dispatchEvent(o));else i.dispatchEvent(o)}w.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(y),a=i.useRef(null),o=c(t,a);return i.useEffect(()=>{let e=a.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(h.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var j="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",C={bubbles:!1,cancelable:!0},S=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:a=!1,onMountAutoFocus:o,onUnmountAutoFocus:r,...s}=e,[p,u]=i.useState(null),d=b(o),m=b(r),f=i.useRef(null),x=c(t,e=>u(e)),v=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(a){let e=function(e){if(v.paused||!p)return;let t=e.target;p.contains(t)?f.current=t:O(f.current,{select:!0})},t=function(e){if(v.paused||!p)return;let t=e.relatedTarget;null===t||p.contains(t)||O(f.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&O(p)});return p&&n.observe(p,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[a,p,v.paused]),i.useEffect(()=>{if(p){T.add(v);let e=document.activeElement;if(!p.contains(e)){let t=new CustomEvent(j,C);p.addEventListener(j,d),p.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let a of e)if(O(a,{select:t}),document.activeElement!==n)return}(A(p).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&O(p))}return()=>{p.removeEventListener(j,d),setTimeout(()=>{let t=new CustomEvent(R,C);p.addEventListener(R,m),p.dispatchEvent(t),t.defaultPrevented||O(e??document.body,{select:!0}),p.removeEventListener(R,m),T.remove(v)},0)}}},[p,d,m,v]);let g=i.useCallback(e=>{if(!n&&!a||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[a,o]=function(e){let t=A(e);return[_(t,e),_(t.reverse(),e)]}(t);a&&o?e.shiftKey||i!==o?e.shiftKey&&i===a&&(e.preventDefault(),n&&O(o,{select:!0})):(e.preventDefault(),n&&O(a,{select:!0})):i===t&&e.preventDefault()}},[n,a,v.paused]);return(0,l.jsx)(h.div,{tabIndex:-1,...s,ref:x,onKeyDown:g})});function A(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function _(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function O(e,{select:t=!1}={}){if(e&&e.focus){var n;let a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}S.displayName="FocusScope";var T=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=P(e,t)).unshift(t)},remove(t){e=P(e,t),e[0]?.resume()}}}();function P(e,t){let n=[...e],a=n.indexOf(t);return -1!==a&&n.splice(a,1),n}var L=i.forwardRef((e,t)=>{let{container:n,...a}=e,[o,r]=i.useState(!1);p(()=>r(!0),[]);let s=n||o&&globalThis?.document?.body;return s?x.createPortal((0,l.jsx)(h.div,{...a,ref:t}),s):null});L.displayName="Portal";var N=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[a,o]=i.useState(),r=i.useRef(null),s=i.useRef(e),c=i.useRef("none"),[l,u]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>n[e][t]??e,t));return i.useEffect(()=>{let e=F(r.current);c.current="mounted"===l?e:"none"},[l]),p(()=>{let t=r.current,n=s.current;if(n!==e){let a=c.current,i=F(t);e?u("MOUNT"):"none"===i||t?.display==="none"?u("UNMOUNT"):n&&a!==i?u("ANIMATION_OUT"):u("UNMOUNT"),s.current=e}},[e,u]),p(()=>{if(a){let e;let t=a.ownerDocument.defaultView??window,n=n=>{let i=F(r.current).includes(n.animationName);if(n.target===a&&i&&(u("ANIMATION_END"),!s.current)){let n=a.style.animationFillMode;a.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=n)})}},i=e=>{e.target===a&&(c.current=F(r.current))};return a.addEventListener("animationstart",i),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{t.clearTimeout(e),a.removeEventListener("animationstart",i),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}u("ANIMATION_END")},[a,u]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:i.useCallback(e=>{r.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:a.isPresent}):i.Children.only(n),r=c(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||a.isPresent?i.cloneElement(o,{ref:r}):null};function F(e){return e?.animationName||"none"}N.displayName="Presence";var D=0;function M(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var I=n(11490),B=n(63376),z="Dialog",[U,q]=function(e,t=[]){let n=[],a=()=>{let t=n.map(e=>i.createContext(e));return function(n){let a=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:a}}),[n,a])}};return a.scopeName=e,[function(t,a){let o=i.createContext(a),r=n.length;n=[...n,a];let s=t=>{let{scope:n,children:a,...s}=t,c=n?.[e]?.[r]||o,p=i.useMemo(()=>s,Object.values(s));return(0,l.jsx)(c.Provider,{value:p,children:a})};return s.displayName=t+"Provider",[s,function(n,s){let c=s?.[e]?.[r]||o,l=i.useContext(c);if(l)return l;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=n.reduce((t,{useScope:n,scopeName:a})=>{let i=n(e)[`__scope${a}`];return{...t,...i}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}(a,...t)]}(z),[W,H]=U(z),$=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:r,modal:s=!0}=e,c=i.useRef(null),p=i.useRef(null),[u,d]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:a}){let[o,r,s]=function({defaultProp:e,onChange:t}){let[n,a]=i.useState(e),o=i.useRef(n),r=i.useRef(t);return f(()=>{r.current=t},[t]),i.useEffect(()=>{o.current!==n&&(r.current?.(n),o.current=n)},[n,o]),[n,a,r]}({defaultProp:t,onChange:n}),c=void 0!==e,l=c?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${a} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,a])}return[l,i.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else r(t)},[c,e,r,s])]}({prop:a,defaultProp:o??!1,onChange:r,caller:z});return(0,l.jsx)(W,{scope:t,triggerRef:c,contentRef:p,contentId:m(),titleId:m(),descriptionId:m(),open:u,onOpenChange:d,onOpenToggle:i.useCallback(()=>d(e=>!e),[d]),modal:s,children:n})};$.displayName=z;var G="DialogTrigger",V=i.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,i=H(G,n),o=c(t,i.triggerRef);return(0,l.jsx)(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":em(i.open),...a,ref:o,onClick:r(e.onClick,i.onOpenToggle)})});V.displayName=G;var K="DialogPortal",[J,Y]=U(K,{forceMount:void 0}),X=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,r=H(K,t);return(0,l.jsx)(J,{scope:t,forceMount:n,children:i.Children.map(a,e=>(0,l.jsx)(N,{present:n||r.open,children:(0,l.jsx)(L,{asChild:!0,container:o,children:e})}))})};X.displayName=K;var Z="DialogOverlay",Q=i.forwardRef((e,t)=>{let n=Y(Z,e.__scopeDialog),{forceMount:a=n.forceMount,...i}=e,o=H(Z,e.__scopeDialog);return o.modal?(0,l.jsx)(N,{present:a||o.open,children:(0,l.jsx)(et,{...i,ref:t})}):null});Q.displayName=Z;var ee=(0,v.TL)("DialogOverlay.RemoveScroll"),et=i.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,i=H(Z,n);return(0,l.jsx)(I.A,{as:ee,allowPinchZoom:!0,shards:[i.contentRef],children:(0,l.jsx)(h.div,{"data-state":em(i.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),en="DialogContent",ea=i.forwardRef((e,t)=>{let n=Y(en,e.__scopeDialog),{forceMount:a=n.forceMount,...i}=e,o=H(en,e.__scopeDialog);return(0,l.jsx)(N,{present:a||o.open,children:o.modal?(0,l.jsx)(ei,{...i,ref:t}):(0,l.jsx)(eo,{...i,ref:t})})});ea.displayName=en;var ei=i.forwardRef((e,t)=>{let n=H(en,e.__scopeDialog),a=i.useRef(null),o=c(t,n.contentRef,a);return i.useEffect(()=>{let e=a.current;if(e)return(0,B.Eq)(e)},[]),(0,l.jsx)(er,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:r(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:r(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:r(e.onFocusOutside,e=>e.preventDefault())})}),eo=i.forwardRef((e,t)=>{let n=H(en,e.__scopeDialog),a=i.useRef(!1),o=i.useRef(!1);return(0,l.jsx)(er,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||n.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let i=t.target;n.triggerRef.current?.contains(i)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),er=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:r,...s}=e,p=H(en,n),u=i.useRef(null),d=c(t,u);return i.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??M()),document.body.insertAdjacentElement("beforeend",e[1]??M()),D++,()=>{1===D&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),D--}},[]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(S,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:r,children:(0,l.jsx)(w,{role:"dialog",id:p.contentId,"aria-describedby":p.descriptionId,"aria-labelledby":p.titleId,"data-state":em(p.open),...s,ref:d,onDismiss:()=>p.onOpenChange(!1)})}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eh,{titleId:p.titleId}),(0,l.jsx)(eb,{contentRef:u,descriptionId:p.descriptionId})]})]})}),es="DialogTitle",ec=i.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,i=H(es,n);return(0,l.jsx)(h.h2,{id:i.titleId,...a,ref:t})});ec.displayName=es;var el="DialogDescription",ep=i.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,i=H(el,n);return(0,l.jsx)(h.p,{id:i.descriptionId,...a,ref:t})});ep.displayName=el;var eu="DialogClose",ed=i.forwardRef((e,t)=>{let{__scopeDialog:n,...a}=e,i=H(eu,n);return(0,l.jsx)(h.button,{type:"button",...a,ref:t,onClick:r(e.onClick,()=>i.onOpenChange(!1))})});function em(e){return e?"open":"closed"}ed.displayName=eu;var ef="DialogTitleWarning",[ex,ev]=function(e,t){let n=i.createContext(t),a=e=>{let{children:t,...a}=e,o=i.useMemo(()=>a,Object.values(a));return(0,l.jsx)(n.Provider,{value:o,children:t})};return a.displayName=e+"Provider",[a,function(a){let o=i.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}(ef,{contentName:en,titleName:es,docsSlug:"dialog"}),eh=({titleId:e})=>{let t=ev(ef),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},eb=({contentRef:e,descriptionId:t})=>{let n=ev("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return i.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},eg=$,ey=V,ew=X,eE=Q,ek=ea,ej=ec,eR=ep,eC=ed},6582:(e,t,n)=>{"use strict";var a="undefined"!=typeof Symbol&&Symbol,i=n(54544);e.exports=function(){return"function"==typeof a&&"function"==typeof Symbol&&"symbol"==typeof a("foo")&&"symbol"==typeof Symbol("bar")&&i()}},7315:e=>{"use strict";e.exports=RangeError},7932:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(t.bind(e)),e.jobs={}};function t(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},9181:(e,t,n)=>{"use strict";var a=n(62427),i=n(81285),o=n(23471);e.exports=a?function(e){return a(e)}:i?function(e){if(!e||"object"!=typeof e&&"function"!=typeof e)throw TypeError("getProto: not an object");return i(e)}:o?function(e){return o(e)}:null},10022:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10096:e=>{"use strict";e.exports=URIError},11273:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var a=n(43210),i=n(60687);function o(e,t=[]){let n=[],r=()=>{let t=n.map(e=>a.createContext(e));return function(n){let i=n?.[e]||t;return a.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return r.scopeName=e,[function(t,o){let r=a.createContext(o),s=n.length;n=[...n,o];let c=t=>{let{scope:n,children:o,...c}=t,l=n?.[e]?.[s]||r,p=a.useMemo(()=>c,Object.values(c));return(0,i.jsx)(l.Provider,{value:p,children:o})};return c.displayName=t+"Provider",[c,function(n,i){let c=i?.[e]?.[s]||r,l=a.useContext(c);if(l)return l;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:a})=>{let i=n(e)[`__scope${a}`];return{...t,...i}},{});return a.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}(r,...t)]}},11490:(e,t,n)=>{"use strict";n.d(t,{A:()=>H});var a,i=n(4363),o=n(43210),r="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var l="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,p=new WeakMap;function u(e){return e}var d=function(e){void 0===e&&(e={});var t,n,a,o,r=(t=null,void 0===n&&(n=u),a=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=n(e,o);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){o=!0;var t=[];if(a.length){var n=a;a=[],n.forEach(e),t=a}var i=function(){var n=t;t=[],n.forEach(e)},r=function(){return Promise.resolve().then(i)};r(),a={push:function(e){t.push(e),r()},filter:function(e){return t=t.filter(e),a}}}});return r.options=(0,i.Cl)({async:!0,ssr:!1},e),r}(),m=function(){},f=o.forwardRef(function(e,t){var n,a,r,s,u=o.useRef(null),f=o.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),x=f[0],v=f[1],h=e.forwardProps,b=e.children,g=e.className,y=e.removeScrollBar,w=e.enabled,E=e.shards,k=e.sideCar,j=e.noIsolation,R=e.inert,C=e.allowPinchZoom,S=e.as,A=e.gapMode,_=(0,i.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(n=[u,t],a=function(e){return n.forEach(function(t){return c(t,e)})},(r=(0,o.useState)(function(){return{value:null,callback:a,facade:{get current(){return r.value},set current(value){var e=r.value;e!==value&&(r.value=value,r.callback(value,e))}}}})[0]).callback=a,s=r.facade,l(function(){var e=p.get(s);if(e){var t=new Set(e),a=new Set(n),i=s.current;t.forEach(function(e){a.has(e)||c(e,null)}),a.forEach(function(e){t.has(e)||c(e,i)})}p.set(s,n)},[n]),s),T=(0,i.Cl)((0,i.Cl)({},_),x);return o.createElement(o.Fragment,null,w&&o.createElement(k,{sideCar:d,removeScrollBar:y,shards:E,noIsolation:j,inert:R,setCallbacks:v,allowPinchZoom:!!C,lockRef:u,gapMode:A}),h?o.cloneElement(o.Children.only(b),(0,i.Cl)((0,i.Cl)({},T),{ref:O})):o.createElement(void 0===S?"div":S,(0,i.Cl)({},T,{className:g,ref:O}),b))});f.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},f.classNames={fullWidth:s,zeroRight:r};var x=function(e){var t=e.sideCar,n=(0,i.Tt)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return o.createElement(a,(0,i.Cl)({},n))};x.isSideCarExport=!0;var v=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,r;(o=t).styleSheet?o.styleSheet.cssText=i:o.appendChild(document.createTextNode(i)),r=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(r)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},h=function(){var e=v();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=h();return function(t){return e(t.styles,t.dynamic),null}},g={left:0,top:0,right:0,gap:0},y=function(e){return parseInt(e||"",10)||0},w=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],a=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[y(n),y(a),y(i)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return g;var t=w(e),n=document.documentElement.clientWidth,a=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,a-n+t[2]-t[0])}},k=b(),j="data-scroll-locked",R=function(e,t,n,a){var i=e.left,o=e.top,c=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(a,";\n   padding-right: ").concat(l,"px ").concat(a,";\n  }\n  body[").concat(j,"] {\n    overflow: hidden ").concat(a,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(a,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(o,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(a,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(a,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(r," {\n    right: ").concat(l,"px ").concat(a,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(l,"px ").concat(a,";\n  }\n  \n  .").concat(r," .").concat(r," {\n    right: 0 ").concat(a,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(a,";\n  }\n  \n  body[").concat(j,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},C=function(){var e=parseInt(document.body.getAttribute(j)||"0",10);return isFinite(e)?e:0},S=function(){o.useEffect(function(){return document.body.setAttribute(j,(C()+1).toString()),function(){var e=C()-1;e<=0?document.body.removeAttribute(j):document.body.setAttribute(j,e.toString())}},[])},A=function(e){var t=e.noRelative,n=e.noImportant,a=e.gapMode,i=void 0===a?"margin":a;S();var r=o.useMemo(function(){return E(i)},[i]);return o.createElement(k,{styles:R(r,!t,i,n?"":"!important")})},_=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return _=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){_=!1}var T=!!_&&{passive:!1},P=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,a=t;do{if("undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&(a=a.host),N(e,a)){var i=F(e,a);if(i[1]>i[2])return!0}a=a.parentNode}while(a&&a!==n.body);return!1},N=function(e,t){return"v"===e?P(t,"overflowY"):P(t,"overflowX")},F=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},D=function(e,t,n,a,i){var o,r=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),s=r*a,c=n.target,l=t.contains(c),p=!1,u=s>0,d=0,m=0;do{var f=F(e,c),x=f[0],v=f[1]-f[2]-r*x;(x||v)&&N(e,c)&&(d+=v,m+=x),c=c instanceof ShadowRoot?c.host:c.parentNode}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return u&&(i&&1>Math.abs(d)||!i&&s>d)?p=!0:!u&&(i&&1>Math.abs(m)||!i&&-s>m)&&(p=!0),p},M=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},I=function(e){return[e.deltaX,e.deltaY]},B=function(e){return e&&"current"in e?e.current:e},z=0,U=[];let q=(d.useMedium(function(e){var t=o.useRef([]),n=o.useRef([0,0]),a=o.useRef(),r=o.useState(z++)[0],s=o.useState(b)[0],c=o.useRef(e);o.useEffect(function(){c.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(r));var t=(0,i.fX)([e.lockRef.current],(e.shards||[]).map(B),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(r))}),function(){document.body.classList.remove("block-interactivity-".concat(r)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(r))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var i,o=M(e),r=n.current,s="deltaX"in e?e.deltaX:r[0]-o[0],l="deltaY"in e?e.deltaY:r[1]-o[1],p=e.target,u=Math.abs(s)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===u&&"range"===p.type)return!1;var d=L(u,p);if(!d)return!0;if(d?i=u:(i="v"===u?"h":"v",d=L(u,p)),!d)return!1;if(!a.current&&"changedTouches"in e&&(s||l)&&(a.current=i),!i)return!0;var m=a.current||i;return D(m,t,e,"h"===m?s:l,!0)},[]),p=o.useCallback(function(e){if(U.length&&U[U.length-1]===s){var n="deltaY"in e?I(e):M(e),a=t.current.filter(function(t){var a;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(a=t.delta)[0]===n[0]&&a[1]===n[1]})[0];if(a&&a.should){e.cancelable&&e.preventDefault();return}if(!a){var i=(c.current.shards||[]).map(B).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=o.useCallback(function(e,n,a,i){var o={name:e,delta:n,target:a,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(a)};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=o.useCallback(function(e){n.current=M(e),a.current=void 0},[]),m=o.useCallback(function(t){u(t.type,I(t),t.target,l(t,e.lockRef.current))},[]),f=o.useCallback(function(t){u(t.type,M(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return U.push(s),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:f}),document.addEventListener("wheel",p,T),document.addEventListener("touchmove",p,T),document.addEventListener("touchstart",d,T),function(){U=U.filter(function(e){return e!==s}),document.removeEventListener("wheel",p,T),document.removeEventListener("touchmove",p,T),document.removeEventListener("touchstart",d,T)}},[]);var x=e.removeScrollBar,v=e.inert;return o.createElement(o.Fragment,null,v?o.createElement(s,{styles:"\n  .block-interactivity-".concat(r," {pointer-events: none;}\n  .allow-interactivity-").concat(r," {pointer-events: all;}\n")}):null,x?o.createElement(A,{gapMode:e.gapMode}):null)}),x);var W=o.forwardRef(function(e,t){return o.createElement(f,(0,i.Cl)({},e,{ref:t,sideCar:q}))});W.classNames=f.classNames;let H=W},12325:(e,t,n)=>{"use strict";n.d(t,{UC:()=>eY,q7:()=>eZ,JU:()=>eX,ZL:()=>eJ,bL:()=>eV,wv:()=>eQ,l9:()=>eK});var a=n(43210),i=n(70569),o=n(98599),r=n(11273),s=n(65551),c=n(3416),l=n(72031),p=n(43),u=n(31355),d=n(1359),m=n(32547),f=n(96963),x=n(29172),v=n(25028),h=n(46059),b=n(72942),g=n(60687),y=a.forwardRef((e,t)=>{let{children:n,...i}=e,o=a.Children.toArray(n),r=o.find(k);if(r){let e=r.props.children,n=o.map(t=>t!==r?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,g.jsx)(w,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,g.jsx)(w,{...i,ref:t,children:n})});y.displayName="Slot";var w=a.forwardRef((e,t)=>{let{children:n,...i}=e;if(a.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),r=function(e,t){let n={...t};for(let a in t){let i=e[a],o=t[a];/^on[A-Z]/.test(a)?i&&o?n[a]=(...e)=>{o(...e),i(...e)}:i&&(n[a]=i):"style"===a?n[a]={...i,...o}:"className"===a&&(n[a]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==a.Fragment&&(r.ref=t?(0,o.t)(t,e):e),a.cloneElement(n,r)}return a.Children.count(n)>1?a.Children.only(null):null});w.displayName="SlotClone";var E=({children:e})=>(0,g.jsx)(g.Fragment,{children:e});function k(e){return a.isValidElement(e)&&e.type===E}var j=n(13495),R=n(63376),C=n(11490),S=["Enter"," "],A=["ArrowUp","PageDown","End"],_=["ArrowDown","PageUp","Home",...A],O={ltr:[...S,"ArrowRight"],rtl:[...S,"ArrowLeft"]},T={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[L,N,F]=(0,l.N)(P),[D,M]=(0,r.A)(P,[F,x.Bk,b.RG]),I=(0,x.Bk)(),B=(0,b.RG)(),[z,U]=D(P),[q,W]=D(P),H=e=>{let{__scopeMenu:t,open:n=!1,children:i,dir:o,onOpenChange:r,modal:s=!0}=e,c=I(t),[l,u]=a.useState(null),d=a.useRef(!1),m=(0,j.c)(r),f=(0,p.jH)(o);return a.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,g.jsx)(x.bL,{...c,children:(0,g.jsx)(z,{scope:t,open:n,onOpenChange:m,content:l,onContentChange:u,children:(0,g.jsx)(q,{scope:t,onClose:a.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:d,dir:f,modal:s,children:i})})})};H.displayName=P;var $=a.forwardRef((e,t)=>{let{__scopeMenu:n,...a}=e,i=I(n);return(0,g.jsx)(x.Mz,{...i,...a,ref:t})});$.displayName="MenuAnchor";var G="MenuPortal",[V,K]=D(G,{forceMount:void 0}),J=e=>{let{__scopeMenu:t,forceMount:n,children:a,container:i}=e,o=U(G,t);return(0,g.jsx)(V,{scope:t,forceMount:n,children:(0,g.jsx)(h.C,{present:n||o.open,children:(0,g.jsx)(v.Z,{asChild:!0,container:i,children:a})})})};J.displayName=G;var Y="MenuContent",[X,Z]=D(Y),Q=a.forwardRef((e,t)=>{let n=K(Y,e.__scopeMenu),{forceMount:a=n.forceMount,...i}=e,o=U(Y,e.__scopeMenu),r=W(Y,e.__scopeMenu);return(0,g.jsx)(L.Provider,{scope:e.__scopeMenu,children:(0,g.jsx)(h.C,{present:a||o.open,children:(0,g.jsx)(L.Slot,{scope:e.__scopeMenu,children:r.modal?(0,g.jsx)(ee,{...i,ref:t}):(0,g.jsx)(et,{...i,ref:t})})})})}),ee=a.forwardRef((e,t)=>{let n=U(Y,e.__scopeMenu),r=a.useRef(null),s=(0,o.s)(t,r);return a.useEffect(()=>{let e=r.current;if(e)return(0,R.Eq)(e)},[]),(0,g.jsx)(en,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),et=a.forwardRef((e,t)=>{let n=U(Y,e.__scopeMenu);return(0,g.jsx)(en,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),en=a.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:s,onOpenAutoFocus:c,onCloseAutoFocus:l,disableOutsidePointerEvents:p,onEntryFocus:f,onEscapeKeyDown:v,onPointerDownOutside:h,onFocusOutside:w,onInteractOutside:E,onDismiss:k,disableOutsideScroll:j,...R}=e,S=U(Y,n),O=W(Y,n),T=I(n),P=B(n),L=N(n),[F,D]=a.useState(null),M=a.useRef(null),z=(0,o.s)(t,M,S.onContentChange),q=a.useRef(0),H=a.useRef(""),$=a.useRef(0),G=a.useRef(null),V=a.useRef("right"),K=a.useRef(0),J=j?C.A:a.Fragment,Z=j?{as:y,allowPinchZoom:!0}:void 0,Q=e=>{let t=H.current+e,n=L().filter(e=>!e.disabled),a=document.activeElement,i=n.find(e=>e.ref.current===a)?.textValue,o=function(e,t,n){var a;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(a=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(a+n)%e.length]));1===i.length&&(o=o.filter(e=>e!==n));let r=o.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return r!==n?r:void 0}(n.map(e=>e.textValue),t,i),r=n.find(e=>e.textValue===o)?.ref.current;(function e(t){H.current=t,window.clearTimeout(q.current),""!==t&&(q.current=window.setTimeout(()=>e(""),1e3))})(t),r&&setTimeout(()=>r.focus())};a.useEffect(()=>()=>window.clearTimeout(q.current),[]),(0,d.Oh)();let ee=a.useCallback(e=>V.current===G.current?.side&&function(e,t){return!!t&&function(e,t){let{x:n,y:a}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let r=t[e].x,s=t[e].y,c=t[o].x,l=t[o].y;s>a!=l>a&&n<(c-r)*(a-s)/(l-s)+r&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,G.current?.area),[]);return(0,g.jsx)(X,{scope:n,searchRef:H,onItemEnter:a.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:a.useCallback(e=>{ee(e)||(M.current?.focus(),D(null))},[ee]),onTriggerLeave:a.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:$,onPointerGraceIntentChange:a.useCallback(e=>{G.current=e},[]),children:(0,g.jsx)(J,{...Z,children:(0,g.jsx)(m.n,{asChild:!0,trapped:s,onMountAutoFocus:(0,i.m)(c,e=>{e.preventDefault(),M.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:l,children:(0,g.jsx)(u.qW,{asChild:!0,disableOutsidePointerEvents:p,onEscapeKeyDown:v,onPointerDownOutside:h,onFocusOutside:w,onInteractOutside:E,onDismiss:k,children:(0,g.jsx)(b.bL,{asChild:!0,...P,dir:O.dir,orientation:"vertical",loop:r,currentTabStopId:F,onCurrentTabStopIdChange:D,onEntryFocus:(0,i.m)(f,e=>{O.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,g.jsx)(x.UC,{role:"menu","aria-orientation":"vertical","data-state":eA(S.open),"data-radix-menu-content":"",dir:O.dir,...T,...R,ref:z,style:{outline:"none",...R.style},onKeyDown:(0,i.m)(R.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,a=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&a&&Q(e.key));let i=M.current;if(e.target!==i||!_.includes(e.key))return;e.preventDefault();let o=L().filter(e=>!e.disabled).map(e=>e.ref.current);A.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(q.current),H.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eT(e=>{let t=e.target,n=K.current!==e.clientX;e.currentTarget.contains(t)&&n&&(V.current=e.clientX>K.current?"right":"left",K.current=e.clientX)}))})})})})})})});Q.displayName=Y;var ea=a.forwardRef((e,t)=>{let{__scopeMenu:n,...a}=e;return(0,g.jsx)(c.sG.div,{role:"group",...a,ref:t})});ea.displayName="MenuGroup";var ei=a.forwardRef((e,t)=>{let{__scopeMenu:n,...a}=e;return(0,g.jsx)(c.sG.div,{...a,ref:t})});ei.displayName="MenuLabel";var eo="MenuItem",er="menu.itemSelect",es=a.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...s}=e,l=a.useRef(null),p=W(eo,e.__scopeMenu),u=Z(eo,e.__scopeMenu),d=(0,o.s)(t,l),m=a.useRef(!1);return(0,g.jsx)(ec,{...s,ref:d,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=l.current;if(!n&&e){let t=new CustomEvent(er,{bubbles:!0,cancelable:!0});e.addEventListener(er,e=>r?.(e),{once:!0}),(0,c.hO)(e,t),t.defaultPrevented?m.current=!1:p.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),m.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{m.current||e.currentTarget?.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==u.searchRef.current;!n&&(!t||" "!==e.key)&&S.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});es.displayName=eo;var ec=a.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:s,...l}=e,p=Z(eo,n),u=B(n),d=a.useRef(null),m=(0,o.s)(t,d),[f,x]=a.useState(!1),[v,h]=a.useState("");return a.useEffect(()=>{let e=d.current;e&&h((e.textContent??"").trim())},[l.children]),(0,g.jsx)(L.ItemSlot,{scope:n,disabled:r,textValue:s??v,children:(0,g.jsx)(b.q7,{asChild:!0,...u,focusable:!r,children:(0,g.jsx)(c.sG.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...l,ref:m,onPointerMove:(0,i.m)(e.onPointerMove,eT(e=>{r?p.onItemLeave(e):(p.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eT(e=>p.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>x(!0)),onBlur:(0,i.m)(e.onBlur,()=>x(!1))})})})}),el=a.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:a,...o}=e;return(0,g.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,g.jsx)(es,{role:"menuitemcheckbox","aria-checked":e_(n)?"mixed":n,...o,ref:t,"data-state":eO(n),onSelect:(0,i.m)(o.onSelect,()=>a?.(!!e_(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var ep="MenuRadioGroup",[eu,ed]=D(ep,{value:void 0,onValueChange:()=>{}}),em=a.forwardRef((e,t)=>{let{value:n,onValueChange:a,...i}=e,o=(0,j.c)(a);return(0,g.jsx)(eu,{scope:e.__scopeMenu,value:n,onValueChange:o,children:(0,g.jsx)(ea,{...i,ref:t})})});em.displayName=ep;var ef="MenuRadioItem",ex=a.forwardRef((e,t)=>{let{value:n,...a}=e,o=ed(ef,e.__scopeMenu),r=n===o.value;return(0,g.jsx)(eh,{scope:e.__scopeMenu,checked:r,children:(0,g.jsx)(es,{role:"menuitemradio","aria-checked":r,...a,ref:t,"data-state":eO(r),onSelect:(0,i.m)(a.onSelect,()=>o.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ex.displayName=ef;var ev="MenuItemIndicator",[eh,eb]=D(ev,{checked:!1}),eg=a.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:a,...i}=e,o=eb(ev,n);return(0,g.jsx)(h.C,{present:a||e_(o.checked)||!0===o.checked,children:(0,g.jsx)(c.sG.span,{...i,ref:t,"data-state":eO(o.checked)})})});eg.displayName=ev;var ey=a.forwardRef((e,t)=>{let{__scopeMenu:n,...a}=e;return(0,g.jsx)(c.sG.div,{role:"separator","aria-orientation":"horizontal",...a,ref:t})});ey.displayName="MenuSeparator";var ew=a.forwardRef((e,t)=>{let{__scopeMenu:n,...a}=e,i=I(n);return(0,g.jsx)(x.i3,{...i,...a,ref:t})});ew.displayName="MenuArrow";var[eE,ek]=D("MenuSub"),ej="MenuSubTrigger",eR=a.forwardRef((e,t)=>{let n=U(ej,e.__scopeMenu),r=W(ej,e.__scopeMenu),s=ek(ej,e.__scopeMenu),c=Z(ej,e.__scopeMenu),l=a.useRef(null),{pointerGraceTimerRef:p,onPointerGraceIntentChange:u}=c,d={__scopeMenu:e.__scopeMenu},m=a.useCallback(()=>{l.current&&window.clearTimeout(l.current),l.current=null},[]);return a.useEffect(()=>m,[m]),a.useEffect(()=>{let e=p.current;return()=>{window.clearTimeout(e),u(null)}},[p,u]),(0,g.jsx)($,{asChild:!0,...d,children:(0,g.jsx)(ec,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":s.contentId,"data-state":eA(n.open),...e,ref:(0,o.t)(t,s.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eT(t=>{c.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||l.current||(c.onPointerGraceIntentChange(null),l.current=window.setTimeout(()=>{n.onOpenChange(!0),m()},100))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eT(e=>{m();let t=n.content?.getBoundingClientRect();if(t){let a=n.content?.dataset.side,i="right"===a,o=t[i?"left":"right"],r=t[i?"right":"left"];c.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:o,y:t.top},{x:r,y:t.top},{x:r,y:t.bottom},{x:o,y:t.bottom}],side:a}),window.clearTimeout(p.current),p.current=window.setTimeout(()=>c.onPointerGraceIntentChange(null),300)}else{if(c.onTriggerLeave(e),e.defaultPrevented)return;c.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let a=""!==c.searchRef.current;!e.disabled&&(!a||" "!==t.key)&&O[r.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eR.displayName=ej;var eC="MenuSubContent",eS=a.forwardRef((e,t)=>{let n=K(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...s}=e,c=U(Y,e.__scopeMenu),l=W(Y,e.__scopeMenu),p=ek(eC,e.__scopeMenu),u=a.useRef(null),d=(0,o.s)(t,u);return(0,g.jsx)(L.Provider,{scope:e.__scopeMenu,children:(0,g.jsx)(h.C,{present:r||c.open,children:(0,g.jsx)(L.Slot,{scope:e.__scopeMenu,children:(0,g.jsx)(en,{id:p.contentId,"aria-labelledby":p.triggerId,...s,ref:d,align:"start",side:"rtl"===l.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{l.isUsingKeyboardRef.current&&u.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==p.trigger&&c.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{l.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=T[l.dir].includes(e.key);t&&n&&(c.onOpenChange(!1),p.trigger?.focus(),e.preventDefault())})})})})})});function eA(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function eO(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eT(e){return t=>"mouse"===t.pointerType?e(t):void 0}eS.displayName=eC;var eP="DropdownMenu",[eL,eN]=(0,r.A)(eP,[M]),eF=M(),[eD,eM]=eL(eP),eI=e=>{let{__scopeDropdownMenu:t,children:n,dir:i,open:o,defaultOpen:r,onOpenChange:c,modal:l=!0}=e,p=eF(t),u=a.useRef(null),[d=!1,m]=(0,s.i)({prop:o,defaultProp:r,onChange:c});return(0,g.jsx)(eD,{scope:t,triggerId:(0,f.B)(),triggerRef:u,contentId:(0,f.B)(),open:d,onOpenChange:m,onOpenToggle:a.useCallback(()=>m(e=>!e),[m]),modal:l,children:(0,g.jsx)(H,{...p,open:d,onOpenChange:m,dir:i,modal:l,children:n})})};eI.displayName=eP;var eB="DropdownMenuTrigger",ez=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:a=!1,...r}=e,s=eM(eB,n),l=eF(n);return(0,g.jsx)($,{asChild:!0,...l,children:(0,g.jsx)(c.sG.button,{type:"button",id:s.triggerId,"aria-haspopup":"menu","aria-expanded":s.open,"aria-controls":s.open?s.contentId:void 0,"data-state":s.open?"open":"closed","data-disabled":a?"":void 0,disabled:a,...r,ref:(0,o.t)(t,s.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{a||0!==e.button||!1!==e.ctrlKey||(s.onOpenToggle(),s.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!a&&(["Enter"," "].includes(e.key)&&s.onOpenToggle(),"ArrowDown"===e.key&&s.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});ez.displayName=eB;var eU=e=>{let{__scopeDropdownMenu:t,...n}=e,a=eF(t);return(0,g.jsx)(J,{...a,...n})};eU.displayName="DropdownMenuPortal";var eq="DropdownMenuContent",eW=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...o}=e,r=eM(eq,n),s=eF(n),c=a.useRef(!1);return(0,g.jsx)(Q,{id:r.contentId,"aria-labelledby":r.triggerId,...s,...o,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{c.current||r.triggerRef.current?.focus(),c.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,a=2===t.button||n;(!r.modal||a)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eW.displayName=eq,a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(ea,{...i,...a,ref:t})}).displayName="DropdownMenuGroup";var eH=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(ei,{...i,...a,ref:t})});eH.displayName="DropdownMenuLabel";var e$=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(es,{...i,...a,ref:t})});e$.displayName="DropdownMenuItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(el,{...i,...a,ref:t})}).displayName="DropdownMenuCheckboxItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(em,{...i,...a,ref:t})}).displayName="DropdownMenuRadioGroup",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(ex,{...i,...a,ref:t})}).displayName="DropdownMenuRadioItem",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(eg,{...i,...a,ref:t})}).displayName="DropdownMenuItemIndicator";var eG=a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(ey,{...i,...a,ref:t})});eG.displayName="DropdownMenuSeparator",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(ew,{...i,...a,ref:t})}).displayName="DropdownMenuArrow",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(eR,{...i,...a,ref:t})}).displayName="DropdownMenuSubTrigger",a.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...a}=e,i=eF(n);return(0,g.jsx)(eS,{...i,...a,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eV=eI,eK=ez,eJ=eU,eY=eW,eX=eH,eZ=e$,eQ=eG},13495:(e,t,n)=>{"use strict";n.d(t,{c:()=>i});var a=n(43210);function i(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>(...e)=>t.current?.(...e),[])}},15219:e=>{"use strict";e.exports=SyntaxError},16189:(e,t,n)=>{"use strict";var a=n(65773);n.o(a,"useParams")&&n.d(t,{useParams:function(){return a.useParams}}),n.o(a,"usePathname")&&n.d(t,{usePathname:function(){return a.usePathname}}),n.o(a,"useRouter")&&n.d(t,{useRouter:function(){return a.useRouter}})},19207:e=>{"use strict";e.exports=(e,t=process.argv)=>{let n=e.startsWith("-")?"":1===e.length?"-":"--",a=t.indexOf(n+e),i=t.indexOf("--");return -1!==a&&(-1===i||a<i)}},23471:(e,t,n)=>{"use strict";var a,i=n(70607),o=n(80036);try{a=[].__proto__===Array.prototype}catch(e){if(!e||"object"!=typeof e||!("code"in e)||"ERR_PROTO_ACCESS"!==e.code)throw e}var r=!!a&&o&&o(Object.prototype,"__proto__"),s=Object,c=s.getPrototypeOf;e.exports=r&&"function"==typeof r.get?i([r.get]):"function"==typeof c&&function(e){return c(null==e?e:s(e))}},25028:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var a=n(43210),i=n(51215),o=n(3416),r=n(66156),s=n(60687),c=a.forwardRef((e,t)=>{let{container:n,...c}=e,[l,p]=a.useState(!1);(0,r.N)(()=>p(!0),[]);let u=n||l&&globalThis?.document?.body;return u?i.createPortal((0,s.jsx)(o.sG.div,{...c,ref:t}),u):null});c.displayName="Portal"},25605:(e,t,n)=>{"use strict";n.d(t,{UE:()=>ed,ll:()=>er,rD:()=>ef,UU:()=>el,jD:()=>eu,ER:()=>em,cY:()=>es,BN:()=>ec,Ej:()=>ep});let a=["top","right","bottom","left"],i=Math.min,o=Math.max,r=Math.round,s=Math.floor,c=e=>({x:e,y:e}),l={left:"right",right:"left",bottom:"top",top:"bottom"},p={start:"end",end:"start"};function u(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function f(e){return"x"===e?"y":"x"}function x(e){return"y"===e?"height":"width"}function v(e){return["top","bottom"].includes(d(e))?"y":"x"}function h(e){return e.replace(/start|end/g,e=>p[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>l[e])}function g(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function y(e){let{x:t,y:n,width:a,height:i}=e;return{width:a,height:i,top:n,left:t,right:t+a,bottom:n+i,x:t,y:n}}function w(e,t,n){let a,{reference:i,floating:o}=e,r=v(t),s=f(v(t)),c=x(s),l=d(t),p="y"===r,u=i.x+i.width/2-o.width/2,h=i.y+i.height/2-o.height/2,b=i[c]/2-o[c]/2;switch(l){case"top":a={x:u,y:i.y-o.height};break;case"bottom":a={x:u,y:i.y+i.height};break;case"right":a={x:i.x+i.width,y:h};break;case"left":a={x:i.x-o.width,y:h};break;default:a={x:i.x,y:i.y}}switch(m(t)){case"start":a[s]-=b*(n&&p?-1:1);break;case"end":a[s]+=b*(n&&p?-1:1)}return a}let E=async(e,t,n)=>{let{placement:a="bottom",strategy:i="absolute",middleware:o=[],platform:r}=n,s=o.filter(Boolean),c=await (null==r.isRTL?void 0:r.isRTL(t)),l=await r.getElementRects({reference:e,floating:t,strategy:i}),{x:p,y:u}=w(l,a,c),d=a,m={},f=0;for(let n=0;n<s.length;n++){let{name:o,fn:x}=s[n],{x:v,y:h,data:b,reset:g}=await x({x:p,y:u,initialPlacement:a,placement:d,strategy:i,middlewareData:m,rects:l,platform:r,elements:{reference:e,floating:t}});p=null!=v?v:p,u=null!=h?h:u,m={...m,[o]:{...m[o],...b}},g&&f<=50&&(f++,"object"==typeof g&&(g.placement&&(d=g.placement),g.rects&&(l=!0===g.rects?await r.getElementRects({reference:e,floating:t,strategy:i}):g.rects),{x:p,y:u}=w(l,d,c)),n=-1)}return{x:p,y:u,placement:d,strategy:i,middlewareData:m}};async function k(e,t){var n;void 0===t&&(t={});let{x:a,y:i,platform:o,rects:r,elements:s,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:p="viewport",elementContext:d="floating",altBoundary:m=!1,padding:f=0}=u(t,e),x=g(f),v=s[m?"floating"===d?"reference":"floating":d],h=y(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(v)))||n?v:v.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:l,rootBoundary:p,strategy:c})),b="floating"===d?{x:a,y:i,width:r.floating.width,height:r.floating.height}:r.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),E=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},k=y(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:b,offsetParent:w,strategy:c}):b);return{top:(h.top-k.top+x.top)/E.y,bottom:(k.bottom-h.bottom+x.bottom)/E.y,left:(h.left-k.left+x.left)/E.x,right:(k.right-h.right+x.right)/E.x}}function j(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function R(e){return a.some(t=>e[t]>=0)}async function C(e,t){let{placement:n,platform:a,elements:i}=e,o=await (null==a.isRTL?void 0:a.isRTL(i.floating)),r=d(n),s=m(n),c="y"===v(n),l=["left","top"].includes(r)?-1:1,p=o&&c?-1:1,f=u(t,e),{mainAxis:x,crossAxis:h,alignmentAxis:b}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return s&&"number"==typeof b&&(h="end"===s?-1*b:b),c?{x:h*p,y:x*l}:{x:x*l,y:h*p}}function S(){return"undefined"!=typeof window}function A(e){return T(e)?(e.nodeName||"").toLowerCase():"#document"}function _(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(T(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function T(e){return!!S()&&(e instanceof Node||e instanceof _(e).Node)}function P(e){return!!S()&&(e instanceof Element||e instanceof _(e).Element)}function L(e){return!!S()&&(e instanceof HTMLElement||e instanceof _(e).HTMLElement)}function N(e){return!!S()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof _(e).ShadowRoot)}function F(e){let{overflow:t,overflowX:n,overflowY:a,display:i}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+a+n)&&!["inline","contents"].includes(i)}function D(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function M(e){let t=I(),n=P(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function I(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(A(e))}function z(e){return _(e).getComputedStyle(e)}function U(e){return P(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function q(e){if("html"===A(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||O(e);return N(t)?t.host:t}function W(e,t,n){var a;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=q(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:L(n)&&F(n)?n:e(n)}(e),o=i===(null==(a=e.ownerDocument)?void 0:a.body),r=_(i);if(o){let e=H(r);return t.concat(r,r.visualViewport||[],F(i)?i:[],e&&n?W(e):[])}return t.concat(i,W(i,[],n))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $(e){let t=z(e),n=parseFloat(t.width)||0,a=parseFloat(t.height)||0,i=L(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:a,c=r(n)!==o||r(a)!==s;return c&&(n=o,a=s),{width:n,height:a,$:c}}function G(e){return P(e)?e:e.contextElement}function V(e){let t=G(e);if(!L(t))return c(1);let n=t.getBoundingClientRect(),{width:a,height:i,$:o}=$(t),s=(o?r(n.width):n.width)/a,l=(o?r(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),l&&Number.isFinite(l)||(l=1),{x:s,y:l}}let K=c(0);function J(e){let t=_(e);return I()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:K}function Y(e,t,n,a){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),r=G(e),s=c(1);t&&(a?P(a)&&(s=V(a)):s=V(e));let l=(void 0===(i=n)&&(i=!1),a&&(!i||a===_(r))&&i)?J(r):c(0),p=(o.left+l.x)/s.x,u=(o.top+l.y)/s.y,d=o.width/s.x,m=o.height/s.y;if(r){let e=_(r),t=a&&P(a)?_(a):a,n=e,i=H(n);for(;i&&a&&t!==n;){let e=V(i),t=i.getBoundingClientRect(),a=z(i),o=t.left+(i.clientLeft+parseFloat(a.paddingLeft))*e.x,r=t.top+(i.clientTop+parseFloat(a.paddingTop))*e.y;p*=e.x,u*=e.y,d*=e.x,m*=e.y,p+=o,u+=r,i=H(n=_(i))}}return y({width:d,height:m,x:p,y:u})}function X(e,t){let n=U(e).scrollLeft;return t?t.left+n:Y(O(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let a=e.getBoundingClientRect();return{x:a.left+t.scrollLeft-(n?0:X(e,a)),y:a.top+t.scrollTop}}function Q(e,t,n){let a;if("viewport"===t)a=function(e,t){let n=_(e),a=O(e),i=n.visualViewport,o=a.clientWidth,r=a.clientHeight,s=0,c=0;if(i){o=i.width,r=i.height;let e=I();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,c=i.offsetTop)}return{width:o,height:r,x:s,y:c}}(e,n);else if("document"===t)a=function(e){let t=O(e),n=U(e),a=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,a.scrollWidth,a.clientWidth),r=o(t.scrollHeight,t.clientHeight,a.scrollHeight,a.clientHeight),s=-n.scrollLeft+X(e),c=-n.scrollTop;return"rtl"===z(a).direction&&(s+=o(t.clientWidth,a.clientWidth)-i),{width:i,height:r,x:s,y:c}}(O(e));else if(P(t))a=function(e,t){let n=Y(e,!0,"fixed"===t),a=n.top+e.clientTop,i=n.left+e.clientLeft,o=L(e)?V(e):c(1),r=e.clientWidth*o.x,s=e.clientHeight*o.y;return{width:r,height:s,x:i*o.x,y:a*o.y}}(t,n);else{let n=J(e);a={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return y(a)}function ee(e){return"static"===z(e).position}function et(e,t){if(!L(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return O(e)===n&&(n=n.ownerDocument.body),n}function en(e,t){let n=_(e);if(D(e))return n;if(!L(e)){let t=q(e);for(;t&&!B(t);){if(P(t)&&!ee(t))return t;t=q(t)}return n}let a=et(e,t);for(;a&&["table","td","th"].includes(A(a))&&ee(a);)a=et(a,t);return a&&B(a)&&ee(a)&&!M(a)?n:a||function(e){let t=q(e);for(;L(t)&&!B(t);){if(M(t))return t;if(D(t))break;t=q(t)}return null}(e)||n}let ea=async function(e){let t=this.getOffsetParent||en,n=this.getDimensions,a=await n(e.floating);return{reference:function(e,t,n){let a=L(t),i=O(t),o="fixed"===n,r=Y(e,!0,o,t),s={scrollLeft:0,scrollTop:0},l=c(0);if(a||!a&&!o){if(("body"!==A(t)||F(i))&&(s=U(t)),a){let e=Y(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=X(i))}let p=!i||a||o?c(0):Z(i,s);return{x:r.left+s.scrollLeft-l.x-p.x,y:r.top+s.scrollTop-l.y-p.y,width:r.width,height:r.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:a.width,height:a.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:a,strategy:i}=e,o="fixed"===i,r=O(a),s=!!t&&D(t.floating);if(a===r||s&&o)return n;let l={scrollLeft:0,scrollTop:0},p=c(1),u=c(0),d=L(a);if((d||!d&&!o)&&(("body"!==A(a)||F(r))&&(l=U(a)),L(a))){let e=Y(a);p=V(a),u.x=e.x+a.clientLeft,u.y=e.y+a.clientTop}let m=!r||d||o?c(0):Z(r,l,!0);return{width:n.width*p.x,height:n.height*p.y,x:n.x*p.x-l.scrollLeft*p.x+u.x+m.x,y:n.y*p.y-l.scrollTop*p.y+u.y+m.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:a,strategy:r}=e,s=[..."clippingAncestors"===n?D(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let a=W(e,[],!1).filter(e=>P(e)&&"body"!==A(e)),i=null,o="fixed"===z(e).position,r=o?q(e):e;for(;P(r)&&!B(r);){let t=z(r),n=M(r);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||F(r)&&!n&&function e(t,n){let a=q(t);return!(a===n||!P(a)||B(a))&&("fixed"===z(a).position||e(a,n))}(e,r))?a=a.filter(e=>e!==r):i=t,r=q(r)}return t.set(e,a),a}(t,this._c):[].concat(n),a],c=s[0],l=s.reduce((e,n)=>{let a=Q(t,n,r);return e.top=o(a.top,e.top),e.right=i(a.right,e.right),e.bottom=i(a.bottom,e.bottom),e.left=o(a.left,e.left),e},Q(t,c,r));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:en,getElementRects:ea,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=$(e);return{width:t,height:n}},getScale:V,isElement:P,isRTL:function(e){return"rtl"===z(e).direction}};function eo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function er(e,t,n,a){let r;void 0===a&&(a={});let{ancestorScroll:c=!0,ancestorResize:l=!0,elementResize:p="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:d=!1}=a,m=G(e),f=c||l?[...m?W(m):[],...W(t)]:[];f.forEach(e=>{c&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let x=m&&u?function(e,t){let n,a=null,r=O(e);function c(){var e;clearTimeout(n),null==(e=a)||e.disconnect(),a=null}return function l(p,u){void 0===p&&(p=!1),void 0===u&&(u=1),c();let d=e.getBoundingClientRect(),{left:m,top:f,width:x,height:v}=d;if(p||t(),!x||!v)return;let h=s(f),b=s(r.clientWidth-(m+x)),g={rootMargin:-h+"px "+-b+"px "+-s(r.clientHeight-(f+v))+"px "+-s(m)+"px",threshold:o(0,i(1,u))||1},y=!0;function w(t){let a=t[0].intersectionRatio;if(a!==u){if(!y)return l();a?l(!1,a):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==a||eo(d,e.getBoundingClientRect())||l(),y=!1}try{a=new IntersectionObserver(w,{...g,root:r.ownerDocument})}catch(e){a=new IntersectionObserver(w,g)}a.observe(e)}(!0),c}(m,n):null,v=-1,h=null;p&&(h=new ResizeObserver(e=>{let[a]=e;a&&a.target===m&&h&&(h.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),m&&!d&&h.observe(m),h.observe(t));let b=d?Y(e):null;return d&&function t(){let a=Y(e);b&&!eo(b,a)&&n(),b=a,r=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{c&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==x||x(),null==(e=h)||e.disconnect(),h=null,d&&cancelAnimationFrame(r)}}let es=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,a;let{x:i,y:o,placement:r,middlewareData:s}=t,c=await C(t,e);return r===(null==(n=s.offset)?void 0:n.placement)&&null!=(a=s.arrow)&&a.alignmentOffset?{}:{x:i+c.x,y:o+c.y,data:{...c,placement:r}}}}},ec=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:a,placement:r}=t,{mainAxis:s=!0,crossAxis:c=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...p}=u(e,t),m={x:n,y:a},x=await k(t,p),h=v(d(r)),b=f(h),g=m[b],y=m[h];if(s){let e="y"===b?"top":"left",t="y"===b?"bottom":"right",n=g+x[e],a=g-x[t];g=o(n,i(g,a))}if(c){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=y+x[e],a=y-x[t];y=o(n,i(y,a))}let w=l.fn({...t,[b]:g,[h]:y});return{...w,data:{x:w.x-n,y:w.y-a,enabled:{[b]:s,[h]:c}}}}}},el=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,a,i,o,r;let{placement:s,middlewareData:c,rects:l,initialPlacement:p,platform:g,elements:y}=t,{mainAxis:w=!0,crossAxis:E=!0,fallbackPlacements:j,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:S=!0,...A}=u(e,t);if(null!=(n=c.arrow)&&n.alignmentOffset)return{};let _=d(s),O=v(p),T=d(p)===p,P=await (null==g.isRTL?void 0:g.isRTL(y.floating)),L=j||(T||!S?[b(p)]:function(e){let t=b(e);return[h(e),t,h(t)]}(p)),N="none"!==C;!j&&N&&L.push(...function(e,t,n,a){let i=m(e),o=function(e,t,n){let a=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:a;return t?a:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(d(e),"start"===n,a);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(h)))),o}(p,S,C,P));let F=[p,...L],D=await k(t,A),M=[],I=(null==(a=c.flip)?void 0:a.overflows)||[];if(w&&M.push(D[_]),E){let e=function(e,t,n){void 0===n&&(n=!1);let a=m(e),i=f(v(e)),o=x(i),r="x"===i?a===(n?"end":"start")?"right":"left":"start"===a?"bottom":"top";return t.reference[o]>t.floating[o]&&(r=b(r)),[r,b(r)]}(s,l,P);M.push(D[e[0]],D[e[1]])}if(I=[...I,{placement:s,overflows:M}],!M.every(e=>e<=0)){let e=((null==(i=c.flip)?void 0:i.index)||0)+1,t=F[e];if(t)return{data:{index:e,overflows:I},reset:{placement:t}};let n=null==(o=I.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(R){case"bestFit":{let e=null==(r=I.filter(e=>{if(N){let t=v(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:r[0];e&&(n=e);break}case"initialPlacement":n=p}if(s!==n)return{reset:{placement:n}}}return{}}}},ep=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,a;let r,s;let{placement:c,rects:l,platform:p,elements:f}=t,{apply:x=()=>{},...h}=u(e,t),b=await k(t,h),g=d(c),y=m(c),w="y"===v(c),{width:E,height:j}=l.floating;"top"===g||"bottom"===g?(r=g,s=y===(await (null==p.isRTL?void 0:p.isRTL(f.floating))?"start":"end")?"left":"right"):(s=g,r="end"===y?"top":"bottom");let R=j-b.top-b.bottom,C=E-b.left-b.right,S=i(j-b[r],R),A=i(E-b[s],C),_=!t.middlewareData.shift,O=S,T=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(T=C),null!=(a=t.middlewareData.shift)&&a.enabled.y&&(O=R),_&&!y){let e=o(b.left,0),t=o(b.right,0),n=o(b.top,0),a=o(b.bottom,0);w?T=E-2*(0!==e||0!==t?e+t:o(b.left,b.right)):O=j-2*(0!==n||0!==a?n+a:o(b.top,b.bottom))}await x({...t,availableWidth:T,availableHeight:O});let P=await p.getDimensions(f.floating);return E!==P.width||j!==P.height?{reset:{rects:!0}}:{}}}},eu=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:a="referenceHidden",...i}=u(e,t);switch(a){case"referenceHidden":{let e=j(await k(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:R(e)}}}case"escaped":{let e=j(await k(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:R(e)}}}default:return{}}}}},ed=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:a,placement:r,rects:s,platform:c,elements:l,middlewareData:p}=t,{element:d,padding:h=0}=u(e,t)||{};if(null==d)return{};let b=g(h),y={x:n,y:a},w=f(v(r)),E=x(w),k=await c.getDimensions(d),j="y"===w,R=j?"clientHeight":"clientWidth",C=s.reference[E]+s.reference[w]-y[w]-s.floating[E],S=y[w]-s.reference[w],A=await (null==c.getOffsetParent?void 0:c.getOffsetParent(d)),_=A?A[R]:0;_&&await (null==c.isElement?void 0:c.isElement(A))||(_=l.floating[R]||s.floating[E]);let O=_/2-k[E]/2-1,T=i(b[j?"top":"left"],O),P=i(b[j?"bottom":"right"],O),L=_-k[E]-P,N=_/2-k[E]/2+(C/2-S/2),F=o(T,i(N,L)),D=!p.arrow&&null!=m(r)&&N!==F&&s.reference[E]/2-(N<T?T:P)-k[E]/2<0,M=D?N<T?N-T:N-L:0;return{[w]:y[w]+M,data:{[w]:F,centerOffset:N-F-M,...D&&{alignmentOffset:M}},reset:D}}}),em=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:a,placement:i,rects:o,middlewareData:r}=t,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=u(e,t),p={x:n,y:a},m=v(i),x=f(m),h=p[x],b=p[m],g=u(s,t),y="number"==typeof g?{mainAxis:g,crossAxis:0}:{mainAxis:0,crossAxis:0,...g};if(c){let e="y"===x?"height":"width",t=o.reference[x]-o.floating[e]+y.mainAxis,n=o.reference[x]+o.reference[e]-y.mainAxis;h<t?h=t:h>n&&(h=n)}if(l){var w,E;let e="y"===x?"width":"height",t=["top","left"].includes(d(i)),n=o.reference[m]-o.floating[e]+(t&&(null==(w=r.offset)?void 0:w[m])||0)+(t?0:y.crossAxis),a=o.reference[m]+o.reference[e]+(t?0:(null==(E=r.offset)?void 0:E[m])||0)-(t?y.crossAxis:0);b<n?b=n:b>a&&(b=a)}return{[x]:h,[m]:b}}}},ef=(e,t,n)=>{let a=new Map,i={platform:ei,...n},o={...i.platform,_c:a};return E(e,t,{...i,platform:o})}},29172:(e,t,n)=>{"use strict";n.d(t,{Mz:()=>P,i3:()=>N,UC:()=>L,bL:()=>T,Bk:()=>x});var a=n(43210),i=n(4503),o=n(25605),r=n(3416),s=n(60687),c=a.forwardRef((e,t)=>{let{children:n,width:a=10,height:i=5,...o}=e;return(0,s.jsx)(r.sG.svg,{...o,ref:t,width:a,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,s.jsx)("polygon",{points:"0,0 30,0 15,10"})})});c.displayName="Arrow";var l=n(98599),p=n(11273),u=n(13495),d=n(66156),m="Popper",[f,x]=(0,p.A)(m),[v,h]=f(m),b=e=>{let{__scopePopper:t,children:n}=e,[i,o]=a.useState(null);return(0,s.jsx)(v,{scope:t,anchor:i,onAnchorChange:o,children:n})};b.displayName=m;var g="PopperAnchor",y=a.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,c=h(g,n),p=a.useRef(null),u=(0,l.s)(t,p);return a.useEffect(()=>{c.onAnchorChange(i?.current||p.current)}),i?null:(0,s.jsx)(r.sG.div,{...o,ref:u})});y.displayName=g;var w="PopperContent",[E,k]=f(w),j=a.forwardRef((e,t)=>{let{__scopePopper:n,side:c="bottom",sideOffset:p=0,align:m="center",alignOffset:f=0,arrowPadding:x=0,avoidCollisions:v=!0,collisionBoundary:b=[],collisionPadding:g=0,sticky:y="partial",hideWhenDetached:k=!1,updatePositionStrategy:j="optimized",onPlaced:R,...C}=e,S=h(w,n),[T,P]=a.useState(null),L=(0,l.s)(t,e=>P(e)),[N,F]=a.useState(null),D=function(e){let[t,n]=a.useState(void 0);return(0,d.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let a,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;a=t.inlineSize,i=t.blockSize}else a=e.offsetWidth,i=e.offsetHeight;n({width:a,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(N),M=D?.width??0,I=D?.height??0,B="number"==typeof g?g:{top:0,right:0,bottom:0,left:0,...g},z=Array.isArray(b)?b:[b],U=z.length>0,q={padding:B,boundary:z.filter(A),altBoundary:U},{refs:W,floatingStyles:H,placement:$,isPositioned:G,middlewareData:V}=(0,i.we)({strategy:"fixed",placement:c+("center"!==m?"-"+m:""),whileElementsMounted:(...e)=>(0,o.ll)(...e,{animationFrame:"always"===j}),elements:{reference:S.anchor},middleware:[(0,i.cY)({mainAxis:p+I,alignmentAxis:f}),v&&(0,i.BN)({mainAxis:!0,crossAxis:!1,limiter:"partial"===y?(0,i.ER)():void 0,...q}),v&&(0,i.UU)({...q}),(0,i.Ej)({...q,apply:({elements:e,rects:t,availableWidth:n,availableHeight:a})=>{let{width:i,height:o}=t.reference,r=e.floating.style;r.setProperty("--radix-popper-available-width",`${n}px`),r.setProperty("--radix-popper-available-height",`${a}px`),r.setProperty("--radix-popper-anchor-width",`${i}px`),r.setProperty("--radix-popper-anchor-height",`${o}px`)}}),N&&(0,i.UE)({element:N,padding:x}),_({arrowWidth:M,arrowHeight:I}),k&&(0,i.jD)({strategy:"referenceHidden",...q})]}),[K,J]=O($),Y=(0,u.c)(R);(0,d.N)(()=>{G&&Y?.()},[G,Y]);let X=V.arrow?.x,Z=V.arrow?.y,Q=V.arrow?.centerOffset!==0,[ee,et]=a.useState();return(0,d.N)(()=>{T&&et(window.getComputedStyle(T).zIndex)},[T]),(0,s.jsx)("div",{ref:W.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:G?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[V.transformOrigin?.x,V.transformOrigin?.y].join(" "),...V.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,s.jsx)(E,{scope:n,placedSide:K,onArrowChange:F,arrowX:X,arrowY:Z,shouldHideArrow:Q,children:(0,s.jsx)(r.sG.div,{"data-side":K,"data-align":J,...C,ref:L,style:{...C.style,animation:G?void 0:"none"}})})})});j.displayName=w;var R="PopperArrow",C={top:"bottom",right:"left",bottom:"top",left:"right"},S=a.forwardRef(function(e,t){let{__scopePopper:n,...a}=e,i=k(R,n),o=C[i.placedSide];return(0,s.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,s.jsx)(c,{...a,ref:t,style:{...a.style,display:"block"}})})});function A(e){return null!==e}S.displayName=R;var _=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:a,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,r=o?0:e.arrowWidth,s=o?0:e.arrowHeight,[c,l]=O(n),p={start:"0%",center:"50%",end:"100%"}[l],u=(i.arrow?.x??0)+r/2,d=(i.arrow?.y??0)+s/2,m="",f="";return"bottom"===c?(m=o?p:`${u}px`,f=`${-s}px`):"top"===c?(m=o?p:`${u}px`,f=`${a.floating.height+s}px`):"right"===c?(m=`${-s}px`,f=o?p:`${d}px`):"left"===c&&(m=`${a.floating.width+s}px`,f=o?p:`${d}px`),{data:{x:m,y:f}}}});function O(e){let[t,n="center"]=e.split("-");return[t,n]}var T=b,P=y,L=j,N=S},30461:e=>{"use strict";e.exports=Math.floor},30678:(e,t,n)=>{let a=n(83997),i=n(28354);t.init=function(e){e.inspectOpts={};let n=Object.keys(t.inspectOpts);for(let a=0;a<n.length;a++)e.inspectOpts[n[a]]=t.inspectOpts[n[a]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(n){let{namespace:a,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),o=`  ${i};1m${a} \u001B[0m`;n[0]=o+n[0].split("\n").join("\n"+o),n.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else n[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+a+" "+n[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:a.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=n(39228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let n=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),a=process.env[t];return a=!!/^(yes|on|true|enabled)$/i.test(a)||!/^(no|off|false|disabled)$/i.test(a)&&("null"===a?null:Number(a)),e[n]=a,e},{}),e.exports=n(96211)(t);let{formatters:o}=e.exports;o.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},o.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},31355:(e,t,n)=>{"use strict";n.d(t,{qW:()=>d});var a,i=n(43210),o=n(70569),r=n(3416),s=n(98599),c=n(13495),l=n(60687),p="dismissableLayer.update",u=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:d,onPointerDownOutside:x,onFocusOutside:v,onInteractOutside:h,onDismiss:b,...g}=e,y=i.useContext(u),[w,E]=i.useState(null),k=w?.ownerDocument??globalThis?.document,[,j]=i.useState({}),R=(0,s.s)(t,e=>E(e)),C=Array.from(y.layers),[S]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),A=C.indexOf(S),_=w?C.indexOf(w):-1,O=y.layersWithOutsidePointerEventsDisabled.size>0,T=_>=A,P=function(e,t=globalThis?.document){let n=(0,c.c)(e),a=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let a=function(){f("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);a.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...y.branches].some(e=>e.contains(t));!T||n||(x?.(e),h?.(e),e.defaultPrevented||b?.())},k),L=function(e,t=globalThis?.document){let n=(0,c.c)(e),a=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!a.current&&f("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...y.branches].some(e=>e.contains(t))||(v?.(e),h?.(e),e.defaultPrevented||b?.())},k);return function(e,t=globalThis?.document){let n=(0,c.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{_===y.layers.size-1&&(d?.(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},k),i.useEffect(()=>{if(w)return n&&(0===y.layersWithOutsidePointerEventsDisabled.size&&(a=k.body.style.pointerEvents,k.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(w)),y.layers.add(w),m(),()=>{n&&1===y.layersWithOutsidePointerEventsDisabled.size&&(k.body.style.pointerEvents=a)}},[w,k,n,y]),i.useEffect(()=>()=>{w&&(y.layers.delete(w),y.layersWithOutsidePointerEventsDisabled.delete(w),m())},[w,y]),i.useEffect(()=>{let e=()=>j({});return document.addEventListener(p,e),()=>document.removeEventListener(p,e)},[]),(0,l.jsx)(r.sG.div,{...g,ref:R,style:{pointerEvents:O?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,P.onPointerDownCapture)})});function m(){let e=new CustomEvent(p);document.dispatchEvent(e)}function f(e,t,n,{discrete:a}){let i=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),a?(0,r.hO)(i,o):i.dispatchEvent(o)}d.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(u),a=i.useRef(null),o=(0,s.s)(t,a);return i.useEffect(()=>{let e=a.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(r.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},32547:(e,t,n)=>{"use strict";n.d(t,{n:()=>u});var a=n(43210),i=n(98599),o=n(3416),r=n(13495),s=n(60687),c="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",p={bubbles:!1,cancelable:!0},u=a.forwardRef((e,t)=>{let{loop:n=!1,trapped:u=!1,onMountAutoFocus:v,onUnmountAutoFocus:h,...b}=e,[g,y]=a.useState(null),w=(0,r.c)(v),E=(0,r.c)(h),k=a.useRef(null),j=(0,i.s)(t,e=>y(e)),R=a.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;a.useEffect(()=>{if(u){let e=function(e){if(R.paused||!g)return;let t=e.target;g.contains(t)?k.current=t:f(k.current,{select:!0})},t=function(e){if(R.paused||!g)return;let t=e.relatedTarget;null===t||g.contains(t)||f(k.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&f(g)});return g&&n.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[u,g,R.paused]),a.useEffect(()=>{if(g){x.add(R);let e=document.activeElement;if(!g.contains(e)){let t=new CustomEvent(c,p);g.addEventListener(c,w),g.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let a of e)if(f(a,{select:t}),document.activeElement!==n)return}(d(g).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&f(g))}return()=>{g.removeEventListener(c,w),setTimeout(()=>{let t=new CustomEvent(l,p);g.addEventListener(l,E),g.dispatchEvent(t),t.defaultPrevented||f(e??document.body,{select:!0}),g.removeEventListener(l,E),x.remove(R)},0)}}},[g,w,E,R]);let C=a.useCallback(e=>{if(!n&&!u||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,a=document.activeElement;if(t&&a){let t=e.currentTarget,[i,o]=function(e){let t=d(e);return[m(t,e),m(t.reverse(),e)]}(t);i&&o?e.shiftKey||a!==o?e.shiftKey&&a===i&&(e.preventDefault(),n&&f(o,{select:!0})):(e.preventDefault(),n&&f(i,{select:!0})):a===t&&e.preventDefault()}},[n,u,R.paused]);return(0,s.jsx)(o.sG.div,{tabIndex:-1,...b,ref:j,onKeyDown:C})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function m(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function f(e,{select:t=!1}={}){if(e&&e.focus){var n;let a=document.activeElement;e.focus({preventScroll:!0}),e!==a&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}u.displayName="FocusScope";var x=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let n=[...e],a=n.indexOf(t);return -1!==a&&n.splice(a,1),n}},35836:(e,t,n)=>{var a=n(83644),i=n(28354),o=n(33873),r=n(81630),s=n(55591),c=n(79551).parse,l=n(29021),p=n(27910).Stream,u=n(95930),d=n(85026),m=n(78002),f=n(41425);function x(e){if(!(this instanceof x))return new x(e);for(var t in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],a.call(this),e=e||{})this[t]=e[t]}e.exports=x,i.inherits(x,a),x.LINE_BREAK="\r\n",x.DEFAULT_CONTENT_TYPE="application/octet-stream",x.prototype.append=function(e,t,n){"string"==typeof(n=n||{})&&(n={filename:n});var i=a.prototype.append.bind(this);if("number"==typeof t&&(t=""+t),Array.isArray(t)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,t,n),r=this._multiPartFooter();i(o),i(t),i(r),this._trackLength(o,t,n)},x.prototype._trackLength=function(e,t,n){var a=0;null!=n.knownLength?a+=+n.knownLength:Buffer.isBuffer(t)?a=t.length:"string"==typeof t&&(a=Buffer.byteLength(t)),this._valueLength+=a,this._overheadLength+=Buffer.byteLength(e)+x.LINE_BREAK.length,t&&(t.path||t.readable&&Object.prototype.hasOwnProperty.call(t,"httpVersion")||t instanceof p)&&(n.knownLength||this._valuesToMeasure.push(t))},x.prototype._lengthRetriever=function(e,t){Object.prototype.hasOwnProperty.call(e,"fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?t(null,e.end+1-(e.start?e.start:0)):l.stat(e.path,function(n,a){if(n){t(n);return}t(null,a.size-(e.start?e.start:0))}):Object.prototype.hasOwnProperty.call(e,"httpVersion")?t(null,+e.headers["content-length"]):Object.prototype.hasOwnProperty.call(e,"httpModule")?(e.on("response",function(n){e.pause(),t(null,+n.headers["content-length"])}),e.resume()):t("Unknown stream")},x.prototype._multiPartHeader=function(e,t,n){if("string"==typeof n.header)return n.header;var a,i=this._getContentDisposition(t,n),o=this._getContentType(t,n),r="",s={"Content-Disposition":["form-data",'name="'+e+'"'].concat(i||[]),"Content-Type":[].concat(o||[])};for(var c in"object"==typeof n.header&&f(s,n.header),s)if(Object.prototype.hasOwnProperty.call(s,c)){if(null==(a=s[c]))continue;Array.isArray(a)||(a=[a]),a.length&&(r+=c+": "+a.join("; ")+x.LINE_BREAK)}return"--"+this.getBoundary()+x.LINE_BREAK+r+x.LINE_BREAK},x.prototype._getContentDisposition=function(e,t){var n,a;return"string"==typeof t.filepath?n=o.normalize(t.filepath).replace(/\\/g,"/"):t.filename||e.name||e.path?n=o.basename(t.filename||e.name||e.path):e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(n=o.basename(e.client._httpMessage.path||"")),n&&(a='filename="'+n+'"'),a},x.prototype._getContentType=function(e,t){var n=t.contentType;return!n&&e.name&&(n=u.lookup(e.name)),!n&&e.path&&(n=u.lookup(e.path)),!n&&e.readable&&Object.prototype.hasOwnProperty.call(e,"httpVersion")&&(n=e.headers["content-type"]),!n&&(t.filepath||t.filename)&&(n=u.lookup(t.filepath||t.filename)),n||"object"!=typeof e||(n=x.DEFAULT_CONTENT_TYPE),n},x.prototype._multiPartFooter=function(){return(function(e){var t=x.LINE_BREAK;0===this._streams.length&&(t+=this._lastBoundary()),e(t)}).bind(this)},x.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+x.LINE_BREAK},x.prototype.getHeaders=function(e){var t,n={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[t.toLowerCase()]=e[t]);return n},x.prototype.setBoundary=function(e){this._boundary=e},x.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},x.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),t=this.getBoundary(),n=0,a=this._streams.length;n<a;n++)"function"!=typeof this._streams[n]&&(e=Buffer.isBuffer(this._streams[n])?Buffer.concat([e,this._streams[n]]):Buffer.concat([e,Buffer.from(this._streams[n])]),("string"!=typeof this._streams[n]||this._streams[n].substring(2,t.length+2)!==t)&&(e=Buffer.concat([e,Buffer.from(x.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},x.prototype._generateBoundary=function(){for(var e="--------------------------",t=0;t<24;t++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},x.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},x.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},x.prototype.getLength=function(e){var t=this._overheadLength+this._valueLength;if(this._streams.length&&(t+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,t));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(n,a){if(n){e(n);return}a.forEach(function(e){t+=e}),e(null,t)})},x.prototype.submit=function(e,t){var n,a,i={method:"post"};return"string"==typeof e?a=f({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},i):(a=f(e,i)).port||(a.port="https:"==a.protocol?443:80),a.headers=this.getHeaders(e.headers),n="https:"==a.protocol?s.request(a):r.request(a),this.getLength((function(e,a){if(e&&"Unknown stream"!==e){this._error(e);return}if(a&&n.setHeader("Content-Length",a),this.pipe(n),t){var i,o=function(e,a){return n.removeListener("error",o),n.removeListener("response",i),t.call(this,e,a)};i=o.bind(this,null),n.on("error",o),n.on("response",i)}}).bind(this)),n},x.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},x.prototype.toString=function(){return"[object FormData]"},m(x,"FormData")},36632:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let n="color: "+this.color;t.splice(1,0,n,"color: inherit");let a=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(a++,"%c"===e&&(i=a))}),t.splice(i,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")||t.storage.getItem("DEBUG")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(96211)(t);let{formatters:a}=e.exports;a.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},39228:(e,t,n)=>{"use strict";let a;let i=n(21820),o=n(83997),r=n(19207),{env:s}=process;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function l(e,t){if(0===a)return 0;if(r("color=16m")||r("color=full")||r("color=truecolor"))return 3;if(r("color=256"))return 2;if(e&&!t&&void 0===a)return 0;let n=a||0;if("dumb"===s.TERM)return n;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in s)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in s)||"codeship"===s.CI_NAME?1:n;if("TEAMCITY_VERSION"in s)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(s.TEAMCITY_VERSION);if("truecolor"===s.COLORTERM)return 3;if("TERM_PROGRAM"in s){let e=parseInt((s.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(s.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(s.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(s.TERM)||"COLORTERM"in s?1:n}r("no-color")||r("no-colors")||r("color=false")||r("color=never")?a=0:(r("color")||r("colors")||r("color=true")||r("color=always"))&&(a=1),"FORCE_COLOR"in s&&(a="true"===s.FORCE_COLOR?1:"false"===s.FORCE_COLOR?0:0===s.FORCE_COLOR.length?1:Math.min(parseInt(s.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(l(e,e&&e.isTTY))},stdout:c(l(!0,o.isatty(1))),stderr:c(l(!0,o.isatty(2)))}},39491:(e,t,n)=>{var a=n(79551),i=a.URL,o=n(81630),r=n(55591),s=n(27910).Writable,c=n(12412),l=n(92296);!function(){var e="undefined"!=typeof process,t="undefined"!=typeof window&&"undefined"!=typeof document,n=O(Error.captureStackTrace);e||!t&&n||console.warn("The follow-redirects package should be excluded from browser builds.")}();var p=!1;try{c(new i(""))}catch(e){p="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(t,n,a){this._redirectable.emit(e,t,n,a)}});var f=S("ERR_INVALID_URL","Invalid URL",TypeError),x=S("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),v=S("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",x),h=S("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=S("ERR_STREAM_WRITE_AFTER_END","write after end"),g=s.prototype.destroy||E;function y(e,t){s.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],t&&this.on("response",t);var n=this;this._onNativeResponse=function(e){try{n._processResponse(e)}catch(e){n.emit("error",e instanceof x?e:new x({cause:e}))}},this._performRequest()}function w(e){var t={maxRedirects:21,maxBodyLength:0xa00000},n={};return Object.keys(e).forEach(function(a){var o=a+":",r=n[o]=e[a],s=t[a]=Object.create(r);Object.defineProperties(s,{request:{value:function(e,a,r){var s;return(s=e,i&&s instanceof i)?e=R(e):_(e)?e=R(k(e)):(r=a,a=j(e),e={protocol:o}),O(a)&&(r=a,a=null),(a=Object.assign({maxRedirects:t.maxRedirects,maxBodyLength:t.maxBodyLength},e,a)).nativeProtocols=n,_(a.host)||_(a.hostname)||(a.hostname="::1"),c.equal(a.protocol,o,"protocol mismatch"),l("options",a),new y(a,r)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,t,n){var a=s.request(e,t,n);return a.end(),a},configurable:!0,enumerable:!0,writable:!0}})}),t}function E(){}function k(e){var t;if(p)t=new i(e);else if(!_((t=j(a.parse(e))).protocol))throw new f({input:e});return t}function j(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function R(e,t){var n=t||{};for(var a of u)n[a]=e[a];return n.hostname.startsWith("[")&&(n.hostname=n.hostname.slice(1,-1)),""!==n.port&&(n.port=Number(n.port)),n.path=n.search?n.pathname+n.search:n.pathname,n}function C(e,t){var n;for(var a in t)e.test(a)&&(n=t[a],delete t[a]);return null==n?void 0:String(n).trim()}function S(e,t,n){function a(n){O(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,n||{}),this.code=e,this.message=this.cause?t+": "+this.cause.message:t}return a.prototype=new(n||Error),Object.defineProperties(a.prototype,{constructor:{value:a,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),a}function A(e,t){for(var n of d)e.removeListener(n,m[n]);e.on("error",E),e.destroy(t)}function _(e){return"string"==typeof e||e instanceof String}function O(e){return"function"==typeof e}y.prototype=Object.create(s.prototype),y.prototype.abort=function(){A(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return A(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,t,n){var a;if(this._ending)throw new b;if(!_(e)&&!("object"==typeof(a=e)&&"length"in a))throw TypeError("data should be a string, Buffer or Uint8Array");if(O(t)&&(n=t,t=null),0===e.length){n&&n();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:t}),this._currentRequest.write(e,t,n)):(this.emit("error",new h),this.abort())},y.prototype.end=function(e,t,n){if(O(e)?(n=e,e=t=null):O(t)&&(n=t,t=null),e){var a=this,i=this._currentRequest;this.write(e,t,function(){a._ended=!0,i.end(null,null,n)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,n)},y.prototype.setHeader=function(e,t){this._options.headers[e]=t,this._currentRequest.setHeader(e,t)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,t){var n=this;function a(t){t.setTimeout(e),t.removeListener("timeout",t.destroy),t.addListener("timeout",t.destroy)}function i(t){n._timeout&&clearTimeout(n._timeout),n._timeout=setTimeout(function(){n.emit("timeout"),o()},e),a(t)}function o(){n._timeout&&(clearTimeout(n._timeout),n._timeout=null),n.removeListener("abort",o),n.removeListener("error",o),n.removeListener("response",o),n.removeListener("close",o),t&&n.removeListener("timeout",t),n.socket||n._currentRequest.removeListener("socket",i)}return t&&this.on("timeout",t),this.socket?i(this.socket):this._currentRequest.once("socket",i),this.on("socket",a),this.on("abort",o),this.on("error",o),this.on("response",o),this.on("close",o),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(t,n){return this._currentRequest[e](t,n)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var t=e.path.indexOf("?");t<0?e.pathname=e.path:(e.pathname=e.path.substring(0,t),e.search=e.path.substring(t))}},y.prototype._performRequest=function(){var e=this._options.protocol,t=this._options.nativeProtocols[e];if(!t)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var n=e.slice(0,-1);this._options.agent=this._options.agents[n]}var i=this._currentRequest=t.request(this._options,this._onNativeResponse);for(var o of(i._redirectable=this,d))i.on(o,m[o]);if(this._currentUrl=/^\//.test(this._options.path)?a.format(this._options):this._options.path,this._isRedirect){var r=0,s=this,c=this._requestBodyBuffers;!function e(t){if(i===s._currentRequest){if(t)s.emit("error",t);else if(r<c.length){var n=c[r++];i.finished||i.write(n.data,n.encoding,e)}else s._ended&&i.end()}}()}},y.prototype._processResponse=function(e){var t,n,o,r,s,u,d=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:d});var m=e.headers.location;if(!m||!1===this._options.followRedirects||d<300||d>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(A(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new v;var f=this._options.beforeRedirect;f&&(u=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var x=this._options.method;(301!==d&&302!==d||"POST"!==this._options.method)&&(303!==d||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],C(/^content-/i,this._options.headers));var h=C(/^host$/i,this._options.headers),b=k(this._currentUrl),g=h||b.host,y=/^\w+:/.test(m)?this._currentUrl:a.format(Object.assign(b,{host:g})),w=(t=m,n=y,p?new i(t,n):k(a.resolve(n,t)));if(l("redirecting to",w.href),this._isRedirect=!0,R(w,this._options),(w.protocol===b.protocol||"https:"===w.protocol)&&(w.host===g||(o=w.host,r=g,c(_(o)&&_(r)),(s=o.length-r.length-1)>0&&"."===o[s]&&o.endsWith(r)))||C(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),O(f)){var E={headers:e.headers,statusCode:d},j={url:y,method:x,headers:u};f(this._options,E,j),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:o,https:r}),e.exports.wrap=w},40083:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("log-out",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},41312:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41425:e=>{e.exports=function(e,t){return Object.keys(t).forEach(function(n){e[n]=e[n]||t[n]}),e}},41536:(e,t,n)=>{var a=n(94458),i=n(7932);e.exports=function(e,t,n,o){var r,s,c,l,p,u=n.keyedList?n.keyedList[n.index]:n.index;n.jobs[u]=(r=t,s=u,c=e[u],l=function(e,t){u in n.jobs&&(delete n.jobs[u],e?i(n):n.results[u]=t,o(e,n.results))},2==r.length?r(c,a(l)):r(c,s,a(l)))}},45793:(e,t,n)=>{var a=n(7932),i=n(94458);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,a(this),i(e)(null,this.results))}},46059:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});var a=n(43210),i=n(98599),o=n(66156),r=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[i,r]=a.useState(),c=a.useRef({}),l=a.useRef(e),p=a.useRef("none"),[u,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,t)=>n[e][t]??e,t));return a.useEffect(()=>{let e=s(c.current);p.current="mounted"===u?e:"none"},[u]),(0,o.N)(()=>{let t=c.current,n=l.current;if(n!==e){let a=p.current,i=s(t);e?d("MOUNT"):"none"===i||t?.display==="none"?d("UNMOUNT"):n&&a!==i?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),(0,o.N)(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,n=n=>{let a=s(c.current).includes(n.animationName);if(n.target===i&&a&&(d("ANIMATION_END"),!l.current)){let n=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=n)})}},a=e=>{e.target===i&&(p.current=s(c.current))};return i.addEventListener("animationstart",a),i.addEventListener("animationcancel",n),i.addEventListener("animationend",n),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",a),i.removeEventListener("animationcancel",n),i.removeEventListener("animationend",n)}}d("ANIMATION_END")},[i,d]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:a.useCallback(e=>{e&&(c.current=getComputedStyle(e)),r(e)},[])}}(t),c="function"==typeof n?n({present:r.isPresent}):a.Children.only(n),l=(0,i.s)(r.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(c));return"function"==typeof n||r.isPresent?a.cloneElement(c,{ref:l}):null};function s(e){return e?.animationName||"none"}r.displayName="Presence"},47530:e=>{"use strict";var t=Object.prototype.toString,n=Math.max,a=function(e,t){for(var n=[],a=0;a<e.length;a+=1)n[a]=e[a];for(var i=0;i<t.length;i+=1)n[i+e.length]=t[i];return n},i=function(e,t){for(var n=[],a=t||0,i=0;a<e.length;a+=1,i+=1)n[i]=e[a];return n},o=function(e,t){for(var n="",a=0;a<e.length;a+=1)n+=e[a],a+1<e.length&&(n+=t);return n};e.exports=function(e){var r,s=this;if("function"!=typeof s||"[object Function]"!==t.apply(s))throw TypeError("Function.prototype.bind called on incompatible "+s);for(var c=i(arguments,1),l=n(0,s.length-c.length),p=[],u=0;u<l;u++)p[u]="$"+u;if(r=Function("binder","return function ("+o(p,",")+"){ return binder.apply(this,arguments); }")(function(){if(this instanceof r){var t=s.apply(this,a(c,arguments));return Object(t)===t?t:this}return s.apply(e,a(c,arguments))}),s.prototype){var d=function(){};d.prototype=s.prototype,r.prototype=new d,d.prototype=null}return r}},48720:e=>{"use strict";var t=Object.defineProperty||!1;if(t)try{t({},"a",{value:1})}catch(e){t=!1}e.exports=t},49088:e=>{"use strict";e.exports=TypeError},49243:(e,t,n)=>{"use strict";var a=n(79551).parse,i={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},o=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function r(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}t.getProxyForUrl=function(e){var t,n,s,c="string"==typeof e?a(e):e||{},l=c.protocol,p=c.host,u=c.port;if("string"!=typeof p||!p||"string"!=typeof l)return"";if(l=l.split(":",1)[0],t=p=p.replace(/:\d*$/,""),n=u=parseInt(u)||i[l]||0,!(!(s=(r("npm_config_no_proxy")||r("no_proxy")).toLowerCase())||"*"!==s&&s.split(/[,\s]/).every(function(e){if(!e)return!0;var a=e.match(/^(.+):(\d+)$/),i=a?a[1]:e,r=a?parseInt(a[2]):0;return!!r&&r!==n||(/^[.*]/.test(i)?("*"===i.charAt(0)&&(i=i.slice(1)),!o.call(t,i)):t!==i)})))return"";var d=r("npm_config_"+l+"_proxy")||r(l+"_proxy")||r("npm_config_proxy")||r("all_proxy");return d&&-1===d.indexOf("://")&&(d=l+"://"+d),d}},49625:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},51060:(e,t,n)=>{"use strict";let a;n.d(t,{A:()=>tG});var i,o,r,s={};function c(e,t){return function(){return e.apply(t,arguments)}}n.r(s),n.d(s,{hasBrowserEnv:()=>ef,hasStandardBrowserEnv:()=>ev,hasStandardBrowserWebWorkerEnv:()=>eh,navigator:()=>ex,origin:()=>eb});let{toString:l}=Object.prototype,{getPrototypeOf:p}=Object,{iterator:u,toStringTag:d}=Symbol,m=(e=>t=>{let n=l.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),f=e=>(e=e.toLowerCase(),t=>m(t)===e),x=e=>t=>typeof t===e,{isArray:v}=Array,h=x("undefined"),b=f("ArrayBuffer"),g=x("string"),y=x("function"),w=x("number"),E=e=>null!==e&&"object"==typeof e,k=e=>{if("object"!==m(e))return!1;let t=p(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(d in e)&&!(u in e)},j=f("Date"),R=f("File"),C=f("Blob"),S=f("FileList"),A=f("URLSearchParams"),[_,O,T,P]=["ReadableStream","Request","Response","Headers"].map(f);function L(e,t,{allOwnKeys:n=!1}={}){let a,i;if(null!=e){if("object"!=typeof e&&(e=[e]),v(e))for(a=0,i=e.length;a<i;a++)t.call(null,e[a],a,e);else{let i;let o=n?Object.getOwnPropertyNames(e):Object.keys(e),r=o.length;for(a=0;a<r;a++)i=o[a],t.call(null,e[i],i,e)}}}function N(e,t){let n;t=t.toLowerCase();let a=Object.keys(e),i=a.length;for(;i-- >0;)if(t===(n=a[i]).toLowerCase())return n;return null}let F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,D=e=>!h(e)&&e!==F,M=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&p(Uint8Array)),I=f("HTMLFormElement"),B=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),z=f("RegExp"),U=(e,t)=>{let n=Object.getOwnPropertyDescriptors(e),a={};L(n,(n,i)=>{let o;!1!==(o=t(n,i,e))&&(a[i]=o||n)}),Object.defineProperties(e,a)},q=f("AsyncFunction"),W=(i="function"==typeof setImmediate,o=y(F.postMessage),i?setImmediate:o?((e,t)=>(F.addEventListener("message",({source:n,data:a})=>{n===F&&a===e&&t.length&&t.shift()()},!1),n=>{t.push(n),F.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),H="undefined"!=typeof queueMicrotask?queueMicrotask.bind(F):"undefined"!=typeof process&&process.nextTick||W,$={isArray:v,isArrayBuffer:b,isBuffer:function(e){return null!==e&&!h(e)&&null!==e.constructor&&!h(e.constructor)&&y(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||y(e.append)&&("formdata"===(t=m(e))||"object"===t&&y(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&b(e.buffer)},isString:g,isNumber:w,isBoolean:e=>!0===e||!1===e,isObject:E,isPlainObject:k,isReadableStream:_,isRequest:O,isResponse:T,isHeaders:P,isUndefined:h,isDate:j,isFile:R,isBlob:C,isRegExp:z,isFunction:y,isStream:e=>E(e)&&y(e.pipe),isURLSearchParams:A,isTypedArray:M,isFileList:S,forEach:L,merge:function e(){let{caseless:t}=D(this)&&this||{},n={},a=(a,i)=>{let o=t&&N(n,i)||i;k(n[o])&&k(a)?n[o]=e(n[o],a):k(a)?n[o]=e({},a):v(a)?n[o]=a.slice():n[o]=a};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&L(arguments[e],a);return n},extend:(e,t,n,{allOwnKeys:a}={})=>(L(t,(t,a)=>{n&&y(t)?e[a]=c(t,n):e[a]=t},{allOwnKeys:a}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,a)=>{e.prototype=Object.create(t.prototype,a),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,a)=>{let i,o,r;let s={};if(t=t||{},null==e)return t;do{for(o=(i=Object.getOwnPropertyNames(e)).length;o-- >0;)r=i[o],(!a||a(r,e,t))&&!s[r]&&(t[r]=e[r],s[r]=!0);e=!1!==n&&p(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:m,kindOfTest:f,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;let a=e.indexOf(t,n);return -1!==a&&a===n},toArray:e=>{if(!e)return null;if(v(e))return e;let t=e.length;if(!w(t))return null;let n=Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{let n;let a=(e&&e[u]).call(e);for(;(n=a.next())&&!n.done;){let a=n.value;t.call(e,a[0],a[1])}},matchAll:(e,t)=>{let n;let a=[];for(;null!==(n=e.exec(t));)a.push(n);return a},isHTMLForm:I,hasOwnProperty:B,hasOwnProp:B,reduceDescriptors:U,freezeMethods:e=>{U(e,(t,n)=>{if(y(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;if(y(e[n])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},toObjectSet:(e,t)=>{let n={};return(e=>{e.forEach(e=>{n[e]=!0})})(v(e)?e:String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e*=1)?e:t,findKey:N,global:F,isContextDefined:D,isSpecCompliantForm:function(e){return!!(e&&y(e.append)&&"FormData"===e[d]&&e[u])},toJSONObject:e=>{let t=Array(10),n=(e,a)=>{if(E(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[a]=e;let i=v(e)?[]:{};return L(e,(e,t)=>{let o=n(e,a+1);h(o)||(i[t]=o)}),t[a]=void 0,i}}return e};return n(e,0)},isAsyncFn:q,isThenable:e=>e&&(E(e)||y(e))&&y(e.then)&&y(e.catch),setImmediate:W,asap:H,isIterable:e=>null!=e&&y(e[u])};function G(e,t,n,a,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),a&&(this.request=a),i&&(this.response=i,this.status=i.status?i.status:null)}$.inherits(G,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:$.toJSONObject(this.config),code:this.code,status:this.status}}});let V=G.prototype,K={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{K[e]={value:e}}),Object.defineProperties(G,K),Object.defineProperty(V,"isAxiosError",{value:!0}),G.from=(e,t,n,a,i,o)=>{let r=Object.create(V);return $.toFlatObject(e,r,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),G.call(r,e.message,t,n,a,i),r.cause=e,r.name=e.name,o&&Object.assign(r,o),r};var J=n(35836);function Y(e){return $.isPlainObject(e)||$.isArray(e)}function X(e){return $.endsWith(e,"[]")?e.slice(0,-2):e}function Z(e,t,n){return e?e.concat(t).map(function(e,t){return e=X(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}let Q=$.toFlatObject($,{},null,function(e){return/^is[A-Z]/.test(e)}),ee=function(e,t,n){if(!$.isObject(e))throw TypeError("target must be an object");t=t||new(J||FormData);let a=(n=$.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!$.isUndefined(t[e])})).metaTokens,i=n.visitor||l,o=n.dots,r=n.indexes,s=(n.Blob||"undefined"!=typeof Blob&&Blob)&&$.isSpecCompliantForm(t);if(!$.isFunction(i))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if($.isDate(e))return e.toISOString();if(!s&&$.isBlob(e))throw new G("Blob is not supported. Use a Buffer instead.");return $.isArrayBuffer(e)||$.isTypedArray(e)?s&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,i){let s=e;if(e&&!i&&"object"==typeof e){if($.endsWith(n,"{}"))n=a?n:n.slice(0,-2),e=JSON.stringify(e);else{var l;if($.isArray(e)&&(l=e,$.isArray(l)&&!l.some(Y))||($.isFileList(e)||$.endsWith(n,"[]"))&&(s=$.toArray(e)))return n=X(n),s.forEach(function(e,a){$.isUndefined(e)||null===e||t.append(!0===r?Z([n],a,o):null===r?n:n+"[]",c(e))}),!1}}return!!Y(e)||(t.append(Z(i,n,o),c(e)),!1)}let p=[],u=Object.assign(Q,{defaultVisitor:l,convertValue:c,isVisitable:Y});if(!$.isObject(e))throw TypeError("data must be an object");return function e(n,a){if(!$.isUndefined(n)){if(-1!==p.indexOf(n))throw Error("Circular reference detected in "+a.join("."));p.push(n),$.forEach(n,function(n,o){!0===(!($.isUndefined(n)||null===n)&&i.call(t,n,$.isString(o)?o.trim():o,a,u))&&e(n,a?a.concat(o):[o])}),p.pop()}}(e),t};function et(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function en(e,t){this._pairs=[],e&&ee(e,this,t)}let ea=en.prototype;function ei(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eo(e,t,n){let a;if(!t)return e;let i=n&&n.encode||ei;$.isFunction(n)&&(n={serialize:n});let o=n&&n.serialize;if(a=o?o(t,n):$.isURLSearchParams(t)?t.toString():new en(t,n).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}ea.append=function(e,t){this._pairs.push([e,t])},ea.toString=function(e){let t=e?function(t){return e.call(this,t,et)}:et;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class er{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){$.forEach(this.handlers,function(t){null!==t&&e(t)})}}let es={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1};var ec=n(55511);let el=n(79551).URLSearchParams,ep="abcdefghijklmnopqrstuvwxyz",eu="0123456789",ed={DIGIT:eu,ALPHA:ep,ALPHA_DIGIT:ep+ep.toUpperCase()+eu},em={isNode:!0,classes:{URLSearchParams:el,FormData:J,Blob:"undefined"!=typeof Blob&&Blob||null},ALPHABET:ed,generateString:(e=16,t=ed.ALPHA_DIGIT)=>{let n="",{length:a}=t,i=new Uint32Array(e);ec.randomFillSync(i);for(let o=0;o<e;o++)n+=t[i[o]%a];return n},protocols:["http","https","file","data"]},ef="undefined"!=typeof window&&"undefined"!=typeof document,ex="object"==typeof navigator&&navigator||void 0,ev=ef&&(!ex||0>["ReactNative","NativeScript","NS"].indexOf(ex.product)),eh="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eb=ef&&window.location.href||"http://localhost",eg={...s,...em},ey=function(e){if($.isFormData(e)&&$.isFunction(e.entries)){let t={};return $.forEachEntry(e,(e,n)=>{!function e(t,n,a,i){let o=t[i++];if("__proto__"===o)return!0;let r=Number.isFinite(+o),s=i>=t.length;return(o=!o&&$.isArray(a)?a.length:o,s)?$.hasOwnProp(a,o)?a[o]=[a[o],n]:a[o]=n:(a[o]&&$.isObject(a[o])||(a[o]=[]),e(t,n,a[o],i)&&$.isArray(a[o])&&(a[o]=function(e){let t,n;let a={},i=Object.keys(e),o=i.length;for(t=0;t<o;t++)a[n=i[t]]=e[n];return a}(a[o]))),!r}($.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),n,t,0)}),t}return null},ew={transitional:es,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let n;let a=t.getContentType()||"",i=a.indexOf("application/json")>-1,o=$.isObject(e);if(o&&$.isHTMLForm(e)&&(e=new FormData(e)),$.isFormData(e))return i?JSON.stringify(ey(e)):e;if($.isArrayBuffer(e)||$.isBuffer(e)||$.isStream(e)||$.isFile(e)||$.isBlob(e)||$.isReadableStream(e))return e;if($.isArrayBufferView(e))return e.buffer;if($.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(a.indexOf("application/x-www-form-urlencoded")>-1){var r,s;return(r=e,s=this.formSerializer,ee(r,new eg.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,a){return eg.isNode&&$.isBuffer(e)?(this.append(t,e.toString("base64")),!1):a.defaultVisitor.apply(this,arguments)}},s))).toString()}if((n=$.isFileList(e))||a.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return ee(n?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||i?(t.setContentType("application/json",!1),function(e,t,n){if($.isString(e))try{return(0,JSON.parse)(e),$.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||ew.transitional,n=t&&t.forcedJSONParsing,a="json"===this.responseType;if($.isResponse(e)||$.isReadableStream(e))return e;if(e&&$.isString(e)&&(n&&!this.responseType||a)){let n=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!n&&a){if("SyntaxError"===e.name)throw G.from(e,G.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eg.classes.FormData,Blob:eg.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};$.forEach(["delete","get","head","post","put","patch"],e=>{ew.headers[e]={}});let eE=$.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ek=e=>{let t,n,a;let i={};return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),t=e.substring(0,a).trim().toLowerCase(),n=e.substring(a+1).trim(),t&&(!i[t]||!eE[t])&&("set-cookie"===t?i[t]?i[t].push(n):i[t]=[n]:i[t]=i[t]?i[t]+", "+n:n)}),i},ej=Symbol("internals");function eR(e){return e&&String(e).trim().toLowerCase()}function eC(e){return!1===e||null==e?e:$.isArray(e)?e.map(eC):String(e)}let eS=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eA(e,t,n,a,i){if($.isFunction(a))return a.call(this,t,n);if(i&&(t=n),$.isString(t)){if($.isString(a))return -1!==t.indexOf(a);if($.isRegExp(a))return a.test(t)}}class e_{constructor(e){e&&this.set(e)}set(e,t,n){let a=this;function i(e,t,n){let i=eR(t);if(!i)throw Error("header name must be a non-empty string");let o=$.findKey(a,i);o&&void 0!==a[o]&&!0!==n&&(void 0!==n||!1===a[o])||(a[o||t]=eC(e))}let o=(e,t)=>$.forEach(e,(e,n)=>i(e,n,t));if($.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if($.isString(e)&&(e=e.trim())&&!eS(e))o(ek(e),t);else if($.isObject(e)&&$.isIterable(e)){let n={},a,i;for(let t of e){if(!$.isArray(t))throw TypeError("Object iterator must return a key-value pair");n[i=t[0]]=(a=n[i])?$.isArray(a)?[...a,t[1]]:[a,t[1]]:t[1]}o(n,t)}else null!=e&&i(t,e,n);return this}get(e,t){if(e=eR(e)){let n=$.findKey(this,e);if(n){let e=this[n];if(!t)return e;if(!0===t)return function(e){let t;let n=Object.create(null),a=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=a.exec(e);)n[t[1]]=t[2];return n}(e);if($.isFunction(t))return t.call(this,e,n);if($.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=eR(e)){let n=$.findKey(this,e);return!!(n&&void 0!==this[n]&&(!t||eA(this,this[n],n,t)))}return!1}delete(e,t){let n=this,a=!1;function i(e){if(e=eR(e)){let i=$.findKey(n,e);i&&(!t||eA(n,n[i],i,t))&&(delete n[i],a=!0)}}return $.isArray(e)?e.forEach(i):i(e),a}clear(e){let t=Object.keys(this),n=t.length,a=!1;for(;n--;){let i=t[n];(!e||eA(this,this[i],i,e,!0))&&(delete this[i],a=!0)}return a}normalize(e){let t=this,n={};return $.forEach(this,(a,i)=>{let o=$.findKey(n,i);if(o){t[o]=eC(a),delete t[i];return}let r=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n):String(i).trim();r!==i&&delete t[i],t[r]=eC(a),n[r]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return $.forEach(this,(n,a)=>{null!=n&&!1!==n&&(t[a]=e&&$.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let n=new this(e);return t.forEach(e=>n.set(e)),n}static accessor(e){let t=(this[ej]=this[ej]={accessors:{}}).accessors,n=this.prototype;function a(e){let a=eR(e);t[a]||(function(e,t){let n=$.toCamelCase(" "+t);["get","set","has"].forEach(a=>{Object.defineProperty(e,a+n,{value:function(e,n,i){return this[a].call(this,t,e,n,i)},configurable:!0})})}(n,e),t[a]=!0)}return $.isArray(e)?e.forEach(a):a(e),this}}function eO(e,t){let n=this||ew,a=t||n,i=e_.from(a.headers),o=a.data;return $.forEach(e,function(e){o=e.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function eT(e){return!!(e&&e.__CANCEL__)}function eP(e,t,n){G.call(this,null==e?"canceled":e,G.ERR_CANCELED,t,n),this.name="CanceledError"}function eL(e,t,n){let a=n.config.validateStatus;!n.status||!a||a(n.status)?e(n):t(new G("Request failed with status code "+n.status,[G.ERR_BAD_REQUEST,G.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function eN(e,t,n){let a=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(a||!1==n)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}e_.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),$.reduceDescriptors(e_.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}}),$.freezeMethods(e_),$.inherits(eP,G,{__CANCEL__:!0});var eF=n(49243),eD=n(81630),eM=n(55591),eI=n(28354),eB=n(39491),ez=n(74075);let eU="1.9.0";function eq(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}let eW=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var eH=n(27910);let e$=Symbol("internals");class eG extends eH.Transform{constructor(e){super({readableHighWaterMark:(e=$.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,t)=>!$.isUndefined(t[e]))).chunkSize});let t=this[e$]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||t.isCaptured||(t.isCaptured=!0)})}_read(e){let t=this[e$];return t.onReadCallback&&t.onReadCallback(),super._read(e)}_transform(e,t,n){let a=this[e$],i=a.maxRate,o=this.readableHighWaterMark,r=a.timeWindow,s=i/(1e3/r),c=!1!==a.minChunkSize?Math.max(a.minChunkSize,.01*s):0,l=(e,t)=>{let n=Buffer.byteLength(e);a.bytesSeen+=n,a.bytes+=n,a.isCaptured&&this.emit("progress",a.bytesSeen),this.push(e)?process.nextTick(t):a.onReadCallback=()=>{a.onReadCallback=null,process.nextTick(t)}},p=(e,t)=>{let n;let p=Buffer.byteLength(e),u=null,d=o,m=0;if(i){let e=Date.now();(!a.ts||(m=e-a.ts)>=r)&&(a.ts=e,n=s-a.bytes,a.bytes=n<0?-n:0,m=0),n=s-a.bytes}if(i){if(n<=0)return setTimeout(()=>{t(null,e)},r-m);n<d&&(d=n)}d&&p>d&&p-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),l(e,u?()=>{process.nextTick(t,null,u)}:t)};p(e,function e(t,a){if(t)return n(t);a?p(a,e):n(null)})}}var eV=n(94735);let{asyncIterator:eK}=Symbol,eJ=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[eK]?yield*e[eK]():yield e},eY=eg.ALPHABET.ALPHA_DIGIT+"-_",eX="function"==typeof TextEncoder?new TextEncoder:new eI.TextEncoder,eZ=eX.encode("\r\n");class eQ{constructor(e,t){let{escapeName:n}=this.constructor,a=$.isString(t),i=`Content-Disposition: form-data; name="${n(e)}"${!a&&t.name?`; filename="${n(t.name)}"`:""}\r
`;a?t=eX.encode(String(t).replace(/\r?\n|\r\n?/g,"\r\n")):i+=`Content-Type: ${t.type||"application/octet-stream"}\r
`,this.headers=eX.encode(i+"\r\n"),this.contentLength=a?t.byteLength:t.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=t}async *encode(){yield this.headers;let{value:e}=this;$.isTypedArray(e)?yield e:yield*eJ(e),yield eZ}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e0=(e,t,n)=>{let{tag:a="form-data-boundary",size:i=25,boundary:o=a+"-"+eg.generateString(i,eY)}=n||{};if(!$.isFormData(e))throw TypeError("FormData instance required");if(o.length<1||o.length>70)throw Error("boundary must be 10-70 characters long");let r=eX.encode("--"+o+"\r\n"),s=eX.encode("--"+o+"--\r\n"),c=s.byteLength,l=Array.from(e.entries()).map(([e,t])=>{let n=new eQ(e,t);return c+=n.size,n});c+=r.byteLength*l.length;let p={"Content-Type":`multipart/form-data; boundary=${o}`};return Number.isFinite(c=$.toFiniteNumber(c))&&(p["Content-Length"]=c),t&&t(p),eH.Readable.from(async function*(){for(let e of l)yield r,yield*e.encode();yield s}())};class e1 extends eH.Transform{__transform(e,t,n){this.push(e),n()}_transform(e,t,n){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,t)}this.__transform(e,t,n)}}let e2=(e,t)=>$.isAsyncFn(e)?function(...n){let a=n.pop();e.apply(this,n).then(e=>{try{t?a(null,...t(e)):a(null,e)}catch(e){a(e)}},a)}:e,e3=function(e,t){let n;let a=Array(e=e||10),i=Array(e),o=0,r=0;return t=void 0!==t?t:1e3,function(s){let c=Date.now(),l=i[r];n||(n=c),a[o]=s,i[o]=c;let p=r,u=0;for(;p!==o;)u+=a[p++],p%=e;if((o=(o+1)%e)===r&&(r=(r+1)%e),c-n<t)return;let d=l&&c-l;return d?Math.round(1e3*u/d):void 0}},e6=function(e,t){let n,a,i=0,o=1e3/t,r=(t,o=Date.now())=>{i=o,n=null,a&&(clearTimeout(a),a=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),s=t-i;s>=o?r(e,t):(n=e,a||(a=setTimeout(()=>{a=null,r(n)},o-s)))},()=>n&&r(n)]},e4=(e,t,n=3)=>{let a=0,i=e3(50,250);return e6(n=>{let o=n.loaded,r=n.lengthComputable?n.total:void 0,s=o-a,c=i(s);a=o,e({loaded:o,total:r,progress:r?o/r:void 0,bytes:s,rate:c||void 0,estimated:c&&r&&o<=r?(r-o)/c:void 0,event:n,lengthComputable:null!=r,[t?"download":"upload"]:!0})},n)},e9=(e,t)=>{let n=null!=e;return[a=>t[0]({lengthComputable:n,total:e,loaded:a}),t[1]]},e5=e=>(...t)=>$.asap(()=>e(...t)),e8={flush:ez.constants.Z_SYNC_FLUSH,finishFlush:ez.constants.Z_SYNC_FLUSH},e7={flush:ez.constants.BROTLI_OPERATION_FLUSH,finishFlush:ez.constants.BROTLI_OPERATION_FLUSH},te=$.isFunction(ez.createBrotliDecompress),{http:tt,https:tn}=eB,ta=/https:?/,ti=eg.protocols.map(e=>e+":"),to=(e,[t,n])=>(e.on("end",n).on("error",n),t);function tr(e,t){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,t)}let ts="undefined"!=typeof process&&"process"===$.kindOf(process),tc=e=>new Promise((t,n)=>{let a,i;let o=(e,t)=>{!i&&(i=!0,a&&a(e,t))},r=e=>{o(e,!0),n(e)};e(e=>{o(e),t(e)},r,e=>a=e).catch(r)}),tl=({address:e,family:t})=>{if(!$.isString(e))throw TypeError("address must be a string");return{address:e,family:t||(0>e.indexOf(".")?6:4)}},tp=(e,t)=>tl($.isObject(e)?e:{address:e,family:t}),tu=ts&&function(e){return tc(async function(t,n,a){let i,o,r,s,c,l,p,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:x}=e,v=e.method.toUpperCase(),h=!1;if(d){let e=e2(d,e=>$.isArray(e)?e:[e]);d=(t,n,a)=>{e(t,n,(e,t,i)=>{if(e)return a(e);let o=$.isArray(t)?t.map(e=>tp(e)):[tp(t,i)];n.all?a(e,o):a(e,o[0].address,o[0].family)})}}let b=new eV.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(t){b.emit("abort",!t||t.type?new eP(null,e,c):t)}a((e,t)=>{s=!0,t&&(h=!0,g())}),b.once("abort",n),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let w=new URL(eN(e.baseURL,e.url,e.allowAbsoluteUrls),eg.hasBrowserEnv?eg.origin:void 0),E=w.protocol||ti[0];if("data:"===E){let a;if("GET"!==v)return eL(t,n,{status:405,statusText:"method not allowed",headers:{},config:e});try{a=function(e,t,n){let a=n&&n.Blob||eg.classes.Blob,i=eq(e);if(void 0===t&&a&&(t=!0),"data"===i){e=i.length?e.slice(i.length+1):e;let n=eW.exec(e);if(!n)throw new G("Invalid URL",G.ERR_INVALID_URL);let o=n[1],r=n[2],s=n[3],c=Buffer.from(decodeURIComponent(s),r?"base64":"utf8");if(t){if(!a)throw new G("Blob is not supported",G.ERR_NOT_SUPPORT);return new a([c],{type:o})}return c}throw new G("Unsupported protocol "+i,G.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(t){throw G.from(t,G.ERR_BAD_REQUEST,e)}return"text"===f?(a=a.toString(x),x&&"utf8"!==x||(a=$.stripBOM(a))):"stream"===f&&(a=eH.Readable.from(a)),eL(t,n,{data:a,status:200,statusText:"OK",headers:new e_,config:e})}if(-1===ti.indexOf(E))return n(new G("Unsupported protocol "+E,G.ERR_BAD_REQUEST,e));let k=e_.from(e.headers).normalize();k.set("User-Agent","axios/"+eU,!1);let{onUploadProgress:j,onDownloadProgress:R}=e,C=e.maxRate;if($.isSpecCompliantForm(u)){let e=k.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e0(u,e=>{k.set(e)},{tag:`axios-${eU}-boundary`,boundary:e&&e[1]||void 0})}else if($.isFormData(u)&&$.isFunction(u.getHeaders)){if(k.set(u.getHeaders()),!k.hasContentLength())try{let e=await eI.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&k.setContentLength(e)}catch(e){}}else if($.isBlob(u)||$.isFile(u))u.size&&k.setContentType(u.type||"application/octet-stream"),k.setContentLength(u.size||0),u=eH.Readable.from(eJ(u));else if(u&&!$.isStream(u)){if(Buffer.isBuffer(u));else if($.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!$.isString(u))return n(new G("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",G.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(k.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return n(new G("Request body larger than maxBodyLength limit",G.ERR_BAD_REQUEST,e))}let S=$.toFiniteNumber(k.getContentLength());$.isArray(C)?(i=C[0],o=C[1]):i=o=C,u&&(j||i)&&($.isStream(u)||(u=eH.Readable.from(u,{objectMode:!1})),u=eH.pipeline([u,new eG({maxRate:$.toFiniteNumber(i)})],$.noop),j&&u.on("progress",to(u,e9(S,e4(e5(j),!1,3))))),e.auth&&(r=(e.auth.username||"")+":"+(e.auth.password||"")),!r&&w.username&&(r=w.username+":"+w.password),r&&k.delete("authorization");try{l=eo(w.pathname+w.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(a){let t=Error(a.message);return t.config=e,t.url=e.url,t.exists=!0,n(t)}k.set("Accept-Encoding","gzip, compress, deflate"+(te?", br":""),!1);let A={path:l,method:v,headers:k.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:r,protocol:E,family:m,beforeRedirect:tr,beforeRedirects:{}};$.isUndefined(d)||(A.lookup=d),e.socketPath?A.socketPath=e.socketPath:(A.hostname=w.hostname.startsWith("[")?w.hostname.slice(1,-1):w.hostname,A.port=w.port,function e(t,n,a){let i=n;if(!i&&!1!==i){let e=eF.getProxyForUrl(a);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));let e=Buffer.from(i.auth,"utf8").toString("base64");t.headers["Proxy-Authorization"]="Basic "+e}t.headers.host=t.hostname+(t.port?":"+t.port:"");let e=i.hostname||i.host;t.hostname=e,t.host=e,t.port=i.port,t.path=a,i.protocol&&(t.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}t.beforeRedirects.proxy=function(t){e(t,n,t.href)}}(A,e.proxy,E+"//"+w.hostname+(w.port?":"+w.port:"")+A.path));let _=ta.test(A.protocol);if(A.agent=_?e.httpsAgent:e.httpAgent,e.transport?p=e.transport:0===e.maxRedirects?p=_?eM:eD:(e.maxRedirects&&(A.maxRedirects=e.maxRedirects),e.beforeRedirect&&(A.beforeRedirects.config=e.beforeRedirect),p=_?tn:tt),e.maxBodyLength>-1?A.maxBodyLength=e.maxBodyLength:A.maxBodyLength=1/0,e.insecureHTTPParser&&(A.insecureHTTPParser=e.insecureHTTPParser),c=p.request(A,function(a){if(c.destroyed)return;let i=[a],r=+a.headers["content-length"];if(R||o){let e=new eG({maxRate:$.toFiniteNumber(o)});R&&e.on("progress",to(e,e9(r,e4(e5(R),!0,3)))),i.push(e)}let s=a,l=a.req||c;if(!1!==e.decompress&&a.headers["content-encoding"])switch(("HEAD"===v||204===a.statusCode)&&delete a.headers["content-encoding"],(a.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":i.push(ez.createUnzip(e8)),delete a.headers["content-encoding"];break;case"deflate":i.push(new e1),i.push(ez.createUnzip(e8)),delete a.headers["content-encoding"];break;case"br":te&&(i.push(ez.createBrotliDecompress(e7)),delete a.headers["content-encoding"])}s=i.length>1?eH.pipeline(i,$.noop):i[0];let p=eH.finished(s,()=>{p(),g()}),u={status:a.statusCode,statusText:a.statusMessage,headers:new e_(a.headers),config:e,request:l};if("stream"===f)u.data=s,eL(t,n,u);else{let a=[],i=0;s.on("data",function(t){a.push(t),i+=t.length,e.maxContentLength>-1&&i>e.maxContentLength&&(h=!0,s.destroy(),n(new G("maxContentLength size of "+e.maxContentLength+" exceeded",G.ERR_BAD_RESPONSE,e,l)))}),s.on("aborted",function(){if(h)return;let t=new G("stream has been aborted",G.ERR_BAD_RESPONSE,e,l);s.destroy(t),n(t)}),s.on("error",function(t){c.destroyed||n(G.from(t,null,e,l))}),s.on("end",function(){try{let e=1===a.length?a[0]:Buffer.concat(a);"arraybuffer"===f||(e=e.toString(x),x&&"utf8"!==x||(e=$.stripBOM(e))),u.data=e}catch(t){return n(G.from(t,null,e,u.request,u))}eL(t,n,u)})}b.once("abort",e=>{s.destroyed||(s.emit("error",e),s.destroy())})}),b.once("abort",e=>{n(e),c.destroy(e)}),c.on("error",function(t){n(G.from(t,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let t=parseInt(e.timeout,10);if(Number.isNaN(t)){n(new G("error trying to parse `config.timeout` to int",G.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(t,function(){if(s)return;let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",a=e.transitional||es;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new G(t,a.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,c)),y()})}if($.isStream(u)){let t=!1,n=!1;u.on("end",()=>{t=!0}),u.once("error",e=>{n=!0,c.destroy(e)}),u.on("close",()=>{t||n||y(new eP("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},td=eg.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,eg.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(eg.origin),eg.navigator&&/(msie|trident)/i.test(eg.navigator.userAgent)):()=>!0,tm=eg.hasStandardBrowserEnv?{write(e,t,n,a,i,o){let r=[e+"="+encodeURIComponent(t)];$.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),$.isString(a)&&r.push("path="+a),$.isString(i)&&r.push("domain="+i),!0===o&&r.push("secure"),document.cookie=r.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},tf=e=>e instanceof e_?{...e}:e;function tx(e,t){t=t||{};let n={};function a(e,t,n,a){return $.isPlainObject(e)&&$.isPlainObject(t)?$.merge.call({caseless:a},e,t):$.isPlainObject(t)?$.merge({},t):$.isArray(t)?t.slice():t}function i(e,t,n,i){return $.isUndefined(t)?$.isUndefined(e)?void 0:a(void 0,e,n,i):a(e,t,n,i)}function o(e,t){if(!$.isUndefined(t))return a(void 0,t)}function r(e,t){return $.isUndefined(t)?$.isUndefined(e)?void 0:a(void 0,e):a(void 0,t)}function s(n,i,o){return o in t?a(n,i):o in e?a(void 0,n):void 0}let c={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:s,headers:(e,t,n)=>i(tf(e),tf(t),n,!0)};return $.forEach(Object.keys(Object.assign({},e,t)),function(a){let o=c[a]||i,r=o(e[a],t[a],a);$.isUndefined(r)&&o!==s||(n[a]=r)}),n}let tv=e=>{let t;let n=tx({},e),{data:a,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:r,headers:s,auth:c}=n;if(n.headers=s=e_.from(s),n.url=eo(eN(n.baseURL,n.url,n.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&s.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),$.isFormData(a)){if(eg.hasStandardBrowserEnv||eg.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(t=s.getContentType())){let[e,...n]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...n].join("; "))}}if(eg.hasStandardBrowserEnv&&(i&&$.isFunction(i)&&(i=i(n)),i||!1!==i&&td(n.url))){let e=o&&r&&tm.read(r);e&&s.set(o,e)}return n},th="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){let a,i,o,r,s;let c=tv(e),l=c.data,p=e_.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){r&&r(),s&&s(),c.cancelToken&&c.cancelToken.unsubscribe(a),c.signal&&c.signal.removeEventListener("abort",a)}let x=new XMLHttpRequest;function v(){if(!x)return;let a=e_.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders());eL(function(e){t(e),f()},function(e){n(e),f()},{data:u&&"text"!==u&&"json"!==u?x.response:x.responseText,status:x.status,statusText:x.statusText,headers:a,config:e,request:x}),x=null}x.open(c.method.toUpperCase(),c.url,!0),x.timeout=c.timeout,"onloadend"in x?x.onloadend=v:x.onreadystatechange=function(){x&&4===x.readyState&&(0!==x.status||x.responseURL&&0===x.responseURL.indexOf("file:"))&&setTimeout(v)},x.onabort=function(){x&&(n(new G("Request aborted",G.ECONNABORTED,e,x)),x=null)},x.onerror=function(){n(new G("Network Error",G.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let t=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",a=c.transitional||es;c.timeoutErrorMessage&&(t=c.timeoutErrorMessage),n(new G(t,a.clarifyTimeoutError?G.ETIMEDOUT:G.ECONNABORTED,e,x)),x=null},void 0===l&&p.setContentType(null),"setRequestHeader"in x&&$.forEach(p.toJSON(),function(e,t){x.setRequestHeader(t,e)}),$.isUndefined(c.withCredentials)||(x.withCredentials=!!c.withCredentials),u&&"json"!==u&&(x.responseType=c.responseType),m&&([o,s]=e4(m,!0),x.addEventListener("progress",o)),d&&x.upload&&([i,r]=e4(d),x.upload.addEventListener("progress",i),x.upload.addEventListener("loadend",r)),(c.cancelToken||c.signal)&&(a=t=>{x&&(n(!t||t.type?new eP(null,e,x):t),x.abort(),x=null)},c.cancelToken&&c.cancelToken.subscribe(a),c.signal&&(c.signal.aborted?a():c.signal.addEventListener("abort",a)));let h=eq(c.url);if(h&&-1===eg.protocols.indexOf(h)){n(new G("Unsupported protocol "+h+":",G.ERR_BAD_REQUEST,e));return}x.send(l||null)})},tb=(e,t)=>{let{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,a=new AbortController,i=function(e){if(!n){n=!0,r();let t=e instanceof Error?e:this.reason;a.abort(t instanceof G?t:new eP(t instanceof Error?t.message:t))}},o=t&&setTimeout(()=>{o=null,i(new G(`timeout ${t} of ms exceeded`,G.ETIMEDOUT))},t),r=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:s}=a;return s.unsubscribe=()=>$.asap(r),s}},tg=function*(e,t){let n,a=e.byteLength;if(!t||a<t){yield e;return}let i=0;for(;i<a;)n=i+t,yield e.slice(i,n),i=n},ty=async function*(e,t){for await(let n of tw(e))yield*tg(n,t)},tw=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},tE=(e,t,n,a)=>{let i;let o=ty(e,t),r=0,s=e=>{!i&&(i=!0,a&&a(e))};return new ReadableStream({async pull(e){try{let{done:t,value:a}=await o.next();if(t){s(),e.close();return}let i=a.byteLength;if(n){let e=r+=i;n(e)}e.enqueue(new Uint8Array(a))}catch(e){throw s(e),e}},cancel:e=>(s(e),o.return())},{highWaterMark:2})},tk="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,tj=tk&&"function"==typeof ReadableStream,tR=tk&&("function"==typeof TextEncoder?(a=new TextEncoder,e=>a.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tC=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},tS=tj&&tC(()=>{let e=!1,t=new Request(eg.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),tA=tj&&tC(()=>$.isReadableStream(new Response("").body)),t_={stream:tA&&(e=>e.body)};tk&&(r=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{t_[e]||(t_[e]=$.isFunction(r[e])?t=>t[e]():(t,n)=>{throw new G(`Response type '${e}' is not supported`,G.ERR_NOT_SUPPORT,n)})}));let tO=async e=>{if(null==e)return 0;if($.isBlob(e))return e.size;if($.isSpecCompliantForm(e)){let t=new Request(eg.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return $.isArrayBufferView(e)||$.isArrayBuffer(e)?e.byteLength:($.isURLSearchParams(e)&&(e+=""),$.isString(e))?(await tR(e)).byteLength:void 0},tT=async(e,t)=>{let n=$.toFiniteNumber(e.getContentLength());return null==n?tO(t):n},tP={http:tu,xhr:th,fetch:tk&&(async e=>{let t,n,{url:a,method:i,data:o,signal:r,cancelToken:s,timeout:c,onDownloadProgress:l,onUploadProgress:p,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=tv(e);u=u?(u+"").toLowerCase():"text";let x=tb([r,s&&s.toAbortSignal()],c),v=x&&x.unsubscribe&&(()=>{x.unsubscribe()});try{if(p&&tS&&"get"!==i&&"head"!==i&&0!==(n=await tT(d,o))){let e,t=new Request(a,{method:"POST",body:o,duplex:"half"});if($.isFormData(o)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,a]=e9(n,e4(e5(p)));o=tE(t.body,65536,e,a)}}$.isString(m)||(m=m?"include":"omit");let r="credentials"in Request.prototype;t=new Request(a,{...f,signal:x,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:r?m:void 0});let s=await fetch(t),c=tA&&("stream"===u||"response"===u);if(tA&&(l||c&&v)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=s[t]});let t=$.toFiniteNumber(s.headers.get("content-length")),[n,a]=l&&e9(t,e4(e5(l),!0))||[];s=new Response(tE(s.body,65536,n,()=>{a&&a(),v&&v()}),e)}u=u||"text";let h=await t_[$.findKey(t_,u)||"text"](s,e);return!c&&v&&v(),await new Promise((n,a)=>{eL(n,a,{data:h,headers:e_.from(s.headers),status:s.status,statusText:s.statusText,config:e,request:t})})}catch(n){if(v&&v(),n&&"TypeError"===n.name&&/Load failed|fetch/i.test(n.message))throw Object.assign(new G("Network Error",G.ERR_NETWORK,e,t),{cause:n.cause||n});throw G.from(n,n&&n.code,e,t)}})};$.forEach(tP,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let tL=e=>`- ${e}`,tN=e=>$.isFunction(e)||null===e||!1===e,tF={getAdapter:e=>{let t,n;let{length:a}=e=$.isArray(e)?e:[e],i={};for(let o=0;o<a;o++){let a;if(n=t=e[o],!tN(t)&&void 0===(n=tP[(a=String(t)).toLowerCase()]))throw new G(`Unknown adapter '${a}'`);if(n)break;i[a||"#"+o]=n}if(!n){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new G("There is no suitable adapter to dispatch the request "+(a?e.length>1?"since :\n"+e.map(tL).join("\n"):" "+tL(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return n}};function tD(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eP(null,e)}function tM(e){return tD(e),e.headers=e_.from(e.headers),e.data=eO.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),tF.getAdapter(e.adapter||ew.adapter)(e).then(function(t){return tD(e),t.data=eO.call(e,e.transformResponse,t),t.headers=e_.from(t.headers),t},function(t){return!eT(t)&&(tD(e),t&&t.response&&(t.response.data=eO.call(e,e.transformResponse,t.response),t.response.headers=e_.from(t.response.headers))),Promise.reject(t)})}let tI={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{tI[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});let tB={};tI.transitional=function(e,t,n){function a(e,t){return"[Axios v"+eU+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,i,o)=>{if(!1===e)throw new G(a(i," has been removed"+(t?" in "+t:"")),G.ERR_DEPRECATED);return t&&!tB[i]&&(tB[i]=!0,console.warn(a(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,i,o)}},tI.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};let tz={assertOptions:function(e,t,n){if("object"!=typeof e)throw new G("options must be an object",G.ERR_BAD_OPTION_VALUE);let a=Object.keys(e),i=a.length;for(;i-- >0;){let o=a[i],r=t[o];if(r){let t=e[o],n=void 0===t||r(t,o,e);if(!0!==n)throw new G("option "+o+" must be "+n,G.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new G("Unknown option "+o,G.ERR_BAD_OPTION)}},validators:tI},tU=tz.validators;class tq{constructor(e){this.defaults=e||{},this.interceptors={request:new er,response:new er}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let n=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}catch(e){}}throw e}}_request(e,t){let n,a;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:o,headers:r}=t=tx(this.defaults,t);void 0!==i&&tz.assertOptions(i,{silentJSONParsing:tU.transitional(tU.boolean),forcedJSONParsing:tU.transitional(tU.boolean),clarifyTimeoutError:tU.transitional(tU.boolean)},!1),null!=o&&($.isFunction(o)?t.paramsSerializer={serialize:o}:tz.assertOptions(o,{encode:tU.function,serialize:tU.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tz.assertOptions(t,{baseUrl:tU.spelling("baseURL"),withXsrfToken:tU.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let s=r&&$.merge(r.common,r[t.method]);r&&$.forEach(["delete","get","head","post","put","patch","common"],e=>{delete r[e]}),t.headers=e_.concat(s,r);let c=[],l=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(l=l&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let p=[];this.interceptors.response.forEach(function(e){p.push(e.fulfilled,e.rejected)});let u=0;if(!l){let e=[tM.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,p),a=e.length,n=Promise.resolve(t);u<a;)n=n.then(e[u++],e[u++]);return n}a=c.length;let d=t;for(u=0;u<a;){let e=c[u++],t=c[u++];try{d=e(d)}catch(e){t.call(this,e);break}}try{n=tM.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,a=p.length;u<a;)n=n.then(p[u++],p[u++]);return n}getUri(e){return eo(eN((e=tx(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}$.forEach(["delete","get","head","options"],function(e){tq.prototype[e]=function(t,n){return this.request(tx(n||{},{method:e,url:t,data:(n||{}).data}))}}),$.forEach(["post","put","patch"],function(e){function t(t){return function(n,a,i){return this.request(tx(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:a}))}}tq.prototype[e]=t(),tq.prototype[e+"Form"]=t(!0)});class tW{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;let a=new Promise(e=>{n.subscribe(e),t=e}).then(e);return a.cancel=function(){n.unsubscribe(t)},a},e(function(e,a,i){!n.reason&&(n.reason=new eP(e,a,i),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new tW(function(t){e=t}),cancel:e}}}let tH={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(tH).forEach(([e,t])=>{tH[t]=e});let t$=function e(t){let n=new tq(t),a=c(tq.prototype.request,n);return $.extend(a,tq.prototype,n,{allOwnKeys:!0}),$.extend(a,n,null,{allOwnKeys:!0}),a.create=function(n){return e(tx(t,n))},a}(ew);t$.Axios=tq,t$.CanceledError=eP,t$.CancelToken=tW,t$.isCancel=eT,t$.VERSION=eU,t$.toFormData=ee,t$.AxiosError=G,t$.Cancel=t$.CanceledError,t$.all=function(e){return Promise.all(e)},t$.spread=function(e){return function(t){return e.apply(null,t)}},t$.isAxiosError=function(e){return $.isObject(e)&&!0===e.isAxiosError},t$.mergeConfig=tx,t$.AxiosHeaders=e_,t$.formToJSON=e=>ey($.isHTMLForm(e)?new FormData(e):e),t$.getAdapter=tF.getAdapter,t$.HttpStatusCode=tH,t$.default=t$;let tG=t$},51105:(e,t,n)=>{"use strict";var a=n(92482),i=n(51951),o=n(99819);e.exports=n(78360)||a.call(o,i)},51951:e=>{"use strict";e.exports=Function.prototype.apply},53147:e=>{"use strict";e.exports=Math.min},54544:e=>{"use strict";e.exports=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),n=Object(t);if("string"==typeof t||"[object Symbol]"!==Object.prototype.toString.call(t)||"[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(var a in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length||"function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var i=Object.getOwnPropertySymbols(e);if(1!==i.length||i[0]!==t||!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}},56786:(e,t,n)=>{"use strict";var a=Function.prototype.call,i=Object.prototype.hasOwnProperty;e.exports=n(92482).call(a,i)},58501:e=>{"use strict";e.exports=Math.pow},58730:(e,t,n)=>{"use strict";n.d(t,{i3:()=>Y,UC:()=>J,ZL:()=>K,Kq:()=>$,bL:()=>G,l9:()=>V});var a=n(43210),i=n(70569),o=n(98599),r=n(11273),s=n(31355),c=n(96963),l=n(29172),p=n(25028),u=n(46059),d=n(3416),m=n(60687);a.forwardRef((e,t)=>{let{children:n,...i}=e,o=a.Children.toArray(n),r=o.find(v);if(r){let e=r.props.children,n=o.map(t=>t!==r?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,m.jsx)(f,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,m.jsx)(f,{...i,ref:t,children:n})}).displayName="Slot";var f=a.forwardRef((e,t)=>{let{children:n,...i}=e;if(a.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),r=function(e,t){let n={...t};for(let a in t){let i=e[a],o=t[a];/^on[A-Z]/.test(a)?i&&o?n[a]=(...e)=>{o(...e),i(...e)}:i&&(n[a]=i):"style"===a?n[a]={...i,...o}:"className"===a&&(n[a]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==a.Fragment&&(r.ref=t?(0,o.t)(t,e):e),a.cloneElement(n,r)}return a.Children.count(n)>1?a.Children.only(null):null});f.displayName="SlotClone";var x=({children:e})=>(0,m.jsx)(m.Fragment,{children:e});function v(e){return a.isValidElement(e)&&e.type===x}var h=n(65551),b=n(69024),[g,y]=(0,r.A)("Tooltip",[l.Bk]),w=(0,l.Bk)(),E="TooltipProvider",k="tooltip.open",[j,R]=g(E),C=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:r}=e,[s,c]=a.useState(!0),l=a.useRef(!1),p=a.useRef(0);return a.useEffect(()=>{let e=p.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(j,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:a.useCallback(()=>{window.clearTimeout(p.current),c(!1)},[]),onClose:a.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>c(!0),i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:a.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:r})};C.displayName=E;var S="Tooltip",[A,_]=g(S),O=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o=!1,onOpenChange:r,disableHoverableContent:s,delayDuration:p}=e,u=R(S,e.__scopeTooltip),d=w(t),[f,x]=a.useState(null),v=(0,c.B)(),b=a.useRef(0),g=s??u.disableHoverableContent,y=p??u.delayDuration,E=a.useRef(!1),[j=!1,C]=(0,h.i)({prop:i,defaultProp:o,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(k))):u.onClose(),r?.(e)}}),_=a.useMemo(()=>j?E.current?"delayed-open":"instant-open":"closed",[j]),O=a.useCallback(()=>{window.clearTimeout(b.current),b.current=0,E.current=!1,C(!0)},[C]),T=a.useCallback(()=>{window.clearTimeout(b.current),b.current=0,C(!1)},[C]),P=a.useCallback(()=>{window.clearTimeout(b.current),b.current=window.setTimeout(()=>{E.current=!0,C(!0),b.current=0},y)},[y,C]);return a.useEffect(()=>()=>{b.current&&(window.clearTimeout(b.current),b.current=0)},[]),(0,m.jsx)(l.bL,{...d,children:(0,m.jsx)(A,{scope:t,contentId:v,open:j,stateAttribute:_,trigger:f,onTriggerChange:x,onTriggerEnter:a.useCallback(()=>{u.isOpenDelayed?P():O()},[u.isOpenDelayed,P,O]),onTriggerLeave:a.useCallback(()=>{g?T():(window.clearTimeout(b.current),b.current=0)},[T,g]),onOpen:O,onClose:T,disableHoverableContent:g,children:n})})};O.displayName=S;var T="TooltipTrigger",P=a.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,s=_(T,n),c=R(T,n),p=w(n),u=a.useRef(null),f=(0,o.s)(t,u,s.onTriggerChange),x=a.useRef(!1),v=a.useRef(!1),h=a.useCallback(()=>x.current=!1,[]);return a.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,m.jsx)(l.Mz,{asChild:!0,...p,children:(0,m.jsx)(d.sG.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...r,ref:f,onPointerMove:(0,i.m)(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||c.isPointerInTransitRef.current||(s.onTriggerEnter(),v.current=!0)}),onPointerLeave:(0,i.m)(e.onPointerLeave,()=>{s.onTriggerLeave(),v.current=!1}),onPointerDown:(0,i.m)(e.onPointerDown,()=>{x.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,i.m)(e.onFocus,()=>{x.current||s.onOpen()}),onBlur:(0,i.m)(e.onBlur,s.onClose),onClick:(0,i.m)(e.onClick,s.onClose)})})});P.displayName=T;var L="TooltipPortal",[N,F]=g(L,{forceMount:void 0}),D=e=>{let{__scopeTooltip:t,forceMount:n,children:a,container:i}=e,o=_(L,t);return(0,m.jsx)(N,{scope:t,forceMount:n,children:(0,m.jsx)(u.C,{present:n||o.open,children:(0,m.jsx)(p.Z,{asChild:!0,container:i,children:a})})})};D.displayName=L;var M="TooltipContent",I=a.forwardRef((e,t)=>{let n=F(M,e.__scopeTooltip),{forceMount:a=n.forceMount,side:i="top",...o}=e,r=_(M,e.__scopeTooltip);return(0,m.jsx)(u.C,{present:a||r.open,children:r.disableHoverableContent?(0,m.jsx)(q,{side:i,...o,ref:t}):(0,m.jsx)(B,{side:i,...o,ref:t})})}),B=a.forwardRef((e,t)=>{let n=_(M,e.__scopeTooltip),i=R(M,e.__scopeTooltip),r=a.useRef(null),s=(0,o.s)(t,r),[c,l]=a.useState(null),{trigger:p,onClose:u}=n,d=r.current,{onPointerInTransitChange:f}=i,x=a.useCallback(()=>{l(null),f(!1)},[f]),v=a.useCallback((e,t)=>{let n=e.currentTarget,a={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),a=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,a,i,o)){case o:return"left";case i:return"right";case n:return"top";case a:return"bottom";default:throw Error("unreachable")}}(a,n.getBoundingClientRect());l(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:+!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let a=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(a.y-n.y)>=(e.y-n.y)*(a.x-n.x))t.pop();else break}t.push(a)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let a=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(a.y-t.y)>=(e.y-t.y)*(a.x-t.x))n.pop();else break}n.push(a)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t,n=5){let a=[];switch(t){case"top":a.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":a.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":a.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":a.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return a}(a,i),...function(e){let{top:t,right:n,bottom:a,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:a},{x:i,y:a}]}(t.getBoundingClientRect())])),f(!0)},[f]);return a.useEffect(()=>()=>x(),[x]),a.useEffect(()=>{if(p&&d){let e=e=>v(e,d),t=e=>v(e,p);return p.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{p.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[p,d,v,x]),a.useEffect(()=>{if(c){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},a=p?.contains(t)||d?.contains(t),i=!function(e,t){let{x:n,y:a}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let r=t[e].x,s=t[e].y,c=t[o].x,l=t[o].y;s>a!=l>a&&n<(c-r)*(a-s)/(l-s)+r&&(i=!i)}return i}(n,c);a?x():i&&(x(),u())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[p,d,c,u,x]),(0,m.jsx)(q,{...e,ref:s})}),[z,U]=g(S,{isInside:!1}),q=a.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:r,onPointerDownOutside:c,...p}=e,u=_(M,n),d=w(n),{onClose:f}=u;return a.useEffect(()=>(document.addEventListener(k,f),()=>document.removeEventListener(k,f)),[f]),a.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;t?.contains(u.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,f]),(0,m.jsx)(s.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:r,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,m.jsxs)(l.UC,{"data-state":u.stateAttribute,...d,...p,ref:t,style:{...p.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(x,{children:i}),(0,m.jsx)(z,{scope:n,isInside:!0,children:(0,m.jsx)(b.b,{id:u.contentId,role:"tooltip",children:o||i})})]})})});I.displayName=M;var W="TooltipArrow",H=a.forwardRef((e,t)=>{let{__scopeTooltip:n,...a}=e,i=w(n);return U(W,n).isInside?null:(0,m.jsx)(l.i3,{...i,...a,ref:t})});H.displayName=W;var $=C,G=O,V=P,K=D,J=I,Y=H},58869:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},60863:e=>{"use strict";e.exports=Math.abs},62369:(e,t,n)=>{"use strict";n.d(t,{b:()=>l});var a=n(43210),i=n(3416),o=n(60687),r="horizontal",s=["horizontal","vertical"],c=a.forwardRef((e,t)=>{var n;let{decorative:a,orientation:c=r,...l}=e,p=(n=c,s.includes(n))?c:r;return(0,o.jsx)(i.sG.div,{"data-orientation":p,...a?{role:"none"}:{"aria-orientation":"vertical"===p?p:void 0,role:"separator"},...l,ref:t})});c.displayName="Separator";var l=c},62427:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect.getPrototypeOf||null},63376:(e,t,n)=>{"use strict";n.d(t,{Eq:()=>p});var a=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,o=new WeakMap,r={},s=0,c=function(e){return e&&(e.host||c(e.parentNode))},l=function(e,t,n,a){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=c(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});r[n]||(r[n]=new WeakMap);var p=r[n],u=[],d=new Set,m=new Set(l),f=function(e){!(!e||d.has(e))&&(d.add(e),f(e.parentNode))};l.forEach(f);var x=function(e){!(!e||m.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(d.has(e))x(e);else try{var t=e.getAttribute(a),r=null!==t&&"false"!==t,s=(i.get(e)||0)+1,c=(p.get(e)||0)+1;i.set(e,s),p.set(e,c),u.push(e),1===s&&r&&o.set(e,!0),1===c&&e.setAttribute(n,"true"),r||e.setAttribute(a,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return x(t),d.clear(),s++,function(){u.forEach(function(e){var t=i.get(e)-1,r=p.get(e)-1;i.set(e,t),p.set(e,r),t||(o.has(e)||e.removeAttribute(a),o.delete(e)),r||e.removeAttribute(n)}),--s||(i=new WeakMap,i=new WeakMap,o=new WeakMap,r={})}},p=function(e,t,n){void 0===n&&(n="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),o=t||a(e);return o?(i.push.apply(i,Array.from(o.querySelectorAll("[aria-live]"))),l(i,o,n,"aria-hidden")):function(){return null}}},63963:(e,t,n)=>{var a=n(41536),i=n(80271),o=n(45793);e.exports=function(e,t,n){for(var r=i(e);r.index<(r.keyedList||e).length;)a(e,t,r,function(e,t){if(e){n(e,t);return}if(0===Object.keys(r.jobs).length){n(null,r.results);return}}),r.index++;return o.bind(r,n)}},64171:(e,t,n)=>{e.exports=n(84933)},65551:(e,t,n)=>{"use strict";n.d(t,{i:()=>o});var a=n(43210),i=n(13495);function o({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,r]=function({defaultProp:e,onChange:t}){let n=a.useState(e),[o]=n,r=a.useRef(o),s=(0,i.c)(t);return a.useEffect(()=>{r.current!==o&&(s(o),r.current=o)},[o,r,s]),n}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:o,l=(0,i.c)(n);return[c,a.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else r(t)},[s,e,r,l])]}},66156:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var a=n(43210),i=globalThis?.document?a.useLayoutEffect:()=>{}},67802:e=>{function t(e,t,n,a){return Math.round(e/n)+" "+a+(t>=1.5*n?"s":"")}e.exports=function(e,n){n=n||{};var a,i,o,r,s=typeof e;if("string"===s&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var n=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*n;case"weeks":case"week":case"w":return 6048e5*n;case"days":case"day":case"d":return 864e5*n;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*n;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*n;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}(e);if("number"===s&&isFinite(e)){return n.long?(i=Math.abs(a=e))>=864e5?t(a,i,864e5,"day"):i>=36e5?t(a,i,36e5,"hour"):i>=6e4?t(a,i,6e4,"minute"):i>=1e3?t(a,i,1e3,"second"):a+" ms":(r=Math.abs(o=e))>=864e5?Math.round(o/864e5)+"d":r>=36e5?Math.round(o/36e5)+"h":r>=6e4?Math.round(o/6e4)+"m":r>=1e3?Math.round(o/1e3)+"s":o+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},69024:(e,t,n)=>{"use strict";n.d(t,{b:()=>s,s:()=>r});var a=n(43210),i=n(3416),o=n(60687),r=a.forwardRef((e,t)=>(0,o.jsx)(i.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));r.displayName="VisuallyHidden";var s=r},69996:(e,t,n)=>{var a=n(27910).Stream,i=n(28354);function o(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=o,i.inherits(o,a),o.create=function(e,t){var n=new this;for(var a in t=t||{})n[a]=t[a];n.source=e;var i=e.emit;return e.emit=function(){return n._handleEmit(arguments),i.apply(e,arguments)},e.on("error",function(){}),n.pauseStream&&e.pause(),n},Object.defineProperty(o.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),o.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},o.prototype.resume=function(){this._released||this.release(),this.source.resume()},o.prototype.pause=function(){this.source.pause()},o.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},o.prototype.pipe=function(){var e=a.prototype.pipe.apply(this,arguments);return this.resume(),e},o.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},o.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},70569:(e,t,n)=>{"use strict";function a(e,t,{checkForDefaultPrevented:n=!0}={}){return function(a){if(e?.(a),!1===n||!a.defaultPrevented)return t?.(a)}}n.d(t,{m:()=>a})},70607:(e,t,n)=>{"use strict";var a=n(92482),i=n(49088),o=n(99819),r=n(51105);e.exports=function(e){if(e.length<1||"function"!=typeof e[0])throw new i("a function is required");return r(a,o,e)}},70965:(e,t,n)=>{"use strict";n.d(t,{uB:()=>P});var a=/[\\\/_+.#"@\[\(\{&]/,i=/[\\\/_+.#"@\[\(\{&]/g,o=/[\s-]/,r=/[\s-]/g;function s(e){return e.toLowerCase().replace(r," ")}var c=n(6491),l=n(43210),p=n(3416),u=n(96963),d=n(98599),m='[cmdk-group=""]',f='[cmdk-group-items=""]',x='[cmdk-item=""]',v=`${x}:not([aria-disabled="true"])`,h="cmdk-item-select",b="data-value",g=(e,t,n)=>(function(e,t,n){return function e(t,n,s,c,l,p,u){if(p===n.length)return l===t.length?1:.99;var d=`${l},${p}`;if(void 0!==u[d])return u[d];for(var m,f,x,v,h=c.charAt(p),b=s.indexOf(h,l),g=0;b>=0;)(m=e(t,n,s,c,b+1,p+1,u))>g&&(b===l?m*=1:a.test(t.charAt(b-1))?(m*=.8,(x=t.slice(l,b-1).match(i))&&l>0&&(m*=Math.pow(.999,x.length))):o.test(t.charAt(b-1))?(m*=.9,(v=t.slice(l,b-1).match(r))&&l>0&&(m*=Math.pow(.999,v.length))):(m*=.17,l>0&&(m*=Math.pow(.999,b-l))),t.charAt(b)!==n.charAt(p)&&(m*=.9999)),(m<.1&&s.charAt(b-1)===c.charAt(p+1)||c.charAt(p+1)===c.charAt(p)&&s.charAt(b-1)!==c.charAt(p))&&.1*(f=e(t,n,s,c,b+1,p+2,u))>m&&(m=.1*f),m>g&&(g=m),b=s.indexOf(h,b+1);return u[d]=g,g}(e=n&&n.length>0?`${e+" "+n.join(" ")}`:e,t,s(e),s(t),0,0,{})})(e,t,n),y=l.createContext(void 0),w=()=>l.useContext(y),E=l.createContext(void 0),k=()=>l.useContext(E),j=l.createContext(void 0),R=l.forwardRef((e,t)=>{let n=F(()=>{var t,n;return{search:"",value:null!=(n=null!=(t=e.value)?t:e.defaultValue)?n:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),a=F(()=>new Set),i=F(()=>new Map),o=F(()=>new Map),r=F(()=>new Set),s=L(e),{label:c,children:d,value:w,onValueChange:k,filter:j,shouldFilter:R,loop:C,disablePointerSelection:S=!1,vimBindings:A=!0,..._}=e,O=(0,u.B)(),T=(0,u.B)(),P=(0,u.B)(),D=l.useRef(null),M=I();N(()=>{if(void 0!==w){let e=w.trim();n.current.value=e,U.emit()}},[w]),N(()=>{M(6,V)},[]);let U=l.useMemo(()=>({subscribe:e=>(r.current.add(e),()=>r.current.delete(e)),snapshot:()=>n.current,setState:(e,t,a)=>{var i,o,r,c;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)G(),H(),M(1,$);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(P);e?e.focus():null==(i=document.getElementById(O))||i.focus()}if(M(7,()=>{var e;n.current.selectedItemId=null==(e=K())?void 0:e.id,U.emit()}),a||M(5,V),(null==(o=s.current)?void 0:o.value)!==void 0){null==(c=(r=s.current).onValueChange)||c.call(r,null!=t?t:"");return}}U.emit()}},emit:()=>{r.current.forEach(e=>e())}}),[]),q=l.useMemo(()=>({value:(e,t,a)=>{var i;t!==(null==(i=o.current.get(e))?void 0:i.value)&&(o.current.set(e,{value:t,keywords:a}),n.current.filtered.items.set(e,W(t,a)),M(2,()=>{H(),U.emit()}))},item:(e,t)=>(a.current.add(e),t&&(i.current.has(t)?i.current.get(t).add(e):i.current.set(t,new Set([e]))),M(3,()=>{G(),H(),n.current.value||$(),U.emit()}),()=>{o.current.delete(e),a.current.delete(e),n.current.filtered.items.delete(e);let t=K();M(4,()=>{G(),(null==t?void 0:t.getAttribute("id"))===e&&$(),U.emit()})}),group:e=>(i.current.has(e)||i.current.set(e,new Set),()=>{o.current.delete(e),i.current.delete(e)}),filter:()=>s.current.shouldFilter,label:c||e["aria-label"],getDisablePointerSelection:()=>s.current.disablePointerSelection,listId:O,inputId:P,labelId:T,listInnerRef:D}),[]);function W(e,t){var a,i;let o=null!=(i=null==(a=s.current)?void 0:a.filter)?i:g;return e?o(e,n.current.search,t):0}function H(){if(!n.current.search||!1===s.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach(n=>{let a=i.current.get(n),o=0;a.forEach(t=>{o=Math.max(e.get(t),o)}),t.push([n,o])});let a=D.current;J().sort((t,n)=>{var a,i;let o=t.getAttribute("id"),r=n.getAttribute("id");return(null!=(a=e.get(r))?a:0)-(null!=(i=e.get(o))?i:0)}).forEach(e=>{let t=e.closest(f);t?t.appendChild(e.parentElement===t?e:e.closest(`${f} > *`)):a.appendChild(e.parentElement===a?e:e.closest(`${f} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let n=null==(t=D.current)?void 0:t.querySelector(`${m}[${b}="${encodeURIComponent(e[0])}"]`);null==n||n.parentElement.appendChild(n)})}function $(){let e=J().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(b);U.setState("value",t||void 0)}function G(){var e,t,r,c;if(!n.current.search||!1===s.current.shouldFilter){n.current.filtered.count=a.current.size;return}n.current.filtered.groups=new Set;let l=0;for(let i of a.current){let a=W(null!=(t=null==(e=o.current.get(i))?void 0:e.value)?t:"",null!=(c=null==(r=o.current.get(i))?void 0:r.keywords)?c:[]);n.current.filtered.items.set(i,a),a>0&&l++}for(let[e,t]of i.current)for(let a of t)if(n.current.filtered.items.get(a)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=l}function V(){var e,t,n;let a=K();a&&((null==(e=a.parentElement)?void 0:e.firstChild)===a&&(null==(n=null==(t=a.closest(m))?void 0:t.querySelector('[cmdk-group-heading=""]'))||n.scrollIntoView({block:"nearest"})),a.scrollIntoView({block:"nearest"}))}function K(){var e;return null==(e=D.current)?void 0:e.querySelector(`${x}[aria-selected="true"]`)}function J(){var e;return Array.from((null==(e=D.current)?void 0:e.querySelectorAll(v))||[])}function Y(e){let t=J()[e];t&&U.setState("value",t.getAttribute(b))}function X(e){var t;let n=K(),a=J(),i=a.findIndex(e=>e===n),o=a[i+e];null!=(t=s.current)&&t.loop&&(o=i+e<0?a[a.length-1]:i+e===a.length?a[0]:a[i+e]),o&&U.setState("value",o.getAttribute(b))}function Z(e){let t=K(),n=null==t?void 0:t.closest(m),a;for(;n&&!a;)a=null==(n=e>0?function(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}(n,m):function(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}(n,m))?void 0:n.querySelector(v);a?U.setState("value",a.getAttribute(b)):X(e)}let Q=()=>Y(J().length-1),ee=e=>{e.preventDefault(),e.metaKey?Q():e.altKey?Z(1):X(1)},et=e=>{e.preventDefault(),e.metaKey?Y(0):e.altKey?Z(-1):X(-1)};return l.createElement(p.sG.div,{ref:t,tabIndex:-1,..._,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=_.onKeyDown)||t.call(_,e);let n=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||n))switch(e.key){case"n":case"j":A&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":A&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),Y(0);break;case"End":e.preventDefault(),Q();break;case"Enter":{e.preventDefault();let t=K();if(t){let e=new Event(h);t.dispatchEvent(e)}}}}},l.createElement("label",{"cmdk-label":"",htmlFor:q.inputId,id:q.labelId,style:z},c),B(e,e=>l.createElement(E.Provider,{value:U},l.createElement(y.Provider,{value:q},e))))}),C=l.forwardRef((e,t)=>{var n,a;let i=(0,u.B)(),o=l.useRef(null),r=l.useContext(j),s=w(),c=L(e),m=null!=(a=null==(n=c.current)?void 0:n.forceMount)?a:null==r?void 0:r.forceMount;N(()=>{if(!m)return s.item(i,null==r?void 0:r.id)},[m]);let f=M(i,o,[e.value,e.children,o],e.keywords),x=k(),v=D(e=>e.value&&e.value===f.current),b=D(e=>!!m||!1===s.filter()||!e.search||e.filtered.items.get(i)>0);function g(){var e,t;y(),null==(t=(e=c.current).onSelect)||t.call(e,f.current)}function y(){x.setState("value",f.current,!0)}if(l.useEffect(()=>{let t=o.current;if(!(!t||e.disabled))return t.addEventListener(h,g),()=>t.removeEventListener(h,g)},[b,e.onSelect,e.disabled]),!b)return null;let{disabled:E,value:R,onSelect:C,forceMount:S,keywords:A,..._}=e;return l.createElement(p.sG.div,{ref:(0,d.t)(o,t),..._,id:i,"cmdk-item":"",role:"option","aria-disabled":!!E,"aria-selected":!!v,"data-disabled":!!E,"data-selected":!!v,onPointerMove:E||s.getDisablePointerSelection()?void 0:y,onClick:E?void 0:g},e.children)}),S=l.forwardRef((e,t)=>{let{heading:n,children:a,forceMount:i,...o}=e,r=(0,u.B)(),s=l.useRef(null),c=l.useRef(null),m=(0,u.B)(),f=w(),x=D(e=>!!i||!1===f.filter()||!e.search||e.filtered.groups.has(r));N(()=>f.group(r),[]),M(r,s,[e.value,e.heading,c]);let v=l.useMemo(()=>({id:r,forceMount:i}),[i]);return l.createElement(p.sG.div,{ref:(0,d.t)(s,t),...o,"cmdk-group":"",role:"presentation",hidden:!x||void 0},n&&l.createElement("div",{ref:c,"cmdk-group-heading":"","aria-hidden":!0,id:m},n),B(e,e=>l.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?m:void 0},l.createElement(j.Provider,{value:v},e))))}),A=l.forwardRef((e,t)=>{let{alwaysRender:n,...a}=e,i=l.useRef(null),o=D(e=>!e.search);return n||o?l.createElement(p.sG.div,{ref:(0,d.t)(i,t),...a,"cmdk-separator":"",role:"separator"}):null}),_=l.forwardRef((e,t)=>{let{onValueChange:n,...a}=e,i=null!=e.value,o=k(),r=D(e=>e.search),s=D(e=>e.selectedItemId),c=w();return l.useEffect(()=>{null!=e.value&&o.setState("search",e.value)},[e.value]),l.createElement(p.sG.input,{ref:t,...a,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.listId,"aria-labelledby":c.labelId,"aria-activedescendant":s,id:c.inputId,type:"text",value:i?e.value:r,onChange:e=>{i||o.setState("search",e.target.value),null==n||n(e.target.value)}})}),O=l.forwardRef((e,t)=>{let{children:n,label:a="Suggestions",...i}=e,o=l.useRef(null),r=l.useRef(null),s=D(e=>e.selectedItemId),c=w();return l.useEffect(()=>{if(r.current&&o.current){let e=r.current,t=o.current,n,a=new ResizeObserver(()=>{n=requestAnimationFrame(()=>{let n=e.offsetHeight;t.style.setProperty("--cmdk-list-height",n.toFixed(1)+"px")})});return a.observe(e),()=>{cancelAnimationFrame(n),a.unobserve(e)}}},[]),l.createElement(p.sG.div,{ref:(0,d.t)(o,t),...i,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":s,"aria-label":a,id:c.listId},B(e,e=>l.createElement("div",{ref:(0,d.t)(r,c.listInnerRef),"cmdk-list-sizer":""},e)))}),T=l.forwardRef((e,t)=>{let{open:n,onOpenChange:a,overlayClassName:i,contentClassName:o,container:r,...s}=e;return l.createElement(c.bL,{open:n,onOpenChange:a},l.createElement(c.ZL,{container:r},l.createElement(c.hJ,{"cmdk-overlay":"",className:i}),l.createElement(c.UC,{"aria-label":e.label,"cmdk-dialog":"",className:o},l.createElement(R,{ref:t,...s}))))}),P=Object.assign(R,{List:O,Item:C,Input:_,Group:S,Separator:A,Dialog:T,Empty:l.forwardRef((e,t)=>D(e=>0===e.filtered.count)?l.createElement(p.sG.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:l.forwardRef((e,t)=>{let{progress:n,children:a,label:i="Loading...",...o}=e;return l.createElement(p.sG.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":i},B(e,e=>l.createElement("div",{"aria-hidden":!0},e)))})});function L(e){let t=l.useRef(e);return N(()=>{t.current=e}),t}var N=l.useEffect;function F(e){let t=l.useRef();return void 0===t.current&&(t.current=e()),t}function D(e){let t=k(),n=()=>e(t.snapshot());return l.useSyncExternalStore(t.subscribe,n,n)}function M(e,t,n,a=[]){let i=l.useRef(),o=w();return N(()=>{var r;let s=(()=>{var e;for(let t of n){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():i.current}})(),c=a.map(e=>e.trim());o.value(e,s,c),null==(r=t.current)||r.setAttribute(b,s),i.current=s}),i}var I=()=>{let[e,t]=l.useState(),n=F(()=>new Map);return N(()=>{n.current.forEach(e=>e()),n.current=new Map},[e]),(e,a)=>{n.current.set(e,a),t({})}};function B({asChild:e,children:t},n){let a;return e&&l.isValidElement(t)?l.cloneElement("function"==typeof(a=t.type)?a(t.props):"render"in a?a.render(t.props):t,{ref:t.ref},n(t.props.children)):n(t)}var z={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},72031:(e,t,n)=>{"use strict";n.d(t,{N:()=>u});var a=n(43210),i=n(11273),o=n(98599),r=n(60687),s=a.forwardRef((e,t)=>{let{children:n,...i}=e,o=a.Children.toArray(n),s=o.find(p);if(s){let e=s.props.children,n=o.map(t=>t!==s?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,r.jsx)(c,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,r.jsx)(c,{...i,ref:t,children:n})});s.displayName="Slot";var c=a.forwardRef((e,t)=>{let{children:n,...i}=e;if(a.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n),r=function(e,t){let n={...t};for(let a in t){let i=e[a],o=t[a];/^on[A-Z]/.test(a)?i&&o?n[a]=(...e)=>{o(...e),i(...e)}:i&&(n[a]=i):"style"===a?n[a]={...i,...o}:"className"===a&&(n[a]=[i,o].filter(Boolean).join(" "))}return{...e,...n}}(i,n.props);return n.type!==a.Fragment&&(r.ref=t?(0,o.t)(t,e):e),a.cloneElement(n,r)}return a.Children.count(n)>1?a.Children.only(null):null});c.displayName="SlotClone";var l=({children:e})=>(0,r.jsx)(r.Fragment,{children:e});function p(e){return a.isValidElement(e)&&e.type===l}function u(e){let t=e+"CollectionProvider",[n,c]=(0,i.A)(t),[l,p]=n(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:n}=e,i=a.useRef(null),o=a.useRef(new Map).current;return(0,r.jsx)(l,{scope:t,itemMap:o,collectionRef:i,children:n})};u.displayName=t;let d=e+"CollectionSlot",m=a.forwardRef((e,t)=>{let{scope:n,children:a}=e,i=p(d,n),c=(0,o.s)(t,i.collectionRef);return(0,r.jsx)(s,{ref:c,children:a})});m.displayName=d;let f=e+"CollectionItemSlot",x="data-radix-collection-item",v=a.forwardRef((e,t)=>{let{scope:n,children:i,...c}=e,l=a.useRef(null),u=(0,o.s)(t,l),d=p(f,n);return a.useEffect(()=>(d.itemMap.set(l,{ref:l,...c}),()=>void d.itemMap.delete(l))),(0,r.jsx)(s,{[x]:"",ref:u,children:i})});return v.displayName=f,[{Provider:u,Slot:m,ItemSlot:v},function(t){let n=p(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${x}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},c]}},72942:(e,t,n)=>{"use strict";n.d(t,{RG:()=>w,bL:()=>O,q7:()=>T});var a=n(43210),i=n(70569),o=n(72031),r=n(98599),s=n(11273),c=n(96963),l=n(3416),p=n(13495),u=n(65551),d=n(43),m=n(60687),f="rovingFocusGroup.onEntryFocus",x={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[h,b,g]=(0,o.N)(v),[y,w]=(0,s.A)(v,[g]),[E,k]=y(v),j=a.forwardRef((e,t)=>(0,m.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(R,{...e,ref:t})})}));j.displayName=v;var R=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:o,loop:s=!1,dir:c,currentTabStopId:v,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:g,onEntryFocus:y,preventScrollOnEntryFocus:w=!1,...k}=e,j=a.useRef(null),R=(0,r.s)(t,j),C=(0,d.jH)(c),[S=null,A]=(0,u.i)({prop:v,defaultProp:h,onChange:g}),[O,T]=a.useState(!1),P=(0,p.c)(y),L=b(n),N=a.useRef(!1),[F,D]=a.useState(0);return a.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(f,P),()=>e.removeEventListener(f,P)},[P]),(0,m.jsx)(E,{scope:n,orientation:o,dir:C,loop:s,currentTabStopId:S,onItemFocus:a.useCallback(e=>A(e),[A]),onItemShiftTab:a.useCallback(()=>T(!0),[]),onFocusableItemAdd:a.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:a.useCallback(()=>D(e=>e-1),[]),children:(0,m.jsx)(l.sG.div,{tabIndex:O||0===F?-1:0,"data-orientation":o,...k,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!O){let t=new CustomEvent(f,x);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);_([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),w)}}N.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>T(!1))})})}),C="RovingFocusGroupItem",S=a.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:o=!0,active:r=!1,tabStopId:s,...p}=e,u=(0,c.B)(),d=s||u,f=k(C,n),x=f.currentTabStopId===d,v=b(n),{onFocusableItemAdd:g,onFocusableItemRemove:y}=f;return a.useEffect(()=>{if(o)return g(),()=>y()},[o,g,y]),(0,m.jsx)(h.ItemSlot,{scope:n,id:d,focusable:o,active:r,children:(0,m.jsx)(l.sG.span,{tabIndex:x?0:-1,"data-orientation":f.orientation,...p,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?f.onItemFocus(d):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>f.onItemFocus(d)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var a;let i=(a=e.key,"rtl"!==n?a:"ArrowLeft"===a?"ArrowRight":"ArrowRight"===a?"ArrowLeft":a);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return A[i]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let a=n.indexOf(e.currentTarget);n=f.loop?function(e,t){return e.map((n,a)=>e[(t+a)%e.length])}(n,a+1):n.slice(a+1)}setTimeout(()=>_(n))}})})})});S.displayName=C;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function _(e,t=!1){let n=document.activeElement;for(let a of e)if(a===n||(a.focus({preventScroll:t}),document.activeElement!==n))return}var O=j,T=S},73514:(e,t,n)=>{"use strict";var a=n(81422);e.exports=function(e){return a(e)||0===e?e:e<0?-1:1}},75012:(e,t,n)=>{"use strict";var a,i=n(3361),o=n(86558),r=n(78750),s=n(7315),c=n(87631),l=n(15219),p=n(49088),u=n(10096),d=n(60863),m=n(30461),f=n(75845),x=n(53147),v=n(58501),h=n(75095),b=n(73514),g=Function,y=function(e){try{return g('"use strict"; return ('+e+").constructor;")()}catch(e){}},w=n(80036),E=n(48720),k=function(){throw new p},j=w?function(){try{return arguments.callee,k}catch(e){try{return w(arguments,"callee").get}catch(e){return k}}}():k,R=n(6582)(),C=n(9181),S=n(81285),A=n(62427),_=n(51951),O=n(99819),T={},P="undefined"!=typeof Uint8Array&&C?C(Uint8Array):a,L={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?a:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?a:ArrayBuffer,"%ArrayIteratorPrototype%":R&&C?C([][Symbol.iterator]()):a,"%AsyncFromSyncIteratorPrototype%":a,"%AsyncFunction%":T,"%AsyncGenerator%":T,"%AsyncGeneratorFunction%":T,"%AsyncIteratorPrototype%":T,"%Atomics%":"undefined"==typeof Atomics?a:Atomics,"%BigInt%":"undefined"==typeof BigInt?a:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?a:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?a:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?a:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":r,"%Float16Array%":"undefined"==typeof Float16Array?a:Float16Array,"%Float32Array%":"undefined"==typeof Float32Array?a:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?a:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?a:FinalizationRegistry,"%Function%":g,"%GeneratorFunction%":T,"%Int8Array%":"undefined"==typeof Int8Array?a:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?a:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?a:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":R&&C?C(C([][Symbol.iterator]())):a,"%JSON%":"object"==typeof JSON?JSON:a,"%Map%":"undefined"==typeof Map?a:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&R&&C?C(new Map()[Symbol.iterator]()):a,"%Math%":Math,"%Number%":Number,"%Object%":i,"%Object.getOwnPropertyDescriptor%":w,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?a:Promise,"%Proxy%":"undefined"==typeof Proxy?a:Proxy,"%RangeError%":s,"%ReferenceError%":c,"%Reflect%":"undefined"==typeof Reflect?a:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?a:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&R&&C?C(new Set()[Symbol.iterator]()):a,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?a:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":R&&C?C(""[Symbol.iterator]()):a,"%Symbol%":R?Symbol:a,"%SyntaxError%":l,"%ThrowTypeError%":j,"%TypedArray%":P,"%TypeError%":p,"%Uint8Array%":"undefined"==typeof Uint8Array?a:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?a:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?a:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?a:Uint32Array,"%URIError%":u,"%WeakMap%":"undefined"==typeof WeakMap?a:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?a:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?a:WeakSet,"%Function.prototype.call%":O,"%Function.prototype.apply%":_,"%Object.defineProperty%":E,"%Object.getPrototypeOf%":S,"%Math.abs%":d,"%Math.floor%":m,"%Math.max%":f,"%Math.min%":x,"%Math.pow%":v,"%Math.round%":h,"%Math.sign%":b,"%Reflect.getPrototypeOf%":A};if(C)try{null.error}catch(e){var N=C(C(e));L["%Error.prototype%"]=N}var F=function e(t){var n;if("%AsyncFunction%"===t)n=y("async function () {}");else if("%GeneratorFunction%"===t)n=y("function* () {}");else if("%AsyncGeneratorFunction%"===t)n=y("async function* () {}");else if("%AsyncGenerator%"===t){var a=e("%AsyncGeneratorFunction%");a&&(n=a.prototype)}else if("%AsyncIteratorPrototype%"===t){var i=e("%AsyncGenerator%");i&&C&&(n=C(i.prototype))}return L[t]=n,n},D={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},M=n(92482),I=n(56786),B=M.call(O,Array.prototype.concat),z=M.call(_,Array.prototype.splice),U=M.call(O,String.prototype.replace),q=M.call(O,String.prototype.slice),W=M.call(O,RegExp.prototype.exec),H=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,$=/\\(\\)?/g,G=function(e){var t=q(e,0,1),n=q(e,-1);if("%"===t&&"%"!==n)throw new l("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==t)throw new l("invalid intrinsic syntax, expected opening `%`");var a=[];return U(e,H,function(e,t,n,i){a[a.length]=n?U(i,$,"$1"):t||e}),a},V=function(e,t){var n,a=e;if(I(D,a)&&(a="%"+(n=D[a])[0]+"%"),I(L,a)){var i=L[a];if(i===T&&(i=F(a)),void 0===i&&!t)throw new p("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:a,value:i}}throw new l("intrinsic "+e+" does not exist!")};e.exports=function(e,t){if("string"!=typeof e||0===e.length)throw new p("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new p('"allowMissing" argument must be a boolean');if(null===W(/^%?[^%]*%?$/,e))throw new l("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=G(e),a=n.length>0?n[0]:"",i=V("%"+a+"%",t),o=i.name,r=i.value,s=!1,c=i.alias;c&&(a=c[0],z(n,B([0,1],c)));for(var u=1,d=!0;u<n.length;u+=1){var m=n[u],f=q(m,0,1),x=q(m,-1);if(('"'===f||"'"===f||"`"===f||'"'===x||"'"===x||"`"===x)&&f!==x)throw new l("property names with quotes must have matching quotes");if("constructor"!==m&&d||(s=!0),a+="."+m,I(L,o="%"+a+"%"))r=L[o];else if(null!=r){if(!(m in r)){if(!t)throw new p("base intrinsic for "+e+" exists, but the property is not available.");return}if(w&&u+1>=n.length){var v=w(r,m);r=(d=!!v)&&"get"in v&&!("originalValue"in v.get)?v.get:r[m]}else d=I(r,m),r=r[m];d&&!s&&(L[o]=r)}}return r}},75095:e=>{"use strict";e.exports=Math.round},75845:e=>{"use strict";e.exports=Math.max},78002:(e,t,n)=>{"use strict";var a=n(75012)("%Object.defineProperty%",!0),i=n(92909)(),o=n(56786),r=n(49088),s=i?Symbol.toStringTag:null;e.exports=function(e,t){var n=arguments.length>2&&!!arguments[2]&&arguments[2].force,i=arguments.length>2&&!!arguments[2]&&arguments[2].nonConfigurable;if(void 0!==n&&"boolean"!=typeof n||void 0!==i&&"boolean"!=typeof i)throw new r("if provided, the `overrideIfSet` and `nonConfigurable` options must be booleans");s&&(n||!o(e,s))&&(a?a(e,s,{configurable:!i,enumerable:!1,value:t,writable:!1}):e[s]=t)}},78272:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},78360:e=>{"use strict";e.exports="undefined"!=typeof Reflect&&Reflect&&Reflect.apply},78750:e=>{"use strict";e.exports=EvalError},80036:(e,t,n)=>{"use strict";var a=n(91176);if(a)try{a([],"length")}catch(e){a=null}e.exports=a},80271:e=>{e.exports=function(e,t){var n=!Array.isArray(e),a={index:0,keyedList:n||t?Object.keys(e):null,jobs:{},results:n?{}:[],size:n?Object.keys(e).length:e.length};return t&&a.keyedList.sort(n?t:function(n,a){return t(e[n],e[a])}),a}},81285:(e,t,n)=>{"use strict";e.exports=n(3361).getPrototypeOf||null},81422:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!=e}},82080:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},83644:(e,t,n)=>{var a=n(28354),i=n(27910).Stream,o=n(69996);function r(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=r,a.inherits(r,i),r.create=function(e){var t=new this;for(var n in e=e||{})t[n]=e[n];return t},r.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},r.prototype.append=function(e){if(r.isStreamLike(e)){if(!(e instanceof o)){var t=o.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=t}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},r.prototype.pipe=function(e,t){return i.prototype.pipe.call(this,e,t),this.resume(),e},r.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},r.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){r.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},r.prototype._pipeNext=function(e){if(this._currentStream=e,r.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},r.prototype._handleErrors=function(e){var t=this;e.on("error",function(e){t._emitError(e)})},r.prototype.write=function(e){this.emit("data",e)},r.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},r.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},r.prototype.end=function(){this._reset(),this.emit("end")},r.prototype.destroy=function(){this._reset(),this.emit("close")},r.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},r.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},r.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(t){t.dataSize&&(e.dataSize+=t.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},r.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},84027:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84933:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')},85026:(e,t,n)=>{e.exports={parallel:n(63963),serial:n(86736),serialOrdered:n(86271)}},86271:(e,t,n)=>{var a=n(41536),i=n(80271),o=n(45793);function r(e,t){return e<t?-1:+(e>t)}e.exports=function(e,t,n,r){var s=i(e,n);return a(e,t,s,function n(i,o){if(i){r(i,o);return}if(s.index++,s.index<(s.keyedList||e).length){a(e,t,s,n);return}r(null,s.results)}),o.bind(s,r)},e.exports.ascending=r,e.exports.descending=function(e,t){return -1*r(e,t)}},86338:e=>{e.exports=function(e){var t="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;t?t(e):setTimeout(e,0)}},86558:e=>{"use strict";e.exports=Error},86736:(e,t,n)=>{var a=n(86271);e.exports=function(e,t,n){return a(e,t,null,n)}},87631:e=>{"use strict";e.exports=ReferenceError},91176:e=>{"use strict";e.exports=Object.getOwnPropertyDescriptor},91268:(e,t,n)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=n(36632):e.exports=n(30678)},92296:(e,t,n)=>{var a;e.exports=function(){if(!a){try{a=n(91268)("follow-redirects")}catch(e){}"function"!=typeof a&&(a=function(){})}a.apply(null,arguments)}},92482:(e,t,n)=>{"use strict";var a=n(47530);e.exports=Function.prototype.bind||a},92909:(e,t,n)=>{"use strict";var a=n(54544);e.exports=function(){return a()&&!!Symbol.toStringTag}},92951:(e,t,n)=>{"use strict";n.d(t,{H4:()=>w,_V:()=>y,bL:()=>g});var a=n(43210),i=n(11273),o=n(13495),r=n(66156),s=n(3416),c=n(60687),l="Avatar",[p,u]=(0,i.A)(l),[d,m]=p(l),f=a.forwardRef((e,t)=>{let{__scopeAvatar:n,...i}=e,[o,r]=a.useState("idle");return(0,c.jsx)(d,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:r,children:(0,c.jsx)(s.sG.span,{...i,ref:t})})});f.displayName=l;var x="AvatarImage",v=a.forwardRef((e,t)=>{let{__scopeAvatar:n,src:i,onLoadingStatusChange:l=()=>{},...p}=e,u=m(x,n),d=function(e,t){let[n,i]=a.useState("idle");return(0,r.N)(()=>{if(!e){i("error");return}let n=!0,a=new window.Image,o=e=>()=>{n&&i(e)};return i("loading"),a.onload=o("loaded"),a.onerror=o("error"),a.src=e,t&&(a.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(i,p.referrerPolicy),f=(0,o.c)(e=>{l(e),u.onImageLoadingStatusChange(e)});return(0,r.N)(()=>{"idle"!==d&&f(d)},[d,f]),"loaded"===d?(0,c.jsx)(s.sG.img,{...p,ref:t,src:i}):null});v.displayName=x;var h="AvatarFallback",b=a.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:i,...o}=e,r=m(h,n),[l,p]=a.useState(void 0===i);return a.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>p(!0),i);return()=>window.clearTimeout(e)}},[i]),l&&"loaded"!==r.imageLoadingStatus?(0,c.jsx)(s.sG.span,{...o,ref:t}):null});b.displayName=h;var g=f,y=v,w=b},94458:(e,t,n)=>{var a=n(86338);e.exports=function(e){var t=!1;return a(function(){t=!0}),function(n,i){t?e(n,i):a(function(){e(n,i)})}}},95930:(e,t,n)=>{"use strict";var a=n(64171),i=n(33873).extname,o=/^\s*([^;\s]*)(?:;|\s|$)/,r=/^text\//i;function s(e){if(!e||"string"!=typeof e)return!1;var t=o.exec(e),n=t&&a[t[1].toLowerCase()];return n&&n.charset?n.charset:!!(t&&r.test(t[1]))&&"UTF-8"}t.charset=s,t.charsets={lookup:s},t.contentType=function(e){if(!e||"string"!=typeof e)return!1;var n=-1===e.indexOf("/")?t.lookup(e):e;if(!n)return!1;if(-1===n.indexOf("charset")){var a=t.charset(n);a&&(n+="; charset="+a.toLowerCase())}return n},t.extension=function(e){if(!e||"string"!=typeof e)return!1;var n=o.exec(e),a=n&&t.extensions[n[1].toLowerCase()];return!!a&&!!a.length&&a[0]},t.extensions=Object.create(null),t.lookup=function(e){if(!e||"string"!=typeof e)return!1;var n=i("x."+e).toLowerCase().substr(1);return!!n&&(t.types[n]||!1)},t.types=Object.create(null),function(e,t){var n=["nginx","apache",void 0,"iana"];Object.keys(a).forEach(function(i){var o=a[i],r=o.extensions;if(r&&r.length){e[i]=r;for(var s=0;s<r.length;s++){var c=r[s];if(t[c]){var l=n.indexOf(a[t[c]].source),p=n.indexOf(o.source);if("application/octet-stream"!==t[c]&&(l>p||l===p&&"application/"===t[c].substr(0,12)))continue}t[c]=i}}})}(t.extensions,t.types)},96211:(e,t,n)=>{e.exports=function(e){function t(e){let n,i,o;let r=null;function s(...e){if(!s.enabled)return;let a=Number(new Date);s.diff=a-(n||a),s.prev=n,s.curr=a,n=a,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(n,a)=>{if("%%"===n)return"%";i++;let o=t.formatters[a];if("function"==typeof o){let t=e[i];n=o.call(s,t),e.splice(i,1),i--}return n}),t.formatArgs.call(s,e),(s.log||t.log).apply(s,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=a,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(i!==t.namespaces&&(i=t.namespaces,o=t.enabled(e)),o),set:e=>{r=e}}),"function"==typeof t.init&&t.init(s),s}function a(e,n){let a=t(this.namespace+(void 0===n?":":n)+e);return a.log=this.log,a}function i(e,t){let n=0,a=0,i=-1,o=0;for(;n<e.length;)if(a<t.length&&(t[a]===e[n]||"*"===t[a]))"*"===t[a]?(i=a,o=n):n++,a++;else{if(-1===i)return!1;a=i+1,n=++o}for(;a<t.length&&"*"===t[a];)a++;return a===t.length}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names,...t.skips.map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){for(let n of(t.save(e),t.namespaces=e,t.names=[],t.skips=[],("string"==typeof e?e:"").trim().replace(/\s+/g,",").split(",").filter(Boolean)))"-"===n[0]?t.skips.push(n.slice(1)):t.names.push(n)},t.enabled=function(e){for(let n of t.skips)if(i(e,n))return!1;for(let n of t.names)if(i(e,n))return!0;return!1},t.humanize=n(67802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(n=>{t[n]=e[n]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t)|0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},96963:(e,t,n)=>{"use strict";n.d(t,{B:()=>c});var a,i=n(43210),o=n(66156),r=(a||(a=n.t(i,2)))["useId".toString()]||(()=>void 0),s=0;function c(e){let[t,n]=i.useState(r());return(0,o.N)(()=>{e||n(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},98599:(e,t,n)=>{"use strict";n.d(t,{s:()=>r,t:()=>o});var a=n(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,a=e.map(e=>{let a=i(e,t);return n||"function"!=typeof a||(n=!0),a});if(n)return()=>{for(let t=0;t<a.length;t++){let n=a[t];"function"==typeof n?n():i(e[t],null)}}}}function r(...e){return a.useCallback(o(...e),e)}},99270:(e,t,n)=>{"use strict";n.d(t,{A:()=>a});let a=(0,n(62688).A)("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99819:e=>{"use strict";e.exports=Function.prototype.call}};