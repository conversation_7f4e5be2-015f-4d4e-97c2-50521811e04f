(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[489],{11723:(e,t,a)=>{"use strict";a.d(t,{HC:()=>s,P_:()=>l,Rb:()=>c,Sp:()=>i,q6:()=>n,qg:()=>o});var r=a(55097);let n=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let n=await fetch("".concat("http://localhost:3000/api","/colleges/").concat(e,"/teachers"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to add teacher. Please try again.")}let s=await n.json();return(0,r.$y)(s,!0,"Teacher added successfully!")}catch(e){return console.error("Error adding teacher:",e),(0,r.hS)(e.message||"Failed to add teacher. Please try again.","Failed to add teacher. Please try again.")}},s=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};try{let s=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!s)return console.error("No authentication token found"),(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let i=new URLSearchParams({page:t.toString(),limit:a.toString(),...n}),o="".concat("http://localhost:3000/api","/colleges/").concat(e,"/teachers?").concat(i);console.log("Fetching teachers: ".concat(o));let l=await fetch(o,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},cache:"no-store"});if(!l.ok){let e=await l.text();try{let t=JSON.parse(e);return(0,r.hS)(t.message||"Error: ".concat(l.status),"Failed to load teachers. Please try again.")}catch(e){return(0,r.hS)("Error: ".concat(l.status," - ").concat(l.statusText),"Failed to load teachers. Please try again.")}}let c=await l.json();if(console.log("Raw API response:",c),Array.isArray(c)){console.log("API returned an array, converting to paginated format");let e={teachers:c,total:c.length,page:t,limit:a,totalPages:Math.ceil(c.length/a)};return(0,r.$y)(e)}return(0,r.$y)(c)}catch(e){return console.error("Error fetching college teachers:",e),(0,r.hS)(e.message||"Failed to load teachers. Please try again.","Failed to load teachers. Please try again.")}},i=async(e,t)=>{try{let a=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!a)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher with data:",t);let n=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!n.ok){let e=await n.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(n.status),"Failed to update teacher. Please try again.")}let s=await n.json();return(0,r.$y)(s,!0,"Teacher updated successfully!")}catch(e){return console.error("Error updating teacher:",e),(0,r.hS)(e.message||"Failed to update teacher. Please try again.","Failed to update teacher. Please try again.")}},o=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let a=await fetch("".concat("http://localhost:3000/api","/teachers/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)}});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to delete teacher. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"Teacher deleted successfully!")}catch(e){return console.error("Error deleting teacher:",e),(0,r.hS)(e.message||"Failed to delete teacher","Failed to delete teacher. Please try again.")}},l=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher profile with data:",e);let a=await fetch("".concat("http://localhost:3000/api","/teachers/me"),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok){let e=await a.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(a.status),"Failed to update profile. Please try again.")}let n=await a.json();return(0,r.$y)(n,!0,"Profile updated successfully!")}catch(e){return console.error("Error updating teacher profile:",e),(0,r.hS)(e.message||"Failed to update profile. Please try again.","Failed to update profile. Please try again.")}},c=async()=>{try{let e=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!e)return(0,r.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let t=await fetch("".concat("http://localhost:3000/api","/users/me"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!t.ok){let e=await t.json().catch(()=>({}));return(0,r.hS)(e.message||"Error: ".concat(t.status),"Failed to load profile. Please try again.")}let a=await t.json();return(0,r.$y)(a)}catch(e){return console.error("Error fetching current teacher profile:",e),(0,r.hS)(e.message||"Failed to load profile. Please try again.","Failed to load profile. Please try again.")}}},17307:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var r=a(95155),n=a(12115),s=a(90221),i=a(62177),o=a(55594),l=a(71007),c=a(29869),d=a(19420),u=a(28883),h=a(30285),m=a(17759),g=a(62523),p=a(91394),f=a(22346);let x=o.Ik({name:o.Yj().min(2,{message:"Name must be at least 2 characters."}),phone:o.Yj().regex(/^\+?[0-9\s-]{10,15}$/,{message:"Please enter a valid phone number."}),email:o.Yj().email({message:"Please enter a valid email address."}),profileImageUrl:o.Yj().optional()});function v(e){let{defaultValues:t={name:"",phone:"",email:"",profileImageUrl:""},onSubmit:a=()=>{},onCancel:o=()=>{}}=e,[v,b]=(0,n.useState)(t.profileImageUrl||null),[y,j]=(0,n.useState)(!1),w=(0,i.mN)({resolver:(0,s.u)(x),defaultValues:t});(0,n.useEffect)(()=>{t.profileImageUrl&&b(t.profileImageUrl)},[t.profileImageUrl]);let N=async e=>{j(!0);try{let t={...e,profileImageUrl:v||e.profileImageUrl||void 0};await a(t)}finally{j(!1)}};return(0,r.jsxs)("div",{className:"w-full mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Settings Page"}),(0,r.jsx)("p",{className:"text-muted-foreground font-medium"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(h.$,{variant:"outline",onClick:o,className:"text-[#05603A] font-medium w-[101px] h-[48px] rounded-[6px] border-[1px] border-[#05603A] p-3 text-center bg-white",style:{top:"113px",left:"1210px",gap:"8px"},children:"Cancel"}),(0,r.jsx)(h.$,{type:"submit",form:"settings-form",disabled:y,className:"bg-[#05603A] text-white font-medium w-[101px] h-[48px] rounded-[6px] border-[1px] p-3 text-center",style:{top:"113px",left:"1210px",gap:"8px"},children:"Save"})]})]}),(0,r.jsx)(f.w,{className:"my-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg border shadow-sm p-6",children:(0,r.jsx)(m.lV,{...w,children:(0,r.jsxs)("form",{id:"settings-form",onSubmit:w.handleSubmit(N),className:"space-y-6 w-[789px]",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg border shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-base font-medium mb-4",children:"Edit profile photo"}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(p.eu,{className:"h-16 w-16",children:[(0,r.jsx)(p.BK,{src:v||"/placeholder.svg?height=64&width=64",alt:"Profile"}),(0,r.jsx)(p.q5,{children:(0,r.jsx)(l.A,{className:"h-8 w-8"})})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(h.$,{type:"button",className:"relative bg-[#2563EB] text-white font-medium",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Upload new picture",(0,r.jsx)("input",{type:"file",className:"absolute inset-0 opacity-0 cursor-pointer",accept:"image/*",onChange:e=>{var t;let a=null===(t=e.target.files)||void 0===t?void 0:t[0];if(a){let e=new FileReader;e.onload=e=>{var t;b(null===(t=e.target)||void 0===t?void 0:t.result)},e.readAsDataURL(a)}}})]}),(0,r.jsx)(h.$,{type:"button",variant:"outline",className:"text-[#EF4444] hover:text-red-600 hover:bg-red-50 bg-white font-medium",onClick:()=>{b(null)},children:"Delete"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg border shadow-sm p-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsx)(m.zB,{control:w.control,name:"name",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{className:"font-medium",children:"Full Name"}),(0,r.jsx)(m.MJ,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(l.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-muted-foreground"}),(0,r.jsx)(g.p,{className:"pl-10 font-medium text-[#6B7280]",placeholder:"John Doe",...t})]})}),(0,r.jsx)(m.C5,{})]})}}),(0,r.jsx)(m.zB,{control:w.control,name:"phone",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{className:"font-medium",children:"Phone Number"}),(0,r.jsx)(m.MJ,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(d.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-muted-foreground"}),(0,r.jsx)(g.p,{className:"pl-10 font-medium text-[#6B7280]",placeholder:"+****************",...t})]})}),(0,r.jsx)(m.C5,{})]})}})]}),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(m.zB,{control:w.control,name:"email",render:e=>{let{field:t}=e;return(0,r.jsxs)(m.eI,{children:[(0,r.jsx)(m.lR,{className:"font-medium",children:"Email"}),(0,r.jsx)(m.MJ,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.A,{className:"absolute left-3 top-2.5 h-5 w-5 text-muted-foreground"}),(0,r.jsx)(g.p,{className:"pl-10 font-medium text-[#6B7280]",placeholder:"<EMAIL>",...t})]})}),(0,r.jsx)(m.C5,{})]})}})})]})]})})})]})}var b=a(11723),y=a(88262),j=a(51154);function w(){let[e,t]=(0,n.useState)(null),[a,s]=(0,n.useState)(!0),[i,o]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{try{s(!0);let e=await (0,b.Rb)();t(e)}catch(e){o(e.message||"Failed to load profile data"),(0,y.o)({title:"Error",description:"Failed to load profile data",variant:"destructive"})}finally{s(!1)}})()},[]);let l=async e=>{try{await (0,b.P_)(e),(0,y.o)({title:"Success",description:"Settings saved successfully!"});let a=await (0,b.Rb)();t(a)}catch(e){throw(0,y.o)({title:"Error",description:e.message||"Failed to save settings",variant:"destructive"}),e}};return a?(0,r.jsx)("div",{className:"container py-10 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 animate-spin"}),(0,r.jsx)("span",{children:"Loading profile..."})]})}):i?(0,r.jsx)("div",{className:"container py-10",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("p",{className:"text-red-600",children:["Error: ",i]}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"mt-2 text-blue-600 hover:underline",children:"Try again"})]})}):(0,r.jsx)("div",{className:"container py-10",children:(0,r.jsx)(v,{defaultValues:{name:(null==e?void 0:e.displayName)||(null==e?void 0:e.name)||"",phone:(null==e?void 0:e.phone)||"",email:(null==e?void 0:e.email)||"",profileImageUrl:(null==e?void 0:e.profileImageUrl)||""},onSubmit:l,onCancel:()=>{(0,y.o)({title:"Info",description:"Changes discarded"})}})})}},17759:(e,t,a)=>{"use strict";a.d(t,{C5:()=>v,MJ:()=>f,Rr:()=>x,eI:()=>g,lR:()=>p,lV:()=>c,zB:()=>u});var r=a(95155),n=a(12115),s=a(66634),i=a(62177),o=a(59434),l=a(85057);let c=i.Op,d=n.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(d.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},h=()=>{let e=n.useContext(d),t=n.useContext(m),{getFieldState:a}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),s=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...s}},m=n.createContext({});function g(e){let{className:t,...a}=e,s=n.useId();return(0,r.jsx)(m.Provider,{value:{id:s},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...a})})}function p(e){let{className:t,...a}=e,{error:n,formItemId:s}=h();return(0,r.jsx)(l.J,{"data-slot":"form-label","data-error":!!n,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:s,...a})}function f(e){let{...t}=e,{error:a,formItemId:n,formDescriptionId:i,formMessageId:o}=h();return(0,r.jsx)(s.Slot,{"data-slot":"form-control",id:n,"aria-describedby":a?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!a,...t})}function x(e){let{className:t,...a}=e,{formDescriptionId:n}=h();return(0,r.jsx)("p",{"data-slot":"form-description",id:n,className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function v(e){var t;let{className:a,...n}=e,{error:s,formMessageId:i}=h(),l=s?String(null!==(t=null==s?void 0:s.message)&&void 0!==t?t:""):n.children;return l?(0,r.jsx)("p",{"data-slot":"form-message",id:i,className:(0,o.cn)("text-destructive text-sm",a),...n,children:l}):null}},22346:(e,t,a)=>{"use strict";a.d(t,{w:()=>i});var r=a(95155);a(12115);var n=a(87489),s=a(59434);function i(e){let{className:t,orientation:a="horizontal",decorative:i=!0,...o}=e;return(0,r.jsx)(n.b,{"data-slot":"separator-root",decorative:i,orientation:a,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>o});var r=a(95155);a(12115);var n=a(66634),s=a(74466),i=a(59434);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:s,asChild:l=!1,...c}=e,d=l?n.Slot:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:s,className:t})),...c})}},52434:(e,t,a)=>{Promise.resolve().then(a.bind(a,17307))},55097:(e,t,a)=>{"use strict";a.d(t,{$y:()=>s,cY:()=>i,hS:()=>n});var r=a(56671);function n(e){var t,a,n,s;let i,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",l=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=o;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)&&(c=e.data.message),(null==e?void 0:e.status)?i=e.status:(null==e?void 0:null===(s=e.response)||void 0===s?void 0:s.status)&&(i=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===i?c="Server error. Please try again later.":503===i&&(c="Service temporarily unavailable. Please try again later.")),l&&r.oR.error(c),{success:!1,error:c,statusCode:i}}function s(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a=arguments.length>2?arguments[2]:void 0;return t&&a&&r.oR.success(a),{success:!0,data:e}}function i(e){return!0===e.success}},59434:(e,t,a)=>{"use strict";a.d(t,{b:()=>i,cn:()=>s});var r=a(52596),n=a(39688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,n.QP)((0,r.$)(t))}function i(e){return new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>s});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155);a(12115);var n=a(40968),s=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},88262:(e,t,a)=>{"use strict";a.d(t,{d:()=>c,o:()=>d});var r=a(12115);new EventTarget;let n=[],s=[];function i(){s.forEach(e=>e([...n]))}function o(e){let t=e.id||Math.random().toString(36).substring(2,9),a={...e,id:t};return n=[...n,a],i(),setTimeout(()=>{l(t)},5e3),t}function l(e){n=n.filter(t=>t.id!==e),i()}function c(){let[e,t]=r.useState(n);return r.useEffect(()=>(s.push(t),t([...n]),()=>{s=s.filter(e=>e!==t)}),[]),{toast:e=>o(e),dismiss:e=>{e?l(e):n.forEach(e=>e.id&&l(e.id))},toasts:e}}let d=e=>o(e)},91394:(e,t,a)=>{"use strict";a.d(t,{BK:()=>o,eu:()=>i,q5:()=>l});var r=a(95155);a(12115);var n=a(85977),s=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(n.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)(n._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4277,6671,7117,221,925,8441,1684,7358],()=>t(52434)),_N_E=e.O()}]);