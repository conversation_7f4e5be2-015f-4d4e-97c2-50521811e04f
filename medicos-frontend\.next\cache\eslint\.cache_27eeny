[{"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx": "8", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\super\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx": "13", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\loading.tsx": "14", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx": "18", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx": "19", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx": "24", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx": "25", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\settings\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\add-question-form.tsx": "29", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-edit-form.tsx": "30", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-registration-form.tsx": "31", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\edit-question-form.tsx": "32", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\file-uploader.tsx": "33", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\pagination-control.tsx": "34", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\phone-input.tsx": "35", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-bank.tsx": "36", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-list.tsx": "37", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-skeleton.tsx": "38", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-card.tsx": "39", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-grid.tsx": "40", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\auth\\protected-route.tsx": "41", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\Breadcrumb.tsx": "42", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeAdmin\\Tabs.tsx": "43", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeChart.tsx": "44", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\index.tsx": "45", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\TablePagination.tsx": "46", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\debug\\TokenDebug.tsx": "47", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\AboutSection.tsx": "48", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Consultation.tsx": "49", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Footer.tsx": "50", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\hero-section.tsx": "51", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\LessonsSection.tsx": "52", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\navbar.tsx": "53", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\NavigationTabs.tsx": "54", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectCard.tsx": "55", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectChart.tsx": "56", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TestimonialsContact.tsx": "57", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TransformingEducation.tsx": "58", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\command-menu.tsx": "59", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-layout.tsx": "60", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-navbar.tsx": "61", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-sidebar.tsx": "62", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\navigation-loading-wrapper.tsx": "63", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\RoleProtectedRoute.tsx": "64", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\StatCard.tsx": "65", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SubjectQuestionsChart.tsx": "66", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SuperAdmin\\Tabs.tsx": "67", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\pagination.tsx": "68", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-header.tsx": "69", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-row.tsx": "70", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-title.tsx": "71", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\teachers-table.tsx": "72", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\types.ts": "73", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TabNavigation.tsx": "74", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\question-paper-wizard.tsx": "75", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\settings-form.tsx": "76", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\actions-step.tsx": "77", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\course-subject-step.tsx": "78", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\difficulty-level-step.tsx": "79", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\include-answers-step.tsx": "80", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\multi-subject-config-step.tsx": "81", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\paper-customization-step.tsx": "82", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-selection-step.tsx": "83", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-title-description-step.tsx": "84", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-type-step.tsx": "85", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\Tabs.tsx": "86", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\info-message.tsx": "87", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\option-button.tsx": "88", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-indicator.tsx": "89", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-navigation.tsx": "90", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\user-profile.tsx": "91", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\ActionsMenu.tsx": "92", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\AddTeacherForm.tsx": "93", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\EditTeacherForm.tsx": "94", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\FilterModal.tsx": "95", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\StatusBadge.tsx": "96", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\Tabs.tsx": "97", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\TeacherProfile.tsx": "98", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\theme-toggle.tsx": "99", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TopTeachersList.tsx": "100", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\alert-dialog.tsx": "101", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\avatar.tsx": "102", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\badge.tsx": "103", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\base64-image.tsx": "104", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\button.tsx": "105", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\card.tsx": "106", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chart.tsx": "107", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\command.tsx": "108", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dialog.tsx": "109", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dropdown-menu.tsx": "110", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\form.tsx": "111", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\input.tsx": "112", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\label.tsx": "113", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\math-text.tsx": "114", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\page-loading.tsx": "115", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\pagination.tsx": "116", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\popover.tsx": "117", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\progress.tsx": "118", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\radio-group.tsx": "119", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\select.tsx": "120", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\separator.tsx": "121", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sheet.tsx": "122", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sidebar.tsx": "123", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\skeleton.tsx": "124", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\slider.tsx": "125", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\table.tsx": "126", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tabs.tsx": "127", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\text-with-images.tsx": "128", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\textarea.tsx": "129", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toast.tsx": "130", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toaster.tsx": "131", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tooltip.tsx": "132", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\use-toast.ts": "133", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\UsageChart.tsx": "134", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\hooks\\use-mobile.ts": "135", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\admin-analytics.ts": "136", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\analytics.ts": "137", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\api.ts": "138", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\apiClient.ts": "139", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\college.ts": "140", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questionPapers.ts": "141", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questions.ts": "142", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\subjects.ts": "143", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\teachers.ts": "144", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\topics.ts": "145", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\upload.ts": "146", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api.ts": "147", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\auth.ts": "148", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx": "149", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\enums.ts": "150", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\menuItems.ts": "151", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\firebase.ts": "152", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\ReactQueryProvider.tsx": "153", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\interface.ts": "154", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\menu.ts": "155", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\university.ts": "156", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils.ts": "157", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\services\\teacherService.ts": "158", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\types\\question.ts": "159", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\imageUtils.ts": "160", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\pdfGenerator.ts": "161", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-answers-excel\\route.ts": "162", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-paper-pdf\\route.ts": "163", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-solutions-pdf\\route.ts": "164", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\subject-chapter-manager.tsx": "165", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\chapter-selection-step.tsx": "166", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\topic-selection-step.tsx": "167", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\test\\table-test.tsx": "168", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chemical-image-display.tsx": "169", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\enhanced-text-renderer.tsx": "170", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\chapters.ts": "171", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils\\errorHandler.ts": "172", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\test-image-extraction.js": "173", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\htmlToImage.ts": "174", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\mathRenderer.ts": "175", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\svgMathRenderer.ts": "176"}, {"size": 818, "mtime": 1754498156029, "results": "177", "hashOfConfig": "178"}, {"size": 1086, "mtime": 1754498200589, "results": "179", "hashOfConfig": "178"}, {"size": 1349, "mtime": 1754121124626, "results": "180", "hashOfConfig": "178"}, {"size": 3420, "mtime": 1754059417275, "results": "181", "hashOfConfig": "178"}, {"size": 2298, "mtime": 1754498026874, "results": "182", "hashOfConfig": "178"}, {"size": 2462, "mtime": 1749352078690, "results": "183", "hashOfConfig": "178"}, {"size": 640, "mtime": 1746861838647, "results": "184", "hashOfConfig": "178"}, {"size": 166, "mtime": 1748951735270, "results": "185", "hashOfConfig": "178"}, {"size": 3337, "mtime": 1754498238828, "results": "186", "hashOfConfig": "178"}, {"size": 1605, "mtime": 1754498277271, "results": "187", "hashOfConfig": "178"}, {"size": 465, "mtime": 1754498295415, "results": "188", "hashOfConfig": "178"}, {"size": 450, "mtime": 1754498321097, "results": "189", "hashOfConfig": "178"}, {"size": 646, "mtime": 1746861855566, "results": "190", "hashOfConfig": "178"}, {"size": 170, "mtime": 1748951739495, "results": "191", "hashOfConfig": "178"}, {"size": 7582, "mtime": 1754498393984, "results": "192", "hashOfConfig": "178"}, {"size": 18621, "mtime": 1754498463943, "results": "193", "hashOfConfig": "178"}, {"size": 6097, "mtime": 1746863258819, "results": "194", "hashOfConfig": "178"}, {"size": 962, "mtime": 1749309798225, "results": "195", "hashOfConfig": "178"}, {"size": 161, "mtime": 1748951726917, "results": "196", "hashOfConfig": "178"}, {"size": 13578, "mtime": 1754058402135, "results": "197", "hashOfConfig": "178"}, {"size": 3032, "mtime": 1748278584823, "results": "198", "hashOfConfig": "178"}, {"size": 13813, "mtime": 1747487664800, "results": "199", "hashOfConfig": "178"}, {"size": 20943, "mtime": 1753291108400, "results": "200", "hashOfConfig": "178"}, {"size": 634, "mtime": 1746861875933, "results": "201", "hashOfConfig": "178"}, {"size": 170, "mtime": 1748951736389, "results": "202", "hashOfConfig": "178"}, {"size": 1165, "mtime": 1748941728476, "results": "203", "hashOfConfig": "178"}, {"size": 4092, "mtime": 1748950224444, "results": "204", "hashOfConfig": "178"}, {"size": 3034, "mtime": 1749356941741, "results": "205", "hashOfConfig": "178"}, {"size": 43641, "mtime": 1754497496295, "results": "206", "hashOfConfig": "178"}, {"size": 8173, "mtime": 1754059439317, "results": "207", "hashOfConfig": "178"}, {"size": 7546, "mtime": 1754059432323, "results": "208", "hashOfConfig": "178"}, {"size": 26350, "mtime": 1753985456390, "results": "209", "hashOfConfig": "178"}, {"size": 5859, "mtime": 1744388637160, "results": "210", "hashOfConfig": "178"}, {"size": 1594, "mtime": 1745038739730, "results": "211", "hashOfConfig": "178"}, {"size": 3904, "mtime": 1744388656393, "results": "212", "hashOfConfig": "178"}, {"size": 19300, "mtime": 1753985468431, "results": "213", "hashOfConfig": "178"}, {"size": 18494, "mtime": 1753884941115, "results": "214", "hashOfConfig": "178"}, {"size": 1234, "mtime": 1749310653822, "results": "215", "hashOfConfig": "178"}, {"size": 6230, "mtime": 1752079280943, "results": "216", "hashOfConfig": "178"}, {"size": 1468, "mtime": 1747494337010, "results": "217", "hashOfConfig": "178"}, {"size": 1738, "mtime": 1747486281263, "results": "218", "hashOfConfig": "178"}, {"size": 2005, "mtime": 1743735365093, "results": "219", "hashOfConfig": "178"}, {"size": 2024, "mtime": 1748941705879, "results": "220", "hashOfConfig": "178"}, {"size": 8166, "mtime": 1753523123606, "results": "221", "hashOfConfig": "178"}, {"size": 10050, "mtime": 1743734514431, "results": "222", "hashOfConfig": "178"}, {"size": 4207, "mtime": 1743734474231, "results": "223", "hashOfConfig": "178"}, {"size": 2658, "mtime": 1749719660381, "results": "224", "hashOfConfig": "178"}, {"size": 1078, "mtime": 1748279045736, "results": "225", "hashOfConfig": "178"}, {"size": 2759, "mtime": 1748279055469, "results": "226", "hashOfConfig": "178"}, {"size": 1516, "mtime": 1748154465611, "results": "227", "hashOfConfig": "178"}, {"size": 1772, "mtime": 1748157685392, "results": "228", "hashOfConfig": "178"}, {"size": 1457, "mtime": 1748279050770, "results": "229", "hashOfConfig": "178"}, {"size": 4638, "mtime": 1749276901013, "results": "230", "hashOfConfig": "178"}, {"size": 918, "mtime": 1748158980847, "results": "231", "hashOfConfig": "178"}, {"size": 1526, "mtime": 1748279067042, "results": "232", "hashOfConfig": "178"}, {"size": 475, "mtime": 1748158604295, "results": "233", "hashOfConfig": "178"}, {"size": 9441, "mtime": 1748279059343, "results": "234", "hashOfConfig": "178"}, {"size": 2646, "mtime": 1748279040960, "results": "235", "hashOfConfig": "178"}, {"size": 4215, "mtime": 1750093835381, "results": "236", "hashOfConfig": "178"}, {"size": 927, "mtime": 1744463201290, "results": "237", "hashOfConfig": "178"}, {"size": 5896, "mtime": 1748951252205, "results": "238", "hashOfConfig": "178"}, {"size": 4658, "mtime": 1754113012821, "results": "239", "hashOfConfig": "178"}, {"size": 1223, "mtime": 1748951853965, "results": "240", "hashOfConfig": "178"}, {"size": 1464, "mtime": 1747485910085, "results": "241", "hashOfConfig": "178"}, {"size": 1640, "mtime": 1747541577575, "results": "242", "hashOfConfig": "178"}, {"size": 5826, "mtime": 1748538501229, "results": "243", "hashOfConfig": "178"}, {"size": 1908, "mtime": 1748941695904, "results": "244", "hashOfConfig": "178"}, {"size": 3047, "mtime": 1748083220804, "results": "245", "hashOfConfig": "178"}, {"size": 1417, "mtime": 1748081284162, "results": "246", "hashOfConfig": "178"}, {"size": 3137, "mtime": 1748527162670, "results": "247", "hashOfConfig": "178"}, {"size": 1157, "mtime": 1748083145875, "results": "248", "hashOfConfig": "178"}, {"size": 5799, "mtime": 1749281632443, "results": "249", "hashOfConfig": "178"}, {"size": 943, "mtime": 1748083038143, "results": "250", "hashOfConfig": "178"}, {"size": 1790, "mtime": 1748951745661, "results": "251", "hashOfConfig": "178"}, {"size": 23242, "mtime": 1754114913291, "results": "252", "hashOfConfig": "178"}, {"size": 8598, "mtime": 1754059637168, "results": "253", "hashOfConfig": "178"}, {"size": 1667, "mtime": 1754059607160, "results": "254", "hashOfConfig": "178"}, {"size": 7239, "mtime": 1754059864439, "results": "255", "hashOfConfig": "178"}, {"size": 6110, "mtime": 1750092487411, "results": "256", "hashOfConfig": "178"}, {"size": 2033, "mtime": 1749718395667, "results": "257", "hashOfConfig": "178"}, {"size": 24835, "mtime": 1754117029097, "results": "258", "hashOfConfig": "178"}, {"size": 3136, "mtime": 1754309395956, "results": "259", "hashOfConfig": "178"}, {"size": 3389, "mtime": 1753877992669, "results": "260", "hashOfConfig": "178"}, {"size": 4601, "mtime": 1749718221176, "results": "261", "hashOfConfig": "178"}, {"size": 2572, "mtime": 1749718241594, "results": "262", "hashOfConfig": "178"}, {"size": 1906, "mtime": 1748941713397, "results": "263", "hashOfConfig": "178"}, {"size": 376, "mtime": 1744306232877, "results": "264", "hashOfConfig": "178"}, {"size": 1329, "mtime": 1754058540036, "results": "265", "hashOfConfig": "178"}, {"size": 914, "mtime": 1745140884774, "results": "266", "hashOfConfig": "178"}, {"size": 1193, "mtime": 1754059949986, "results": "267", "hashOfConfig": "178"}, {"size": 7242, "mtime": 1748950269237, "results": "268", "hashOfConfig": "178"}, {"size": 2589, "mtime": 1743734241174, "results": "269", "hashOfConfig": "178"}, {"size": 14350, "mtime": 1754059543486, "results": "270", "hashOfConfig": "178"}, {"size": 4337, "mtime": 1754059555218, "results": "271", "hashOfConfig": "178"}, {"size": 5216, "mtime": 1748083018250, "results": "272", "hashOfConfig": "178"}, {"size": 589, "mtime": 1743734363851, "results": "273", "hashOfConfig": "178"}, {"size": 2170, "mtime": 1749277872317, "results": "274", "hashOfConfig": "178"}, {"size": 1195, "mtime": 1743734427151, "results": "275", "hashOfConfig": "178"}, {"size": 1163, "mtime": 1743411677883, "results": "276", "hashOfConfig": "178"}, {"size": 2964, "mtime": 1748537241527, "results": "277", "hashOfConfig": "178"}, {"size": 3864, "mtime": 1747493623353, "results": "278", "hashOfConfig": "178"}, {"size": 1097, "mtime": 1743411547295, "results": "279", "hashOfConfig": "178"}, {"size": 1631, "mtime": 1743411547299, "results": "280", "hashOfConfig": "178"}, {"size": 1959, "mtime": 1754058346664, "results": "281", "hashOfConfig": "178"}, {"size": 2123, "mtime": 1743411547257, "results": "282", "hashOfConfig": "178"}, {"size": 1989, "mtime": 1743411831373, "results": "283", "hashOfConfig": "178"}, {"size": 9781, "mtime": 1743561991844, "results": "284", "hashOfConfig": "178"}, {"size": 4656, "mtime": 1743411332113, "results": "285", "hashOfConfig": "178"}, {"size": 3813, "mtime": 1743411332135, "results": "286", "hashOfConfig": "178"}, {"size": 8284, "mtime": 1743411547290, "results": "287", "hashOfConfig": "178"}, {"size": 3759, "mtime": 1743734332220, "results": "288", "hashOfConfig": "178"}, {"size": 967, "mtime": 1743411547271, "results": "289", "hashOfConfig": "178"}, {"size": 611, "mtime": 1743734314422, "results": "290", "hashOfConfig": "178"}, {"size": 2684, "mtime": 1751716372054, "results": "291", "hashOfConfig": "178"}, {"size": 820, "mtime": 1748951722646, "results": "292", "hashOfConfig": "178"}, {"size": 4035, "mtime": 1748084548783, "results": "293", "hashOfConfig": "178"}, {"size": 1635, "mtime": 1744388677549, "results": "294", "hashOfConfig": "178"}, {"size": 798, "mtime": 1744388507234, "results": "295", "hashOfConfig": "178"}, {"size": 1466, "mtime": 1746113320858, "results": "296", "hashOfConfig": "178"}, {"size": 6253, "mtime": 1743561991797, "results": "297", "hashOfConfig": "178"}, {"size": 704, "mtime": 1743411547366, "results": "298", "hashOfConfig": "178"}, {"size": 4090, "mtime": 1743411547371, "results": "299", "hashOfConfig": "178"}, {"size": 21727, "mtime": 1744968540744, "results": "300", "hashOfConfig": "178"}, {"size": 276, "mtime": 1743411547378, "results": "301", "hashOfConfig": "178"}, {"size": 2427, "mtime": 1745140151553, "results": "302", "hashOfConfig": "178"}, {"size": 2448, "mtime": 1743734537564, "results": "303", "hashOfConfig": "178"}, {"size": 1969, "mtime": 1743947262123, "results": "304", "hashOfConfig": "178"}, {"size": 1502, "mtime": 1752980163332, "results": "305", "hashOfConfig": "178"}, {"size": 759, "mtime": 1744388618328, "results": "306", "hashOfConfig": "178"}, {"size": 4886, "mtime": 1749309412142, "results": "307", "hashOfConfig": "178"}, {"size": 953, "mtime": 1749309810030, "results": "308", "hashOfConfig": "178"}, {"size": 1891, "mtime": 1743411547374, "results": "309", "hashOfConfig": "178"}, {"size": 1886, "mtime": 1749309728742, "results": "310", "hashOfConfig": "178"}, {"size": 6840, "mtime": 1753522976795, "results": "311", "hashOfConfig": "178"}, {"size": 565, "mtime": 1743406728995, "results": "312", "hashOfConfig": "178"}, {"size": 3293, "mtime": 1748942722046, "results": "313", "hashOfConfig": "178"}, {"size": 5568, "mtime": 1753523020965, "results": "314", "hashOfConfig": "178"}, {"size": 2148, "mtime": 1748539684250, "results": "315", "hashOfConfig": "178"}, {"size": 1249, "mtime": 1747497035401, "results": "316", "hashOfConfig": "178"}, {"size": 9732, "mtime": 1754327693784, "results": "317", "hashOfConfig": "178"}, {"size": 15519, "mtime": 1753982272265, "results": "318", "hashOfConfig": "178"}, {"size": 20091, "mtime": 1754324130390, "results": "319", "hashOfConfig": "178"}, {"size": 4520, "mtime": 1754033562484, "results": "320", "hashOfConfig": "178"}, {"size": 9698, "mtime": 1752078772721, "results": "321", "hashOfConfig": "178"}, {"size": 2749, "mtime": 1753981678189, "results": "322", "hashOfConfig": "178"}, {"size": 981, "mtime": 1747493748067, "results": "323", "hashOfConfig": "178"}, {"size": 4252, "mtime": 1748947428443, "results": "324", "hashOfConfig": "178"}, {"size": 718, "mtime": 1746861811304, "results": "325", "hashOfConfig": "178"}, {"size": 8172, "mtime": 1749309804607, "results": "326", "hashOfConfig": "178"}, {"size": 116, "mtime": 1747486012484, "results": "327", "hashOfConfig": "178"}, {"size": 5186, "mtime": 1753981988962, "results": "328", "hashOfConfig": "178"}, {"size": 875, "mtime": 1747069638958, "results": "329", "hashOfConfig": "178"}, {"size": 784, "mtime": 1743735208208, "results": "330", "hashOfConfig": "178"}, {"size": 1579, "mtime": 1748538942845, "results": "331", "hashOfConfig": "178"}, {"size": 611, "mtime": 1744966332004, "results": "332", "hashOfConfig": "178"}, {"size": 267, "mtime": 1747493757572, "results": "333", "hashOfConfig": "178"}, {"size": 830, "mtime": 1749048619068, "results": "334", "hashOfConfig": "178"}, {"size": 2655, "mtime": 1748084176532, "results": "335", "hashOfConfig": "178"}, {"size": 2036, "mtime": 1753981590902, "results": "336", "hashOfConfig": "178"}, {"size": 9171, "mtime": 1752907720744, "results": "337", "hashOfConfig": "178"}, {"size": 39711, "mtime": 1754498630298, "results": "338", "hashOfConfig": "178"}, {"size": 3647, "mtime": 1754498348689, "results": "339", "hashOfConfig": "178"}, {"size": 28589, "mtime": 1754310242548, "results": "340", "hashOfConfig": "178"}, {"size": 20980, "mtime": 1753879445361, "results": "341", "hashOfConfig": "178"}, {"size": 27294, "mtime": 1754124252020, "results": "342", "hashOfConfig": "178"}, {"size": 13078, "mtime": 1754291647475, "results": "343", "hashOfConfig": "178"}, {"size": 10772, "mtime": 1753982167889, "results": "344", "hashOfConfig": "178"}, {"size": 3273, "mtime": 1752980733333, "results": "345", "hashOfConfig": "178"}, {"size": 11160, "mtime": 1754058336322, "results": "346", "hashOfConfig": "178"}, {"size": 11602, "mtime": 1752980699691, "results": "347", "hashOfConfig": "178"}, {"size": 9434, "mtime": 1754291389413, "results": "348", "hashOfConfig": "178"}, {"size": 4157, "mtime": 1752078662945, "results": "349", "hashOfConfig": "178"}, {"size": 5339, "mtime": 1752600392806, "results": "350", "hashOfConfig": "351"}, {"size": 9161, "mtime": 1751720807660, "results": "352", "hashOfConfig": "178"}, {"size": 8070, "mtime": 1751720349397, "results": "353", "hashOfConfig": "178"}, {"size": 7287, "mtime": 1751721776870, "results": "354", "hashOfConfig": "178"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "59<PERSON>ot", {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ppsau4", {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\super\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx", ["883", "884", "885", "886", "887"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx", ["888"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx", ["889", "890"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx", ["891", "892"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx", ["893", "894", "895"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx", ["896"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx", ["897", "898"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\add-question-form.tsx", ["899", "900", "901", "902", "903"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-edit-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-registration-form.tsx", ["904"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\edit-question-form.tsx", ["905", "906", "907", "908", "909"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\file-uploader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\pagination-control.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\phone-input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-bank.tsx", ["910", "911", "912"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-list.tsx", ["913"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-card.tsx", ["914"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\auth\\protected-route.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\Breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeAdmin\\Tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeChart.tsx", ["915", "916", "917", "918"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\TablePagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\debug\\TokenDebug.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\AboutSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Consultation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Footer.tsx", ["919"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\LessonsSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\NavigationTabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TestimonialsContact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TransformingEducation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\command-menu.tsx", ["920", "921", "922", "923", "924"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-layout.tsx", ["925", "926"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-navbar.tsx", ["927", "928", "929", "930", "931"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-sidebar.tsx", ["932", "933", "934", "935", "936", "937", "938", "939", "940"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\navigation-loading-wrapper.tsx", ["941"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\RoleProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\StatCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SubjectQuestionsChart.tsx", ["942", "943", "944"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SuperAdmin\\Tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-row.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-title.tsx", ["945"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\teachers-table.tsx", ["946", "947", "948", "949", "950", "951"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TabNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\question-paper-wizard.tsx", ["952"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\settings-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\actions-step.tsx", ["953", "954", "955"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\course-subject-step.tsx", ["956"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\difficulty-level-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\include-answers-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\multi-subject-config-step.tsx", ["957", "958", "959"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\paper-customization-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-selection-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-title-description-step.tsx", ["960"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-type-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\Tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\info-message.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\option-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-indicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\user-profile.tsx", ["961", "962", "963", "964", "965", "966", "967", "968"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\ActionsMenu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\AddTeacherForm.tsx", ["969", "970", "971", "972", "973"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\EditTeacherForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\FilterModal.tsx", ["974", "975", "976"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\StatusBadge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\Tabs.tsx", ["977"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\TeacherProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TopTeachersList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\base64-image.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\math-text.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\page-loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sidebar.tsx", ["978"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\text-with-images.tsx", ["979"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toaster.tsx", ["980"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\use-toast.ts", ["981", "982", "983"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\UsageChart.tsx", ["984", "985", "986"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\hooks\\use-mobile.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\admin-analytics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\analytics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\apiClient.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\college.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questionPapers.ts", ["987", "988", "989"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questions.ts", ["990"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\subjects.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\teachers.ts", ["991", "992"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\topics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\upload.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\auth.ts", ["993"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\enums.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\menuItems.ts", ["994", "995", "996", "997", "998", "999", "1000", "1001"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\ReactQueryProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\interface.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\menu.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\university.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\services\\teacherService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\types\\question.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\imageUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\pdfGenerator.ts", ["1002", "1003"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-answers-excel\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-paper-pdf\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-solutions-pdf\\route.ts", ["1004"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\subject-chapter-manager.tsx", ["1005", "1006"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\chapter-selection-step.tsx", ["1007", "1008"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\topic-selection-step.tsx", ["1009"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\test\\table-test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chemical-image-display.tsx", ["1010"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\enhanced-text-renderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\chapters.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils\\errorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\test-image-extraction.js", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\htmlToImage.ts", ["1011"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\mathRenderer.ts", ["1012"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\svgMathRenderer.ts", ["1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024"], [], {"ruleId": "1025", "severity": 1, "message": "1026", "line": 23, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 23, "endColumn": 27}, {"ruleId": "1025", "severity": 1, "message": "1028", "line": 30, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 30, "endColumn": 21}, {"ruleId": "1025", "severity": 1, "message": "1029", "line": 38, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 38, "endColumn": 20}, {"ruleId": "1025", "severity": 1, "message": "1030", "line": 199, "column": 17, "nodeType": null, "messageId": "1027", "endLine": 199, "endColumn": 30}, {"ruleId": "1025", "severity": 1, "message": "1031", "line": 306, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 306, "endColumn": 24}, {"ruleId": "1025", "severity": 1, "message": "1032", "line": 8, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 8, "endColumn": 14}, {"ruleId": "1025", "severity": 1, "message": "1033", "line": 4, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 34}, {"ruleId": "1025", "severity": 1, "message": "1034", "line": 9, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 13}, {"ruleId": "1025", "severity": 1, "message": "1032", "line": 9, "column": 23, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 27}, {"ruleId": "1025", "severity": 1, "message": "1035", "line": 9, "column": 29, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 33}, {"ruleId": "1025", "severity": 1, "message": "1032", "line": 9, "column": 23, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 27}, {"ruleId": "1025", "severity": 1, "message": "1035", "line": 9, "column": 29, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 33}, {"ruleId": "1025", "severity": 1, "message": "1036", "line": 9, "column": 35, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 39}, {"ruleId": "1025", "severity": 1, "message": "1037", "line": 3, "column": 27, "nodeType": null, "messageId": "1027", "endLine": 3, "endColumn": 36}, {"ruleId": "1025", "severity": 1, "message": "1038", "line": 8, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 8, "endColumn": 19}, {"ruleId": "1025", "severity": 1, "message": "1039", "line": 10, "column": 9, "nodeType": null, "messageId": "1027", "endLine": 10, "endColumn": 24}, {"ruleId": "1025", "severity": 1, "message": "1040", "line": 43, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 43, "endColumn": 27}, {"ruleId": "1025", "severity": 1, "message": "1041", "line": 44, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 44, "endColumn": 25}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 223, "column": 9, "nodeType": "1044", "messageId": "1045", "endLine": 223, "endColumn": 71, "suggestions": "1046"}, {"ruleId": "1047", "severity": 1, "message": "1048", "line": 329, "column": 11, "nodeType": "1049", "messageId": "1050", "endLine": 329, "endColumn": 28, "fix": "1051"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 706, "column": 38, "nodeType": "1044", "messageId": "1045", "endLine": 706, "endColumn": 59}, {"ruleId": "1025", "severity": 1, "message": "1052", "line": 15, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 15, "endColumn": 20}, {"ruleId": "1025", "severity": 1, "message": "1053", "line": 15, "column": 29, "nodeType": null, "messageId": "1027", "endLine": 15, "endColumn": 44}, {"ruleId": "1025", "severity": 1, "message": "1054", "line": 41, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 41, "endColumn": 20}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 262, "column": 9, "nodeType": "1044", "messageId": "1045", "endLine": 262, "endColumn": 71, "suggestions": "1055"}, {"ruleId": "1047", "severity": 1, "message": "1048", "line": 356, "column": 11, "nodeType": "1049", "messageId": "1050", "endLine": 356, "endColumn": 28, "fix": "1056"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 580, "column": 36, "nodeType": "1044", "messageId": "1045", "endLine": 580, "endColumn": 57}, {"ruleId": "1025", "severity": 1, "message": "1057", "line": 38, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 38, "endColumn": 22}, {"ruleId": "1025", "severity": 1, "message": "1058", "line": 38, "column": 24, "nodeType": null, "messageId": "1027", "endLine": 38, "endColumn": 39}, {"ruleId": "1059", "severity": 1, "message": "1060", "line": 432, "column": 15, "nodeType": "1061", "endLine": 432, "endColumn": 44}, {"ruleId": "1025", "severity": 1, "message": "1062", "line": 21, "column": 26, "nodeType": null, "messageId": "1027", "endLine": 21, "endColumn": 40}, {"ruleId": "1025", "severity": 1, "message": "1063", "line": 24, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 24, "endColumn": 22}, {"ruleId": "1025", "severity": 1, "message": "1064", "line": 5, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 12}, {"ruleId": "1025", "severity": 1, "message": "1065", "line": 6, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 6, "endColumn": 7}, {"ruleId": "1025", "severity": 1, "message": "1066", "line": 18, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 18, "endColumn": 12}, {"ruleId": "1025", "severity": 1, "message": "1067", "line": 20, "column": 28, "nodeType": null, "messageId": "1027", "endLine": 20, "endColumn": 49}, {"ruleId": "1025", "severity": 1, "message": "1068", "line": 25, "column": 35, "nodeType": null, "messageId": "1027", "endLine": 25, "endColumn": 40}, {"ruleId": "1025", "severity": 1, "message": "1069", "line": 6, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 6, "endColumn": 12}, {"ruleId": "1025", "severity": 1, "message": "1070", "line": 9, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1032", "line": 11, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 11, "endColumn": 7}, {"ruleId": "1025", "severity": 1, "message": "1071", "line": 12, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 12, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1072", "line": 13, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 13, "endColumn": 10}, {"ruleId": "1025", "severity": 1, "message": "1073", "line": 17, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 17, "endColumn": 21}, {"ruleId": "1025", "severity": 1, "message": "1074", "line": 17, "column": 23, "nodeType": null, "messageId": "1027", "endLine": 17, "endColumn": 37}, {"ruleId": "1025", "severity": 1, "message": "1075", "line": 4, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 14}, {"ruleId": "1025", "severity": 1, "message": "1076", "line": 7, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 7, "endColumn": 21}, {"ruleId": "1025", "severity": 1, "message": "1077", "line": 17, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 17, "endColumn": 15}, {"ruleId": "1025", "severity": 1, "message": "1078", "line": 28, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 28, "endColumn": 27}, {"ruleId": "1025", "severity": 1, "message": "1079", "line": 28, "column": 29, "nodeType": null, "messageId": "1027", "endLine": 28, "endColumn": 49}, {"ruleId": "1025", "severity": 1, "message": "1069", "line": 6, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 6, "endColumn": 12}, {"ruleId": "1025", "severity": 1, "message": "1080", "line": 7, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 7, "endColumn": 11}, {"ruleId": "1025", "severity": 1, "message": "1081", "line": 8, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 8, "endColumn": 11}, {"ruleId": "1025", "severity": 1, "message": "1070", "line": 9, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1082", "line": 10, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 10, "endColumn": 18}, {"ruleId": "1025", "severity": 1, "message": "1032", "line": 11, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 11, "endColumn": 7}, {"ruleId": "1025", "severity": 1, "message": "1071", "line": 12, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 12, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1072", "line": 13, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 13, "endColumn": 10}, {"ruleId": "1025", "severity": 1, "message": "1083", "line": 14, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 14, "endColumn": 8}, {"ruleId": "1025", "severity": 1, "message": "1084", "line": 31, "column": 11, "nodeType": null, "messageId": "1027", "endLine": 31, "endColumn": 36}, {"ruleId": "1025", "severity": 1, "message": "1085", "line": 56, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 56, "endColumn": 17}, {"ruleId": "1047", "severity": 1, "message": "1086", "line": 73, "column": 9, "nodeType": "1049", "messageId": "1050", "endLine": 73, "endColumn": 14, "fix": "1087"}, {"ruleId": "1088", "severity": 1, "message": "1089", "line": 133, "column": 6, "nodeType": "1090", "endLine": 133, "endColumn": 28, "suggestions": "1091"}, {"ruleId": "1025", "severity": 1, "message": "1092", "line": 2, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 2, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1093", "line": 5, "column": 45, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 51}, {"ruleId": "1025", "severity": 1, "message": "1094", "line": 9, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 8}, {"ruleId": "1025", "severity": 1, "message": "1095", "line": 12, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 12, "endColumn": 10}, {"ruleId": "1025", "severity": 1, "message": "1096", "line": 15, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 15, "endColumn": 12}, {"ruleId": "1025", "severity": 1, "message": "1097", "line": 16, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 16, "endColumn": 11}, {"ruleId": "1025", "severity": 1, "message": "1029", "line": 33, "column": 9, "nodeType": null, "messageId": "1027", "endLine": 33, "endColumn": 19}, {"ruleId": "1025", "severity": 1, "message": "1098", "line": 446, "column": 14, "nodeType": null, "messageId": "1027", "endLine": 446, "endColumn": 19}, {"ruleId": "1025", "severity": 1, "message": "1099", "line": 5, "column": 20, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 23}, {"ruleId": "1025", "severity": 1, "message": "1100", "line": 5, "column": 25, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 29}, {"ruleId": "1025", "severity": 1, "message": "1101", "line": 14, "column": 31, "nodeType": null, "messageId": "1027", "endLine": 14, "endColumn": 39}, {"ruleId": "1025", "severity": 1, "message": "1102", "line": 3, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 3, "endColumn": 18}, {"ruleId": "1025", "severity": 1, "message": "1103", "line": 49, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 49, "endColumn": 25}, {"ruleId": "1088", "severity": 1, "message": "1104", "line": 125, "column": 6, "nodeType": "1090", "endLine": 125, "endColumn": 27, "suggestions": "1105"}, {"ruleId": "1088", "severity": 1, "message": "1106", "line": 241, "column": 6, "nodeType": "1090", "endLine": 241, "endColumn": 66, "suggestions": "1107"}, {"ruleId": "1025", "severity": 1, "message": "1108", "line": 4, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 22}, {"ruleId": "1025", "severity": 1, "message": "1109", "line": 3, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 3, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1110", "line": 5, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 18}, {"ruleId": "1025", "severity": 1, "message": "1111", "line": 5, "column": 20, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 29}, {"ruleId": "1025", "severity": 1, "message": "1112", "line": 5, "column": 31, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 39}, {"ruleId": "1025", "severity": 1, "message": "1113", "line": 5, "column": 41, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 47}, {"ruleId": "1025", "severity": 1, "message": "1114", "line": 5, "column": 49, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 56}, {"ruleId": "1025", "severity": 1, "message": "1115", "line": 6, "column": 8, "nodeType": null, "messageId": "1027", "endLine": 6, "endColumn": 13}, {"ruleId": "1025", "severity": 1, "message": "1116", "line": 32, "column": 9, "nodeType": null, "messageId": "1027", "endLine": 32, "endColumn": 19}, {"ruleId": "1025", "severity": 1, "message": "1117", "line": 27, "column": 6, "nodeType": null, "messageId": "1027", "endLine": 27, "endColumn": 23}, {"ruleId": "1025", "severity": 1, "message": "1118", "line": 30, "column": 9, "nodeType": null, "messageId": "1027", "endLine": 30, "endColumn": 15}, {"ruleId": "1025", "severity": 1, "message": "1119", "line": 31, "column": 11, "nodeType": null, "messageId": "1027", "endLine": 31, "endColumn": 15}, {"ruleId": "1025", "severity": 1, "message": "1120", "line": 31, "column": 17, "nodeType": null, "messageId": "1027", "endLine": 31, "endColumn": 25}, {"ruleId": "1025", "severity": 1, "message": "1121", "line": 85, "column": 21, "nodeType": null, "messageId": "1027", "endLine": 85, "endColumn": 33}, {"ruleId": "1025", "severity": 1, "message": "1102", "line": 2, "column": 17, "nodeType": null, "messageId": "1027", "endLine": 2, "endColumn": 25}, {"ruleId": "1025", "severity": 1, "message": "1122", "line": 11, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 11, "endColumn": 15}, {"ruleId": "1025", "severity": 1, "message": "1123", "line": 28, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 28, "endColumn": 11}, {"ruleId": "1025", "severity": 1, "message": "1124", "line": 2, "column": 15, "nodeType": null, "messageId": "1027", "endLine": 2, "endColumn": 26}, {"ruleId": "1025", "severity": 1, "message": "1125", "line": 6, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 6, "endColumn": 23}, {"ruleId": "1025", "severity": 1, "message": "1126", "line": 4, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 18}, {"ruleId": "1025", "severity": 1, "message": "1127", "line": 16, "column": 9, "nodeType": null, "messageId": "1027", "endLine": 16, "endColumn": 13}, {"ruleId": "1025", "severity": 1, "message": "1128", "line": 14, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 14, "endColumn": 23}, {"ruleId": "1025", "severity": 1, "message": "1129", "line": 15, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 15, "endColumn": 22}, {"ruleId": "1025", "severity": 1, "message": "1130", "line": 16, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 16, "endColumn": 25}, {"ruleId": "1025", "severity": 1, "message": "1131", "line": 18, "column": 26, "nodeType": null, "messageId": "1027", "endLine": 18, "endColumn": 45}, {"ruleId": "1088", "severity": 1, "message": "1132", "line": 87, "column": 27, "nodeType": "1090", "endLine": 87, "endColumn": 42}, {"ruleId": "1088", "severity": 1, "message": "1133", "line": 93, "column": 26, "nodeType": "1134", "endLine": 93, "endColumn": 87}, {"ruleId": "1025", "severity": 1, "message": "1135", "line": 207, "column": 20, "nodeType": null, "messageId": "1027", "endLine": 207, "endColumn": 29}, {"ruleId": "1025", "severity": 1, "message": "1136", "line": 212, "column": 16, "nodeType": null, "messageId": "1027", "endLine": 212, "endColumn": 26}, {"ruleId": "1025", "severity": 1, "message": "1098", "line": 260, "column": 12, "nodeType": null, "messageId": "1027", "endLine": 260, "endColumn": 17}, {"ruleId": "1025", "severity": 1, "message": "1137", "line": 4, "column": 7, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 19}, {"ruleId": "1025", "severity": 1, "message": "1138", "line": 1, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 1, "endColumn": 13}, {"ruleId": "1025", "severity": 1, "message": "1139", "line": 121, "column": 16, "nodeType": null, "messageId": "1027", "endLine": 121, "endColumn": 17}, {"ruleId": "1140", "severity": 1, "message": "1141", "line": 21, "column": 27, "nodeType": "1049", "endLine": 21, "endColumn": 34}, {"ruleId": "1025", "severity": 1, "message": "1080", "line": 3, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 3, "endColumn": 11}, {"ruleId": "1025", "severity": 1, "message": "1083", "line": 4, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 8}, {"ruleId": "1025", "severity": 1, "message": "1070", "line": 5, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 5, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1081", "line": 6, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 6, "endColumn": 11}, {"ruleId": "1025", "severity": 1, "message": "1069", "line": 7, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 7, "endColumn": 12}, {"ruleId": "1025", "severity": 1, "message": "1071", "line": 8, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 8, "endColumn": 16}, {"ruleId": "1025", "severity": 1, "message": "1032", "line": 9, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 9, "endColumn": 7}, {"ruleId": "1025", "severity": 1, "message": "1072", "line": 10, "column": 3, "nodeType": null, "messageId": "1027", "endLine": 10, "endColumn": 10}, {"ruleId": "1025", "severity": 1, "message": "1142", "line": 3, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 3, "endColumn": 22}, {"ruleId": "1047", "severity": 1, "message": "1143", "line": 539, "column": 9, "nodeType": "1049", "messageId": "1050", "endLine": 539, "endColumn": 20, "fix": "1144"}, {"ruleId": "1047", "severity": 1, "message": "1145", "line": 417, "column": 13, "nodeType": "1049", "messageId": "1050", "endLine": 417, "endColumn": 25, "fix": "1146"}, {"ruleId": "1025", "severity": 1, "message": "1147", "line": 11, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 11, "endColumn": 18}, {"ruleId": "1088", "severity": 1, "message": "1148", "line": 129, "column": 6, "nodeType": "1090", "endLine": 129, "endColumn": 8, "suggestions": "1149"}, {"ruleId": "1025", "severity": 1, "message": "1150", "line": 13, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 13, "endColumn": 14}, {"ruleId": "1088", "severity": 1, "message": "1151", "line": 42, "column": 6, "nodeType": "1090", "endLine": 42, "endColumn": 24, "suggestions": "1152"}, {"ruleId": "1088", "severity": 1, "message": "1153", "line": 51, "column": 6, "nodeType": "1090", "endLine": 51, "endColumn": 63, "suggestions": "1154"}, {"ruleId": "1025", "severity": 1, "message": "1126", "line": 4, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 4, "endColumn": 18}, {"ruleId": "1025", "severity": 1, "message": "1155", "line": 8, "column": 10, "nodeType": null, "messageId": "1027", "endLine": 8, "endColumn": 15}, {"ruleId": "1047", "severity": 1, "message": "1156", "line": 106, "column": 9, "nodeType": "1049", "messageId": "1050", "endLine": 106, "endColumn": 21, "fix": "1157"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 62, "column": 34, "nodeType": "1044", "messageId": "1045", "endLine": 62, "endColumn": 57}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 62, "column": 66, "nodeType": "1044", "messageId": "1045", "endLine": 62, "endColumn": 89}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 65, "column": 58, "nodeType": "1044", "messageId": "1045", "endLine": 65, "endColumn": 81}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 68, "column": 39, "nodeType": "1044", "messageId": "1045", "endLine": 68, "endColumn": 61}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 69, "column": 41, "nodeType": "1044", "messageId": "1045", "endLine": 69, "endColumn": 63}, {"ruleId": "1025", "severity": 1, "message": "1158", "line": 148, "column": 33, "nodeType": null, "messageId": "1027", "endLine": 148, "endColumn": 43}, {"ruleId": "1025", "severity": 1, "message": "1159", "line": 148, "column": 53, "nodeType": null, "messageId": "1027", "endLine": 148, "endColumn": 60}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 221, "column": 32, "nodeType": "1044", "messageId": "1045", "endLine": 221, "endColumn": 55}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 221, "column": 64, "nodeType": "1044", "messageId": "1045", "endLine": 221, "endColumn": 87}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 223, "column": 61, "nodeType": "1044", "messageId": "1045", "endLine": 223, "endColumn": 84}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 227, "column": 27, "nodeType": "1044", "messageId": "1045", "endLine": 227, "endColumn": 49}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 228, "column": 29, "nodeType": "1044", "messageId": "1045", "endLine": 228, "endColumn": 51}, "@typescript-eslint/no-unused-vars", "'isFilterModalOpen' is assigned a value but never used.", "unusedVar", "'departments' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", "'DepartmentMap' is defined but never used.", "'handleFilterApply' is assigned a value but never used.", "'Mail' is defined but never used.", "'NavigationLoadingWrapper' is defined but never used.", "'outfit' is assigned a value but never used.", "'Lock' is defined but never used.", "'User' is defined but never used.", "'useEffect' is defined but never used.", "'activeTab' is assigned a value but never used.", "'handleTabChange' is assigned a value but never used.", "'ACCEPTED_IMAGE_TYPES' is assigned a value but never used.", "'ACCEPTED_PDF_TYPES' is assigned a value but never used.", "@typescript-eslint/no-non-null-assertion", "Forbidden non-null assertion.", "TSNonNullExpression", "noNonNull", ["1160"], "prefer-const", "'finalQuestionData' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1161", "text": "1162"}, "'uploadFile' is defined but never used.", "'FormDescription' is defined but never used.", "'MAX_FILE_SIZE' is assigned a value but never used.", ["1163"], {"range": "1164", "text": "1162"}, "'refreshToken' is assigned a value but never used.", "'setRefreshToken' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", "'reviewQuestion' is defined but never used.", "'isApiSuccess' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'cn' is defined but never used.", "'CollegeGrowthResponse' is defined but never used.", "'index' is defined but never used.", "'BarChart3' is defined but never used.", "'FolderArchive' is defined but never used.", "'MessageSquare' is defined but never used.", "'Receipt' is defined but never used.", "'sidebarOpen' is assigned a value but never used.", "'setSidebarOpen' is assigned a value but never used.", "'Bell' is defined but never used.", "'ThemeToggle' is defined but never used.", "'Badge' is defined but never used.", "'notificationCount' is assigned a value but never used.", "'setNotificationCount' is assigned a value but never used.", "'BookOpen' is defined but never used.", "'FileText' is defined but never used.", "'LayoutDashboard' is defined but never used.", "'Users' is defined but never used.", "'handleRouteChangeComplete' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'start' is never reassigned. Use 'const' instead.", {"range": "1165", "text": "1166"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchChartData'. Either include it or remove the dependency array.", "ArrayExpression", ["1167"], "'Search' is defined but never used.", "'Filter' is defined but never used.", "'title' is assigned a value but never used.", "'columns' is assigned a value but never used.", "'onRefresh' is defined but never used.", "'onFilter' is defined but never used.", "'error' is defined but never used.", "'Eye' is defined but never used.", "'Edit' is defined but never used.", "'formData' is defined but never used.", "'useState' is defined but never used.", "'loadingSubjects' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadChaptersForSubject'. Either include it or remove the dependency array.", ["1168"], "React Hook useEffect has a missing dependency: 'initializeSubjectConfig'. Either include it or remove the dependency array.", ["1169"], "'OptionButton' is defined but never used.", "'Button' is defined but never used.", "'Facebook' is defined but never used.", "'Instagram' is defined but never used.", "'Linkedin' is defined but never used.", "'Pencil' is defined but never used.", "'Twitter' is defined but never used.", "'Image' is defined but never used.", "'handleEdit' is assigned a value but never used.", "'TeacherFormValues' is defined but never used.", "'params' is assigned a value but never used.", "'user' is assigned a value but never used.", "'userRole' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "'Label' is defined but never used.", "'X' is defined but never used.", "'CheckCircle' is defined but never used.", "'PanelLeftIcon' is defined but never used.", "'MathText' is defined but never used.", "'auth' is assigned a value but never used.", "'toastEventTarget' is assigned a value but never used.", "'TOAST_ADD_EVENT' is assigned a value but never used.", "'TOAST_REMOVE_EVENT' is assigned a value but never used.", "'UsageTrendsResponse' is defined but never used.", "Assignments to the 'yAxisDomain' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "Assignments to the 'yAxisTicks' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", "'jsonError' is defined but never used.", "'parseError' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "'api' is defined but never used.", "'e' is defined but never used.", "react-hooks/rules-of-hooks", "React Hook \"useAuth\" is called in function \"setUserRole\" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word \"use\".", "'MathRenderer' is defined but never used.", "'displayText' is never reassigned. Use 'const' instead.", {"range": "1170", "text": "1171"}, "'processedOpt' is never reassigned. Use 'const' instead.", {"range": "1172", "text": "1173"}, "'useToast' is defined but never used.", "React Hook useEffect has missing dependencies: 'initialSubjects.length', 'initialTopics.length', and 'selectedSubjectId'. Either include them or remove the dependency array.", ["1174"], "'Plus' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadChapters'. Either include it or remove the dependency array.", ["1175"], "React Hook useEffect has a missing dependency: 'loadTopics'. Either include it or remove the dependency array.", ["1176"], "'JSDOM' is defined but never used.", "'fallbackText' is never reassigned. Use 'const' instead.", {"range": "1177", "text": "1178"}, "'expression' is defined but never used.", "'options' is defined but never used.", {"messageId": "1179", "fix": "1180", "desc": "1181"}, [11129, 11173], "const finalQuestionData = { ...questionData };", {"messageId": "1179", "fix": "1182", "desc": "1181"}, [12373, 12417], [1796, 1822], "const start = new Date(now);", {"desc": "1183", "fix": "1184"}, {"desc": "1185", "fix": "1186"}, {"desc": "1187", "fix": "1188"}, [17988, 24325], "const displayText = text\n        // Arrows and implications\n        .replace(/\\\\Rightarrow/g, '⇒')\n        .replace(/\\\\Leftarrow/g, '⇐')\n        .replace(/\\\\Leftrightarrow/g, '⇔')\n        .replace(/\\\\rightarrow/g, '→')\n        .replace(/\\\\leftarrow/g, '←')\n        .replace(/\\\\leftrightarrow/g, '↔')\n        .replace(/\\\\uparrow/g, '↑')\n        .replace(/\\\\downarrow/g, '↓')\n        .replace(/\\\\updownarrow/g, '↕')\n        .replace(/\\\\Uparrow/g, '⇑')\n        .replace(/\\\\Downarrow/g, '⇓')\n        .replace(/\\\\Updownarrow/g, '⇕')\n\n        // Comparison operators\n        .replace(/\\\\geq/g, '≥')\n        .replace(/\\\\leq/g, '≤')\n        .replace(/\\\\neq/g, '≠')\n        .replace(/\\\\approx/g, '≈')\n        .replace(/\\\\equiv/g, '≡')\n        .replace(/\\\\sim/g, '∼')\n        .replace(/\\\\simeq/g, '≃')\n        .replace(/\\\\cong/g, '≅')\n        .replace(/\\\\propto/g, '∝')\n\n        // Binary operators\n        .replace(/\\\\pm/g, '±')\n        .replace(/\\\\mp/g, '∓')\n        .replace(/\\\\times/g, '×')\n        .replace(/\\\\div/g, '÷')\n        .replace(/\\\\cdot/g, '⋅')\n        .replace(/\\\\ast/g, '∗')\n        .replace(/\\\\star/g, '⋆')\n        .replace(/\\\\circ/g, '∘')\n        .replace(/\\\\bullet/g, '•')\n        .replace(/\\\\oplus/g, '⊕')\n        .replace(/\\\\ominus/g, '⊖')\n        .replace(/\\\\otimes/g, '⊗')\n        .replace(/\\\\oslash/g, '⊘')\n\n        // Greek letters (lowercase)\n        .replace(/\\\\alpha/g, 'α')\n        .replace(/\\\\beta/g, 'β')\n        .replace(/\\\\gamma/g, 'γ')\n        .replace(/\\\\delta/g, 'δ')\n        .replace(/\\\\epsilon/g, 'ε')\n        .replace(/\\\\varepsilon/g, 'ε')\n        .replace(/\\\\zeta/g, 'ζ')\n        .replace(/\\\\eta/g, 'η')\n        .replace(/\\\\theta/g, 'θ')\n        .replace(/\\\\vartheta/g, 'ϑ')\n        .replace(/\\\\iota/g, 'ι')\n        .replace(/\\\\kappa/g, 'κ')\n        .replace(/\\\\lambda/g, 'λ')\n        .replace(/\\\\mu/g, 'μ')\n        .replace(/\\\\nu/g, 'ν')\n        .replace(/\\\\xi/g, 'ξ')\n        .replace(/\\\\pi/g, 'π')\n        .replace(/\\\\varpi/g, 'ϖ')\n        .replace(/\\\\rho/g, 'ρ')\n        .replace(/\\\\varrho/g, 'ϱ')\n        .replace(/\\\\sigma/g, 'σ')\n        .replace(/\\\\varsigma/g, 'ς')\n        .replace(/\\\\tau/g, 'τ')\n        .replace(/\\\\upsilon/g, 'υ')\n        .replace(/\\\\phi/g, 'φ')\n        .replace(/\\\\varphi/g, 'φ')\n        .replace(/\\\\chi/g, 'χ')\n        .replace(/\\\\psi/g, 'ψ')\n        .replace(/\\\\omega/g, 'ω')\n\n        // Greek letters (uppercase)\n        .replace(/\\\\Gamma/g, 'Γ')\n        .replace(/\\\\Delta/g, 'Δ')\n        .replace(/\\\\Theta/g, 'Θ')\n        .replace(/\\\\Lambda/g, 'Λ')\n        .replace(/\\\\Xi/g, 'Ξ')\n        .replace(/\\\\Pi/g, 'Π')\n        .replace(/\\\\Sigma/g, 'Σ')\n        .replace(/\\\\Upsilon/g, 'Υ')\n        .replace(/\\\\Phi/g, 'Φ')\n        .replace(/\\\\Psi/g, 'Ψ')\n        .replace(/\\\\Omega/g, 'Ω')\n\n        // Large operators\n        .replace(/\\\\sum/g, '∑')\n        .replace(/\\\\prod/g, '∏')\n        .replace(/\\\\coprod/g, '∐')\n        .replace(/\\\\int/g, '∫')\n        .replace(/\\\\iint/g, '∬')\n        .replace(/\\\\iiint/g, '∭')\n        .replace(/\\\\oint/g, '∮')\n        .replace(/\\\\bigcup/g, '⋃')\n        .replace(/\\\\bigcap/g, '⋂')\n        .replace(/\\\\bigoplus/g, '⨁')\n        .replace(/\\\\bigotimes/g, '⨂')\n\n        // Set theory and logic\n        .replace(/\\\\in/g, '∈')\n        .replace(/\\\\notin/g, '∉')\n        .replace(/\\\\ni/g, '∋')\n        .replace(/\\\\subset/g, '⊂')\n        .replace(/\\\\supset/g, '⊃')\n        .replace(/\\\\subseteq/g, '⊆')\n        .replace(/\\\\supseteq/g, '⊇')\n        .replace(/\\\\cup/g, '∪')\n        .replace(/\\\\cap/g, '∩')\n        .replace(/\\\\setminus/g, '∖')\n        .replace(/\\\\forall/g, '∀')\n        .replace(/\\\\exists/g, '∃')\n        .replace(/\\\\nexists/g, '∄')\n        .replace(/\\\\emptyset/g, '∅')\n        .replace(/\\\\varnothing/g, '∅')\n\n        // Special symbols\n        .replace(/\\\\infty/g, '∞')\n        .replace(/\\\\partial/g, '∂')\n        .replace(/\\\\nabla/g, '∇')\n        .replace(/\\\\angle/g, '∠')\n        .replace(/\\\\triangle/g, '△')\n        .replace(/\\\\square/g, '□')\n        .replace(/\\\\diamond/g, '◊')\n        .replace(/\\\\clubsuit/g, '♣')\n        .replace(/\\\\diamondsuit/g, '♦')\n        .replace(/\\\\heartsuit/g, '♥')\n        .replace(/\\\\spadesuit/g, '♠')\n\n        // Number sets\n        .replace(/\\\\mathbb{N}/g, 'ℕ')\n        .replace(/\\\\mathbb{Z}/g, 'ℤ')\n        .replace(/\\\\mathbb{Q}/g, 'ℚ')\n        .replace(/\\\\mathbb{R}/g, 'ℝ')\n        .replace(/\\\\mathbb{C}/g, 'ℂ')\n        .replace(/\\\\mathbb{P}/g, 'ℙ')\n\n        // Functions and operations\n        .replace(/\\\\sqrt{([^}]+)}/g, '√($1)')\n        .replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)')\n        .replace(/\\\\binom{([^}]+)}{([^}]+)}/g, 'C($1,$2)')\n        .replace(/\\\\choose/g, 'C')\n\n        // Delimiters - remove \\left and \\right commands\n        .replace(/\\\\left\\(/g, '(')\n        .replace(/\\\\right\\)/g, ')')\n        .replace(/\\\\left\\[/g, '[')\n        .replace(/\\\\right\\]/g, ']')\n        .replace(/\\\\left\\{/g, '{')\n        .replace(/\\\\right\\}/g, '}')\n        .replace(/\\\\left\\|/g, '|')\n        .replace(/\\\\right\\|/g, '|')\n        .replace(/\\\\left</g, '⟨')\n        .replace(/\\\\right>/g, '⟩')\n        .replace(/\\\\left/g, '') // Remove any remaining \\left\n        .replace(/\\\\right/g, '') // Remove any remaining \\right\n\n        // Dots and ellipsis\n        .replace(/\\\\ldots/g, '...')\n        .replace(/\\\\cdots/g, '⋯')\n        .replace(/\\\\vdots/g, '⋮')\n        .replace(/\\\\ddots/g, '⋱')\n\n        // Superscripts and subscripts\n        .replace(/\\^{([^}]+)}/g, '^($1)')\n        .replace(/_{([^}]+)}/g, '_($1)')\n        .replace(/\\^(\\w)/g, '^$1')\n        .replace(/_(\\w)/g, '_$1')\n\n        // Text formatting\n        .replace(/\\\\mathrm{([^}]+)}/g, '$1')\n        .replace(/\\\\mathbf{([^}]+)}/g, '$1')\n        .replace(/\\\\mathit{([^}]+)}/g, '$1')\n        .replace(/\\\\mathcal{([^}]+)}/g, '$1')\n        .replace(/\\\\text{([^}]+)}/g, '$1')\n        .replace(/\\\\textbf{([^}]+)}/g, '$1')\n        .replace(/\\\\textit{([^}]+)}/g, '$1')\n\n        // Clean up any remaining LaTeX commands that might cause issues\n        .replace(/\\\\[a-zA-Z]+\\{([^}]*)\\}/g, '$1') // Remove unknown commands with braces\n        .replace(/\\\\[a-zA-Z]+/g, '') // Remove unknown commands without braces\n\n        // Fix spacing issues - normalize multiple spaces\n        .replace(/\\s+/g, ' ')\n        .trim()\n\n        // Remove delimiters\n        .replace(/\\$([^$]+)\\$/g, '$1') // Remove $ delimiters\n        .replace(/\\$\\$([^$]+)\\$\\$/g, '$1');", [16353, 16395], "const processedOpt = processTextForPDF(opt);", {"desc": "1189", "fix": "1190"}, {"desc": "1191", "fix": "1192"}, {"desc": "1193", "fix": "1194"}, [2785, 5908], "const fallbackText = expression\n      // Remove delimiters\n      .replace(/^\\$+/, '')\n      .replace(/\\$+$/, '')\n      \n      // Arrows and implications\n      .replace(/\\\\Rightarrow/g, '⇒')\n      .replace(/\\\\Leftarrow/g, '⇐')\n      .replace(/\\\\Leftrightarrow/g, '⇔')\n      .replace(/\\\\rightarrow/g, '→')\n      .replace(/\\\\leftarrow/g, '←')\n      .replace(/\\\\leftrightarrow/g, '↔')\n      \n      // Comparison operators\n      .replace(/\\\\geq/g, '≥')\n      .replace(/\\\\leq/g, '≤')\n      .replace(/\\\\neq/g, '≠')\n      .replace(/\\\\approx/g, '≈')\n      .replace(/\\\\equiv/g, '≡')\n      .replace(/\\\\sim/g, '∼')\n      \n      // Binary operators\n      .replace(/\\\\pm/g, '±')\n      .replace(/\\\\mp/g, '∓')\n      .replace(/\\\\times/g, '×')\n      .replace(/\\\\div/g, '÷')\n      .replace(/\\\\cdot/g, '⋅')\n      \n      // Greek letters (common ones)\n      .replace(/\\\\alpha/g, 'α')\n      .replace(/\\\\beta/g, 'β')\n      .replace(/\\\\gamma/g, 'γ')\n      .replace(/\\\\delta/g, 'δ')\n      .replace(/\\\\epsilon/g, 'ε')\n      .replace(/\\\\theta/g, 'θ')\n      .replace(/\\\\lambda/g, 'λ')\n      .replace(/\\\\mu/g, 'μ')\n      .replace(/\\\\pi/g, 'π')\n      .replace(/\\\\sigma/g, 'σ')\n      .replace(/\\\\phi/g, 'φ')\n      .replace(/\\\\omega/g, 'ω')\n      \n      // Large operators\n      .replace(/\\\\sum/g, '∑')\n      .replace(/\\\\prod/g, '∏')\n      .replace(/\\\\int/g, '∫')\n      \n      // Set theory\n      .replace(/\\\\in/g, '∈')\n      .replace(/\\\\notin/g, '∉')\n      .replace(/\\\\subset/g, '⊂')\n      .replace(/\\\\supset/g, '⊃')\n      .replace(/\\\\cup/g, '∪')\n      .replace(/\\\\cap/g, '∩')\n      .replace(/\\\\forall/g, '∀')\n      .replace(/\\\\exists/g, '∃')\n      .replace(/\\\\emptyset/g, '∅')\n      \n      // Special symbols\n      .replace(/\\\\infty/g, '∞')\n      .replace(/\\\\partial/g, '∂')\n      .replace(/\\\\nabla/g, '∇')\n      \n      // Number sets\n      .replace(/\\\\mathbb{N}/g, 'ℕ')\n      .replace(/\\\\mathbb{Z}/g, 'ℤ')\n      .replace(/\\\\mathbb{Q}/g, 'ℚ')\n      .replace(/\\\\mathbb{R}/g, 'ℝ')\n      .replace(/\\\\mathbb{C}/g, 'ℂ')\n      \n      // Functions and operations\n      .replace(/\\\\sqrt{([^}]+)}/g, '√($1)')\n      .replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)')\n      \n      // Delimiters\n      .replace(/\\\\left\\(/g, '(')\n      .replace(/\\\\right\\)/g, ')')\n      .replace(/\\\\left\\[/g, '[')\n      .replace(/\\\\right\\]/g, ']')\n      .replace(/\\\\left\\{/g, '{')\n      .replace(/\\\\right\\}/g, '}')\n      .replace(/\\\\left\\|/g, '|')\n      .replace(/\\\\right\\|/g, '|')\n      .replace(/\\\\left/g, '')\n      .replace(/\\\\right/g, '')\n      \n      // Dots\n      .replace(/\\\\ldots/g, '...')\n      .replace(/\\\\cdots/g, '⋯')\n      \n      // Superscripts and subscripts\n      .replace(/\\^{([^}]+)}/g, '^($1)')\n      .replace(/_{([^}]+)}/g, '_($1)')\n      .replace(/\\^(\\w)/g, '^$1')\n      .replace(/_(\\w)/g, '_$1')\n      \n      // Text formatting\n      .replace(/\\\\mathrm{([^}]+)}/g, '$1')\n      .replace(/\\\\mathbf{([^}]+)}/g, '$1')\n      .replace(/\\\\text{([^}]+)}/g, '$1')\n      \n      // Clean up remaining LaTeX commands\n      .replace(/\\\\[a-zA-Z]+\\{([^}]*)\\}/g, '$1')\n      .replace(/\\\\[a-zA-Z]+/g, '')\n      \n      // Normalize spaces\n      .replace(/\\s+/g, ' ')\n      .trim();", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", {"range": "1195", "text": "1196"}, "Consider using the optional chain operator `?.` instead. This operator includes runtime checks, so it is safer than the compile-only non-null assertion operator.", {"range": "1197", "text": "1196"}, "Update the dependencies array to be: [collegeId, fetchChartData, timeRange]", {"range": "1198", "text": "1199"}, "Update the dependencies array to be: [activeTab, loadChaptersForSubject, subjects]", {"range": "1200", "text": "1201"}, "Update the dependencies array to be: [formData.subjects, formData.subjectConfigs, updateFormData, initializeSubjectConfig]", {"range": "1202", "text": "1203"}, "Update the dependencies array to be: [initialSubjects.length, initialTopics.length, selectedSubjectId]", {"range": "1204", "text": "1205"}, "Update the dependencies array to be: [formData.subject, loadChapters]", {"range": "1206", "text": "1207"}, "Update the dependencies array to be: [formData.subject, formData.chapters, formData.chapterId, loadTopics]", {"range": "1208", "text": "1209"}, [7540, 7541], "?", [9162, 9163], [3281, 3303], "[collegeId, fetchChartData, timeRange]", [4246, 4267], "[activeTab, loadChaptersForSubject, subjects]", [8478, 8538], "[formData.subjects, formData.subjectConfigs, updateFormData, initializeSubjectConfig]", [4433, 4435], "[initialSubjects.length, initialTopics.length, selectedSubjectId]", [1406, 1424], "[formData.subject, loadChapters]", [1615, 1672], "[formData.subject, formData.chapters, formData.chapterId, loadTopics]"]