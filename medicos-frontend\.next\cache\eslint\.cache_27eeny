[{"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx": "3", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx": "8", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx": "9", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\super\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx": "13", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\loading.tsx": "14", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx": "17", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx": "18", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx": "19", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx": "20", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx": "22", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx": "23", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx": "24", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx": "25", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx": "26", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx": "27", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\settings\\page.tsx": "28", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\add-question-form.tsx": "29", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-edit-form.tsx": "30", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-registration-form.tsx": "31", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\edit-question-form.tsx": "32", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\file-uploader.tsx": "33", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\pagination-control.tsx": "34", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\phone-input.tsx": "35", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-bank.tsx": "36", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-list.tsx": "37", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-skeleton.tsx": "38", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-card.tsx": "39", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-grid.tsx": "40", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\auth\\protected-route.tsx": "41", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\Breadcrumb.tsx": "42", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeAdmin\\Tabs.tsx": "43", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeChart.tsx": "44", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\index.tsx": "45", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\TablePagination.tsx": "46", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\debug\\TokenDebug.tsx": "47", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\AboutSection.tsx": "48", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Consultation.tsx": "49", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Footer.tsx": "50", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\hero-section.tsx": "51", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\LessonsSection.tsx": "52", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\navbar.tsx": "53", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\NavigationTabs.tsx": "54", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectCard.tsx": "55", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectChart.tsx": "56", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TestimonialsContact.tsx": "57", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TransformingEducation.tsx": "58", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\command-menu.tsx": "59", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-layout.tsx": "60", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-navbar.tsx": "61", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-sidebar.tsx": "62", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\navigation-loading-wrapper.tsx": "63", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\RoleProtectedRoute.tsx": "64", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\StatCard.tsx": "65", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SubjectQuestionsChart.tsx": "66", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SuperAdmin\\Tabs.tsx": "67", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\pagination.tsx": "68", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-header.tsx": "69", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-row.tsx": "70", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-title.tsx": "71", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\teachers-table.tsx": "72", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\types.ts": "73", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TabNavigation.tsx": "74", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\question-paper-wizard.tsx": "75", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\settings-form.tsx": "76", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\actions-step.tsx": "77", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\course-subject-step.tsx": "78", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\difficulty-level-step.tsx": "79", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\include-answers-step.tsx": "80", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\multi-subject-config-step.tsx": "81", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\paper-customization-step.tsx": "82", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-selection-step.tsx": "83", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-title-description-step.tsx": "84", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-type-step.tsx": "85", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\Tabs.tsx": "86", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\info-message.tsx": "87", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\option-button.tsx": "88", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-indicator.tsx": "89", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-navigation.tsx": "90", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\user-profile.tsx": "91", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\ActionsMenu.tsx": "92", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\AddTeacherForm.tsx": "93", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\EditTeacherForm.tsx": "94", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\FilterModal.tsx": "95", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\StatusBadge.tsx": "96", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\Tabs.tsx": "97", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\TeacherProfile.tsx": "98", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\theme-toggle.tsx": "99", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TopTeachersList.tsx": "100", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\alert-dialog.tsx": "101", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\avatar.tsx": "102", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\badge.tsx": "103", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\base64-image.tsx": "104", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\button.tsx": "105", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\card.tsx": "106", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chart.tsx": "107", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\command.tsx": "108", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dialog.tsx": "109", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dropdown-menu.tsx": "110", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\form.tsx": "111", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\input.tsx": "112", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\label.tsx": "113", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\math-text.tsx": "114", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\page-loading.tsx": "115", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\pagination.tsx": "116", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\popover.tsx": "117", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\progress.tsx": "118", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\radio-group.tsx": "119", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\select.tsx": "120", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\separator.tsx": "121", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sheet.tsx": "122", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sidebar.tsx": "123", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\skeleton.tsx": "124", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\slider.tsx": "125", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\table.tsx": "126", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tabs.tsx": "127", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\text-with-images.tsx": "128", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\textarea.tsx": "129", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toast.tsx": "130", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toaster.tsx": "131", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tooltip.tsx": "132", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\use-toast.ts": "133", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\UsageChart.tsx": "134", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\hooks\\use-mobile.ts": "135", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\admin-analytics.ts": "136", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\analytics.ts": "137", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\api.ts": "138", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\apiClient.ts": "139", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\college.ts": "140", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questionPapers.ts": "141", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questions.ts": "142", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\subjects.ts": "143", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\teachers.ts": "144", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\topics.ts": "145", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\upload.ts": "146", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api.ts": "147", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\auth.ts": "148", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx": "149", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\enums.ts": "150", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\menuItems.ts": "151", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\firebase.ts": "152", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\ReactQueryProvider.tsx": "153", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\interface.ts": "154", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\menu.ts": "155", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\university.ts": "156", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils.ts": "157", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\services\\teacherService.ts": "158", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\types\\question.ts": "159", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\imageUtils.ts": "160", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\pdfGenerator.ts": "161", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-answers-excel\\route.ts": "162", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-paper-pdf\\route.ts": "163", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-solutions-pdf\\route.ts": "164", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\subject-chapter-manager.tsx": "165", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\chapter-selection-step.tsx": "166", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\topic-selection-step.tsx": "167", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\test\\table-test.tsx": "168", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chemical-image-display.tsx": "169", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\enhanced-text-renderer.tsx": "170", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\chapters.ts": "171", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils\\errorHandler.ts": "172", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\test-image-extraction.js": "173", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\htmlToImage.ts": "174", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\mathRenderer.ts": "175", "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\svgMathRenderer.ts": "176"}, {"size": 901, "mtime": 1744388774842, "results": "177", "hashOfConfig": "178"}, {"size": 1583, "mtime": 1753981980310, "results": "179", "hashOfConfig": "178"}, {"size": 1349, "mtime": 1754121124626, "results": "180", "hashOfConfig": "178"}, {"size": 3420, "mtime": 1754059417275, "results": "181", "hashOfConfig": "178"}, {"size": 2298, "mtime": 1754498026874, "results": "182", "hashOfConfig": "178"}, {"size": 2462, "mtime": 1749352078690, "results": "183", "hashOfConfig": "178"}, {"size": 640, "mtime": 1746861838647, "results": "184", "hashOfConfig": "178"}, {"size": 166, "mtime": 1748951735270, "results": "185", "hashOfConfig": "178"}, {"size": 3435, "mtime": 1747541479336, "results": "186", "hashOfConfig": "178"}, {"size": 1777, "mtime": 1754059463722, "results": "187", "hashOfConfig": "178"}, {"size": 590, "mtime": 1747485916673, "results": "188", "hashOfConfig": "178"}, {"size": 575, "mtime": 1747486046335, "results": "189", "hashOfConfig": "178"}, {"size": 646, "mtime": 1746861855566, "results": "190", "hashOfConfig": "178"}, {"size": 170, "mtime": 1748951739495, "results": "191", "hashOfConfig": "178"}, {"size": 8209, "mtime": 1754327749674, "results": "192", "hashOfConfig": "178"}, {"size": 18790, "mtime": 1752078856800, "results": "193", "hashOfConfig": "178"}, {"size": 6097, "mtime": 1746863258819, "results": "194", "hashOfConfig": "178"}, {"size": 962, "mtime": 1749309798225, "results": "195", "hashOfConfig": "178"}, {"size": 161, "mtime": 1748951726917, "results": "196", "hashOfConfig": "178"}, {"size": 13578, "mtime": 1754058402135, "results": "197", "hashOfConfig": "178"}, {"size": 3032, "mtime": 1748278584823, "results": "198", "hashOfConfig": "178"}, {"size": 13813, "mtime": 1747487664800, "results": "199", "hashOfConfig": "178"}, {"size": 20943, "mtime": 1753291108400, "results": "200", "hashOfConfig": "178"}, {"size": 634, "mtime": 1746861875933, "results": "201", "hashOfConfig": "178"}, {"size": 170, "mtime": 1748951736389, "results": "202", "hashOfConfig": "178"}, {"size": 1165, "mtime": 1748941728476, "results": "203", "hashOfConfig": "178"}, {"size": 4092, "mtime": 1748950224444, "results": "204", "hashOfConfig": "178"}, {"size": 3034, "mtime": 1749356941741, "results": "205", "hashOfConfig": "178"}, {"size": 43641, "mtime": 1754497496295, "results": "206", "hashOfConfig": "178"}, {"size": 8173, "mtime": 1754059439317, "results": "207", "hashOfConfig": "178"}, {"size": 7546, "mtime": 1754059432323, "results": "208", "hashOfConfig": "178"}, {"size": 26350, "mtime": 1753985456390, "results": "209", "hashOfConfig": "178"}, {"size": 5859, "mtime": 1744388637160, "results": "210", "hashOfConfig": "178"}, {"size": 1594, "mtime": 1745038739730, "results": "211", "hashOfConfig": "178"}, {"size": 3904, "mtime": 1744388656393, "results": "212", "hashOfConfig": "178"}, {"size": 19300, "mtime": 1753985468431, "results": "213", "hashOfConfig": "178"}, {"size": 18494, "mtime": 1753884941115, "results": "214", "hashOfConfig": "178"}, {"size": 1234, "mtime": 1749310653822, "results": "215", "hashOfConfig": "178"}, {"size": 6230, "mtime": 1752079280943, "results": "216", "hashOfConfig": "178"}, {"size": 1468, "mtime": 1747494337010, "results": "217", "hashOfConfig": "178"}, {"size": 1738, "mtime": 1747486281263, "results": "218", "hashOfConfig": "178"}, {"size": 2005, "mtime": 1743735365093, "results": "219", "hashOfConfig": "178"}, {"size": 2024, "mtime": 1748941705879, "results": "220", "hashOfConfig": "178"}, {"size": 8166, "mtime": 1753523123606, "results": "221", "hashOfConfig": "178"}, {"size": 10050, "mtime": 1743734514431, "results": "222", "hashOfConfig": "178"}, {"size": 4207, "mtime": 1743734474231, "results": "223", "hashOfConfig": "178"}, {"size": 2658, "mtime": 1749719660381, "results": "224", "hashOfConfig": "178"}, {"size": 1078, "mtime": 1748279045736, "results": "225", "hashOfConfig": "178"}, {"size": 2759, "mtime": 1748279055469, "results": "226", "hashOfConfig": "178"}, {"size": 1516, "mtime": 1748154465611, "results": "227", "hashOfConfig": "178"}, {"size": 1772, "mtime": 1748157685392, "results": "228", "hashOfConfig": "178"}, {"size": 1457, "mtime": 1748279050770, "results": "229", "hashOfConfig": "178"}, {"size": 4638, "mtime": 1749276901013, "results": "230", "hashOfConfig": "178"}, {"size": 918, "mtime": 1748158980847, "results": "231", "hashOfConfig": "178"}, {"size": 1526, "mtime": 1748279067042, "results": "232", "hashOfConfig": "178"}, {"size": 475, "mtime": 1748158604295, "results": "233", "hashOfConfig": "178"}, {"size": 9441, "mtime": 1748279059343, "results": "234", "hashOfConfig": "178"}, {"size": 2646, "mtime": 1748279040960, "results": "235", "hashOfConfig": "178"}, {"size": 4215, "mtime": 1750093835381, "results": "236", "hashOfConfig": "178"}, {"size": 927, "mtime": 1744463201290, "results": "237", "hashOfConfig": "178"}, {"size": 5896, "mtime": 1748951252205, "results": "238", "hashOfConfig": "178"}, {"size": 4658, "mtime": 1754113012821, "results": "239", "hashOfConfig": "178"}, {"size": 1223, "mtime": 1748951853965, "results": "240", "hashOfConfig": "178"}, {"size": 1464, "mtime": 1747485910085, "results": "241", "hashOfConfig": "178"}, {"size": 1640, "mtime": 1747541577575, "results": "242", "hashOfConfig": "178"}, {"size": 5826, "mtime": 1748538501229, "results": "243", "hashOfConfig": "178"}, {"size": 1908, "mtime": 1748941695904, "results": "244", "hashOfConfig": "178"}, {"size": 3047, "mtime": 1748083220804, "results": "245", "hashOfConfig": "178"}, {"size": 1417, "mtime": 1748081284162, "results": "246", "hashOfConfig": "178"}, {"size": 3137, "mtime": 1748527162670, "results": "247", "hashOfConfig": "178"}, {"size": 1157, "mtime": 1748083145875, "results": "248", "hashOfConfig": "178"}, {"size": 5799, "mtime": 1749281632443, "results": "249", "hashOfConfig": "178"}, {"size": 943, "mtime": 1748083038143, "results": "250", "hashOfConfig": "178"}, {"size": 1790, "mtime": 1748951745661, "results": "251", "hashOfConfig": "178"}, {"size": 23242, "mtime": 1754114913291, "results": "252", "hashOfConfig": "178"}, {"size": 8598, "mtime": 1754059637168, "results": "253", "hashOfConfig": "178"}, {"size": 1667, "mtime": 1754059607160, "results": "254", "hashOfConfig": "178"}, {"size": 7239, "mtime": 1754059864439, "results": "255", "hashOfConfig": "178"}, {"size": 6110, "mtime": 1750092487411, "results": "256", "hashOfConfig": "178"}, {"size": 2033, "mtime": 1749718395667, "results": "257", "hashOfConfig": "178"}, {"size": 24835, "mtime": 1754117029097, "results": "258", "hashOfConfig": "178"}, {"size": 3136, "mtime": 1754309395956, "results": "259", "hashOfConfig": "178"}, {"size": 3389, "mtime": 1753877992669, "results": "260", "hashOfConfig": "178"}, {"size": 4601, "mtime": 1749718221176, "results": "261", "hashOfConfig": "178"}, {"size": 2572, "mtime": 1749718241594, "results": "262", "hashOfConfig": "178"}, {"size": 1906, "mtime": 1748941713397, "results": "263", "hashOfConfig": "178"}, {"size": 376, "mtime": 1744306232877, "results": "264", "hashOfConfig": "178"}, {"size": 1329, "mtime": 1754058540036, "results": "265", "hashOfConfig": "178"}, {"size": 914, "mtime": 1745140884774, "results": "266", "hashOfConfig": "178"}, {"size": 1193, "mtime": 1754059949986, "results": "267", "hashOfConfig": "178"}, {"size": 7242, "mtime": 1748950269237, "results": "268", "hashOfConfig": "178"}, {"size": 2589, "mtime": 1743734241174, "results": "269", "hashOfConfig": "178"}, {"size": 14350, "mtime": 1754059543486, "results": "270", "hashOfConfig": "178"}, {"size": 4337, "mtime": 1754059555218, "results": "271", "hashOfConfig": "178"}, {"size": 5216, "mtime": 1748083018250, "results": "272", "hashOfConfig": "178"}, {"size": 589, "mtime": 1743734363851, "results": "273", "hashOfConfig": "178"}, {"size": 2170, "mtime": 1749277872317, "results": "274", "hashOfConfig": "178"}, {"size": 1195, "mtime": 1743734427151, "results": "275", "hashOfConfig": "178"}, {"size": 1163, "mtime": 1743411677883, "results": "276", "hashOfConfig": "178"}, {"size": 2964, "mtime": 1748537241527, "results": "277", "hashOfConfig": "178"}, {"size": 3864, "mtime": 1747493623353, "results": "278", "hashOfConfig": "178"}, {"size": 1097, "mtime": 1743411547295, "results": "279", "hashOfConfig": "178"}, {"size": 1631, "mtime": 1743411547299, "results": "280", "hashOfConfig": "178"}, {"size": 1959, "mtime": 1754058346664, "results": "281", "hashOfConfig": "178"}, {"size": 2123, "mtime": 1743411547257, "results": "282", "hashOfConfig": "178"}, {"size": 1989, "mtime": 1743411831373, "results": "283", "hashOfConfig": "178"}, {"size": 9781, "mtime": 1743561991844, "results": "284", "hashOfConfig": "178"}, {"size": 4656, "mtime": 1743411332113, "results": "285", "hashOfConfig": "178"}, {"size": 3813, "mtime": 1743411332135, "results": "286", "hashOfConfig": "178"}, {"size": 8284, "mtime": 1743411547290, "results": "287", "hashOfConfig": "178"}, {"size": 3759, "mtime": 1743734332220, "results": "288", "hashOfConfig": "178"}, {"size": 967, "mtime": 1743411547271, "results": "289", "hashOfConfig": "178"}, {"size": 611, "mtime": 1743734314422, "results": "290", "hashOfConfig": "178"}, {"size": 2684, "mtime": 1751716372054, "results": "291", "hashOfConfig": "178"}, {"size": 820, "mtime": 1748951722646, "results": "292", "hashOfConfig": "178"}, {"size": 4035, "mtime": 1748084548783, "results": "293", "hashOfConfig": "178"}, {"size": 1635, "mtime": 1744388677549, "results": "294", "hashOfConfig": "178"}, {"size": 798, "mtime": 1744388507234, "results": "295", "hashOfConfig": "178"}, {"size": 1466, "mtime": 1746113320858, "results": "296", "hashOfConfig": "178"}, {"size": 6253, "mtime": 1743561991797, "results": "297", "hashOfConfig": "178"}, {"size": 704, "mtime": 1743411547366, "results": "298", "hashOfConfig": "178"}, {"size": 4090, "mtime": 1743411547371, "results": "299", "hashOfConfig": "178"}, {"size": 21727, "mtime": 1744968540744, "results": "300", "hashOfConfig": "178"}, {"size": 276, "mtime": 1743411547378, "results": "301", "hashOfConfig": "178"}, {"size": 2427, "mtime": 1745140151553, "results": "302", "hashOfConfig": "178"}, {"size": 2448, "mtime": 1743734537564, "results": "303", "hashOfConfig": "178"}, {"size": 1969, "mtime": 1743947262123, "results": "304", "hashOfConfig": "178"}, {"size": 1502, "mtime": 1752980163332, "results": "305", "hashOfConfig": "178"}, {"size": 759, "mtime": 1744388618328, "results": "306", "hashOfConfig": "178"}, {"size": 4886, "mtime": 1749309412142, "results": "307", "hashOfConfig": "178"}, {"size": 953, "mtime": 1749309810030, "results": "308", "hashOfConfig": "178"}, {"size": 1891, "mtime": 1743411547374, "results": "309", "hashOfConfig": "178"}, {"size": 1886, "mtime": 1749309728742, "results": "310", "hashOfConfig": "178"}, {"size": 6840, "mtime": 1753522976795, "results": "311", "hashOfConfig": "178"}, {"size": 565, "mtime": 1743406728995, "results": "312", "hashOfConfig": "178"}, {"size": 3293, "mtime": 1748942722046, "results": "313", "hashOfConfig": "178"}, {"size": 5568, "mtime": 1753523020965, "results": "314", "hashOfConfig": "178"}, {"size": 2148, "mtime": 1748539684250, "results": "315", "hashOfConfig": "178"}, {"size": 1249, "mtime": 1747497035401, "results": "316", "hashOfConfig": "178"}, {"size": 9732, "mtime": 1754327693784, "results": "317", "hashOfConfig": "178"}, {"size": 15519, "mtime": 1753982272265, "results": "318", "hashOfConfig": "178"}, {"size": 20091, "mtime": 1754324130390, "results": "319", "hashOfConfig": "178"}, {"size": 4520, "mtime": 1754033562484, "results": "320", "hashOfConfig": "178"}, {"size": 9698, "mtime": 1752078772721, "results": "321", "hashOfConfig": "178"}, {"size": 2749, "mtime": 1753981678189, "results": "322", "hashOfConfig": "178"}, {"size": 981, "mtime": 1747493748067, "results": "323", "hashOfConfig": "178"}, {"size": 4252, "mtime": 1748947428443, "results": "324", "hashOfConfig": "178"}, {"size": 718, "mtime": 1746861811304, "results": "325", "hashOfConfig": "178"}, {"size": 8172, "mtime": 1749309804607, "results": "326", "hashOfConfig": "178"}, {"size": 116, "mtime": 1747486012484, "results": "327", "hashOfConfig": "178"}, {"size": 5186, "mtime": 1753981988962, "results": "328", "hashOfConfig": "178"}, {"size": 875, "mtime": 1747069638958, "results": "329", "hashOfConfig": "178"}, {"size": 784, "mtime": 1743735208208, "results": "330", "hashOfConfig": "178"}, {"size": 1579, "mtime": 1748538942845, "results": "331", "hashOfConfig": "178"}, {"size": 611, "mtime": 1744966332004, "results": "332", "hashOfConfig": "178"}, {"size": 267, "mtime": 1747493757572, "results": "333", "hashOfConfig": "178"}, {"size": 830, "mtime": 1749048619068, "results": "334", "hashOfConfig": "178"}, {"size": 2655, "mtime": 1748084176532, "results": "335", "hashOfConfig": "178"}, {"size": 2036, "mtime": 1753981590902, "results": "336", "hashOfConfig": "178"}, {"size": 9171, "mtime": 1752907720744, "results": "337", "hashOfConfig": "178"}, {"size": 39733, "mtime": 1751722877757, "results": "338", "hashOfConfig": "178"}, {"size": 3654, "mtime": 1753879460985, "results": "339", "hashOfConfig": "178"}, {"size": 28589, "mtime": 1754310242548, "results": "340", "hashOfConfig": "178"}, {"size": 20980, "mtime": 1753879445361, "results": "341", "hashOfConfig": "178"}, {"size": 27294, "mtime": 1754124252020, "results": "342", "hashOfConfig": "178"}, {"size": 13078, "mtime": 1754291647475, "results": "343", "hashOfConfig": "178"}, {"size": 10772, "mtime": 1753982167889, "results": "344", "hashOfConfig": "178"}, {"size": 3273, "mtime": 1752980733333, "results": "345", "hashOfConfig": "178"}, {"size": 11160, "mtime": 1754058336322, "results": "346", "hashOfConfig": "178"}, {"size": 11602, "mtime": 1752980699691, "results": "347", "hashOfConfig": "178"}, {"size": 9434, "mtime": 1754291389413, "results": "348", "hashOfConfig": "178"}, {"size": 4157, "mtime": 1752078662945, "results": "349", "hashOfConfig": "178"}, {"size": 5339, "mtime": 1752600392806, "results": "350", "hashOfConfig": "351"}, {"size": 9161, "mtime": 1751720807660, "results": "352", "hashOfConfig": "178"}, {"size": 8070, "mtime": 1751720349397, "results": "353", "hashOfConfig": "178"}, {"size": 7287, "mtime": 1751721776870, "results": "354", "hashOfConfig": "178"}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "ds3qb5", {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 12, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 32, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "k4gnkr", {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-college\\page.tsx", ["883"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-question\\page.tsx", ["884", "885", "886", "887", "888", "889"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\add-subjectandtopic\\page.tsx", ["890", "891"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\college\\page.tsx", ["892", "893"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-college\\[id]\\page.tsx", ["894", "895"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\edit-question\\[id]\\page.tsx", ["896"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\page.tsx", ["897", "898", "899", "900", "901", "902"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\question-bank\\page.tsx", ["903", "904"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\super\\page.tsx", ["905", "906", "907"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx", ["908", "909", "910"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\page.tsx", ["911", "912", "913", "914", "915", "916", "917", "918"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\college\\teachers-list\\page.tsx", ["919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\forgot-password\\page.tsx", ["931", "932", "933", "934", "935", "936", "937"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx", ["938", "939"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\login\\page.tsx", ["940", "941", "942", "943", "944", "945", "946", "947"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\signup\\page.tsx", ["948", "949", "950", "951", "952", "953", "954", "955"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\downloaded-papers\\page.tsx", ["956", "957", "958", "959", "960", "961", "962", "963", "964", "965", "966", "967"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\page.tsx", ["968", "969"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx", ["970", "971"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\settings\\page.tsx", ["972", "973", "974"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\add-question-form.tsx", ["975", "976", "977", "978", "979", "980", "981", "982", "983", "984"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-edit-form.tsx", ["985", "986", "987"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\college-registration-form.tsx", ["988", "989"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\edit-question-form.tsx", ["990", "991", "992", "993", "994", "995", "996"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\file-uploader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\pagination-control.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\phone-input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-bank.tsx", ["997", "998", "999", "1000"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-list.tsx", ["1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019", "1020", "1021", "1022", "1023", "1024", "1025", "1026", "1027", "1028", "1029", "1030", "1031", "1032"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\question-bank\\question-skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-card.tsx", ["1033", "1034", "1035"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\university-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\auth\\protected-route.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\Breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeAdmin\\Tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\CollegeChart.tsx", ["1036", "1037", "1038", "1039", "1040", "1041"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\index.tsx", ["1042", "1043", "1044", "1045", "1046", "1047"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\DataTable\\TablePagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\debug\\TokenDebug.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\AboutSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Consultation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\Footer.tsx", ["1048"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\LessonsSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\navbar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\NavigationTabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\SubjectChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TestimonialsContact.tsx", ["1049", "1050", "1051", "1052", "1053"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\landing-page\\TransformingEducation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\command-menu.tsx", ["1054", "1055", "1056", "1057", "1058"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-layout.tsx", ["1059", "1060"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-navbar.tsx", ["1061", "1062", "1063", "1064", "1065"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\dashboard-sidebar.tsx", ["1066", "1067", "1068", "1069", "1070", "1071", "1072", "1073", "1074", "1075", "1076"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\layout\\navigation-loading-wrapper.tsx", ["1077"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\RoleProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\StatCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SubjectQuestionsChart.tsx", ["1078", "1079", "1080", "1081"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\SuperAdmin\\Tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-row.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\table-title.tsx", ["1082"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\teachers-table.tsx", ["1083", "1084", "1085", "1086", "1087", "1088", "1089"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\table\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TabNavigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\question-paper-wizard.tsx", ["1090", "1091", "1092", "1093", "1094", "1095"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\settings-form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\actions-step.tsx", ["1096", "1097", "1098"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\course-subject-step.tsx", ["1099"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\difficulty-level-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\include-answers-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\multi-subject-config-step.tsx", ["1100", "1101", "1102"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\paper-customization-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-selection-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-title-description-step.tsx", ["1103"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\question-type-step.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\Tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\info-message.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\option-button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-indicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\ui\\step-navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\user-profile.tsx", ["1104", "1105", "1106", "1107", "1108", "1109", "1110", "1111", "1112"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\ActionsMenu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\AddTeacherForm.tsx", ["1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\EditTeacherForm.tsx", ["1122"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\FilterModal.tsx", ["1123", "1124", "1125"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\StatusBadge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\Tabs.tsx", ["1126"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TeacherList\\TeacherProfile.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\TopTeachersList.tsx", ["1127"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\base64-image.tsx", ["1128"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\math-text.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\page-loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\sidebar.tsx", ["1129", "1130"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\text-with-images.tsx", ["1131"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\toaster.tsx", ["1132"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\use-toast.ts", ["1133", "1134", "1135"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\UsageChart.tsx", ["1136", "1137", "1138", "1139"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\hooks\\use-mobile.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\admin-analytics.ts", ["1140", "1141", "1142", "1143", "1144"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\analytics.ts", ["1145", "1146", "1147", "1148", "1149", "1150"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\apiClient.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\college.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questionPapers.ts", ["1151", "1152", "1153", "1154", "1155"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\questions.ts", ["1156"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\subjects.ts", ["1157", "1158", "1159", "1160", "1161", "1162", "1163", "1164", "1165"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\teachers.ts", ["1166", "1167", "1168", "1169", "1170", "1171", "1172", "1173", "1174"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\topics.ts", ["1175", "1176", "1177", "1178", "1179"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\upload.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\auth.ts", ["1180"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\enums.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\constants\\menuItems.ts", ["1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\firebase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\ReactQueryProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\interface.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\menu.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\types\\university.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\services\\teacherService.ts", ["1189"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\types\\question.ts", ["1190"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\imageUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\pdfGenerator.ts", ["1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-answers-excel\\route.ts", ["1199", "1200"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-paper-pdf\\route.ts", ["1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-solutions-pdf\\route.ts", ["1210", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\admin\\subject-chapter-manager.tsx", ["1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\chapter-selection-step.tsx", ["1227", "1228"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\teacher\\steps\\topic-selection-step.tsx", ["1229"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\test\\table-test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\chemical-image-display.tsx", ["1230"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\components\\ui\\enhanced-text-renderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\api\\chapters.ts", ["1231", "1232", "1233", "1234", "1235", "1236", "1237", "1238", "1239"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\lib\\utils\\errorHandler.ts", ["1240", "1241", "1242"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\test-image-extraction.js", [], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\htmlToImage.ts", ["1243"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\mathRenderer.ts", ["1244"], [], "C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\utils\\svgMathRenderer.ts", ["1245", "1246"], [], {"ruleId": "1247", "severity": 2, "message": "1248", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 29}, {"ruleId": "1247", "severity": 2, "message": "1248", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 29}, {"ruleId": "1247", "severity": 2, "message": "1250", "line": 5, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 33}, {"ruleId": "1247", "severity": 2, "message": "1251", "line": 7, "column": 8, "nodeType": null, "messageId": "1249", "endLine": 7, "endColumn": 34}, {"ruleId": "1247", "severity": 2, "message": "1252", "line": 11, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 11, "endColumn": 25}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 11, "column": 39, "nodeType": "1255", "messageId": "1256", "endLine": 11, "endColumn": 42, "suggestions": "1257"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 11, "column": 56, "nodeType": "1255", "messageId": "1256", "endLine": 11, "endColumn": 59, "suggestions": "1258"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 8, "column": 39, "nodeType": "1255", "messageId": "1256", "endLine": 8, "endColumn": 42, "suggestions": "1259"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 8, "column": 54, "nodeType": "1255", "messageId": "1256", "endLine": 8, "endColumn": 57, "suggestions": "1260"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 25, "column": 60, "nodeType": "1255", "messageId": "1256", "endLine": 25, "endColumn": 63, "suggestions": "1261"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 43, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 43, "endColumn": 24, "suggestions": "1262"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 10, "column": 50, "nodeType": "1255", "messageId": "1256", "endLine": 10, "endColumn": 53, "suggestions": "1263"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 25, "column": 23, "nodeType": "1255", "messageId": "1256", "endLine": 25, "endColumn": 26, "suggestions": "1264"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 23, "column": 23, "nodeType": "1255", "messageId": "1256", "endLine": 23, "endColumn": 26, "suggestions": "1265"}, {"ruleId": "1247", "severity": 2, "message": "1266", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 14}, {"ruleId": "1247", "severity": 2, "message": "1267", "line": 4, "column": 16, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 27}, {"ruleId": "1247", "severity": 2, "message": "1268", "line": 4, "column": 29, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 44}, {"ruleId": "1247", "severity": 2, "message": "1269", "line": 4, "column": 46, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 56}, {"ruleId": "1247", "severity": 2, "message": "1270", "line": 4, "column": 58, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 67}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 27, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 27, "endColumn": 24, "suggestions": "1271"}, {"ruleId": "1247", "severity": 2, "message": "1248", "line": 6, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 29}, {"ruleId": "1247", "severity": 2, "message": "1250", "line": 7, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 7, "endColumn": 33}, {"ruleId": "1247", "severity": 2, "message": "1272", "line": 3, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1273", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1274", "line": 5, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 17}, {"ruleId": "1247", "severity": 2, "message": "1272", "line": 3, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1273", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1274", "line": 5, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 17}, {"ruleId": "1247", "severity": 2, "message": "1266", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 14}, {"ruleId": "1247", "severity": 2, "message": "1275", "line": 5, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 25}, {"ruleId": "1247", "severity": 2, "message": "1276", "line": 7, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 7, "endColumn": 20}, {"ruleId": "1247", "severity": 2, "message": "1277", "line": 15, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 15, "endColumn": 18}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 26, "column": 44, "nodeType": "1255", "messageId": "1256", "endLine": 26, "endColumn": 47, "suggestions": "1278"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 94, "column": 42, "nodeType": "1255", "messageId": "1256", "endLine": 94, "endColumn": 45, "suggestions": "1279"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 106, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 106, "endColumn": 24, "suggestions": "1280"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 123, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 123, "endColumn": 24, "suggestions": "1281"}, {"ruleId": "1247", "severity": 2, "message": "1282", "line": 11, "column": 8, "nodeType": null, "messageId": "1249", "endLine": 11, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1283", "line": 15, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 15, "endColumn": 24}, {"ruleId": "1247", "severity": 2, "message": "1284", "line": 18, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 18, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1285", "line": 26, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 26, "endColumn": 27}, {"ruleId": "1247", "severity": 2, "message": "1286", "line": 33, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 33, "endColumn": 21}, {"ruleId": "1247", "severity": 2, "message": "1287", "line": 41, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 41, "endColumn": 20}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 165, "column": 61, "nodeType": "1255", "messageId": "1256", "endLine": 165, "endColumn": 64, "suggestions": "1288"}, {"ruleId": "1247", "severity": 2, "message": "1289", "line": 202, "column": 17, "nodeType": null, "messageId": "1249", "endLine": 202, "endColumn": 30}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 268, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 268, "endColumn": 24, "suggestions": "1290"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 278, "column": 48, "nodeType": "1255", "messageId": "1256", "endLine": 278, "endColumn": 51, "suggestions": "1291"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 299, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 299, "endColumn": 24, "suggestions": "1292"}, {"ruleId": "1247", "severity": 2, "message": "1293", "line": 309, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 309, "endColumn": 24}, {"ruleId": "1247", "severity": 2, "message": "1294", "line": 8, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 8, "endColumn": 14}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 47, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 47, "endColumn": 24, "suggestions": "1295"}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 63, "column": 15, "nodeType": "1298", "endLine": 63, "endColumn": 76}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 70, "column": 44, "nodeType": "1301", "messageId": "1302", "suggestions": "1303"}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 97, "column": 86, "nodeType": "1301", "messageId": "1302", "suggestions": "1304"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 159, "column": 13, "nodeType": "1301", "messageId": "1302", "suggestions": "1306"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 159, "column": 90, "nodeType": "1301", "messageId": "1302", "suggestions": "1307"}, {"ruleId": "1247", "severity": 2, "message": "1308", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 34}, {"ruleId": "1247", "severity": 2, "message": "1309", "line": 9, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 13}, {"ruleId": "1247", "severity": 2, "message": "1294", "line": 9, "column": 23, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 27}, {"ruleId": "1247", "severity": 2, "message": "1310", "line": 9, "column": 29, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 33}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 100, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 100, "endColumn": 24, "suggestions": "1311"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 158, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 158, "endColumn": 24, "suggestions": "1312"}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 193, "column": 15, "nodeType": "1298", "endLine": 193, "endColumn": 100}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 310, "column": 22, "nodeType": "1301", "messageId": "1302", "suggestions": "1313"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 326, "column": 13, "nodeType": "1301", "messageId": "1302", "suggestions": "1314"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 326, "column": 90, "nodeType": "1301", "messageId": "1302", "suggestions": "1315"}, {"ruleId": "1247", "severity": 2, "message": "1294", "line": 9, "column": 23, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 27}, {"ruleId": "1247", "severity": 2, "message": "1310", "line": 9, "column": 29, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 33}, {"ruleId": "1247", "severity": 2, "message": "1316", "line": 9, "column": 35, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 39}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 101, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 101, "endColumn": 24, "suggestions": "1317"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 144, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 144, "endColumn": 24, "suggestions": "1318"}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 178, "column": 15, "nodeType": "1298", "endLine": 178, "endColumn": 100}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 338, "column": 13, "nodeType": "1301", "messageId": "1302", "suggestions": "1319"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 338, "column": 90, "nodeType": "1301", "messageId": "1302", "suggestions": "1320"}, {"ruleId": "1247", "severity": 2, "message": "1272", "line": 3, "column": 27, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 36}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 40, "column": 44, "nodeType": "1255", "messageId": "1256", "endLine": 40, "endColumn": 47, "suggestions": "1321"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 41, "column": 22, "nodeType": "1255", "messageId": "1256", "endLine": 41, "endColumn": 25, "suggestions": "1322"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 45, "column": 48, "nodeType": "1255", "messageId": "1256", "endLine": 45, "endColumn": 51, "suggestions": "1323"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 47, "column": 51, "nodeType": "1255", "messageId": "1256", "endLine": 47, "endColumn": 54, "suggestions": "1324"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 63, "column": 50, "nodeType": "1255", "messageId": "1256", "endLine": 63, "endColumn": 53, "suggestions": "1325"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 80, "column": 54, "nodeType": "1255", "messageId": "1256", "endLine": 80, "endColumn": 57, "suggestions": "1326"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 80, "column": 91, "nodeType": "1255", "messageId": "1256", "endLine": 80, "endColumn": 94, "suggestions": "1327"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 109, "column": 54, "nodeType": "1255", "messageId": "1256", "endLine": 109, "endColumn": 57, "suggestions": "1328"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 109, "column": 91, "nodeType": "1255", "messageId": "1256", "endLine": 109, "endColumn": 94, "suggestions": "1329"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 138, "column": 54, "nodeType": "1255", "messageId": "1256", "endLine": 138, "endColumn": 57, "suggestions": "1330"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 175, "column": 51, "nodeType": "1255", "messageId": "1256", "endLine": 175, "endColumn": 54, "suggestions": "1331"}, {"ruleId": "1247", "severity": 2, "message": "1332", "line": 8, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 8, "endColumn": 19}, {"ruleId": "1247", "severity": 2, "message": "1333", "line": 10, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 10, "endColumn": 24}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 11, "column": 50, "nodeType": "1255", "messageId": "1256", "endLine": 11, "endColumn": 53, "suggestions": "1334"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 51, "column": 23, "nodeType": "1255", "messageId": "1256", "endLine": 51, "endColumn": 26, "suggestions": "1335"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 28, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 28, "endColumn": 24, "suggestions": "1336"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 43, "column": 37, "nodeType": "1255", "messageId": "1256", "endLine": 43, "endColumn": 40, "suggestions": "1337"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 54, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 54, "endColumn": 22, "suggestions": "1338"}, {"ruleId": "1247", "severity": 2, "message": "1339", "line": 43, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 43, "endColumn": 27}, {"ruleId": "1247", "severity": 2, "message": "1340", "line": 44, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 44, "endColumn": 25}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 109, "column": 23, "nodeType": "1255", "messageId": "1256", "endLine": 109, "endColumn": 26, "suggestions": "1341"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 144, "column": 28, "nodeType": "1255", "messageId": "1256", "endLine": 144, "endColumn": 31, "suggestions": "1342"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 146, "column": 25, "nodeType": "1255", "messageId": "1256", "endLine": 146, "endColumn": 28, "suggestions": "1343"}, {"ruleId": "1344", "severity": 2, "message": "1345", "line": 329, "column": 11, "nodeType": "1346", "messageId": "1347", "endLine": 329, "endColumn": 28, "fix": "1348"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 345, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 345, "endColumn": 24, "suggestions": "1349"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 401, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 401, "endColumn": 24, "suggestions": "1350"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 423, "column": 28, "nodeType": "1255", "messageId": "1256", "endLine": 423, "endColumn": 31, "suggestions": "1351"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 425, "column": 25, "nodeType": "1255", "messageId": "1256", "endLine": 425, "endColumn": 28, "suggestions": "1352"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 49, "column": 16, "nodeType": "1255", "messageId": "1256", "endLine": 49, "endColumn": 19, "suggestions": "1353"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 113, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 113, "endColumn": 24, "suggestions": "1354"}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 213, "column": 21, "nodeType": "1298", "endLine": 213, "endColumn": 118}, {"ruleId": "1247", "severity": 2, "message": "1355", "line": 15, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 15, "endColumn": 20}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 99, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 99, "endColumn": 24, "suggestions": "1356"}, {"ruleId": "1247", "severity": 2, "message": "1357", "line": 15, "column": 29, "nodeType": null, "messageId": "1249", "endLine": 15, "endColumn": 44}, {"ruleId": "1247", "severity": 2, "message": "1358", "line": 41, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 41, "endColumn": 20}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 109, "column": 28, "nodeType": "1255", "messageId": "1256", "endLine": 109, "endColumn": 31, "suggestions": "1359"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 111, "column": 25, "nodeType": "1255", "messageId": "1256", "endLine": 111, "endColumn": 28, "suggestions": "1360"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 196, "column": 23, "nodeType": "1255", "messageId": "1256", "endLine": 196, "endColumn": 26, "suggestions": "1361"}, {"ruleId": "1344", "severity": 2, "message": "1345", "line": 356, "column": 11, "nodeType": "1346", "messageId": "1347", "endLine": 356, "endColumn": 28, "fix": "1362"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 375, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 375, "endColumn": 24, "suggestions": "1363"}, {"ruleId": "1247", "severity": 2, "message": "1364", "line": 38, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 38, "endColumn": 22}, {"ruleId": "1247", "severity": 2, "message": "1365", "line": 38, "column": 24, "nodeType": null, "messageId": "1249", "endLine": 38, "endColumn": 39}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 310, "column": 41, "nodeType": "1255", "messageId": "1256", "endLine": 310, "endColumn": 44, "suggestions": "1366"}, {"ruleId": "1367", "severity": 1, "message": "1368", "line": 432, "column": 15, "nodeType": "1298", "endLine": 432, "endColumn": 44}, {"ruleId": "1247", "severity": 2, "message": "1369", "line": 21, "column": 26, "nodeType": null, "messageId": "1249", "endLine": 21, "endColumn": 40}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 93, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 93, "endColumn": 24, "suggestions": "1370"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 189, "column": 31, "nodeType": "1255", "messageId": "1256", "endLine": 189, "endColumn": 34, "suggestions": "1371"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 189, "column": 64, "nodeType": "1255", "messageId": "1256", "endLine": 189, "endColumn": 67, "suggestions": "1372"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 189, "column": 100, "nodeType": "1255", "messageId": "1256", "endLine": 189, "endColumn": 103, "suggestions": "1373"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 192, "column": 42, "nodeType": "1255", "messageId": "1256", "endLine": 192, "endColumn": 45, "suggestions": "1374"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 192, "column": 73, "nodeType": "1255", "messageId": "1256", "endLine": 192, "endColumn": 76, "suggestions": "1375"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 203, "column": 36, "nodeType": "1255", "messageId": "1256", "endLine": 203, "endColumn": 39, "suggestions": "1376"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 203, "column": 81, "nodeType": "1255", "messageId": "1256", "endLine": 203, "endColumn": 84, "suggestions": "1377"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 204, "column": 53, "nodeType": "1255", "messageId": "1256", "endLine": 204, "endColumn": 56, "suggestions": "1378"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 205, "column": 41, "nodeType": "1255", "messageId": "1256", "endLine": 205, "endColumn": 44, "suggestions": "1379"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 205, "column": 72, "nodeType": "1255", "messageId": "1256", "endLine": 205, "endColumn": 75, "suggestions": "1380"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 214, "column": 32, "nodeType": "1255", "messageId": "1256", "endLine": 214, "endColumn": 35, "suggestions": "1381"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 214, "column": 65, "nodeType": "1255", "messageId": "1256", "endLine": 214, "endColumn": 68, "suggestions": "1382"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 214, "column": 101, "nodeType": "1255", "messageId": "1256", "endLine": 214, "endColumn": 104, "suggestions": "1383"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 218, "column": 44, "nodeType": "1255", "messageId": "1256", "endLine": 218, "endColumn": 47, "suggestions": "1384"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 218, "column": 75, "nodeType": "1255", "messageId": "1256", "endLine": 218, "endColumn": 78, "suggestions": "1385"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 240, "column": 46, "nodeType": "1255", "messageId": "1256", "endLine": 240, "endColumn": 49, "suggestions": "1386"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 240, "column": 91, "nodeType": "1255", "messageId": "1256", "endLine": 240, "endColumn": 94, "suggestions": "1387"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 241, "column": 63, "nodeType": "1255", "messageId": "1256", "endLine": 241, "endColumn": 66, "suggestions": "1388"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 242, "column": 51, "nodeType": "1255", "messageId": "1256", "endLine": 242, "endColumn": 54, "suggestions": "1389"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 242, "column": 82, "nodeType": "1255", "messageId": "1256", "endLine": 242, "endColumn": 85, "suggestions": "1390"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 319, "column": 50, "nodeType": "1255", "messageId": "1256", "endLine": 319, "endColumn": 53, "suggestions": "1391"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 319, "column": 95, "nodeType": "1255", "messageId": "1256", "endLine": 319, "endColumn": 98, "suggestions": "1392"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 320, "column": 67, "nodeType": "1255", "messageId": "1256", "endLine": 320, "endColumn": 70, "suggestions": "1393"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 321, "column": 55, "nodeType": "1255", "messageId": "1256", "endLine": 321, "endColumn": 58, "suggestions": "1394"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 321, "column": 86, "nodeType": "1255", "messageId": "1256", "endLine": 321, "endColumn": 89, "suggestions": "1395"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 340, "column": 44, "nodeType": "1255", "messageId": "1256", "endLine": 340, "endColumn": 47, "suggestions": "1396"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 340, "column": 89, "nodeType": "1255", "messageId": "1256", "endLine": 340, "endColumn": 92, "suggestions": "1397"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 341, "column": 61, "nodeType": "1255", "messageId": "1256", "endLine": 341, "endColumn": 64, "suggestions": "1398"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 342, "column": 49, "nodeType": "1255", "messageId": "1256", "endLine": 342, "endColumn": 52, "suggestions": "1399"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 342, "column": 80, "nodeType": "1255", "messageId": "1256", "endLine": 342, "endColumn": 83, "suggestions": "1400"}, {"ruleId": "1247", "severity": 2, "message": "1401", "line": 24, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 24, "endColumn": 22}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 55, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 55, "endColumn": 24, "suggestions": "1402"}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 104, "column": 15, "nodeType": "1298", "endLine": 108, "endColumn": 17}, {"ruleId": "1247", "severity": 2, "message": "1403", "line": 5, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 12}, {"ruleId": "1247", "severity": 2, "message": "1404", "line": 6, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 7}, {"ruleId": "1247", "severity": 2, "message": "1405", "line": 18, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 18, "endColumn": 12}, {"ruleId": "1247", "severity": 2, "message": "1406", "line": 20, "column": 28, "nodeType": null, "messageId": "1249", "endLine": 20, "endColumn": 49}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 23, "column": 52, "nodeType": "1255", "messageId": "1256", "endLine": 23, "endColumn": 55, "suggestions": "1407"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 28, "column": 30, "nodeType": "1255", "messageId": "1256", "endLine": 28, "endColumn": 33, "suggestions": "1408"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 78, "column": 32, "nodeType": "1255", "messageId": "1256", "endLine": 78, "endColumn": 35, "suggestions": "1409"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 91, "column": 28, "nodeType": "1255", "messageId": "1256", "endLine": 91, "endColumn": 31, "suggestions": "1410"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 92, "column": 28, "nodeType": "1255", "messageId": "1256", "endLine": 92, "endColumn": 31, "suggestions": "1411"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 259, "column": 65, "nodeType": "1255", "messageId": "1256", "endLine": 259, "endColumn": 68, "suggestions": "1412"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 269, "column": 44, "nodeType": "1301", "messageId": "1302", "suggestions": "1413"}, {"ruleId": "1299", "severity": 2, "message": "1305", "line": 269, "column": 57, "nodeType": "1301", "messageId": "1302", "suggestions": "1414"}, {"ruleId": "1247", "severity": 2, "message": "1415", "line": 25, "column": 35, "nodeType": null, "messageId": "1249", "endLine": 25, "endColumn": 40}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 110, "column": 17, "nodeType": "1298", "endLine": 114, "endColumn": 19}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 135, "column": 15, "nodeType": "1298", "endLine": 139, "endColumn": 17}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 188, "column": 13, "nodeType": "1298", "endLine": 192, "endColumn": 15}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 199, "column": 16, "nodeType": "1301", "messageId": "1302", "suggestions": "1416"}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 221, "column": 88, "nodeType": "1301", "messageId": "1302", "suggestions": "1417"}, {"ruleId": "1247", "severity": 2, "message": "1418", "line": 6, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 12}, {"ruleId": "1247", "severity": 2, "message": "1419", "line": 9, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1294", "line": 11, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 11, "endColumn": 7}, {"ruleId": "1247", "severity": 2, "message": "1420", "line": 12, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 12, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1421", "line": 13, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 13, "endColumn": 10}, {"ruleId": "1247", "severity": 2, "message": "1422", "line": 17, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 17, "endColumn": 21}, {"ruleId": "1247", "severity": 2, "message": "1423", "line": 17, "column": 23, "nodeType": null, "messageId": "1249", "endLine": 17, "endColumn": 37}, {"ruleId": "1247", "severity": 2, "message": "1424", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 14}, {"ruleId": "1247", "severity": 2, "message": "1425", "line": 7, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 7, "endColumn": 21}, {"ruleId": "1247", "severity": 2, "message": "1426", "line": 17, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 17, "endColumn": 15}, {"ruleId": "1247", "severity": 2, "message": "1427", "line": 28, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 28, "endColumn": 27}, {"ruleId": "1247", "severity": 2, "message": "1428", "line": 28, "column": 29, "nodeType": null, "messageId": "1249", "endLine": 28, "endColumn": 49}, {"ruleId": "1247", "severity": 2, "message": "1418", "line": 6, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 12}, {"ruleId": "1247", "severity": 2, "message": "1429", "line": 7, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 7, "endColumn": 11}, {"ruleId": "1247", "severity": 2, "message": "1430", "line": 8, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 8, "endColumn": 11}, {"ruleId": "1247", "severity": 2, "message": "1419", "line": 9, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1431", "line": 10, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 10, "endColumn": 18}, {"ruleId": "1247", "severity": 2, "message": "1294", "line": 11, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 11, "endColumn": 7}, {"ruleId": "1247", "severity": 2, "message": "1420", "line": 12, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 12, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1421", "line": 13, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 13, "endColumn": 10}, {"ruleId": "1247", "severity": 2, "message": "1432", "line": 14, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 14, "endColumn": 8}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 67, "column": 11, "nodeType": "1298", "endLine": 71, "endColumn": 13}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 107, "column": 31, "nodeType": "1298", "endLine": 111, "endColumn": 33}, {"ruleId": "1247", "severity": 2, "message": "1433", "line": 31, "column": 11, "nodeType": null, "messageId": "1249", "endLine": 31, "endColumn": 36}, {"ruleId": "1247", "severity": 2, "message": "1434", "line": 56, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 56, "endColumn": 17}, {"ruleId": "1344", "severity": 2, "message": "1435", "line": 73, "column": 9, "nodeType": "1346", "messageId": "1347", "endLine": 73, "endColumn": 14, "fix": "1436"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 116, "column": 67, "nodeType": "1255", "messageId": "1256", "endLine": 116, "endColumn": 70, "suggestions": "1437"}, {"ruleId": "1438", "severity": 1, "message": "1439", "line": 133, "column": 6, "nodeType": "1440", "endLine": 133, "endColumn": 28, "suggestions": "1441"}, {"ruleId": "1247", "severity": 2, "message": "1442", "line": 2, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 2, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1443", "line": 5, "column": 45, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 51}, {"ruleId": "1247", "severity": 2, "message": "1444", "line": 9, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 8}, {"ruleId": "1247", "severity": 2, "message": "1445", "line": 12, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 12, "endColumn": 10}, {"ruleId": "1247", "severity": 2, "message": "1446", "line": 15, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 15, "endColumn": 12}, {"ruleId": "1247", "severity": 2, "message": "1447", "line": 16, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 16, "endColumn": 11}, {"ruleId": "1247", "severity": 2, "message": "1287", "line": 33, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 33, "endColumn": 19}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 82, "column": 23, "nodeType": "1298", "endLine": 82, "endColumn": 125}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 369, "column": 22, "nodeType": "1255", "messageId": "1256", "endLine": 369, "endColumn": 25, "suggestions": "1448"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 370, "column": 37, "nodeType": "1255", "messageId": "1256", "endLine": 370, "endColumn": 40, "suggestions": "1449"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 372, "column": 43, "nodeType": "1255", "messageId": "1256", "endLine": 372, "endColumn": 46, "suggestions": "1450"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 387, "column": 45, "nodeType": "1255", "messageId": "1256", "endLine": 387, "endColumn": 48, "suggestions": "1451"}, {"ruleId": "1247", "severity": 2, "message": "1452", "line": 446, "column": 14, "nodeType": null, "messageId": "1249", "endLine": 446, "endColumn": 19}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 643, "column": 72, "nodeType": "1255", "messageId": "1256", "endLine": 643, "endColumn": 75, "suggestions": "1453"}, {"ruleId": "1247", "severity": 2, "message": "1454", "line": 5, "column": 20, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 23}, {"ruleId": "1247", "severity": 2, "message": "1455", "line": 5, "column": 25, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 29}, {"ruleId": "1247", "severity": 2, "message": "1456", "line": 14, "column": 31, "nodeType": null, "messageId": "1249", "endLine": 14, "endColumn": 39}, {"ruleId": "1247", "severity": 2, "message": "1457", "line": 3, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 18}, {"ruleId": "1247", "severity": 2, "message": "1458", "line": 49, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 49, "endColumn": 25}, {"ruleId": "1438", "severity": 1, "message": "1459", "line": 125, "column": 6, "nodeType": "1440", "endLine": 125, "endColumn": 27, "suggestions": "1460"}, {"ruleId": "1438", "severity": 1, "message": "1461", "line": 241, "column": 6, "nodeType": "1440", "endLine": 241, "endColumn": 66, "suggestions": "1462"}, {"ruleId": "1247", "severity": 2, "message": "1463", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 22}, {"ruleId": "1247", "severity": 2, "message": "1464", "line": 3, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1465", "line": 5, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 18}, {"ruleId": "1247", "severity": 2, "message": "1466", "line": 5, "column": 20, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 29}, {"ruleId": "1247", "severity": 2, "message": "1467", "line": 5, "column": 31, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 39}, {"ruleId": "1247", "severity": 2, "message": "1468", "line": 5, "column": 41, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 47}, {"ruleId": "1247", "severity": 2, "message": "1469", "line": 5, "column": 49, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 56}, {"ruleId": "1247", "severity": 2, "message": "1470", "line": 6, "column": 8, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 13}, {"ruleId": "1247", "severity": 2, "message": "1471", "line": 32, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 32, "endColumn": 19}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 55, "column": 23, "nodeType": "1298", "endLine": 55, "endColumn": 144}, {"ruleId": "1247", "severity": 2, "message": "1472", "line": 27, "column": 6, "nodeType": null, "messageId": "1249", "endLine": 27, "endColumn": 23}, {"ruleId": "1247", "severity": 2, "message": "1473", "line": 30, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 30, "endColumn": 15}, {"ruleId": "1247", "severity": 2, "message": "1474", "line": 31, "column": 11, "nodeType": null, "messageId": "1249", "endLine": 31, "endColumn": 15}, {"ruleId": "1247", "severity": 2, "message": "1475", "line": 31, "column": 17, "nodeType": null, "messageId": "1249", "endLine": 31, "endColumn": 25}, {"ruleId": "1247", "severity": 2, "message": "1476", "line": 85, "column": 21, "nodeType": null, "messageId": "1249", "endLine": 85, "endColumn": 33}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 193, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 193, "endColumn": 24, "suggestions": "1477"}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 294, "column": 22, "nodeType": "1301", "messageId": "1302", "suggestions": "1478"}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 368, "column": 20, "nodeType": "1301", "messageId": "1302", "suggestions": "1479"}, {"ruleId": "1299", "severity": 2, "message": "1300", "line": 382, "column": 20, "nodeType": "1301", "messageId": "1302", "suggestions": "1480"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 11, "column": 24, "nodeType": "1255", "messageId": "1256", "endLine": 11, "endColumn": 27, "suggestions": "1481"}, {"ruleId": "1247", "severity": 2, "message": "1457", "line": 2, "column": 17, "nodeType": null, "messageId": "1249", "endLine": 2, "endColumn": 25}, {"ruleId": "1247", "severity": 2, "message": "1482", "line": 11, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 11, "endColumn": 15}, {"ruleId": "1247", "severity": 2, "message": "1483", "line": 28, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 28, "endColumn": 11}, {"ruleId": "1247", "severity": 2, "message": "1484", "line": 2, "column": 15, "nodeType": null, "messageId": "1249", "endLine": 2, "endColumn": 26}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 63, "column": 17, "nodeType": "1298", "endLine": 67, "endColumn": 19}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 51, "column": 7, "nodeType": "1298", "endLine": 70, "endColumn": 9}, {"ruleId": "1247", "severity": 2, "message": "1485", "line": 6, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 23}, {"ruleId": "1296", "severity": 1, "message": "1297", "line": 277, "column": 7, "nodeType": "1298", "endLine": 277, "endColumn": 85}, {"ruleId": "1247", "severity": 2, "message": "1486", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 18}, {"ruleId": "1247", "severity": 2, "message": "1487", "line": 16, "column": 9, "nodeType": null, "messageId": "1249", "endLine": 16, "endColumn": 13}, {"ruleId": "1247", "severity": 2, "message": "1488", "line": 14, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 14, "endColumn": 23}, {"ruleId": "1247", "severity": 2, "message": "1489", "line": 15, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 15, "endColumn": 22}, {"ruleId": "1247", "severity": 2, "message": "1490", "line": 16, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 16, "endColumn": 25}, {"ruleId": "1247", "severity": 2, "message": "1491", "line": 18, "column": 26, "nodeType": null, "messageId": "1249", "endLine": 18, "endColumn": 45}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 21, "column": 52, "nodeType": "1255", "messageId": "1256", "endLine": 21, "endColumn": 55, "suggestions": "1492"}, {"ruleId": "1438", "severity": 1, "message": "1493", "line": 87, "column": 27, "nodeType": "1440", "endLine": 87, "endColumn": 42}, {"ruleId": "1438", "severity": 1, "message": "1494", "line": 93, "column": 26, "nodeType": "1495", "endLine": 93, "endColumn": 87}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 23, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 23, "endColumn": 22, "suggestions": "1496"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 36, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 36, "endColumn": 22, "suggestions": "1497"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 49, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 49, "endColumn": 22, "suggestions": "1498"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 62, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 62, "endColumn": 22, "suggestions": "1499"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 114, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 114, "endColumn": 22, "suggestions": "1500"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 24, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 24, "endColumn": 22, "suggestions": "1501"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 38, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 38, "endColumn": 22, "suggestions": "1502"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 55, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 55, "endColumn": 22, "suggestions": "1503"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 72, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 72, "endColumn": 22, "suggestions": "1504"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 126, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 126, "endColumn": 22, "suggestions": "1505"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 192, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 192, "endColumn": 22, "suggestions": "1506"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 137, "column": 17, "nodeType": "1255", "messageId": "1256", "endLine": 137, "endColumn": 20, "suggestions": "1507"}, {"ruleId": "1247", "severity": 2, "message": "1508", "line": 207, "column": 20, "nodeType": null, "messageId": "1249", "endLine": 207, "endColumn": 29}, {"ruleId": "1247", "severity": 2, "message": "1509", "line": 212, "column": 16, "nodeType": null, "messageId": "1249", "endLine": 212, "endColumn": 26}, {"ruleId": "1247", "severity": 2, "message": "1452", "line": 260, "column": 12, "nodeType": null, "messageId": "1249", "endLine": 260, "endColumn": 17}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 271, "column": 18, "nodeType": "1255", "messageId": "1256", "endLine": 271, "endColumn": 21, "suggestions": "1510"}, {"ruleId": "1247", "severity": 2, "message": "1511", "line": 4, "column": 7, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 19}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 42, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 42, "endColumn": 22, "suggestions": "1512"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 57, "column": 33, "nodeType": "1255", "messageId": "1256", "endLine": 57, "endColumn": 36, "suggestions": "1513"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 61, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 61, "endColumn": 22, "suggestions": "1514"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 74, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 74, "endColumn": 22, "suggestions": "1515"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 89, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 89, "endColumn": 22, "suggestions": "1516"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 103, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 103, "endColumn": 22, "suggestions": "1517"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 120, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 120, "endColumn": 22, "suggestions": "1518"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 141, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 141, "endColumn": 22, "suggestions": "1519"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 156, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 156, "endColumn": 22, "suggestions": "1520"}, {"ruleId": "1247", "severity": 2, "message": "1521", "line": 1, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 1, "endColumn": 13}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 65, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 65, "endColumn": 22, "suggestions": "1522"}, {"ruleId": "1247", "severity": 2, "message": "1523", "line": 121, "column": 16, "nodeType": null, "messageId": "1249", "endLine": 121, "endColumn": 17}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 146, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 146, "endColumn": 22, "suggestions": "1524"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 156, "column": 69, "nodeType": "1255", "messageId": "1256", "endLine": 156, "endColumn": 72, "suggestions": "1525"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 190, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 190, "endColumn": 22, "suggestions": "1526"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 231, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 231, "endColumn": 22, "suggestions": "1527"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 275, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 275, "endColumn": 22, "suggestions": "1528"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 316, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 316, "endColumn": 22, "suggestions": "1529"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 28, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 28, "endColumn": 22, "suggestions": "1530"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 42, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 42, "endColumn": 22, "suggestions": "1531"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 61, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 61, "endColumn": 22, "suggestions": "1532"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 82, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 82, "endColumn": 22, "suggestions": "1533"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 97, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 97, "endColumn": 22, "suggestions": "1534"}, {"ruleId": "1535", "severity": 2, "message": "1536", "line": 21, "column": 27, "nodeType": "1346", "endLine": 21, "endColumn": 34}, {"ruleId": "1247", "severity": 2, "message": "1429", "line": 3, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 11}, {"ruleId": "1247", "severity": 2, "message": "1432", "line": 4, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 8}, {"ruleId": "1247", "severity": 2, "message": "1419", "line": 5, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 5, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1430", "line": 6, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 6, "endColumn": 11}, {"ruleId": "1247", "severity": 2, "message": "1418", "line": 7, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 7, "endColumn": 12}, {"ruleId": "1247", "severity": 2, "message": "1420", "line": 8, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 8, "endColumn": 16}, {"ruleId": "1247", "severity": 2, "message": "1294", "line": 9, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 9, "endColumn": 7}, {"ruleId": "1247", "severity": 2, "message": "1421", "line": 10, "column": 3, "nodeType": null, "messageId": "1249", "endLine": 10, "endColumn": 10}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 20, "column": 38, "nodeType": "1255", "messageId": "1256", "endLine": 20, "endColumn": 41, "suggestions": "1537"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 32, "column": 23, "nodeType": "1255", "messageId": "1256", "endLine": 32, "endColumn": 26, "suggestions": "1538"}, {"ruleId": "1247", "severity": 2, "message": "1539", "line": 3, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 3, "endColumn": 22}, {"ruleId": "1247", "severity": 2, "message": "1452", "line": 132, "column": 14, "nodeType": null, "messageId": "1249", "endLine": 132, "endColumn": 19}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 244, "column": 50, "nodeType": "1255", "messageId": "1256", "endLine": 244, "endColumn": 53, "suggestions": "1540"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 255, "column": 49, "nodeType": "1255", "messageId": "1256", "endLine": 255, "endColumn": 52, "suggestions": "1541"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 272, "column": 54, "nodeType": "1255", "messageId": "1256", "endLine": 272, "endColumn": 57, "suggestions": "1542"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 276, "column": 53, "nodeType": "1255", "messageId": "1256", "endLine": 276, "endColumn": 56, "suggestions": "1543"}, {"ruleId": "1344", "severity": 2, "message": "1544", "line": 539, "column": 9, "nodeType": "1346", "messageId": "1347", "endLine": 539, "endColumn": 20, "fix": "1545"}, {"ruleId": "1247", "severity": 2, "message": "1546", "line": 806, "column": 11, "nodeType": null, "messageId": "1249", "endLine": 806, "endColumn": 24}, {"ruleId": "1247", "severity": 2, "message": "1415", "line": 72, "column": 35, "nodeType": null, "messageId": "1249", "endLine": 72, "endColumn": 40}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 122, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 122, "endColumn": 22, "suggestions": "1547"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 151, "column": 42, "nodeType": "1255", "messageId": "1256", "endLine": 151, "endColumn": 45, "suggestions": "1548"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 152, "column": 43, "nodeType": "1255", "messageId": "1256", "endLine": 152, "endColumn": 46, "suggestions": "1549"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 348, "column": 43, "nodeType": "1255", "messageId": "1256", "endLine": 348, "endColumn": 46, "suggestions": "1550"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 407, "column": 43, "nodeType": "1255", "messageId": "1256", "endLine": 407, "endColumn": 46, "suggestions": "1551"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 407, "column": 67, "nodeType": "1255", "messageId": "1256", "endLine": 407, "endColumn": 70, "suggestions": "1552"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 630, "column": 25, "nodeType": "1255", "messageId": "1256", "endLine": 630, "endColumn": 28, "suggestions": "1553"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 635, "column": 22, "nodeType": "1255", "messageId": "1256", "endLine": 635, "endColumn": 25, "suggestions": "1554"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 636, "column": 20, "nodeType": "1255", "messageId": "1256", "endLine": 636, "endColumn": 23, "suggestions": "1555"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 675, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 675, "endColumn": 22, "suggestions": "1556"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 319, "column": 31, "nodeType": "1255", "messageId": "1256", "endLine": 319, "endColumn": 34, "suggestions": "1557"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 343, "column": 31, "nodeType": "1255", "messageId": "1256", "endLine": 343, "endColumn": 34, "suggestions": "1558"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 343, "column": 55, "nodeType": "1255", "messageId": "1256", "endLine": 343, "endColumn": 58, "suggestions": "1559"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 367, "column": 35, "nodeType": "1255", "messageId": "1256", "endLine": 367, "endColumn": 38, "suggestions": "1560"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 367, "column": 59, "nodeType": "1255", "messageId": "1256", "endLine": 367, "endColumn": 62, "suggestions": "1561"}, {"ruleId": "1344", "severity": 2, "message": "1562", "line": 417, "column": 13, "nodeType": "1346", "messageId": "1347", "endLine": 417, "endColumn": 25, "fix": "1563"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 427, "column": 37, "nodeType": "1255", "messageId": "1256", "endLine": 427, "endColumn": 40, "suggestions": "1564"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 427, "column": 61, "nodeType": "1255", "messageId": "1256", "endLine": 427, "endColumn": 64, "suggestions": "1565"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 527, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 527, "endColumn": 22, "suggestions": "1566"}, {"ruleId": "1247", "severity": 2, "message": "1567", "line": 11, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 11, "endColumn": 18}, {"ruleId": "1438", "severity": 1, "message": "1568", "line": 129, "column": 6, "nodeType": "1440", "endLine": 129, "endColumn": 8, "suggestions": "1569"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 172, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 172, "endColumn": 24, "suggestions": "1570"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 217, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 217, "endColumn": 24, "suggestions": "1571"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 248, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 248, "endColumn": 24, "suggestions": "1572"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 309, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 309, "endColumn": 24, "suggestions": "1573"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 353, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 353, "endColumn": 24, "suggestions": "1574"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 376, "column": 21, "nodeType": "1255", "messageId": "1256", "endLine": 376, "endColumn": 24, "suggestions": "1575"}, {"ruleId": "1247", "severity": 2, "message": "1576", "line": 13, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 13, "endColumn": 14}, {"ruleId": "1438", "severity": 1, "message": "1577", "line": 42, "column": 6, "nodeType": "1440", "endLine": 42, "endColumn": 24, "suggestions": "1578"}, {"ruleId": "1438", "severity": 1, "message": "1579", "line": 51, "column": 6, "nodeType": "1440", "endLine": 51, "endColumn": 63, "suggestions": "1580"}, {"ruleId": "1247", "severity": 2, "message": "1486", "line": 4, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 4, "endColumn": 18}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 24, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 24, "endColumn": 22, "suggestions": "1581"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 62, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 62, "endColumn": 22, "suggestions": "1582"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 76, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 76, "endColumn": 22, "suggestions": "1583"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 97, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 97, "endColumn": 22, "suggestions": "1584"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 122, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 122, "endColumn": 22, "suggestions": "1585"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 137, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 137, "endColumn": 22, "suggestions": "1586"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 185, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 185, "endColumn": 22, "suggestions": "1587"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 219, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 219, "endColumn": 22, "suggestions": "1588"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 262, "column": 19, "nodeType": "1255", "messageId": "1256", "endLine": 262, "endColumn": 22, "suggestions": "1589"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 9, "column": 33, "nodeType": "1255", "messageId": "1256", "endLine": 9, "endColumn": 36, "suggestions": "1590"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 14, "column": 29, "nodeType": "1255", "messageId": "1256", "endLine": 14, "endColumn": 32, "suggestions": "1591"}, {"ruleId": "1253", "severity": 2, "message": "1254", "line": 24, "column": 10, "nodeType": "1255", "messageId": "1256", "endLine": 24, "endColumn": 13, "suggestions": "1592"}, {"ruleId": "1247", "severity": 2, "message": "1593", "line": 8, "column": 10, "nodeType": null, "messageId": "1249", "endLine": 8, "endColumn": 15}, {"ruleId": "1344", "severity": 2, "message": "1594", "line": 106, "column": 9, "nodeType": "1346", "messageId": "1347", "endLine": 106, "endColumn": 21, "fix": "1595"}, {"ruleId": "1247", "severity": 2, "message": "1596", "line": 148, "column": 33, "nodeType": null, "messageId": "1249", "endLine": 148, "endColumn": 43}, {"ruleId": "1247", "severity": 2, "message": "1597", "line": 148, "column": 53, "nodeType": null, "messageId": "1249", "endLine": 148, "endColumn": 60}, "@typescript-eslint/no-unused-vars", "'QuestionPaperWizard' is defined but never used.", "unusedVar", "'CollegeRegistrationForm' is defined but never used.", "'SubjectChapterTopicManager' is defined but never used.", "'handleDataChange' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1598", "1599"], ["1600", "1601"], ["1602", "1603"], ["1604", "1605"], ["1606", "1607"], ["1608", "1609"], ["1610", "1611"], ["1612", "1613"], ["1614", "1615"], "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardDescription' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", ["1616", "1617"], "'useEffect' is defined but never used.", "'useRouter' is defined but never used.", "'useAuth' is defined but never used.", "'DashboardLayout' is defined but never used.", "'HelpCircle' is defined but never used.", "'chartData' is assigned a value but never used.", ["1618", "1619"], ["1620", "1621"], ["1622", "1623"], ["1624", "1625"], "'FilterModal' is defined but never used.", "'filterTeachers' is defined but never used.", "'RefreshCw' is defined but never used.", "'isFilterModalOpen' is assigned a value but never used.", "'departments' is assigned a value but never used.", "'totalPages' is assigned a value but never used.", ["1626", "1627"], "'DepartmentMap' is defined but never used.", ["1628", "1629"], ["1630", "1631"], ["1632", "1633"], "'handleFilterApply' is assigned a value but never used.", "'Mail' is defined but never used.", ["1634", "1635"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1636", "1637", "1638", "1639"], ["1640", "1641", "1642", "1643"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1644", "1645", "1646", "1647"], ["1648", "1649", "1650", "1651"], "'NavigationLoadingWrapper' is defined but never used.", "'outfit' is assigned a value but never used.", "'Lock' is defined but never used.", ["1652", "1653"], ["1654", "1655"], ["1656", "1657", "1658", "1659"], ["1660", "1661", "1662", "1663"], ["1664", "1665", "1666", "1667"], "'User' is defined but never used.", ["1668", "1669"], ["1670", "1671"], ["1672", "1673", "1674", "1675"], ["1676", "1677", "1678", "1679"], ["1680", "1681"], ["1682", "1683"], ["1684", "1685"], ["1686", "1687"], ["1688", "1689"], ["1690", "1691"], ["1692", "1693"], ["1694", "1695"], ["1696", "1697"], ["1698", "1699"], ["1700", "1701"], "'activeTab' is assigned a value but never used.", "'handleTabChange' is assigned a value but never used.", ["1702", "1703"], ["1704", "1705"], ["1706", "1707"], ["1708", "1709"], ["1710", "1711"], "'ACCEPTED_IMAGE_TYPES' is assigned a value but never used.", "'ACCEPTED_PDF_TYPES' is assigned a value but never used.", ["1712", "1713"], ["1714", "1715"], ["1716", "1717"], "prefer-const", "'finalQuestionData' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1718", "text": "1719"}, ["1720", "1721"], ["1722", "1723"], ["1724", "1725"], ["1726", "1727"], ["1728", "1729"], ["1730", "1731"], "'uploadFile' is defined but never used.", ["1732", "1733"], "'FormDescription' is defined but never used.", "'MAX_FILE_SIZE' is assigned a value but never used.", ["1734", "1735"], ["1736", "1737"], ["1738", "1739"], {"range": "1740", "text": "1719"}, ["1741", "1742"], "'refreshToken' is assigned a value but never used.", "'setRefreshToken' is assigned a value but never used.", ["1743", "1744"], "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "'reviewQuestion' is defined but never used.", ["1745", "1746"], ["1747", "1748"], ["1749", "1750"], ["1751", "1752"], ["1753", "1754"], ["1755", "1756"], ["1757", "1758"], ["1759", "1760"], ["1761", "1762"], ["1763", "1764"], ["1765", "1766"], ["1767", "1768"], ["1769", "1770"], ["1771", "1772"], ["1773", "1774"], ["1775", "1776"], ["1777", "1778"], ["1779", "1780"], ["1781", "1782"], ["1783", "1784"], ["1785", "1786"], ["1787", "1788"], ["1789", "1790"], ["1791", "1792"], ["1793", "1794"], ["1795", "1796"], ["1797", "1798"], ["1799", "1800"], ["1801", "1802"], ["1803", "1804"], ["1805", "1806"], "'isApiSuccess' is defined but never used.", ["1807", "1808"], "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'cn' is defined but never used.", "'CollegeGrowthResponse' is defined but never used.", ["1809", "1810"], ["1811", "1812"], ["1813", "1814"], ["1815", "1816"], ["1817", "1818"], ["1819", "1820"], ["1821", "1822", "1823", "1824"], ["1825", "1826", "1827", "1828"], "'index' is defined but never used.", ["1829", "1830", "1831", "1832"], ["1833", "1834", "1835", "1836"], "'BarChart3' is defined but never used.", "'FolderArchive' is defined but never used.", "'MessageSquare' is defined but never used.", "'Receipt' is defined but never used.", "'sidebarOpen' is assigned a value but never used.", "'setSidebarOpen' is assigned a value but never used.", "'Bell' is defined but never used.", "'ThemeToggle' is defined but never used.", "'Badge' is defined but never used.", "'notificationCount' is assigned a value but never used.", "'setNotificationCount' is assigned a value but never used.", "'BookOpen' is defined but never used.", "'FileText' is defined but never used.", "'LayoutDashboard' is defined but never used.", "'Users' is defined but never used.", "'handleRouteChangeComplete' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'start' is never reassigned. Use 'const' instead.", {"range": "1837", "text": "1838"}, ["1839", "1840"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchChartData'. Either include it or remove the dependency array.", "ArrayExpression", ["1841"], "'Search' is defined but never used.", "'Filter' is defined but never used.", "'title' is assigned a value but never used.", "'columns' is assigned a value but never used.", "'onRefresh' is defined but never used.", "'onFilter' is defined but never used.", ["1842", "1843"], ["1844", "1845"], ["1846", "1847"], ["1848", "1849"], "'error' is defined but never used.", ["1850", "1851"], "'Eye' is defined but never used.", "'Edit' is defined but never used.", "'formData' is defined but never used.", "'useState' is defined but never used.", "'loadingSubjects' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadChaptersForSubject'. Either include it or remove the dependency array.", ["1852"], "React Hook useEffect has a missing dependency: 'initializeSubjectConfig'. Either include it or remove the dependency array.", ["1853"], "'OptionButton' is defined but never used.", "'Button' is defined but never used.", "'Facebook' is defined but never used.", "'Instagram' is defined but never used.", "'Linkedin' is defined but never used.", "'Pencil' is defined but never used.", "'Twitter' is defined but never used.", "'Image' is defined but never used.", "'handleEdit' is assigned a value but never used.", "'TeacherFormValues' is defined but never used.", "'params' is assigned a value but never used.", "'user' is assigned a value but never used.", "'userRole' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", ["1854", "1855"], ["1856", "1857", "1858", "1859"], ["1860", "1861", "1862", "1863"], ["1864", "1865", "1866", "1867"], ["1868", "1869"], "'Label' is defined but never used.", "'X' is defined but never used.", "'CheckCircle' is defined but never used.", "'PanelLeftIcon' is defined but never used.", "'MathText' is defined but never used.", "'auth' is assigned a value but never used.", "'toastEventTarget' is assigned a value but never used.", "'TOAST_ADD_EVENT' is assigned a value but never used.", "'TOAST_REMOVE_EVENT' is assigned a value but never used.", "'UsageTrendsResponse' is defined but never used.", ["1870", "1871"], "Assignments to the 'yAxisDomain' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "Assignments to the 'yAxisTicks' variable from inside React Hook useEffect will be lost after each render. To preserve the value over time, store it in a useRef Hook and keep the mutable value in the '.current' property. Otherwise, you can move this variable directly inside useEffect.", "CallExpression", ["1872", "1873"], ["1874", "1875"], ["1876", "1877"], ["1878", "1879"], ["1880", "1881"], ["1882", "1883"], ["1884", "1885"], ["1886", "1887"], ["1888", "1889"], ["1890", "1891"], ["1892", "1893"], ["1894", "1895"], "'jsonError' is defined but never used.", "'parseError' is defined but never used.", ["1896", "1897"], "'API_BASE_URL' is assigned a value but never used.", ["1898", "1899"], ["1900", "1901"], ["1902", "1903"], ["1904", "1905"], ["1906", "1907"], ["1908", "1909"], ["1910", "1911"], ["1912", "1913"], ["1914", "1915"], "'api' is defined but never used.", ["1916", "1917"], "'e' is defined but never used.", ["1918", "1919"], ["1920", "1921"], ["1922", "1923"], ["1924", "1925"], ["1926", "1927"], ["1928", "1929"], ["1930", "1931"], ["1932", "1933"], ["1934", "1935"], ["1936", "1937"], ["1938", "1939"], "react-hooks/rules-of-hooks", "React Hook \"useAuth\" is called in function \"setUserRole\" that is neither a React function component nor a custom React Hook function. React component names must start with an uppercase letter. React Hook names must start with the word \"use\".", ["1940", "1941"], ["1942", "1943"], "'MathRenderer' is defined but never used.", ["1944", "1945"], ["1946", "1947"], ["1948", "1949"], ["1950", "1951"], "'displayText' is never reassigned. Use 'const' instead.", {"range": "1952", "text": "1953"}, "'safetyPadding' is assigned a value but never used.", ["1954", "1955"], ["1956", "1957"], ["1958", "1959"], ["1960", "1961"], ["1962", "1963"], ["1964", "1965"], ["1966", "1967"], ["1968", "1969"], ["1970", "1971"], ["1972", "1973"], ["1974", "1975"], ["1976", "1977"], ["1978", "1979"], ["1980", "1981"], ["1982", "1983"], "'processedOpt' is never reassigned. Use 'const' instead.", {"range": "1984", "text": "1985"}, ["1986", "1987"], ["1988", "1989"], ["1990", "1991"], "'useToast' is defined but never used.", "React Hook useEffect has missing dependencies: 'initialSubjects.length', 'initialTopics.length', and 'selectedSubjectId'. Either include them or remove the dependency array.", ["1992"], ["1993", "1994"], ["1995", "1996"], ["1997", "1998"], ["1999", "2000"], ["2001", "2002"], ["2003", "2004"], "'Plus' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadChapters'. Either include it or remove the dependency array.", ["2005"], "React Hook useEffect has a missing dependency: 'loadTopics'. Either include it or remove the dependency array.", ["2006"], ["2007", "2008"], ["2009", "2010"], ["2011", "2012"], ["2013", "2014"], ["2015", "2016"], ["2017", "2018"], ["2019", "2020"], ["2021", "2022"], ["2023", "2024"], ["2025", "2026"], ["2027", "2028"], ["2029", "2030"], "'JSDOM' is defined but never used.", "'fallbackText' is never reassigned. Use 'const' instead.", {"range": "2031", "text": "2032"}, "'expression' is defined but never used.", "'options' is defined but never used.", {"messageId": "2033", "fix": "2034", "desc": "2035"}, {"messageId": "2036", "fix": "2037", "desc": "2038"}, {"messageId": "2033", "fix": "2039", "desc": "2035"}, {"messageId": "2036", "fix": "2040", "desc": "2038"}, {"messageId": "2033", "fix": "2041", "desc": "2035"}, {"messageId": "2036", "fix": "2042", "desc": "2038"}, {"messageId": "2033", "fix": "2043", "desc": "2035"}, {"messageId": "2036", "fix": "2044", "desc": "2038"}, {"messageId": "2033", "fix": "2045", "desc": "2035"}, {"messageId": "2036", "fix": "2046", "desc": "2038"}, {"messageId": "2033", "fix": "2047", "desc": "2035"}, {"messageId": "2036", "fix": "2048", "desc": "2038"}, {"messageId": "2033", "fix": "2049", "desc": "2035"}, {"messageId": "2036", "fix": "2050", "desc": "2038"}, {"messageId": "2033", "fix": "2051", "desc": "2035"}, {"messageId": "2036", "fix": "2052", "desc": "2038"}, {"messageId": "2033", "fix": "2053", "desc": "2035"}, {"messageId": "2036", "fix": "2054", "desc": "2038"}, {"messageId": "2033", "fix": "2055", "desc": "2035"}, {"messageId": "2036", "fix": "2056", "desc": "2038"}, {"messageId": "2033", "fix": "2057", "desc": "2035"}, {"messageId": "2036", "fix": "2058", "desc": "2038"}, {"messageId": "2033", "fix": "2059", "desc": "2035"}, {"messageId": "2036", "fix": "2060", "desc": "2038"}, {"messageId": "2033", "fix": "2061", "desc": "2035"}, {"messageId": "2036", "fix": "2062", "desc": "2038"}, {"messageId": "2033", "fix": "2063", "desc": "2035"}, {"messageId": "2036", "fix": "2064", "desc": "2038"}, {"messageId": "2033", "fix": "2065", "desc": "2035"}, {"messageId": "2036", "fix": "2066", "desc": "2038"}, {"messageId": "2033", "fix": "2067", "desc": "2035"}, {"messageId": "2036", "fix": "2068", "desc": "2038"}, {"messageId": "2033", "fix": "2069", "desc": "2035"}, {"messageId": "2036", "fix": "2070", "desc": "2038"}, {"messageId": "2033", "fix": "2071", "desc": "2035"}, {"messageId": "2036", "fix": "2072", "desc": "2038"}, {"messageId": "2033", "fix": "2073", "desc": "2035"}, {"messageId": "2036", "fix": "2074", "desc": "2038"}, {"messageId": "2075", "data": "2076", "fix": "2077", "desc": "2078"}, {"messageId": "2075", "data": "2079", "fix": "2080", "desc": "2081"}, {"messageId": "2075", "data": "2082", "fix": "2083", "desc": "2084"}, {"messageId": "2075", "data": "2085", "fix": "2086", "desc": "2087"}, {"messageId": "2075", "data": "2088", "fix": "2089", "desc": "2078"}, {"messageId": "2075", "data": "2090", "fix": "2091", "desc": "2081"}, {"messageId": "2075", "data": "2092", "fix": "2093", "desc": "2084"}, {"messageId": "2075", "data": "2094", "fix": "2095", "desc": "2087"}, {"messageId": "2075", "data": "2096", "fix": "2097", "desc": "2098"}, {"messageId": "2075", "data": "2099", "fix": "2100", "desc": "2101"}, {"messageId": "2075", "data": "2102", "fix": "2103", "desc": "2104"}, {"messageId": "2075", "data": "2105", "fix": "2106", "desc": "2107"}, {"messageId": "2075", "data": "2108", "fix": "2109", "desc": "2098"}, {"messageId": "2075", "data": "2110", "fix": "2111", "desc": "2101"}, {"messageId": "2075", "data": "2112", "fix": "2113", "desc": "2104"}, {"messageId": "2075", "data": "2114", "fix": "2115", "desc": "2107"}, {"messageId": "2033", "fix": "2116", "desc": "2035"}, {"messageId": "2036", "fix": "2117", "desc": "2038"}, {"messageId": "2033", "fix": "2118", "desc": "2035"}, {"messageId": "2036", "fix": "2119", "desc": "2038"}, {"messageId": "2075", "data": "2120", "fix": "2121", "desc": "2078"}, {"messageId": "2075", "data": "2122", "fix": "2123", "desc": "2081"}, {"messageId": "2075", "data": "2124", "fix": "2125", "desc": "2084"}, {"messageId": "2075", "data": "2126", "fix": "2127", "desc": "2087"}, {"messageId": "2075", "data": "2128", "fix": "2129", "desc": "2098"}, {"messageId": "2075", "data": "2130", "fix": "2131", "desc": "2101"}, {"messageId": "2075", "data": "2132", "fix": "2133", "desc": "2104"}, {"messageId": "2075", "data": "2134", "fix": "2135", "desc": "2107"}, {"messageId": "2075", "data": "2136", "fix": "2137", "desc": "2098"}, {"messageId": "2075", "data": "2138", "fix": "2139", "desc": "2101"}, {"messageId": "2075", "data": "2140", "fix": "2141", "desc": "2104"}, {"messageId": "2075", "data": "2142", "fix": "2143", "desc": "2107"}, {"messageId": "2033", "fix": "2144", "desc": "2035"}, {"messageId": "2036", "fix": "2145", "desc": "2038"}, {"messageId": "2033", "fix": "2146", "desc": "2035"}, {"messageId": "2036", "fix": "2147", "desc": "2038"}, {"messageId": "2075", "data": "2148", "fix": "2149", "desc": "2098"}, {"messageId": "2075", "data": "2150", "fix": "2151", "desc": "2101"}, {"messageId": "2075", "data": "2152", "fix": "2153", "desc": "2104"}, {"messageId": "2075", "data": "2154", "fix": "2155", "desc": "2107"}, {"messageId": "2075", "data": "2156", "fix": "2157", "desc": "2098"}, {"messageId": "2075", "data": "2158", "fix": "2159", "desc": "2101"}, {"messageId": "2075", "data": "2160", "fix": "2161", "desc": "2104"}, {"messageId": "2075", "data": "2162", "fix": "2163", "desc": "2107"}, {"messageId": "2033", "fix": "2164", "desc": "2035"}, {"messageId": "2036", "fix": "2165", "desc": "2038"}, {"messageId": "2033", "fix": "2166", "desc": "2035"}, {"messageId": "2036", "fix": "2167", "desc": "2038"}, {"messageId": "2033", "fix": "2168", "desc": "2035"}, {"messageId": "2036", "fix": "2169", "desc": "2038"}, {"messageId": "2033", "fix": "2170", "desc": "2035"}, {"messageId": "2036", "fix": "2171", "desc": "2038"}, {"messageId": "2033", "fix": "2172", "desc": "2035"}, {"messageId": "2036", "fix": "2173", "desc": "2038"}, {"messageId": "2033", "fix": "2174", "desc": "2035"}, {"messageId": "2036", "fix": "2175", "desc": "2038"}, {"messageId": "2033", "fix": "2176", "desc": "2035"}, {"messageId": "2036", "fix": "2177", "desc": "2038"}, {"messageId": "2033", "fix": "2178", "desc": "2035"}, {"messageId": "2036", "fix": "2179", "desc": "2038"}, {"messageId": "2033", "fix": "2180", "desc": "2035"}, {"messageId": "2036", "fix": "2181", "desc": "2038"}, {"messageId": "2033", "fix": "2182", "desc": "2035"}, {"messageId": "2036", "fix": "2183", "desc": "2038"}, {"messageId": "2033", "fix": "2184", "desc": "2035"}, {"messageId": "2036", "fix": "2185", "desc": "2038"}, {"messageId": "2033", "fix": "2186", "desc": "2035"}, {"messageId": "2036", "fix": "2187", "desc": "2038"}, {"messageId": "2033", "fix": "2188", "desc": "2035"}, {"messageId": "2036", "fix": "2189", "desc": "2038"}, {"messageId": "2033", "fix": "2190", "desc": "2035"}, {"messageId": "2036", "fix": "2191", "desc": "2038"}, {"messageId": "2033", "fix": "2192", "desc": "2035"}, {"messageId": "2036", "fix": "2193", "desc": "2038"}, {"messageId": "2033", "fix": "2194", "desc": "2035"}, {"messageId": "2036", "fix": "2195", "desc": "2038"}, {"messageId": "2033", "fix": "2196", "desc": "2035"}, {"messageId": "2036", "fix": "2197", "desc": "2038"}, {"messageId": "2033", "fix": "2198", "desc": "2035"}, {"messageId": "2036", "fix": "2199", "desc": "2038"}, {"messageId": "2033", "fix": "2200", "desc": "2035"}, {"messageId": "2036", "fix": "2201", "desc": "2038"}, [11129, 11173], "const finalQuestionData = { ...questionData };", {"messageId": "2033", "fix": "2202", "desc": "2035"}, {"messageId": "2036", "fix": "2203", "desc": "2038"}, {"messageId": "2033", "fix": "2204", "desc": "2035"}, {"messageId": "2036", "fix": "2205", "desc": "2038"}, {"messageId": "2033", "fix": "2206", "desc": "2035"}, {"messageId": "2036", "fix": "2207", "desc": "2038"}, {"messageId": "2033", "fix": "2208", "desc": "2035"}, {"messageId": "2036", "fix": "2209", "desc": "2038"}, {"messageId": "2033", "fix": "2210", "desc": "2035"}, {"messageId": "2036", "fix": "2211", "desc": "2038"}, {"messageId": "2033", "fix": "2212", "desc": "2035"}, {"messageId": "2036", "fix": "2213", "desc": "2038"}, {"messageId": "2033", "fix": "2214", "desc": "2035"}, {"messageId": "2036", "fix": "2215", "desc": "2038"}, {"messageId": "2033", "fix": "2216", "desc": "2035"}, {"messageId": "2036", "fix": "2217", "desc": "2038"}, {"messageId": "2033", "fix": "2218", "desc": "2035"}, {"messageId": "2036", "fix": "2219", "desc": "2038"}, {"messageId": "2033", "fix": "2220", "desc": "2035"}, {"messageId": "2036", "fix": "2221", "desc": "2038"}, [12373, 12417], {"messageId": "2033", "fix": "2222", "desc": "2035"}, {"messageId": "2036", "fix": "2223", "desc": "2038"}, {"messageId": "2033", "fix": "2224", "desc": "2035"}, {"messageId": "2036", "fix": "2225", "desc": "2038"}, {"messageId": "2033", "fix": "2226", "desc": "2035"}, {"messageId": "2036", "fix": "2227", "desc": "2038"}, {"messageId": "2033", "fix": "2228", "desc": "2035"}, {"messageId": "2036", "fix": "2229", "desc": "2038"}, {"messageId": "2033", "fix": "2230", "desc": "2035"}, {"messageId": "2036", "fix": "2231", "desc": "2038"}, {"messageId": "2033", "fix": "2232", "desc": "2035"}, {"messageId": "2036", "fix": "2233", "desc": "2038"}, {"messageId": "2033", "fix": "2234", "desc": "2035"}, {"messageId": "2036", "fix": "2235", "desc": "2038"}, {"messageId": "2033", "fix": "2236", "desc": "2035"}, {"messageId": "2036", "fix": "2237", "desc": "2038"}, {"messageId": "2033", "fix": "2238", "desc": "2035"}, {"messageId": "2036", "fix": "2239", "desc": "2038"}, {"messageId": "2033", "fix": "2240", "desc": "2035"}, {"messageId": "2036", "fix": "2241", "desc": "2038"}, {"messageId": "2033", "fix": "2242", "desc": "2035"}, {"messageId": "2036", "fix": "2243", "desc": "2038"}, {"messageId": "2033", "fix": "2244", "desc": "2035"}, {"messageId": "2036", "fix": "2245", "desc": "2038"}, {"messageId": "2033", "fix": "2246", "desc": "2035"}, {"messageId": "2036", "fix": "2247", "desc": "2038"}, {"messageId": "2033", "fix": "2248", "desc": "2035"}, {"messageId": "2036", "fix": "2249", "desc": "2038"}, {"messageId": "2033", "fix": "2250", "desc": "2035"}, {"messageId": "2036", "fix": "2251", "desc": "2038"}, {"messageId": "2033", "fix": "2252", "desc": "2035"}, {"messageId": "2036", "fix": "2253", "desc": "2038"}, {"messageId": "2033", "fix": "2254", "desc": "2035"}, {"messageId": "2036", "fix": "2255", "desc": "2038"}, {"messageId": "2033", "fix": "2256", "desc": "2035"}, {"messageId": "2036", "fix": "2257", "desc": "2038"}, {"messageId": "2033", "fix": "2258", "desc": "2035"}, {"messageId": "2036", "fix": "2259", "desc": "2038"}, {"messageId": "2033", "fix": "2260", "desc": "2035"}, {"messageId": "2036", "fix": "2261", "desc": "2038"}, {"messageId": "2033", "fix": "2262", "desc": "2035"}, {"messageId": "2036", "fix": "2263", "desc": "2038"}, {"messageId": "2033", "fix": "2264", "desc": "2035"}, {"messageId": "2036", "fix": "2265", "desc": "2038"}, {"messageId": "2033", "fix": "2266", "desc": "2035"}, {"messageId": "2036", "fix": "2267", "desc": "2038"}, {"messageId": "2033", "fix": "2268", "desc": "2035"}, {"messageId": "2036", "fix": "2269", "desc": "2038"}, {"messageId": "2033", "fix": "2270", "desc": "2035"}, {"messageId": "2036", "fix": "2271", "desc": "2038"}, {"messageId": "2033", "fix": "2272", "desc": "2035"}, {"messageId": "2036", "fix": "2273", "desc": "2038"}, {"messageId": "2033", "fix": "2274", "desc": "2035"}, {"messageId": "2036", "fix": "2275", "desc": "2038"}, {"messageId": "2033", "fix": "2276", "desc": "2035"}, {"messageId": "2036", "fix": "2277", "desc": "2038"}, {"messageId": "2033", "fix": "2278", "desc": "2035"}, {"messageId": "2036", "fix": "2279", "desc": "2038"}, {"messageId": "2033", "fix": "2280", "desc": "2035"}, {"messageId": "2036", "fix": "2281", "desc": "2038"}, {"messageId": "2033", "fix": "2282", "desc": "2035"}, {"messageId": "2036", "fix": "2283", "desc": "2038"}, {"messageId": "2033", "fix": "2284", "desc": "2035"}, {"messageId": "2036", "fix": "2285", "desc": "2038"}, {"messageId": "2033", "fix": "2286", "desc": "2035"}, {"messageId": "2036", "fix": "2287", "desc": "2038"}, {"messageId": "2033", "fix": "2288", "desc": "2035"}, {"messageId": "2036", "fix": "2289", "desc": "2038"}, {"messageId": "2033", "fix": "2290", "desc": "2035"}, {"messageId": "2036", "fix": "2291", "desc": "2038"}, {"messageId": "2033", "fix": "2292", "desc": "2035"}, {"messageId": "2036", "fix": "2293", "desc": "2038"}, {"messageId": "2033", "fix": "2294", "desc": "2035"}, {"messageId": "2036", "fix": "2295", "desc": "2038"}, {"messageId": "2033", "fix": "2296", "desc": "2035"}, {"messageId": "2036", "fix": "2297", "desc": "2038"}, {"messageId": "2033", "fix": "2298", "desc": "2035"}, {"messageId": "2036", "fix": "2299", "desc": "2038"}, {"messageId": "2033", "fix": "2300", "desc": "2035"}, {"messageId": "2036", "fix": "2301", "desc": "2038"}, {"messageId": "2075", "data": "2302", "fix": "2303", "desc": "2098"}, {"messageId": "2075", "data": "2304", "fix": "2305", "desc": "2101"}, {"messageId": "2075", "data": "2306", "fix": "2307", "desc": "2104"}, {"messageId": "2075", "data": "2308", "fix": "2309", "desc": "2107"}, {"messageId": "2075", "data": "2310", "fix": "2311", "desc": "2098"}, {"messageId": "2075", "data": "2312", "fix": "2313", "desc": "2101"}, {"messageId": "2075", "data": "2314", "fix": "2315", "desc": "2104"}, {"messageId": "2075", "data": "2316", "fix": "2317", "desc": "2107"}, {"messageId": "2075", "data": "2318", "fix": "2319", "desc": "2078"}, {"messageId": "2075", "data": "2320", "fix": "2321", "desc": "2081"}, {"messageId": "2075", "data": "2322", "fix": "2323", "desc": "2084"}, {"messageId": "2075", "data": "2324", "fix": "2325", "desc": "2087"}, {"messageId": "2075", "data": "2326", "fix": "2327", "desc": "2078"}, {"messageId": "2075", "data": "2328", "fix": "2329", "desc": "2081"}, {"messageId": "2075", "data": "2330", "fix": "2331", "desc": "2084"}, {"messageId": "2075", "data": "2332", "fix": "2333", "desc": "2087"}, [1796, 1822], "const start = new Date(now);", {"messageId": "2033", "fix": "2334", "desc": "2035"}, {"messageId": "2036", "fix": "2335", "desc": "2038"}, {"desc": "2336", "fix": "2337"}, {"messageId": "2033", "fix": "2338", "desc": "2035"}, {"messageId": "2036", "fix": "2339", "desc": "2038"}, {"messageId": "2033", "fix": "2340", "desc": "2035"}, {"messageId": "2036", "fix": "2341", "desc": "2038"}, {"messageId": "2033", "fix": "2342", "desc": "2035"}, {"messageId": "2036", "fix": "2343", "desc": "2038"}, {"messageId": "2033", "fix": "2344", "desc": "2035"}, {"messageId": "2036", "fix": "2345", "desc": "2038"}, {"messageId": "2033", "fix": "2346", "desc": "2035"}, {"messageId": "2036", "fix": "2347", "desc": "2038"}, {"desc": "2348", "fix": "2349"}, {"desc": "2350", "fix": "2351"}, {"messageId": "2033", "fix": "2352", "desc": "2035"}, {"messageId": "2036", "fix": "2353", "desc": "2038"}, {"messageId": "2075", "data": "2354", "fix": "2355", "desc": "2078"}, {"messageId": "2075", "data": "2356", "fix": "2357", "desc": "2081"}, {"messageId": "2075", "data": "2358", "fix": "2359", "desc": "2084"}, {"messageId": "2075", "data": "2360", "fix": "2361", "desc": "2087"}, {"messageId": "2075", "data": "2362", "fix": "2363", "desc": "2078"}, {"messageId": "2075", "data": "2364", "fix": "2365", "desc": "2081"}, {"messageId": "2075", "data": "2366", "fix": "2367", "desc": "2084"}, {"messageId": "2075", "data": "2368", "fix": "2369", "desc": "2087"}, {"messageId": "2075", "data": "2370", "fix": "2371", "desc": "2078"}, {"messageId": "2075", "data": "2372", "fix": "2373", "desc": "2081"}, {"messageId": "2075", "data": "2374", "fix": "2375", "desc": "2084"}, {"messageId": "2075", "data": "2376", "fix": "2377", "desc": "2087"}, {"messageId": "2033", "fix": "2378", "desc": "2035"}, {"messageId": "2036", "fix": "2379", "desc": "2038"}, {"messageId": "2033", "fix": "2380", "desc": "2035"}, {"messageId": "2036", "fix": "2381", "desc": "2038"}, {"messageId": "2033", "fix": "2382", "desc": "2035"}, {"messageId": "2036", "fix": "2383", "desc": "2038"}, {"messageId": "2033", "fix": "2384", "desc": "2035"}, {"messageId": "2036", "fix": "2385", "desc": "2038"}, {"messageId": "2033", "fix": "2386", "desc": "2035"}, {"messageId": "2036", "fix": "2387", "desc": "2038"}, {"messageId": "2033", "fix": "2388", "desc": "2035"}, {"messageId": "2036", "fix": "2389", "desc": "2038"}, {"messageId": "2033", "fix": "2390", "desc": "2035"}, {"messageId": "2036", "fix": "2391", "desc": "2038"}, {"messageId": "2033", "fix": "2392", "desc": "2035"}, {"messageId": "2036", "fix": "2393", "desc": "2038"}, {"messageId": "2033", "fix": "2394", "desc": "2035"}, {"messageId": "2036", "fix": "2395", "desc": "2038"}, {"messageId": "2033", "fix": "2396", "desc": "2035"}, {"messageId": "2036", "fix": "2397", "desc": "2038"}, {"messageId": "2033", "fix": "2398", "desc": "2035"}, {"messageId": "2036", "fix": "2399", "desc": "2038"}, {"messageId": "2033", "fix": "2400", "desc": "2035"}, {"messageId": "2036", "fix": "2401", "desc": "2038"}, {"messageId": "2033", "fix": "2402", "desc": "2035"}, {"messageId": "2036", "fix": "2403", "desc": "2038"}, {"messageId": "2033", "fix": "2404", "desc": "2035"}, {"messageId": "2036", "fix": "2405", "desc": "2038"}, {"messageId": "2033", "fix": "2406", "desc": "2035"}, {"messageId": "2036", "fix": "2407", "desc": "2038"}, {"messageId": "2033", "fix": "2408", "desc": "2035"}, {"messageId": "2036", "fix": "2409", "desc": "2038"}, {"messageId": "2033", "fix": "2410", "desc": "2035"}, {"messageId": "2036", "fix": "2411", "desc": "2038"}, {"messageId": "2033", "fix": "2412", "desc": "2035"}, {"messageId": "2036", "fix": "2413", "desc": "2038"}, {"messageId": "2033", "fix": "2414", "desc": "2035"}, {"messageId": "2036", "fix": "2415", "desc": "2038"}, {"messageId": "2033", "fix": "2416", "desc": "2035"}, {"messageId": "2036", "fix": "2417", "desc": "2038"}, {"messageId": "2033", "fix": "2418", "desc": "2035"}, {"messageId": "2036", "fix": "2419", "desc": "2038"}, {"messageId": "2033", "fix": "2420", "desc": "2035"}, {"messageId": "2036", "fix": "2421", "desc": "2038"}, {"messageId": "2033", "fix": "2422", "desc": "2035"}, {"messageId": "2036", "fix": "2423", "desc": "2038"}, {"messageId": "2033", "fix": "2424", "desc": "2035"}, {"messageId": "2036", "fix": "2425", "desc": "2038"}, {"messageId": "2033", "fix": "2426", "desc": "2035"}, {"messageId": "2036", "fix": "2427", "desc": "2038"}, {"messageId": "2033", "fix": "2428", "desc": "2035"}, {"messageId": "2036", "fix": "2429", "desc": "2038"}, {"messageId": "2033", "fix": "2430", "desc": "2035"}, {"messageId": "2036", "fix": "2431", "desc": "2038"}, {"messageId": "2033", "fix": "2432", "desc": "2035"}, {"messageId": "2036", "fix": "2433", "desc": "2038"}, {"messageId": "2033", "fix": "2434", "desc": "2035"}, {"messageId": "2036", "fix": "2435", "desc": "2038"}, {"messageId": "2033", "fix": "2436", "desc": "2035"}, {"messageId": "2036", "fix": "2437", "desc": "2038"}, {"messageId": "2033", "fix": "2438", "desc": "2035"}, {"messageId": "2036", "fix": "2439", "desc": "2038"}, {"messageId": "2033", "fix": "2440", "desc": "2035"}, {"messageId": "2036", "fix": "2441", "desc": "2038"}, {"messageId": "2033", "fix": "2442", "desc": "2035"}, {"messageId": "2036", "fix": "2443", "desc": "2038"}, {"messageId": "2033", "fix": "2444", "desc": "2035"}, {"messageId": "2036", "fix": "2445", "desc": "2038"}, {"messageId": "2033", "fix": "2446", "desc": "2035"}, {"messageId": "2036", "fix": "2447", "desc": "2038"}, {"messageId": "2033", "fix": "2448", "desc": "2035"}, {"messageId": "2036", "fix": "2449", "desc": "2038"}, {"messageId": "2033", "fix": "2450", "desc": "2035"}, {"messageId": "2036", "fix": "2451", "desc": "2038"}, {"messageId": "2033", "fix": "2452", "desc": "2035"}, {"messageId": "2036", "fix": "2453", "desc": "2038"}, {"messageId": "2033", "fix": "2454", "desc": "2035"}, {"messageId": "2036", "fix": "2455", "desc": "2038"}, {"messageId": "2033", "fix": "2456", "desc": "2035"}, {"messageId": "2036", "fix": "2457", "desc": "2038"}, {"messageId": "2033", "fix": "2458", "desc": "2035"}, {"messageId": "2036", "fix": "2459", "desc": "2038"}, {"messageId": "2033", "fix": "2460", "desc": "2035"}, {"messageId": "2036", "fix": "2461", "desc": "2038"}, [17981, 24318], "const displayText = text\n        // Arrows and implications\n        .replace(/\\\\Rightarrow/g, '⇒')\n        .replace(/\\\\Leftarrow/g, '⇐')\n        .replace(/\\\\Leftrightarrow/g, '⇔')\n        .replace(/\\\\rightarrow/g, '→')\n        .replace(/\\\\leftarrow/g, '←')\n        .replace(/\\\\leftrightarrow/g, '↔')\n        .replace(/\\\\uparrow/g, '↑')\n        .replace(/\\\\downarrow/g, '↓')\n        .replace(/\\\\updownarrow/g, '↕')\n        .replace(/\\\\Uparrow/g, '⇑')\n        .replace(/\\\\Downarrow/g, '⇓')\n        .replace(/\\\\Updownarrow/g, '⇕')\n\n        // Comparison operators\n        .replace(/\\\\geq/g, '≥')\n        .replace(/\\\\leq/g, '≤')\n        .replace(/\\\\neq/g, '≠')\n        .replace(/\\\\approx/g, '≈')\n        .replace(/\\\\equiv/g, '≡')\n        .replace(/\\\\sim/g, '∼')\n        .replace(/\\\\simeq/g, '≃')\n        .replace(/\\\\cong/g, '≅')\n        .replace(/\\\\propto/g, '∝')\n\n        // Binary operators\n        .replace(/\\\\pm/g, '±')\n        .replace(/\\\\mp/g, '∓')\n        .replace(/\\\\times/g, '×')\n        .replace(/\\\\div/g, '÷')\n        .replace(/\\\\cdot/g, '⋅')\n        .replace(/\\\\ast/g, '∗')\n        .replace(/\\\\star/g, '⋆')\n        .replace(/\\\\circ/g, '∘')\n        .replace(/\\\\bullet/g, '•')\n        .replace(/\\\\oplus/g, '⊕')\n        .replace(/\\\\ominus/g, '⊖')\n        .replace(/\\\\otimes/g, '⊗')\n        .replace(/\\\\oslash/g, '⊘')\n\n        // Greek letters (lowercase)\n        .replace(/\\\\alpha/g, 'α')\n        .replace(/\\\\beta/g, 'β')\n        .replace(/\\\\gamma/g, 'γ')\n        .replace(/\\\\delta/g, 'δ')\n        .replace(/\\\\epsilon/g, 'ε')\n        .replace(/\\\\varepsilon/g, 'ε')\n        .replace(/\\\\zeta/g, 'ζ')\n        .replace(/\\\\eta/g, 'η')\n        .replace(/\\\\theta/g, 'θ')\n        .replace(/\\\\vartheta/g, 'ϑ')\n        .replace(/\\\\iota/g, 'ι')\n        .replace(/\\\\kappa/g, 'κ')\n        .replace(/\\\\lambda/g, 'λ')\n        .replace(/\\\\mu/g, 'μ')\n        .replace(/\\\\nu/g, 'ν')\n        .replace(/\\\\xi/g, 'ξ')\n        .replace(/\\\\pi/g, 'π')\n        .replace(/\\\\varpi/g, 'ϖ')\n        .replace(/\\\\rho/g, 'ρ')\n        .replace(/\\\\varrho/g, 'ϱ')\n        .replace(/\\\\sigma/g, 'σ')\n        .replace(/\\\\varsigma/g, 'ς')\n        .replace(/\\\\tau/g, 'τ')\n        .replace(/\\\\upsilon/g, 'υ')\n        .replace(/\\\\phi/g, 'φ')\n        .replace(/\\\\varphi/g, 'φ')\n        .replace(/\\\\chi/g, 'χ')\n        .replace(/\\\\psi/g, 'ψ')\n        .replace(/\\\\omega/g, 'ω')\n\n        // Greek letters (uppercase)\n        .replace(/\\\\Gamma/g, 'Γ')\n        .replace(/\\\\Delta/g, 'Δ')\n        .replace(/\\\\Theta/g, 'Θ')\n        .replace(/\\\\Lambda/g, 'Λ')\n        .replace(/\\\\Xi/g, 'Ξ')\n        .replace(/\\\\Pi/g, 'Π')\n        .replace(/\\\\Sigma/g, 'Σ')\n        .replace(/\\\\Upsilon/g, 'Υ')\n        .replace(/\\\\Phi/g, 'Φ')\n        .replace(/\\\\Psi/g, 'Ψ')\n        .replace(/\\\\Omega/g, 'Ω')\n\n        // Large operators\n        .replace(/\\\\sum/g, '∑')\n        .replace(/\\\\prod/g, '∏')\n        .replace(/\\\\coprod/g, '∐')\n        .replace(/\\\\int/g, '∫')\n        .replace(/\\\\iint/g, '∬')\n        .replace(/\\\\iiint/g, '∭')\n        .replace(/\\\\oint/g, '∮')\n        .replace(/\\\\bigcup/g, '⋃')\n        .replace(/\\\\bigcap/g, '⋂')\n        .replace(/\\\\bigoplus/g, '⨁')\n        .replace(/\\\\bigotimes/g, '⨂')\n\n        // Set theory and logic\n        .replace(/\\\\in/g, '∈')\n        .replace(/\\\\notin/g, '∉')\n        .replace(/\\\\ni/g, '∋')\n        .replace(/\\\\subset/g, '⊂')\n        .replace(/\\\\supset/g, '⊃')\n        .replace(/\\\\subseteq/g, '⊆')\n        .replace(/\\\\supseteq/g, '⊇')\n        .replace(/\\\\cup/g, '∪')\n        .replace(/\\\\cap/g, '∩')\n        .replace(/\\\\setminus/g, '∖')\n        .replace(/\\\\forall/g, '∀')\n        .replace(/\\\\exists/g, '∃')\n        .replace(/\\\\nexists/g, '∄')\n        .replace(/\\\\emptyset/g, '∅')\n        .replace(/\\\\varnothing/g, '∅')\n\n        // Special symbols\n        .replace(/\\\\infty/g, '∞')\n        .replace(/\\\\partial/g, '∂')\n        .replace(/\\\\nabla/g, '∇')\n        .replace(/\\\\angle/g, '∠')\n        .replace(/\\\\triangle/g, '△')\n        .replace(/\\\\square/g, '□')\n        .replace(/\\\\diamond/g, '◊')\n        .replace(/\\\\clubsuit/g, '♣')\n        .replace(/\\\\diamondsuit/g, '♦')\n        .replace(/\\\\heartsuit/g, '♥')\n        .replace(/\\\\spadesuit/g, '♠')\n\n        // Number sets\n        .replace(/\\\\mathbb{N}/g, 'ℕ')\n        .replace(/\\\\mathbb{Z}/g, 'ℤ')\n        .replace(/\\\\mathbb{Q}/g, 'ℚ')\n        .replace(/\\\\mathbb{R}/g, 'ℝ')\n        .replace(/\\\\mathbb{C}/g, 'ℂ')\n        .replace(/\\\\mathbb{P}/g, 'ℙ')\n\n        // Functions and operations\n        .replace(/\\\\sqrt{([^}]+)}/g, '√($1)')\n        .replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)')\n        .replace(/\\\\binom{([^}]+)}{([^}]+)}/g, 'C($1,$2)')\n        .replace(/\\\\choose/g, 'C')\n\n        // Delimiters - remove \\left and \\right commands\n        .replace(/\\\\left\\(/g, '(')\n        .replace(/\\\\right\\)/g, ')')\n        .replace(/\\\\left\\[/g, '[')\n        .replace(/\\\\right\\]/g, ']')\n        .replace(/\\\\left\\{/g, '{')\n        .replace(/\\\\right\\}/g, '}')\n        .replace(/\\\\left\\|/g, '|')\n        .replace(/\\\\right\\|/g, '|')\n        .replace(/\\\\left</g, '⟨')\n        .replace(/\\\\right>/g, '⟩')\n        .replace(/\\\\left/g, '') // Remove any remaining \\left\n        .replace(/\\\\right/g, '') // Remove any remaining \\right\n\n        // Dots and ellipsis\n        .replace(/\\\\ldots/g, '...')\n        .replace(/\\\\cdots/g, '⋯')\n        .replace(/\\\\vdots/g, '⋮')\n        .replace(/\\\\ddots/g, '⋱')\n\n        // Superscripts and subscripts\n        .replace(/\\^{([^}]+)}/g, '^($1)')\n        .replace(/_{([^}]+)}/g, '_($1)')\n        .replace(/\\^(\\w)/g, '^$1')\n        .replace(/_(\\w)/g, '_$1')\n\n        // Text formatting\n        .replace(/\\\\mathrm{([^}]+)}/g, '$1')\n        .replace(/\\\\mathbf{([^}]+)}/g, '$1')\n        .replace(/\\\\mathit{([^}]+)}/g, '$1')\n        .replace(/\\\\mathcal{([^}]+)}/g, '$1')\n        .replace(/\\\\text{([^}]+)}/g, '$1')\n        .replace(/\\\\textbf{([^}]+)}/g, '$1')\n        .replace(/\\\\textit{([^}]+)}/g, '$1')\n\n        // Clean up any remaining LaTeX commands that might cause issues\n        .replace(/\\\\[a-zA-Z]+\\{([^}]*)\\}/g, '$1') // Remove unknown commands with braces\n        .replace(/\\\\[a-zA-Z]+/g, '') // Remove unknown commands without braces\n\n        // Fix spacing issues - normalize multiple spaces\n        .replace(/\\s+/g, ' ')\n        .trim()\n\n        // Remove delimiters\n        .replace(/\\$([^$]+)\\$/g, '$1') // Remove $ delimiters\n        .replace(/\\$\\$([^$]+)\\$\\$/g, '$1');", {"messageId": "2033", "fix": "2462", "desc": "2035"}, {"messageId": "2036", "fix": "2463", "desc": "2038"}, {"messageId": "2033", "fix": "2464", "desc": "2035"}, {"messageId": "2036", "fix": "2465", "desc": "2038"}, {"messageId": "2033", "fix": "2466", "desc": "2035"}, {"messageId": "2036", "fix": "2467", "desc": "2038"}, {"messageId": "2033", "fix": "2468", "desc": "2035"}, {"messageId": "2036", "fix": "2469", "desc": "2038"}, {"messageId": "2033", "fix": "2470", "desc": "2035"}, {"messageId": "2036", "fix": "2471", "desc": "2038"}, {"messageId": "2033", "fix": "2472", "desc": "2035"}, {"messageId": "2036", "fix": "2473", "desc": "2038"}, {"messageId": "2033", "fix": "2474", "desc": "2035"}, {"messageId": "2036", "fix": "2475", "desc": "2038"}, {"messageId": "2033", "fix": "2476", "desc": "2035"}, {"messageId": "2036", "fix": "2477", "desc": "2038"}, {"messageId": "2033", "fix": "2478", "desc": "2035"}, {"messageId": "2036", "fix": "2479", "desc": "2038"}, {"messageId": "2033", "fix": "2480", "desc": "2035"}, {"messageId": "2036", "fix": "2481", "desc": "2038"}, {"messageId": "2033", "fix": "2482", "desc": "2035"}, {"messageId": "2036", "fix": "2483", "desc": "2038"}, {"messageId": "2033", "fix": "2484", "desc": "2035"}, {"messageId": "2036", "fix": "2485", "desc": "2038"}, {"messageId": "2033", "fix": "2486", "desc": "2035"}, {"messageId": "2036", "fix": "2487", "desc": "2038"}, {"messageId": "2033", "fix": "2488", "desc": "2035"}, {"messageId": "2036", "fix": "2489", "desc": "2038"}, {"messageId": "2033", "fix": "2490", "desc": "2035"}, {"messageId": "2036", "fix": "2491", "desc": "2038"}, [16353, 16395], "const processedOpt = processTextForPDF(opt);", {"messageId": "2033", "fix": "2492", "desc": "2035"}, {"messageId": "2036", "fix": "2493", "desc": "2038"}, {"messageId": "2033", "fix": "2494", "desc": "2035"}, {"messageId": "2036", "fix": "2495", "desc": "2038"}, {"messageId": "2033", "fix": "2496", "desc": "2035"}, {"messageId": "2036", "fix": "2497", "desc": "2038"}, {"desc": "2498", "fix": "2499"}, {"messageId": "2033", "fix": "2500", "desc": "2035"}, {"messageId": "2036", "fix": "2501", "desc": "2038"}, {"messageId": "2033", "fix": "2502", "desc": "2035"}, {"messageId": "2036", "fix": "2503", "desc": "2038"}, {"messageId": "2033", "fix": "2504", "desc": "2035"}, {"messageId": "2036", "fix": "2505", "desc": "2038"}, {"messageId": "2033", "fix": "2506", "desc": "2035"}, {"messageId": "2036", "fix": "2507", "desc": "2038"}, {"messageId": "2033", "fix": "2508", "desc": "2035"}, {"messageId": "2036", "fix": "2509", "desc": "2038"}, {"messageId": "2033", "fix": "2510", "desc": "2035"}, {"messageId": "2036", "fix": "2511", "desc": "2038"}, {"desc": "2512", "fix": "2513"}, {"desc": "2514", "fix": "2515"}, {"messageId": "2033", "fix": "2516", "desc": "2035"}, {"messageId": "2036", "fix": "2517", "desc": "2038"}, {"messageId": "2033", "fix": "2518", "desc": "2035"}, {"messageId": "2036", "fix": "2519", "desc": "2038"}, {"messageId": "2033", "fix": "2520", "desc": "2035"}, {"messageId": "2036", "fix": "2521", "desc": "2038"}, {"messageId": "2033", "fix": "2522", "desc": "2035"}, {"messageId": "2036", "fix": "2523", "desc": "2038"}, {"messageId": "2033", "fix": "2524", "desc": "2035"}, {"messageId": "2036", "fix": "2525", "desc": "2038"}, {"messageId": "2033", "fix": "2526", "desc": "2035"}, {"messageId": "2036", "fix": "2527", "desc": "2038"}, {"messageId": "2033", "fix": "2528", "desc": "2035"}, {"messageId": "2036", "fix": "2529", "desc": "2038"}, {"messageId": "2033", "fix": "2530", "desc": "2035"}, {"messageId": "2036", "fix": "2531", "desc": "2038"}, {"messageId": "2033", "fix": "2532", "desc": "2035"}, {"messageId": "2036", "fix": "2533", "desc": "2038"}, {"messageId": "2033", "fix": "2534", "desc": "2035"}, {"messageId": "2036", "fix": "2535", "desc": "2038"}, {"messageId": "2033", "fix": "2536", "desc": "2035"}, {"messageId": "2036", "fix": "2537", "desc": "2038"}, {"messageId": "2033", "fix": "2538", "desc": "2035"}, {"messageId": "2036", "fix": "2539", "desc": "2038"}, [2785, 5908], "const fallbackText = expression\n      // Remove delimiters\n      .replace(/^\\$+/, '')\n      .replace(/\\$+$/, '')\n      \n      // Arrows and implications\n      .replace(/\\\\Rightarrow/g, '⇒')\n      .replace(/\\\\Leftarrow/g, '⇐')\n      .replace(/\\\\Leftrightarrow/g, '⇔')\n      .replace(/\\\\rightarrow/g, '→')\n      .replace(/\\\\leftarrow/g, '←')\n      .replace(/\\\\leftrightarrow/g, '↔')\n      \n      // Comparison operators\n      .replace(/\\\\geq/g, '≥')\n      .replace(/\\\\leq/g, '≤')\n      .replace(/\\\\neq/g, '≠')\n      .replace(/\\\\approx/g, '≈')\n      .replace(/\\\\equiv/g, '≡')\n      .replace(/\\\\sim/g, '∼')\n      \n      // Binary operators\n      .replace(/\\\\pm/g, '±')\n      .replace(/\\\\mp/g, '∓')\n      .replace(/\\\\times/g, '×')\n      .replace(/\\\\div/g, '÷')\n      .replace(/\\\\cdot/g, '⋅')\n      \n      // Greek letters (common ones)\n      .replace(/\\\\alpha/g, 'α')\n      .replace(/\\\\beta/g, 'β')\n      .replace(/\\\\gamma/g, 'γ')\n      .replace(/\\\\delta/g, 'δ')\n      .replace(/\\\\epsilon/g, 'ε')\n      .replace(/\\\\theta/g, 'θ')\n      .replace(/\\\\lambda/g, 'λ')\n      .replace(/\\\\mu/g, 'μ')\n      .replace(/\\\\pi/g, 'π')\n      .replace(/\\\\sigma/g, 'σ')\n      .replace(/\\\\phi/g, 'φ')\n      .replace(/\\\\omega/g, 'ω')\n      \n      // Large operators\n      .replace(/\\\\sum/g, '∑')\n      .replace(/\\\\prod/g, '∏')\n      .replace(/\\\\int/g, '∫')\n      \n      // Set theory\n      .replace(/\\\\in/g, '∈')\n      .replace(/\\\\notin/g, '∉')\n      .replace(/\\\\subset/g, '⊂')\n      .replace(/\\\\supset/g, '⊃')\n      .replace(/\\\\cup/g, '∪')\n      .replace(/\\\\cap/g, '∩')\n      .replace(/\\\\forall/g, '∀')\n      .replace(/\\\\exists/g, '∃')\n      .replace(/\\\\emptyset/g, '∅')\n      \n      // Special symbols\n      .replace(/\\\\infty/g, '∞')\n      .replace(/\\\\partial/g, '∂')\n      .replace(/\\\\nabla/g, '∇')\n      \n      // Number sets\n      .replace(/\\\\mathbb{N}/g, 'ℕ')\n      .replace(/\\\\mathbb{Z}/g, 'ℤ')\n      .replace(/\\\\mathbb{Q}/g, 'ℚ')\n      .replace(/\\\\mathbb{R}/g, 'ℝ')\n      .replace(/\\\\mathbb{C}/g, 'ℂ')\n      \n      // Functions and operations\n      .replace(/\\\\sqrt{([^}]+)}/g, '√($1)')\n      .replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)')\n      \n      // Delimiters\n      .replace(/\\\\left\\(/g, '(')\n      .replace(/\\\\right\\)/g, ')')\n      .replace(/\\\\left\\[/g, '[')\n      .replace(/\\\\right\\]/g, ']')\n      .replace(/\\\\left\\{/g, '{')\n      .replace(/\\\\right\\}/g, '}')\n      .replace(/\\\\left\\|/g, '|')\n      .replace(/\\\\right\\|/g, '|')\n      .replace(/\\\\left/g, '')\n      .replace(/\\\\right/g, '')\n      \n      // Dots\n      .replace(/\\\\ldots/g, '...')\n      .replace(/\\\\cdots/g, '⋯')\n      \n      // Superscripts and subscripts\n      .replace(/\\^{([^}]+)}/g, '^($1)')\n      .replace(/_{([^}]+)}/g, '_($1)')\n      .replace(/\\^(\\w)/g, '^$1')\n      .replace(/_(\\w)/g, '_$1')\n      \n      // Text formatting\n      .replace(/\\\\mathrm{([^}]+)}/g, '$1')\n      .replace(/\\\\mathbf{([^}]+)}/g, '$1')\n      .replace(/\\\\text{([^}]+)}/g, '$1')\n      \n      // Clean up remaining LaTeX commands\n      .replace(/\\\\[a-zA-Z]+\\{([^}]*)\\}/g, '$1')\n      .replace(/\\\\[a-zA-Z]+/g, '')\n      \n      // Normalize spaces\n      .replace(/\\s+/g, ' ')\n      .trim();", "suggestUnknown", {"range": "2540", "text": "2541"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2542", "text": "2543"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2544", "text": "2541"}, {"range": "2545", "text": "2543"}, {"range": "2546", "text": "2541"}, {"range": "2547", "text": "2543"}, {"range": "2548", "text": "2541"}, {"range": "2549", "text": "2543"}, {"range": "2550", "text": "2541"}, {"range": "2551", "text": "2543"}, {"range": "2552", "text": "2541"}, {"range": "2553", "text": "2543"}, {"range": "2554", "text": "2541"}, {"range": "2555", "text": "2543"}, {"range": "2556", "text": "2541"}, {"range": "2557", "text": "2543"}, {"range": "2558", "text": "2541"}, {"range": "2559", "text": "2543"}, {"range": "2560", "text": "2541"}, {"range": "2561", "text": "2543"}, {"range": "2562", "text": "2541"}, {"range": "2563", "text": "2543"}, {"range": "2564", "text": "2541"}, {"range": "2565", "text": "2543"}, {"range": "2566", "text": "2541"}, {"range": "2567", "text": "2543"}, {"range": "2568", "text": "2541"}, {"range": "2569", "text": "2543"}, {"range": "2570", "text": "2541"}, {"range": "2571", "text": "2543"}, {"range": "2572", "text": "2541"}, {"range": "2573", "text": "2543"}, {"range": "2574", "text": "2541"}, {"range": "2575", "text": "2543"}, {"range": "2576", "text": "2541"}, {"range": "2577", "text": "2543"}, {"range": "2578", "text": "2541"}, {"range": "2579", "text": "2543"}, "replaceWithAlt", {"alt": "2580"}, {"range": "2581", "text": "2582"}, "Replace with `&apos;`.", {"alt": "2583"}, {"range": "2584", "text": "2585"}, "Replace with `&lsquo;`.", {"alt": "2586"}, {"range": "2587", "text": "2588"}, "Replace with `&#39;`.", {"alt": "2589"}, {"range": "2590", "text": "2591"}, "Replace with `&rsquo;`.", {"alt": "2580"}, {"range": "2592", "text": "2593"}, {"alt": "2583"}, {"range": "2594", "text": "2595"}, {"alt": "2586"}, {"range": "2596", "text": "2597"}, {"alt": "2589"}, {"range": "2598", "text": "2599"}, {"alt": "2600"}, {"range": "2601", "text": "2602"}, "Replace with `&quot;`.", {"alt": "2603"}, {"range": "2604", "text": "2605"}, "Replace with `&ldquo;`.", {"alt": "2606"}, {"range": "2607", "text": "2608"}, "Replace with `&#34;`.", {"alt": "2609"}, {"range": "2610", "text": "2611"}, "Replace with `&rdquo;`.", {"alt": "2600"}, {"range": "2612", "text": "2613"}, {"alt": "2603"}, {"range": "2614", "text": "2615"}, {"alt": "2606"}, {"range": "2616", "text": "2617"}, {"alt": "2609"}, {"range": "2618", "text": "2619"}, {"range": "2620", "text": "2541"}, {"range": "2621", "text": "2543"}, {"range": "2622", "text": "2541"}, {"range": "2623", "text": "2543"}, {"alt": "2580"}, {"range": "2624", "text": "2625"}, {"alt": "2583"}, {"range": "2626", "text": "2627"}, {"alt": "2586"}, {"range": "2628", "text": "2629"}, {"alt": "2589"}, {"range": "2630", "text": "2631"}, {"alt": "2600"}, {"range": "2632", "text": "2602"}, {"alt": "2603"}, {"range": "2633", "text": "2605"}, {"alt": "2606"}, {"range": "2634", "text": "2608"}, {"alt": "2609"}, {"range": "2635", "text": "2611"}, {"alt": "2600"}, {"range": "2636", "text": "2613"}, {"alt": "2603"}, {"range": "2637", "text": "2615"}, {"alt": "2606"}, {"range": "2638", "text": "2617"}, {"alt": "2609"}, {"range": "2639", "text": "2619"}, {"range": "2640", "text": "2541"}, {"range": "2641", "text": "2543"}, {"range": "2642", "text": "2541"}, {"range": "2643", "text": "2543"}, {"alt": "2600"}, {"range": "2644", "text": "2602"}, {"alt": "2603"}, {"range": "2645", "text": "2605"}, {"alt": "2606"}, {"range": "2646", "text": "2608"}, {"alt": "2609"}, {"range": "2647", "text": "2611"}, {"alt": "2600"}, {"range": "2648", "text": "2613"}, {"alt": "2603"}, {"range": "2649", "text": "2615"}, {"alt": "2606"}, {"range": "2650", "text": "2617"}, {"alt": "2609"}, {"range": "2651", "text": "2619"}, {"range": "2652", "text": "2541"}, {"range": "2653", "text": "2543"}, {"range": "2654", "text": "2541"}, {"range": "2655", "text": "2543"}, {"range": "2656", "text": "2541"}, {"range": "2657", "text": "2543"}, {"range": "2658", "text": "2541"}, {"range": "2659", "text": "2543"}, {"range": "2660", "text": "2541"}, {"range": "2661", "text": "2543"}, {"range": "2662", "text": "2541"}, {"range": "2663", "text": "2543"}, {"range": "2664", "text": "2541"}, {"range": "2665", "text": "2543"}, {"range": "2666", "text": "2541"}, {"range": "2667", "text": "2543"}, {"range": "2668", "text": "2541"}, {"range": "2669", "text": "2543"}, {"range": "2670", "text": "2541"}, {"range": "2671", "text": "2543"}, {"range": "2672", "text": "2541"}, {"range": "2673", "text": "2543"}, {"range": "2674", "text": "2541"}, {"range": "2675", "text": "2543"}, {"range": "2676", "text": "2541"}, {"range": "2677", "text": "2543"}, {"range": "2678", "text": "2541"}, {"range": "2679", "text": "2543"}, {"range": "2680", "text": "2541"}, {"range": "2681", "text": "2543"}, {"range": "2682", "text": "2541"}, {"range": "2683", "text": "2543"}, {"range": "2684", "text": "2541"}, {"range": "2685", "text": "2543"}, {"range": "2686", "text": "2541"}, {"range": "2687", "text": "2543"}, {"range": "2688", "text": "2541"}, {"range": "2689", "text": "2543"}, {"range": "2690", "text": "2541"}, {"range": "2691", "text": "2543"}, {"range": "2692", "text": "2541"}, {"range": "2693", "text": "2543"}, {"range": "2694", "text": "2541"}, {"range": "2695", "text": "2543"}, {"range": "2696", "text": "2541"}, {"range": "2697", "text": "2543"}, {"range": "2698", "text": "2541"}, {"range": "2699", "text": "2543"}, {"range": "2700", "text": "2541"}, {"range": "2701", "text": "2543"}, {"range": "2702", "text": "2541"}, {"range": "2703", "text": "2543"}, {"range": "2704", "text": "2541"}, {"range": "2705", "text": "2543"}, {"range": "2706", "text": "2541"}, {"range": "2707", "text": "2543"}, {"range": "2708", "text": "2541"}, {"range": "2709", "text": "2543"}, {"range": "2710", "text": "2541"}, {"range": "2711", "text": "2543"}, {"range": "2712", "text": "2541"}, {"range": "2713", "text": "2543"}, {"range": "2714", "text": "2541"}, {"range": "2715", "text": "2543"}, {"range": "2716", "text": "2541"}, {"range": "2717", "text": "2543"}, {"range": "2718", "text": "2541"}, {"range": "2719", "text": "2543"}, {"range": "2720", "text": "2541"}, {"range": "2721", "text": "2543"}, {"range": "2722", "text": "2541"}, {"range": "2723", "text": "2543"}, {"range": "2724", "text": "2541"}, {"range": "2725", "text": "2543"}, {"range": "2726", "text": "2541"}, {"range": "2727", "text": "2543"}, {"range": "2728", "text": "2541"}, {"range": "2729", "text": "2543"}, {"range": "2730", "text": "2541"}, {"range": "2731", "text": "2543"}, {"range": "2732", "text": "2541"}, {"range": "2733", "text": "2543"}, {"range": "2734", "text": "2541"}, {"range": "2735", "text": "2543"}, {"range": "2736", "text": "2541"}, {"range": "2737", "text": "2543"}, {"range": "2738", "text": "2541"}, {"range": "2739", "text": "2543"}, {"range": "2740", "text": "2541"}, {"range": "2741", "text": "2543"}, {"range": "2742", "text": "2541"}, {"range": "2743", "text": "2543"}, {"range": "2744", "text": "2541"}, {"range": "2745", "text": "2543"}, {"range": "2746", "text": "2541"}, {"range": "2747", "text": "2543"}, {"range": "2748", "text": "2541"}, {"range": "2749", "text": "2543"}, {"range": "2750", "text": "2541"}, {"range": "2751", "text": "2543"}, {"range": "2752", "text": "2541"}, {"range": "2753", "text": "2543"}, {"range": "2754", "text": "2541"}, {"range": "2755", "text": "2543"}, {"range": "2756", "text": "2541"}, {"range": "2757", "text": "2543"}, {"range": "2758", "text": "2541"}, {"range": "2759", "text": "2543"}, {"range": "2760", "text": "2541"}, {"range": "2761", "text": "2543"}, {"range": "2762", "text": "2541"}, {"range": "2763", "text": "2543"}, {"range": "2764", "text": "2541"}, {"range": "2765", "text": "2543"}, {"range": "2766", "text": "2541"}, {"range": "2767", "text": "2543"}, {"range": "2768", "text": "2541"}, {"range": "2769", "text": "2543"}, {"range": "2770", "text": "2541"}, {"range": "2771", "text": "2543"}, {"range": "2772", "text": "2541"}, {"range": "2773", "text": "2543"}, {"range": "2774", "text": "2541"}, {"range": "2775", "text": "2543"}, {"range": "2776", "text": "2541"}, {"range": "2777", "text": "2543"}, {"range": "2778", "text": "2541"}, {"range": "2779", "text": "2543"}, {"range": "2780", "text": "2541"}, {"range": "2781", "text": "2543"}, {"range": "2782", "text": "2541"}, {"range": "2783", "text": "2543"}, {"range": "2784", "text": "2541"}, {"range": "2785", "text": "2543"}, {"range": "2786", "text": "2541"}, {"range": "2787", "text": "2543"}, {"range": "2788", "text": "2541"}, {"range": "2789", "text": "2543"}, {"alt": "2600"}, {"range": "2790", "text": "2791"}, {"alt": "2603"}, {"range": "2792", "text": "2793"}, {"alt": "2606"}, {"range": "2794", "text": "2795"}, {"alt": "2609"}, {"range": "2796", "text": "2797"}, {"alt": "2600"}, {"range": "2798", "text": "2799"}, {"alt": "2603"}, {"range": "2800", "text": "2801"}, {"alt": "2606"}, {"range": "2802", "text": "2803"}, {"alt": "2609"}, {"range": "2804", "text": "2805"}, {"alt": "2580"}, {"range": "2806", "text": "2807"}, {"alt": "2583"}, {"range": "2808", "text": "2809"}, {"alt": "2586"}, {"range": "2810", "text": "2811"}, {"alt": "2589"}, {"range": "2812", "text": "2813"}, {"alt": "2580"}, {"range": "2814", "text": "2815"}, {"alt": "2583"}, {"range": "2816", "text": "2817"}, {"alt": "2586"}, {"range": "2818", "text": "2819"}, {"alt": "2589"}, {"range": "2820", "text": "2821"}, {"range": "2822", "text": "2541"}, {"range": "2823", "text": "2543"}, "Update the dependencies array to be: [collegeId, fetchChartData, timeRange]", {"range": "2824", "text": "2825"}, {"range": "2826", "text": "2541"}, {"range": "2827", "text": "2543"}, {"range": "2828", "text": "2541"}, {"range": "2829", "text": "2543"}, {"range": "2830", "text": "2541"}, {"range": "2831", "text": "2543"}, {"range": "2832", "text": "2541"}, {"range": "2833", "text": "2543"}, {"range": "2834", "text": "2541"}, {"range": "2835", "text": "2543"}, "Update the dependencies array to be: [activeTab, loadChaptersForSubject, subjects]", {"range": "2836", "text": "2837"}, "Update the dependencies array to be: [formData.subjects, formData.subjectConfigs, updateFormData, initializeSubjectConfig]", {"range": "2838", "text": "2839"}, {"range": "2840", "text": "2541"}, {"range": "2841", "text": "2543"}, {"alt": "2580"}, {"range": "2842", "text": "2843"}, {"alt": "2583"}, {"range": "2844", "text": "2845"}, {"alt": "2586"}, {"range": "2846", "text": "2847"}, {"alt": "2589"}, {"range": "2848", "text": "2849"}, {"alt": "2580"}, {"range": "2850", "text": "2851"}, {"alt": "2583"}, {"range": "2852", "text": "2853"}, {"alt": "2586"}, {"range": "2854", "text": "2855"}, {"alt": "2589"}, {"range": "2856", "text": "2857"}, {"alt": "2580"}, {"range": "2858", "text": "2859"}, {"alt": "2583"}, {"range": "2860", "text": "2861"}, {"alt": "2586"}, {"range": "2862", "text": "2863"}, {"alt": "2589"}, {"range": "2864", "text": "2865"}, {"range": "2866", "text": "2541"}, {"range": "2867", "text": "2543"}, {"range": "2868", "text": "2541"}, {"range": "2869", "text": "2543"}, {"range": "2870", "text": "2541"}, {"range": "2871", "text": "2543"}, {"range": "2872", "text": "2541"}, {"range": "2873", "text": "2543"}, {"range": "2874", "text": "2541"}, {"range": "2875", "text": "2543"}, {"range": "2876", "text": "2541"}, {"range": "2877", "text": "2543"}, {"range": "2878", "text": "2541"}, {"range": "2879", "text": "2543"}, {"range": "2880", "text": "2541"}, {"range": "2881", "text": "2543"}, {"range": "2882", "text": "2541"}, {"range": "2883", "text": "2543"}, {"range": "2884", "text": "2541"}, {"range": "2885", "text": "2543"}, {"range": "2886", "text": "2541"}, {"range": "2887", "text": "2543"}, {"range": "2888", "text": "2541"}, {"range": "2889", "text": "2543"}, {"range": "2890", "text": "2541"}, {"range": "2891", "text": "2543"}, {"range": "2892", "text": "2541"}, {"range": "2893", "text": "2543"}, {"range": "2894", "text": "2541"}, {"range": "2895", "text": "2543"}, {"range": "2896", "text": "2541"}, {"range": "2897", "text": "2543"}, {"range": "2898", "text": "2541"}, {"range": "2899", "text": "2543"}, {"range": "2900", "text": "2541"}, {"range": "2901", "text": "2543"}, {"range": "2902", "text": "2541"}, {"range": "2903", "text": "2543"}, {"range": "2904", "text": "2541"}, {"range": "2905", "text": "2543"}, {"range": "2906", "text": "2541"}, {"range": "2907", "text": "2543"}, {"range": "2908", "text": "2541"}, {"range": "2909", "text": "2543"}, {"range": "2910", "text": "2541"}, {"range": "2911", "text": "2543"}, {"range": "2912", "text": "2541"}, {"range": "2913", "text": "2543"}, {"range": "2914", "text": "2541"}, {"range": "2915", "text": "2543"}, {"range": "2916", "text": "2541"}, {"range": "2917", "text": "2543"}, {"range": "2918", "text": "2541"}, {"range": "2919", "text": "2543"}, {"range": "2920", "text": "2541"}, {"range": "2921", "text": "2543"}, {"range": "2922", "text": "2541"}, {"range": "2923", "text": "2543"}, {"range": "2924", "text": "2541"}, {"range": "2925", "text": "2543"}, {"range": "2926", "text": "2541"}, {"range": "2927", "text": "2543"}, {"range": "2928", "text": "2541"}, {"range": "2929", "text": "2543"}, {"range": "2930", "text": "2541"}, {"range": "2931", "text": "2543"}, {"range": "2932", "text": "2541"}, {"range": "2933", "text": "2543"}, {"range": "2934", "text": "2541"}, {"range": "2935", "text": "2543"}, {"range": "2936", "text": "2541"}, {"range": "2937", "text": "2543"}, {"range": "2938", "text": "2541"}, {"range": "2939", "text": "2543"}, {"range": "2940", "text": "2541"}, {"range": "2941", "text": "2543"}, {"range": "2942", "text": "2541"}, {"range": "2943", "text": "2543"}, {"range": "2944", "text": "2541"}, {"range": "2945", "text": "2543"}, {"range": "2946", "text": "2541"}, {"range": "2947", "text": "2543"}, {"range": "2948", "text": "2541"}, {"range": "2949", "text": "2543"}, {"range": "2950", "text": "2541"}, {"range": "2951", "text": "2543"}, {"range": "2952", "text": "2541"}, {"range": "2953", "text": "2543"}, {"range": "2954", "text": "2541"}, {"range": "2955", "text": "2543"}, {"range": "2956", "text": "2541"}, {"range": "2957", "text": "2543"}, {"range": "2958", "text": "2541"}, {"range": "2959", "text": "2543"}, {"range": "2960", "text": "2541"}, {"range": "2961", "text": "2543"}, {"range": "2962", "text": "2541"}, {"range": "2963", "text": "2543"}, {"range": "2964", "text": "2541"}, {"range": "2965", "text": "2543"}, {"range": "2966", "text": "2541"}, {"range": "2967", "text": "2543"}, {"range": "2968", "text": "2541"}, {"range": "2969", "text": "2543"}, {"range": "2970", "text": "2541"}, {"range": "2971", "text": "2543"}, {"range": "2972", "text": "2541"}, {"range": "2973", "text": "2543"}, {"range": "2974", "text": "2541"}, {"range": "2975", "text": "2543"}, {"range": "2976", "text": "2541"}, {"range": "2977", "text": "2543"}, {"range": "2978", "text": "2541"}, {"range": "2979", "text": "2543"}, {"range": "2980", "text": "2541"}, {"range": "2981", "text": "2543"}, {"range": "2982", "text": "2541"}, {"range": "2983", "text": "2543"}, {"range": "2984", "text": "2541"}, {"range": "2985", "text": "2543"}, "Update the dependencies array to be: [initialSubjects.length, initialTopics.length, selectedSubjectId]", {"range": "2986", "text": "2987"}, {"range": "2988", "text": "2541"}, {"range": "2989", "text": "2543"}, {"range": "2990", "text": "2541"}, {"range": "2991", "text": "2543"}, {"range": "2992", "text": "2541"}, {"range": "2993", "text": "2543"}, {"range": "2994", "text": "2541"}, {"range": "2995", "text": "2543"}, {"range": "2996", "text": "2541"}, {"range": "2997", "text": "2543"}, {"range": "2998", "text": "2541"}, {"range": "2999", "text": "2543"}, "Update the dependencies array to be: [formData.subject, loadChapters]", {"range": "3000", "text": "3001"}, "Update the dependencies array to be: [formData.subject, formData.chapters, formData.chapterId, loadTopics]", {"range": "3002", "text": "3003"}, {"range": "3004", "text": "2541"}, {"range": "3005", "text": "2543"}, {"range": "3006", "text": "2541"}, {"range": "3007", "text": "2543"}, {"range": "3008", "text": "2541"}, {"range": "3009", "text": "2543"}, {"range": "3010", "text": "2541"}, {"range": "3011", "text": "2543"}, {"range": "3012", "text": "2541"}, {"range": "3013", "text": "2543"}, {"range": "3014", "text": "2541"}, {"range": "3015", "text": "2543"}, {"range": "3016", "text": "2541"}, {"range": "3017", "text": "2543"}, {"range": "3018", "text": "2541"}, {"range": "3019", "text": "2543"}, {"range": "3020", "text": "2541"}, {"range": "3021", "text": "2543"}, {"range": "3022", "text": "2541"}, {"range": "3023", "text": "2543"}, {"range": "3024", "text": "2541"}, {"range": "3025", "text": "2543"}, {"range": "3026", "text": "2541"}, {"range": "3027", "text": "2543"}, [561, 564], "unknown", [561, 564], "never", [578, 581], [578, 581], [311, 314], [311, 314], [326, 329], [326, 329], [1008, 1011], [1008, 1011], [1631, 1634], [1631, 1634], [439, 442], [439, 442], [947, 950], [947, 950], [876, 879], [876, 879], [1117, 1120], [1117, 1120], [1219, 1222], [1219, 1222], [3911, 3914], [3911, 3914], [4338, 4341], [4338, 4341], [4807, 4810], [4807, 4810], [6690, 6693], [6690, 6693], [9874, 9877], [9874, 9877], [10225, 10228], [10225, 10228], [10971, 10974], [10971, 10974], [1232, 1235], [1232, 1235], "&apos;", [2091, 2189], "\n            Enter your email address and we&apos;ll send you a link to reset your password.\n          ", "&lsquo;", [2091, 2189], "\n            Enter your email address and we&lsquo;ll send you a link to reset your password.\n          ", "&#39;", [2091, 2189], "\n            Enter your email address and we&#39;ll send you a link to reset your password.\n          ", "&rsquo;", [2091, 2189], "\n            Enter your email address and we&rsquo;ll send you a link to reset your password.\n          ", [3254, 3416], "\n                      Check your email for a link to reset your password. If it doesn&apos;t appear within a few minutes, check your spam folder.\n                    ", [3254, 3416], "\n                      Check your email for a link to reset your password. If it doesn&lsquo;t appear within a few minutes, check your spam folder.\n                    ", [3254, 3416], "\n                      Check your email for a link to reset your password. If it doesn&#39;t appear within a few minutes, check your spam folder.\n                    ", [3254, 3416], "\n                      Check your email for a link to reset your password. If it doesn&rsquo;t appear within a few minutes, check your spam folder.\n                    ", "&quot;", [5843, 5947], "\n            &quot;Education is the most powerful weapon which you can use to change the world.\"\n            ", "&ldquo;", [5843, 5947], "\n            &ldquo;Education is the most powerful weapon which you can use to change the world.\"\n            ", "&#34;", [5843, 5947], "\n            &#34;Education is the most powerful weapon which you can use to change the world.\"\n            ", "&rdquo;", [5843, 5947], "\n            &rdquo;Education is the most powerful weapon which you can use to change the world.\"\n            ", [5843, 5947], "\n            \"Education is the most powerful weapon which you can use to change the world.&quot;\n            ", [5843, 5947], "\n            \"Education is the most powerful weapon which you can use to change the world.&ldquo;\n            ", [5843, 5947], "\n            \"Education is the most powerful weapon which you can use to change the world.&#34;\n            ", [5843, 5947], "\n            \"Education is the most powerful weapon which you can use to change the world.&rdquo;\n            ", [3432, 3435], [3432, 3435], [5591, 5594], [5591, 5594], [12652, 12693], "\n                  Don&apos;t have an account?", [12652, 12693], "\n                  Don&lsquo;t have an account?", [12652, 12693], "\n                  Don&#39;t have an account?", [12652, 12693], "\n                  Don&rsquo;t have an account?", [13321, 13425], [13321, 13425], [13321, 13425], [13321, 13425], [13321, 13425], [13321, 13425], [13321, 13425], [13321, 13425], [3312, 3315], [3312, 3315], [4831, 4834], [4831, 4834], [13554, 13658], [13554, 13658], [13554, 13658], [13554, 13658], [13554, 13658], [13554, 13658], [13554, 13658], [13554, 13658], [1411, 1414], [1411, 1414], [1442, 1445], [1442, 1445], [1606, 1609], [1606, 1609], [1735, 1738], [1735, 1738], [2481, 2484], [2481, 2484], [3071, 3074], [3071, 3074], [3108, 3111], [3108, 3111], [3980, 3983], [3980, 3983], [4017, 4020], [4017, 4020], [4943, 4946], [4943, 4946], [6378, 6381], [6378, 6381], [489, 492], [489, 492], [2097, 2100], [2097, 2100], [884, 887], [884, 887], [1233, 1236], [1233, 1236], [1546, 1549], [1546, 1549], [4316, 4319], [4316, 4319], [5276, 5279], [5276, 5279], [5330, 5333], [5330, 5333], [11652, 11655], [11652, 11655], [13419, 13422], [13419, 13422], [13963, 13966], [13963, 13966], [14017, 14020], [14017, 14020], [1720, 1723], [1720, 1723], [3559, 3562], [3559, 3562], [3204, 3207], [3204, 3207], [3750, 3753], [3750, 3753], [3802, 3805], [3802, 3805], [7283, 7286], [7283, 7286], [13044, 13047], [13044, 13047], [12035, 12038], [12035, 12038], [3465, 3468], [3465, 3468], [7667, 7670], [7667, 7670], [7700, 7703], [7700, 7703], [7736, 7739], [7736, 7739], [7881, 7884], [7881, 7884], [7912, 7915], [7912, 7915], [8381, 8384], [8381, 8384], [8426, 8429], [8426, 8429], [8495, 8498], [8495, 8498], [8556, 8559], [8556, 8559], [8587, 8590], [8587, 8590], [8905, 8908], [8905, 8908], [8938, 8941], [8938, 8941], [8974, 8977], [8974, 8977], [9156, 9159], [9156, 9159], [9187, 9190], [9187, 9190], [10419, 10422], [10419, 10422], [10464, 10467], [10464, 10467], [10543, 10546], [10543, 10546], [10614, 10617], [10614, 10617], [10645, 10648], [10645, 10648], [14420, 14423], [14420, 14423], [14465, 14468], [14465, 14468], [14548, 14551], [14548, 14551], [14623, 14626], [14623, 14626], [14654, 14657], [14654, 14657], [15558, 15561], [15558, 15561], [15603, 15606], [15603, 15606], [15680, 15683], [15680, 15683], [15749, 15752], [15749, 15752], [15780, 15783], [15780, 15783], [1871, 1874], [1871, 1874], [726, 729], [726, 729], [969, 972], [969, 972], [2063, 2066], [2063, 2066], [2493, 2496], [2493, 2496], [2550, 2553], [2550, 2553], [8949, 8952], [8949, 8952], [9300, 9346], "\r\n                      No results found for &quot;", [9300, 9346], "\r\n                      No results found for &ldquo;", [9300, 9346], "\r\n                      No results found for &#34;", [9300, 9346], "\r\n                      No results found for &rdquo;", [9358, 9382], "&quot;.\r\n                    ", [9358, 9382], "&ldquo;.\r\n                    ", [9358, 9382], "&#34;.\r\n                    ", [9358, 9382], "&rdquo;.\r\n                    ", [8235, 8364], "\r\n            Don&apos;t hesitate to leave us your phone number. We will contact you to discuss any questions you may have\r\n          ", [8235, 8364], "\r\n            Don&lsquo;t hesitate to leave us your phone number. We will contact you to discuss any questions you may have\r\n          ", [8235, 8364], "\r\n            Don&#39;t hesitate to leave us your phone number. We will contact you to discuss any questions you may have\r\n          ", [8235, 8364], "\r\n            Don&rsquo;t hesitate to leave us your phone number. We will contact you to discuss any questions you may have\r\n          ", [9346, 9380], "Thank you! We&apos;ll contact you soon.", [9346, 9380], "Thank you! We&lsquo;ll contact you soon.", [9346, 9380], "Thank you! We&#39;ll contact you soon.", [9346, 9380], "Thank you! We&rsquo;ll contact you soon.", [2937, 2940], [2937, 2940], [3281, 3303], "[collegeId, fetchChartData, timeRange]", [14733, 14736], [14733, 14736], [14782, 14785], [14782, 14785], [14903, 14906], [14903, 14906], [15474, 15477], [15474, 15477], [23129, 23132], [23129, 23132], [4246, 4267], "[activeTab, loadChaptersForSubject, subjects]", [8478, 8538], "[formData.subjects, formData.subjectConfigs, updateFormData, initializeSubjectConfig]", [6209, 6212], [6209, 6212], [9212, 9256], "\r\n              Teacher&apos;s Name\r\n            ", [9212, 9256], "\r\n              Teacher&lsquo;s Name\r\n            ", [9212, 9256], "\r\n              Teacher&#39;s Name\r\n            ", [9212, 9256], "\r\n              Teacher&rsquo;s Name\r\n            ", [12658, 12704], "\r\n            Teacher&apos;s Department\r\n          ", [12658, 12704], "\r\n            Teacher&lsquo;s Department\r\n          ", [12658, 12704], "\r\n            Teacher&#39;s Department\r\n          ", [12658, 12704], "\r\n            Teacher&rsquo;s Department\r\n          ", [13151, 13198], "\r\n            Teacher&apos;s Designation\r\n          ", [13151, 13198], "\r\n            Teacher&lsquo;s Designation\r\n          ", [13151, 13198], "\r\n            Teacher&#39;s Designation\r\n          ", [13151, 13198], "\r\n            Teacher&rsquo;s Designation\r\n          ", [388, 391], [388, 391], [654, 657], [654, 657], [552, 555], [552, 555], [916, 919], [916, 919], [1277, 1280], [1277, 1280], [1641, 1644], [1641, 1644], [3141, 3144], [3141, 3144], [625, 628], [625, 628], [1048, 1051], [1048, 1051], [1547, 1550], [1547, 1550], [2051, 2054], [2051, 2054], [3571, 3574], [3571, 3574], [5423, 5426], [5423, 5426], [3320, 3323], [3320, 3323], [7939, 7942], [7939, 7942], [837, 840], [837, 840], [1435, 1438], [1435, 1438], [1555, 1558], [1555, 1558], [2048, 2051], [2048, 2051], [2613, 2616], [2613, 2616], [2998, 3001], [2998, 3001], [3491, 3494], [3491, 3494], [4025, 4028], [4025, 4028], [4382, 4385], [4382, 4385], [1801, 1804], [1801, 1804], [4245, 4248], [4245, 4248], [4564, 4567], [4564, 4567], [5626, 5629], [5626, 5629], [6882, 6885], [6882, 6885], [8235, 8238], [8235, 8238], [9449, 9452], [9449, 9452], [921, 924], [921, 924], [1266, 1269], [1266, 1269], [1760, 1763], [1760, 1763], [2270, 2273], [2270, 2273], [2615, 2618], [2615, 2618], [633, 636], [633, 636], [720, 723], [720, 723], [6902, 6905], [6902, 6905], [7497, 7500], [7497, 7500], [8274, 8277], [8274, 8277], [8486, 8489], [8486, 8489], [3470, 3473], [3470, 3473], [4908, 4911], [4908, 4911], [4967, 4970], [4967, 4970], [11581, 11584], [11581, 11584], [15699, 15702], [15699, 15702], [15723, 15726], [15723, 15726], [26740, 26743], [26740, 26743], [26931, 26934], [26931, 26934], [26979, 26982], [26979, 26982], [28298, 28301], [28298, 28301], [11442, 11445], [11442, 11445], [12985, 12988], [12985, 12988], [13009, 13012], [13009, 13012], [14388, 14391], [14388, 14391], [14412, 14415], [14412, 14415], [17045, 17048], [17045, 17048], [17069, 17072], [17069, 17072], [20792, 20795], [20792, 20795], [4433, 4435], "[initialSubjects.length, initialTopics.length, selectedSubjectId]", [5509, 5512], [5509, 5512], [6686, 6689], [6686, 6689], [7553, 7556], [7553, 7556], [9293, 9296], [9293, 9296], [10350, 10353], [10350, 10353], [10912, 10915], [10912, 10915], [1406, 1424], "[formData.subject, loadChapters]", [1615, 1672], "[formData.subject, formData.chapters, formData.chapterId, loadTopics]", [620, 623], [620, 623], [1968, 1971], [1968, 1971], [2371, 2374], [2371, 2374], [2880, 2883], [2880, 2883], [3454, 3457], [3454, 3457], [3811, 3814], [3811, 3814], [5907, 5910], [5907, 5910], [7564, 7567], [7564, 7567], [9239, 9242], [9239, 9242], [154, 157], [154, 157], [220, 223], [220, 223], [607, 610], [607, 610]]