(()=>{var e={};e.id=1297,e.ids=[1297],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5398:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx","default")},6068:(e,t,r)=>{Promise.resolve().then(r.bind(r,86964))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17879:(e,t,r)=>{"use strict";r.d(t,{HC:()=>o,P_:()=>l,Rb:()=>c,Sp:()=>i,q6:()=>s,qg:()=>n});var a=r(31981);let s=async(e,t)=>{try{let r=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!r)return(0,a.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let s={"Content-Type":"application/json",Authorization:`Bearer ${r}`},o=await fetch(`http://localhost:3000/api/colleges/${e}/teachers`,{method:"POST",headers:s,body:JSON.stringify(t)});if(!o.ok){let e=await o.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${o.status}`,"Failed to add teacher. Please try again.")}let i=await o.json();return(0,a.$y)(i,!0,"Teacher added successfully!")}catch(e){return console.error("Error adding teacher:",e),(0,a.hS)(e.message||"Failed to add teacher. Please try again.","Failed to add teacher. Please try again.")}},o=async(e,t=1,r=10,s={})=>{try{let o=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!o)return console.error("No authentication token found"),(0,a.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let i=new URLSearchParams({page:t.toString(),limit:r.toString(),...s}),n=`http://localhost:3000/api/colleges/${e}/teachers?${i}`;console.log(`Fetching teachers: ${n}`);let l=await fetch(n,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},cache:"no-store"});if(!l.ok){let e=await l.text();try{let t=JSON.parse(e);return(0,a.hS)(t.message||`Error: ${l.status}`,"Failed to load teachers. Please try again.")}catch(e){return(0,a.hS)(`Error: ${l.status} - ${l.statusText}`,"Failed to load teachers. Please try again.")}}let c=await l.json();if(console.log("Raw API response:",c),Array.isArray(c)){console.log("API returned an array, converting to paginated format");let e={teachers:c,total:c.length,page:t,limit:r,totalPages:Math.ceil(c.length/r)};return(0,a.$y)(e)}return(0,a.$y)(c)}catch(e){return console.error("Error fetching college teachers:",e),(0,a.hS)(e.message||"Failed to load teachers. Please try again.","Failed to load teachers. Please try again.")}},i=async(e,t)=>{try{let r=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!r)return(0,a.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher with data:",t);let s={"Content-Type":"application/json",Authorization:`Bearer ${r}`},o=await fetch(`http://localhost:3000/api/teachers/${e}`,{method:"PUT",headers:s,body:JSON.stringify(t)});if(!o.ok){let e=await o.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${o.status}`,"Failed to update teacher. Please try again.")}let i=await o.json();return(0,a.$y)(i,!0,"Teacher updated successfully!")}catch(e){return console.error("Error updating teacher:",e),(0,a.hS)(e.message||"Failed to update teacher. Please try again.","Failed to update teacher. Please try again.")}},n=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`},s=await fetch(`http://localhost:3000/api/teachers/${e}`,{method:"DELETE",headers:r});if(!s.ok){let e=await s.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${s.status}`,"Failed to delete teacher. Please try again.")}let o=await s.json();return(0,a.$y)(o,!0,"Teacher deleted successfully!")}catch(e){return console.error("Error deleting teacher:",e),(0,a.hS)(e.message||"Failed to delete teacher","Failed to delete teacher. Please try again.")}},l=async e=>{try{let t=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");console.log("Updating teacher profile with data:",e);let r={"Content-Type":"application/json",Authorization:`Bearer ${t}`},s=await fetch("http://localhost:3000/api/teachers/me",{method:"PUT",headers:r,body:JSON.stringify(e)});if(!s.ok){let e=await s.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${s.status}`,"Failed to update profile. Please try again.")}let o=await s.json();return(0,a.$y)(o,!0,"Profile updated successfully!")}catch(e){return console.error("Error updating teacher profile:",e),(0,a.hS)(e.message||"Failed to update profile. Please try again.","Failed to update profile. Please try again.")}},c=async()=>{try{let e=localStorage.getItem("token")||localStorage.getItem("backendToken");if(!e)return(0,a.hS)("Authentication token is missing. Please log in again.","Authentication required. Please log in again.");let t={"Content-Type":"application/json",Authorization:`Bearer ${e}`},r=await fetch("http://localhost:3000/api/users/me",{method:"GET",headers:t});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,a.hS)(e.message||`Error: ${r.status}`,"Failed to load profile. Please try again.")}let s=await r.json();return(0,a.$y)(s)}catch(e){return console.error("Error fetching current teacher profile:",e),(0,a.hS)(e.message||"Failed to load profile. Please try again.","Failed to load profile. Please try again.")}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27548:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var a=r(60687),s=r(43210),o=r(63239),i=r(44493),n=r(58869);function l({profileData:e,onEdit:t}){return(0,a.jsxs)("div",{className:"w-full mx-auto space-y-6 py-10",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{className:"text-lg font-medium",children:"My Profile"})}),(0,a.jsx)(i.Wu,{className:"pb-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("div",{className:"relative h-16 w-16 rounded-full overflow-hidden",children:e.profileImage?(0,a.jsx)("img",{src:e.profileImage,alt:e.firstName,className:"w-10 h-10 rounded-full object-cover border"}):(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-gray-400"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-base",children:`${e.firstName} ${e.lastName}`}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground flex flex-col sm:flex-row sm:gap-2",children:[(0,a.jsx)("span",{children:e.designation}),e.location&&(0,a.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,a.jsx)("span",{children:e.location})]})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2"})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{className:"flex flex-row items-center justify-between",children:(0,a.jsx)(i.ZB,{className:"text-lg font-medium",children:"Personal Information"})}),(0,a.jsx)(i.Wu,{className:"pb-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"First Name"}),(0,a.jsx)("p",{className:"font-medium",children:e.firstName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Last Name"}),(0,a.jsx)("p",{className:"font-medium",children:e.lastName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Email address"}),(0,a.jsx)("p",{className:"font-medium",children:e.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Designation"}),(0,a.jsx)("p",{className:"font-medium",children:e.designation})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-1",children:"Department"}),(0,a.jsx)("p",{className:"font-medium",children:e.department})]})]})})]})]})}r(17879);let c=async e=>{try{let t=await fetch(`http://localhost:3000/api/teachers/${e}`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")||localStorage.getItem("backendToken")}`}});if(!t.ok)throw Error("Failed to fetch teacher");return await t.json()}catch(e){throw console.error("Error fetching teacher by ID:",e),e}};var d=r(99802),u=r(20140),h=r(41862);let m=()=>{let[e,t]=(0,s.useState)(null),[r,i]=(0,s.useState)(!0),[n,m]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{try{i(!0);let e=await (0,d.p)();if(!e||!e._id)throw Error("Unable to retrieve user information");let r=await c(e._id);if(!r)throw Error("Unable to retrieve teacher profile");t({firstName:r.firstName||"",lastName:r.lastName||"",occupation:"Teacher",location:r.cityState||"",email:r.email||"",gender:r.gender||"",designation:r.designation||"",dateOfBirth:r.dateOfBirth||"",phone:r.phone||"",country:r.country||"",cityState:r.cityState||"",postalCode:r.postalCode||"",taxId:r.taxId||"",profileImage:r.profileImageUrl||"",department:r.department||""})}catch(e){console.error("Error fetching profile:",e),m(e.message||"Failed to load profile data"),(0,u.o)({title:"Error",description:e.message||"Failed to load profile data",variant:"destructive"})}finally{i(!1)}})()},[]),(0,a.jsx)("main",{className:"min-h-screen bg-gray-50 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-semibold text-black",children:"User Profile"}),(0,a.jsx)(o.A,{items:[{label:"Home",href:"/teacher"},{label:"...",href:"#"},{label:"User Profile"}],className:"text-sm mt-1"})]}),r?(0,a.jsxs)("div",{className:"flex justify-center items-center py-20",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading profile..."})]}):n?(0,a.jsxs)("div",{className:"text-center py-20 text-destructive",children:[(0,a.jsx)("p",{children:n}),(0,a.jsx)("button",{className:"mt-4 px-4 py-2 bg-primary text-white rounded-md",onClick:()=>window.location.reload(),children:"Retry"})]}):e?(0,a.jsx)(l,{profileData:e,onEdit:e=>{console.log(`Edit ${e} section`)}}):(0,a.jsx)("div",{className:"text-center py-20 text-muted-foreground",children:"No profile data available"})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31981:(e,t,r)=>{"use strict";r.d(t,{$y:()=>o,cY:()=>i,hS:()=>s});var a=r(52581);function s(e,t="An error occurred. Please try again.",r=!0){let o,i=t;return e?.message?i=e.message:"string"==typeof e?i=e:e?.response?.data?.message?i=e.response.data.message:e?.data?.message&&(i=e.data.message),e?.status?o=e.status:e?.response?.status&&(o=e.response.status),i.includes("already exists")||(i.includes("Authentication")||i.includes("Unauthorized")?i="Please log in again to continue. Your session may have expired.":i.includes("Network")||i.includes("fetch")?i="Please check your internet connection and try again.":i.includes("not found")?i="The requested resource was not found.":i.includes("Forbidden")?i="You do not have permission to perform this action.":500===o?i="Server error. Please try again later.":503===o&&(i="Service temporarily unavailable. Please try again later.")),r&&a.oR.error(i),{success:!1,error:i,statusCode:o}}function o(e,t=!1,r){return t&&r&&a.oR.success(r),{success:!0,data:e}}function i(e){return!0===e.success}},33873:e=>{"use strict";e.exports=require("path")},39414:(e,t,r)=>{Promise.resolve().then(r.bind(r,96503))},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>n,Zp:()=>o,aR:()=>i});var a=r(60687);r(43210);var s=r(4780);function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}},50289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=r(65239),s=r(48088),o=r(88170),i=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["teacher",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96503)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,5398)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,71182)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/teacher/profile/page",pathname:"/teacher/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63239:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(60687),s=r(43210),o=r.n(s),i=r(85814),n=r.n(i),l=r(4780),c=r(14952),d=r(93661);let u=({items:e,maxItems:t=4,className:r})=>{let s=o().useMemo(()=>e.length<=t?e:[e[0],{label:"..."},...e.slice(-2)],[e,t]);return(0,a.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,l.cn)("flex items-center text-sm",r),children:(0,a.jsx)("ol",{className:"flex items-center space-x-1",children:s.map((e,t)=>{let r=t===s.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):r?(0,a.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,a.jsx)(n(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},69684:(e,t,r)=>{Promise.resolve().then(r.bind(r,5398))},71182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(37413),s=r(92555);function o(){return(0,a.jsx)(s.W,{message:"Loading teacher dashboard..."})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86270:(e,t,r)=>{Promise.resolve().then(r.bind(r,27548))},86964:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(60687),s=r(66327),o=r(53355),i=r(99557),n=r(45285);function l({children:e}){return(0,a.jsx)(n.A,{allowedRoles:[i.g.TEACHER],children:(0,a.jsx)(o.default,{children:(0,a.jsx)(s.N,{role:i.g.TEACHER,children:e})})})}},93661:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:e=>{"use strict";e.exports=require("events")},96503:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\teacher\\profile\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,4619,3287,9592,2581,4707,6658],()=>r(50289));module.exports=a})();