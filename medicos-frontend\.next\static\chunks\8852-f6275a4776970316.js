"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8852],{12379:(e,t,r)=>{r.d(t,{A:()=>d});var a=r(95155),n=r(12115),o=r(6874),s=r.n(o),i=r(59434),l=r(13052),c=r(5623);let d=e=>{let{items:t,maxItems:r=4,className:o}=e,d=n.useMemo(()=>t.length<=r?t:[t[0],{label:"..."},...t.slice(-2)],[t,r]);return(0,a.jsx)("nav",{"aria-label":"Breadcrumb",className:(0,i.cn)("flex items-center text-sm",o),children:(0,a.jsx)("ol",{className:"flex items-center space-x-1",children:d.map((e,t)=>{let r=t===d.length-1;return(0,a.jsxs)("li",{className:"flex items-center",children:[t>0&&(0,a.jsx)(l.A,{className:"h-4 w-4 mx-1 text-muted-foreground"}),"..."===e.label?(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"}):r?(0,a.jsx)("span",{className:"font-semibold",children:e.label}):e.href?(0,a.jsx)(s(),{href:e.href,className:"text-muted-foreground hover:text-foreground transition-colors",children:e.label}):(0,a.jsx)("span",{className:"text-muted-foreground",children:e.label})]},t)})})})}},17809:(e,t,r)=>{r.d(t,{N_:()=>d,hG:()=>n,ly:()=>c,sr:()=>s,ul:()=>i,v5:()=>l,zi:()=>o});var a=r(55097);async function n(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r={...e};r.explanation&&""!==r.explanation.trim()||delete r.explanation,delete r.status,delete r.reviewStatus,r.type||(r.type="multiple-choice"),console.log("Sending question data:",JSON.stringify(r,null,2));let n=await fetch("".concat("http://localhost:3000/api","/questions"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(r)});if(!n.ok){let e=await n.json().catch(()=>({}));if(console.error("API error response:",e),e.details){let t=Object.entries(e.details).map(e=>{let[t,r]=e;return"".concat(t,": ").concat(r)}).join(", ");return(0,a.hS)(t||e.message||"Error: ".concat(n.status),"Failed to create question. Please check your input and try again.")}return(0,a.hS)(e.message||"Error: ".concat(n.status),"Failed to create question. Please try again.")}let o=await n.json();return(0,a.$y)(o,!0,"Question created successfully!")}catch(e){return console.error("Error creating question:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to create question. Please try again.","Failed to create question. Please try again.")}}async function o(e){let t=localStorage.getItem("backendToken");if(!t)throw Error("Authentication required");try{let r=await fetch("".concat("http://localhost:3000/api","/questions/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok){let e=await r.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(r.status))}return await r.json()}catch(e){throw console.error("Error fetching question:",e),e}}async function s(e,t){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let a=await fetch("".concat("http://localhost:3000/api","/questions/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify(t)});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(a.status))}return await a.json()}catch(e){throw console.error("Error updating question:",e),e}}async function i(e){let t=localStorage.getItem("backendToken");if(!t)return(0,a.hS)("Authentication required","Authentication required. Please log in again.");try{let r=await fetch("".concat("http://localhost:3000/api","/questions/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!r.ok){let e=await r.json().catch(()=>({}));return(0,a.hS)(e.message||"Error: ".concat(r.status),"Failed to delete question. Please try again.")}let n=await r.json();return(0,a.$y)(n,!0,"Question deleted successfully!")}catch(e){return console.error("Error deleting question:",e),(0,a.hS)(e instanceof Error?e.message:"Failed to delete question. Please try again.","Failed to delete question. Please try again.")}}async function l(e,t){let r=localStorage.getItem("backendToken");if(!r)throw Error("Authentication required");try{let a=await fetch("".concat("http://localhost:3000/api","/questions/").concat(e,"/review"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify({status:t})});if(!a.ok){let e=await a.json().catch(()=>({}));throw Error(e.message||"Error: ".concat(a.status))}return await a.json()}catch(e){throw console.error("Error reviewing question:",e),e}}async function c(e,t,r){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"gemini",n=localStorage.getItem("backendToken");if(!n)throw Error("Authentication required");try{let o=new FormData;o.append("file",e),o.append("subjectId",t),o.append("aiProvider",a),r&&o.append("topicId",r);let s=await fetch("".concat("http://localhost:3000/api","/questions/bulk-upload-pdf"),{method:"POST",headers:{Authorization:"Bearer ".concat(n)},body:o});if(!s.ok){let e=await s.json().catch(()=>({}));if(console.error("PDF upload error response:",e),e.details){let t=Object.entries(e.details).map(e=>{let[t,r]=e;return"".concat(t,": ").concat(r)}).join(", ");throw Error(t||e.message||"Error: ".concat(s.status))}throw Error(e.message||"Error: ".concat(s.status))}return await s.json()}catch(e){throw console.error("Error uploading PDF questions:",e),e}}async function d(e,t,r){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"gemini",n=localStorage.getItem("backendToken");if(!n)throw Error("Authentication required");try{let o=new FormData;o.append("file",e),o.append("subjectId",t),o.append("aiProvider",a),r&&o.append("topicId",r);let s=await fetch("".concat("http://localhost:3000/api","/questions/bulk-upload-chemical-pdf"),{method:"POST",headers:{Authorization:"Bearer ".concat(n)},body:o});if(!s.ok){let e=await s.json().catch(()=>({}));if(console.error("Chemical PDF upload error response:",e),e.details){let t=Object.entries(e.details).map(e=>{let[t,r]=e;return"".concat(t,": ").concat(r)}).join(", ");throw Error(t||e.message||"Error: ".concat(s.status))}throw Error(e.message||"Error: ".concat(s.status))}return await s.json()}catch(e){throw console.error("Error uploading chemical PDF questions:",e),e}}},30285:(e,t,r)=>{r.d(t,{$:()=>l,r:()=>i});var a=r(95155);r(12115);var n=r(66634),o=r(74466),s=r(59434);let i=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:o,asChild:l=!1,...c}=e,d=l?n.Slot:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,s.cn)(i({variant:r,size:o,className:t})),...c})}},55097:(e,t,r)=>{r.d(t,{$y:()=>o,cY:()=>s,hS:()=>n});var a=r(56671);function n(e){var t,r,n,o;let s,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"An error occurred. Please try again.",l=!(arguments.length>2)||void 0===arguments[2]||arguments[2],c=i;return(null==e?void 0:e.message)?c=e.message:"string"==typeof e?c=e:(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)?c=e.response.data.message:(null==e?void 0:null===(n=e.data)||void 0===n?void 0:n.message)&&(c=e.data.message),(null==e?void 0:e.status)?s=e.status:(null==e?void 0:null===(o=e.response)||void 0===o?void 0:o.status)&&(s=e.response.status),c.includes("already exists")||(c.includes("Authentication")||c.includes("Unauthorized")?c="Please log in again to continue. Your session may have expired.":c.includes("Network")||c.includes("fetch")?c="Please check your internet connection and try again.":c.includes("not found")?c="The requested resource was not found.":c.includes("Forbidden")?c="You do not have permission to perform this action.":500===s?c="Server error. Please try again later.":503===s&&(c="Service temporarily unavailable. Please try again later.")),l&&a.oR.error(c),{success:!1,error:c,statusCode:s}}function o(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2?arguments[2]:void 0;return t&&r&&a.oR.success(r),{success:!0,data:e}}function s(e){return!0===e.success}},59409:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>g,gC:()=>h,l6:()=>c,yv:()=>d});var a=r(95155);r(12115);var n=r(4011),o=r(66474),s=r(5196),i=r(47863),l=r(59434);function c(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:r="default",children:s,...i}=e;return(0,a.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":r,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[s,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:r,position:o="popper",...s}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:o,...s,children:[(0,a.jsx)(p,{}),(0,a.jsx)(n.LM,{className:(0,l.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,a.jsx)(f,{})]})})}function g(e){let{className:t,children:r,...o}=e;return(0,a.jsxs)(n.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...o,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(s.A,{className:"size-4"})})}),(0,a.jsx)(n.p4,{children:r})]})}function p(e){let{className:t,...r}=e;return(0,a.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"size-4"})})}function f(e){let{className:t,...r}=e;return(0,a.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(o.A,{className:"size-4"})})}},59434:(e,t,r)=>{r.d(t,{b:()=>s,cn:()=>o});var a=r(52596),n=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}function s(e){return new Promise((t,r)=>{let a=new FileReader;a.readAsDataURL(e),a.onload=()=>t(a.result),a.onerror=e=>r(e)})}},62523:(e,t,r)=>{r.d(t,{p:()=>o});var a=r(95155);r(12115);var n=r(59434);function o(e){let{className:t,type:r,...o}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},66695:(e,t,r)=>{r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>s});var a=r(95155);r(12115);var n=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},88262:(e,t,r)=>{r.d(t,{d:()=>c,o:()=>d});var a=r(12115);new EventTarget;let n=[],o=[];function s(){o.forEach(e=>e([...n]))}function i(e){let t=e.id||Math.random().toString(36).substring(2,9),r={...e,id:t};return n=[...n,r],s(),setTimeout(()=>{l(t)},5e3),t}function l(e){n=n.filter(t=>t.id!==e),s()}function c(){let[e,t]=a.useState(n);return a.useEffect(()=>(o.push(t),t([...n]),()=>{o=o.filter(e=>e!==t)}),[]),{toast:e=>i(e),dismiss:e=>{e?l(e):n.forEach(e=>e.id&&l(e.id))},toasts:e}}let d=e=>i(e)}}]);