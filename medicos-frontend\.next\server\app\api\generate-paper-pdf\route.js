(()=>{var e={};e.id=9547,e.ids=[9547],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10589:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{patchFetch:()=>p,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>d});var i=r(96559),o=r(48088),n=r(37719),l=r(38451),s=e([l]);l=(s.then?(await s)():s)[0];let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/generate-paper-pdf/route",pathname:"/api/generate-paper-pdf",filename:"route",bundlePath:"app/api/generate-paper-pdf/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\api\\generate-paper-pdf\\route.ts",nextConfigOutput:"",userland:l}),{workAsyncStorage:g,workUnitAsyncStorage:d,serverHooks:m}=c;function p(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:d})}a()}catch(e){a(e)}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},38451:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{POST:()=>s});var i=r(32190),o=r(83636),n=e([o]);function l(e){if(!e)return"";let t=e,r=(t=t.replace(/!\s*\(\s*(data:image\/[^;]+;base64,[A-Za-z0-9+/=]+)\s*\)/g,(e,t)=>{let r=t.replace(/\s+/g,"");return`<img src="${r}" alt="Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`})).match(/data:image\/[^;]+;base64,[A-Za-z0-9+/=]+/g);return r&&r.forEach(e=>{let r=RegExp(`<img[^>]*src=["']${e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}["'][^>]*>`,"i");if(!t.match(r)){let r=e.replace(/\s+/g,""),a=`<img src="${r}" alt="Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`;t=t.replace(e,a)}}),t=t.replace(/(\|[^|\n]*\|[^|\n]*\|[\s\S]*?)(?=\n\n|\n(?!\|)|$)/g,e=>{try{let t=e.trim(),r=(t=t.replace(/<br\s*\/?>/gi," ")).split("\n").filter(e=>e.trim());if(r.length<2)return e;let a=[],i=!1;for(let e of r){let t=e.split("|").map(e=>e.trim()).filter(e=>e);if(0!==t.length){if(t.every(e=>e.match(/^:?-+:?$/))){i=!0;continue}a.push(t)}}if(0===a.length)return e;let o="<table>";if(i&&a.length>0){for(let e of(o+="<thead><tr>",a[0]))o+=`<th>${e}</th>`;if(o+="</tr></thead>",a.length>1){o+="<tbody>";for(let e=1;e<a.length;e++){for(let t of(o+="<tr>",a[e]))o+=`<td>${t}</td>`;o+="</tr>"}o+="</tbody>"}}else{for(let e of(o+="<tbody>",a)){for(let t of(o+="<tr>",e))o+=`<td>${t}</td>`;o+="</tr>"}o+="</tbody>"}return o+="</table>"}catch(t){return console.warn("Error processing table:",t),e}})}o=(n.then?(await n)():n)[0];let s=async e=>{try{let{title:t,description:r,duration:a,totalMarks:n,questions:s,includeAnswers:p,filename:c="question-paper.pdf",collegeName:g="",collegeLogoUrl:d=""}=await e.json();if(console.log("PDF Generation Request:",{title:t,questionsCount:s?.length||0,firstQuestion:s?.[0]?{question:s[0].question?.substring(0,100)+"...",hasImageUrls:!!s[0].imageUrls,imageUrlsCount:(s[0].imageUrls||[]).length}:null}),!s||!Array.isArray(s)||0===s.length)throw console.error("PDF Generation Error: No questions provided"),Error("No questions provided for PDF generation");let m=s.filter(e=>e&&e.question);if(0===m.length)throw console.error("PDF Generation Error: No valid questions found"),Error("No valid questions found for PDF generation");console.log(`Processing ${m.length} valid questions out of ${s.length} total`);let f=`<!doctype html>
<html>
<head>
  <meta charset="utf-8" />
  <title>${t}</title>
  <link href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css" rel="stylesheet" />
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"></script>
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      if (window.renderMathInElement) {
        window.renderMathInElement(document.body, {
          delimiters: [
            {left: '$$', right: '$$', display: true},
            {left: '$', right: '$', display: false},
            {left: '\\(', right: '\\)', display: false},
            {left: '\\[', right: '\\]', display: true}
          ],
          throwOnError: false,
          errorColor: '#cc0000',
          strict: false
        });
      }
    });
  </script>
  <style>
    @page {
      size: A4;
      margin: 25mm 15mm 20mm 15mm;
    }
    body { font-family: 'Times New Roman', serif; font-size: 10pt; line-height: 1.2; position: relative; }
    h1,h2,h3 { margin: 0; padding: 0; }
    hr { margin: 8px 0; border: none; border-top: 1px solid #000; }
    /* Watermark */
    body::before {
      content: 'MEDICOS';
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-30deg);
      font-size: 96pt;
      font-weight: bold;
      color: rgba(0,128,0,0.08); /* greenish */
      z-index: 0;
      pointer-events: none;
    }
    /* Header / Footer */
    header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .college { display: flex; align-items: center; gap: 6px; }
    .college img { height: 24px; width: auto; }
    .title { text-align: center; flex: 1; font-size: 14pt; font-weight: bold; }
    .meta { text-align: right; font-size: 10pt; }
    .meta div { margin: 0; }

    .questions { position: relative; z-index: 1; padding-bottom: 25mm; }
    .subject-section {
      page-break-before: avoid;
      margin-top: 20px;
    }
    .subject-section:first-child {
      page-break-before: avoid;
      margin-top: 0;
    }
    .subject-content {
      column-count: 2;
      column-gap: 10mm;
      column-rule: 1px solid #ccc; /* Add middle line separator */
      column-rule-style: solid;
    }
    .question { break-inside: avoid; margin-bottom: 12px; }
    .options { margin-left: 16px; }
    .options p { margin: 2px 0; }
    footer { position: fixed; bottom: 3mm; left: 0; right: 0; text-align: center; font-size: 9pt; color: #666; background: #fff; z-index: 2; }
    .subject-section {
      page-break-inside: auto;
      margin-bottom: 20px;
      page-break-after: avoid;
    }
    .subject-heading {
      font-weight: bold;
      font-size: 12pt;
      margin: 0 0 12px 0;
      text-align: left;
      
      padding-bottom: 4px;
      page-break-after: avoid;
      page-break-before: avoid;
      width: 48%;
      display: inline-block;
      vertical-align: top;
    }
    /* Table styling for proper rendering */
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 8px 0;
      font-size: 9pt;
      break-inside: avoid;
    }
    th, td {
      border: 1px solid #333;
      padding: 4px 6px;
      text-align: left;
      vertical-align: top;
    }
    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    /* Math rendering support */
    .katex {
      font-size: 1em;
    }
    .katex-display {
      margin: 0.3em 0;
    }
    /* Image styling */
    img {
      max-width: 300px !important;
      height: auto !important;
      display: block !important;
      margin: 10px auto !important;
      border: 1px solid #ddd !important;
      padding: 5px !important;
      break-inside: avoid;
    }
  </style>
</head>
<body>
  <header>
    <div class="college">
      ${d?`<img src="${d}" alt="logo" />`:""}
      <span>${g}</span>
    </div>
    <div class="title">${t}</div>
    <div class="meta">
      <div>Total Marks: ${n}</div>
      <div>Duration: ${a} mins</div>
    </div>
  </header>
  <hr style="page-break-after: avoid;" />
  <p style="page-break-after: avoid; margin-bottom: 10px;">${r}</p>
  <div class="questions">
    ${(()=>{let e=m.reduce((e,t)=>{let r=t.subject||"General";return e[r]||(e[r]=[]),e[r].push(t),e},{});console.log("Grouped questions for HTML:",Object.keys(e));let t=1;return Object.entries(e).map(([e,r])=>`
          <div class="subject-section">
            <div class="subject-heading">Subject: ${e}</div>

            <div class="subject-content">
              ${r.map((e,r)=>{let a=t++;try{let t=e.question;try{let a=e.imageUrls||[];if(console.log(`Question ${r+1} imageUrls:`,a?.length||0,"images"),a&&Array.isArray(a)&&a.length>0){let e=a[0];if(console.log(`Question ${r+1} first image:`,e?e.substring(0,50)+"...":"null"),e&&"string"==typeof e&&e.startsWith("data:image/")){console.log(`Question ${r+1}: Processing valid base64 image`);let i=t,o=0;t=t.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(t,r)=>(console.log(`Replacing markdown image: ${t}`),o++,`<img src="${e}" alt="${r||"Question Image"}" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`));let n=0;t=t.replace(/<img[^>]*>/gi,t=>t.includes('src="data:image/')?t:(console.log(`Replacing incomplete HTML img tag: ${t.substring(0,50)}...`),n++,`<img src="${e}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`)),console.log(`Question ${r+1}: Made ${o} markdown replacements, ${n} HTML replacements`),!t.includes("<img")&&a.length>0&&/image|figure|diagram|chart|graph|picture|represents|shown|below|above/i.test(t)&&(console.log(`Question ${r+1}: Adding image for keywords`),t+=`
<img src="${e}" alt="Question Image" style="max-width:300px;height:auto;display:block;margin:10px auto;border:1px solid #ddd;padding:5px;" onerror="this.style.display='none';" />`),i!==t&&console.log(`Question ${r+1}: Text modified for images`)}else console.log(`Question ${r+1}: Invalid image format:`,typeof e,e?e.substring(0,20):"null")}else console.log(`Question ${r+1}: No imageUrls found`)}catch(e){console.error(`Error processing images for question ${r+1}:`,e)}try{let r=e.imageData||e.chemicalImages;if(r&&"object"==typeof r&&!t.includes("<img")&&!(t.includes("data:image/")||t.includes("!["))){let e=Object.keys(r)[0];e&&r[e]&&(t=t+"\n"+r[e])}}catch(e){console.error("Error processing legacy image data:",e)}let i="";try{i=(i=(i=l(t)).replace(/<img(?![^>]*src=)[^>]*>/gi,"")).replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"")}catch(e){console.error("Error processing question text:",e),i=t}i=i.replace(/\\ffracωLR/g,"\\frac{ω}{LR}").replace(/\\ffrac1ωCR/g,"\\frac{1}{ωCR}").replace(/\\ffracLC\\ffrac1R/g,"\\frac{LC}{\\frac{1}{R}}").replace(/\\ffracRLC/g,"\\frac{R}{LC}").replace(/\\ffrac100πMHz/g,"\\frac{100}{πMHz}").replace(/\\ffrac1000πHz/g,"\\frac{1000}{πHz}").replace(/\\ffrac11000ohm/g,"\\frac{1}{1000ohm}").replace(/\\ffrac1Cω/g,"\\frac{1}{Cω}").replace(/\\ffrac\{/g,"\\frac{").replace(/\\ffrac([ωπα-ωΩ])([A-Z]+)/g,"\\frac{$1}{$2}").replace(/\\ffrac(\d+)([ωπα-ωΩ])([A-Z]+)/g,"\\frac{$1}{$2$3}").replace(/\\ffrac([A-Z]+)([A-Z]+)/g,"\\frac{$1}{$2}").replace(/\\alpha/g,"α").replace(/\\beta/g,"β").replace(/\\gamma/g,"γ").replace(/\\delta/g,"δ").replace(/\\epsilon/g,"ε").replace(/\\varepsilon/g,"ε").replace(/\\zeta/g,"ζ").replace(/\\eta/g,"η").replace(/\\theta/g,"θ").replace(/\\iota/g,"ι").replace(/\\kappa/g,"κ").replace(/\\lambda/g,"λ").replace(/\\mu/g,"μ").replace(/\\nu/g,"ν").replace(/\\xi/g,"ξ").replace(/\\pi/g,"π").replace(/\\rho/g,"ρ").replace(/\\sigma/g,"σ").replace(/\\tau/g,"τ").replace(/\\upsilon/g,"υ").replace(/\\phi/g,"φ").replace(/\\chi/g,"χ").replace(/\\psi/g,"ψ").replace(/\\omega/g,"ω").replace(/\\Omega/g,"Ω").replace(/\\Delta/g,"Δ").replace(/\\Gamma/g,"Γ").replace(/\\Lambda/g,"Λ").replace(/\\Phi/g,"Φ").replace(/\\Pi/g,"Π").replace(/\\Psi/g,"Ψ").replace(/\\Sigma/g,"Σ").replace(/\\Theta/g,"Θ").replace(/\\Xi/g,"Ξ").replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"").replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"");let o=(e.options||[]).map(e=>{try{let t=l(e);return(t=t.replace(/<img(?![^>]*src=)[^>]*>/gi,"")).replace(/\\ffracωLR/g,"\\frac{ω}{LR}").replace(/\\ffrac1ωCR/g,"\\frac{1}{ωCR}").replace(/\\ffracLC\\ffrac1R/g,"\\frac{LC}{\\frac{1}{R}}").replace(/\\ffracRLC/g,"\\frac{R}{LC}").replace(/\\ffrac100πMHz/g,"\\frac{100}{πMHz}").replace(/\\ffrac1000πHz/g,"\\frac{1000}{πHz}").replace(/\\ffrac11000ohm/g,"\\frac{1}{1000ohm}").replace(/\\ffrac1Cω/g,"\\frac{1}{Cω}").replace(/\\ffrac\{/g,"\\frac{").replace(/\\ffrac([ωπα-ωΩ])([A-Z]+)/g,"\\frac{$1}{$2}").replace(/\\ffrac(\d+)([ωπα-ωΩ])([A-Z]+)/g,"\\frac{$1}{$2$3}").replace(/\\ffrac([A-Z]+)([A-Z]+)/g,"\\frac{$1}{$2}").replace(/\\alpha/g,"α").replace(/\\beta/g,"β").replace(/\\gamma/g,"γ").replace(/\\delta/g,"δ").replace(/\\epsilon/g,"ε").replace(/\\varepsilon/g,"ε").replace(/\\zeta/g,"ζ").replace(/\\eta/g,"η").replace(/\\theta/g,"θ").replace(/\\iota/g,"ι").replace(/\\kappa/g,"κ").replace(/\\lambda/g,"λ").replace(/\\mu/g,"μ").replace(/\\nu/g,"ν").replace(/\\xi/g,"ξ").replace(/\\pi/g,"π").replace(/\\rho/g,"ρ").replace(/\\sigma/g,"σ").replace(/\\tau/g,"τ").replace(/\\upsilon/g,"υ").replace(/\\phi/g,"φ").replace(/\\chi/g,"χ").replace(/\\psi/g,"ψ").replace(/\\omega/g,"ω").replace(/\\Omega/g,"Ω").replace(/\\Delta/g,"Δ").replace(/\\Gamma/g,"Γ").replace(/\\Lambda/g,"Λ").replace(/\\Phi/g,"Φ").replace(/\\Pi/g,"Π").replace(/\\Psi/g,"Ψ").replace(/\\Sigma/g,"Σ").replace(/\\Theta/g,"Θ").replace(/\\Xi/g,"Ξ").replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"").replace(/img\s*[−-]\s*\d+\.(jpeg|jpg|png)\s*\([^)]*\)/gi,"")}catch(t){return console.error("Error processing option:",t),e}});return`
                  <div class="question">
                    <p><strong>${a}.</strong> ${i}</p>
                    <div class="options">
                      ${o.map((e,t)=>`<p>${String.fromCharCode(97+t)}) ${e}</p>`).join("")}
                      ${p?`<p><em>Answer:</em> ${e.answer}</p>`:""}
                    </div>
                  </div>`}catch(t){return console.error("Error processing question:",t),`
                    <div class="question">
                      <p><strong>${a}.</strong> ${e.question||"Error loading question"}</p>
                      <div class="options">
                        ${(e.options||[]).map((e,t)=>`<p>${String.fromCharCode(97+t)}) ${e}</p>`).join("")}
                        ${p?`<p><em>Answer:</em> ${e.answer}</p>`:""}
                      </div>
                    </div>`}}).join("")}
            </div>
          </div>`).join("")})()}
  </div>
  <footer>Medicos | ${new Date().toLocaleDateString()}</footer>
</body>
</html>`,u=await o.default.launch({args:["--no-sandbox","--disable-setuid-sandbox"]}),h=await u.newPage();await h.setContent(f,{waitUntil:"domcontentloaded"}),await h.evaluate(()=>Promise.all(Array.from(document.images).map(e=>e.complete?Promise.resolve():new Promise(t=>{e.addEventListener("load",t),e.addEventListener("error",t),setTimeout(t,3e3)})))),await h.waitForFunction(()=>void 0!==window.renderMathInElement,{timeout:5e3}).catch(()=>{}),await h.evaluate(()=>{window.renderMathInElement&&window.renderMathInElement(document.body,{delimiters:[{left:"$$",right:"$$",display:!0},{left:"$",right:"$",display:!1},{left:"\\(",right:"\\)",display:!1},{left:"\\[",right:"\\]",display:!0}],throwOnError:!1,errorColor:"#cc0000",strict:!1})}),await h.waitForFunction(()=>{let e=document.querySelectorAll('script[type="math/tex"]'),t=document.querySelectorAll(".katex");return 0===e.length||t.length>0},{timeout:5e3}).catch(()=>{}),await new Promise(e=>setTimeout(e,500));let b=await h.pdf({format:"A4",printBackground:!0,margin:{top:"20mm",right:"15mm",bottom:"20mm",left:"15mm"}});return await u.close(),new i.NextResponse(b,{status:200,headers:{"Content-Type":"application/pdf","Content-Disposition":`attachment; filename="${c}"`}})}catch(e){return console.error("PDF generation failed:",e),new i.NextResponse(JSON.stringify({error:"PDF generation failed"}),{status:500})}};a()}catch(e){a(e)}})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},83636:e=>{"use strict";e.exports=import("puppeteer")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(10589));module.exports=a})();