(()=>{var e={};e.id=2495,e.ids=[2495],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6893:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60687),o=t(43210),n=t(16189),i=t(60478);function a({children:e,allowedRoles:r,redirectTo:t="/login"}){let{user:a,userRole:d,loading:c}=(0,i.A)();(0,n.useRouter)();let[l,p]=(0,o.useState)(!1);return c||!l?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):(0,s.jsx)(s.Fragment,{children:e})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},18234:(e,r,t)=>{Promise.resolve().then(t.bind(t,78885))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20943:(e,r,t)=>{Promise.resolve().then(t.bind(t,99111))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42505:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(37413),o=t(92555);function n(){return(0,s.jsx)(o.W,{message:"Loading admin dashboard..."})}},54411:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(60687),o=t(99557),n=t(6893);function i(){return(0,s.jsx)(n.A,{allowedRoles:[o.g.TEACHER],children:(0,s.jsx)("div",{className:"container mx-auto p-6",children:(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Teacher Dashboard"})})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61193:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),o=t(66327),n=t(99557),i=t(45285),a=t(53355);function d({children:e}){return(0,s.jsx)(i.A,{allowedRoles:[n.g.SUPER_ADMIN],children:(0,s.jsx)(a.default,{children:(0,s.jsx)(o.N,{role:n.g.SUPER_ADMIN,children:e})})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78885:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\teacher\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},80775:(e,r,t)=>{Promise.resolve().then(t.bind(t,61193))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86690:(e,r,t)=>{Promise.resolve().then(t.bind(t,54411))},92369:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>c});var s=t(65239),o=t(48088),n=t(88170),i=t.n(n),a=t(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let c={children:["",{children:["admin",{children:["teacher",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78885)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,42505)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\loading.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\teacher\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/teacher/page",pathname:"/admin/teacher",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")},99111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\admin\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\FL\\medicos\\medicos-frontend\\src\\app\\admin\\layout.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,4619,3287,9592,4707,6658],()=>t(92369));module.exports=s})();